<?php
/**
 * 论坛附件管理页面
 * 管理员可以查看、下载、删除所有附件
 */

session_start();
require_once 'includes/Database.php';
require_once 'includes/Logger.php';

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// 安全防护函数
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            die('CSRF验证失败');
        }
    }
}

validateCSRFToken();

$database = new Database();
$logger = new Logger();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'delete_attachment':
            $attachmentId = (int)($_POST['attachment_id'] ?? 0);
            
            if ($attachmentId) {
                try {
                    // 获取附件信息
                    $attachment = $database->fetchOne("SELECT * FROM forum_attachments WHERE id = ?", [$attachmentId]);
                    
                    if ($attachment) {
                        // 开启事务
                        $database->beginTransaction();
                        
                        try {
                            // 删除物理文件
                            if (file_exists($attachment['file_path'])) {
                                unlink($attachment['file_path']);
                            }
                            
                            // 删除数据库记录
                            $database->query("DELETE FROM forum_attachments WHERE id = ?", [$attachmentId]);
                            
                            // 更新关联的帖子或回复的附件数量
                            if ($attachment['post_id']) {
                                $database->query("UPDATE forum_posts SET attachments_count = GREATEST(0, attachments_count - 1) WHERE id = ?", [$attachment['post_id']]);
                            }
                            if ($attachment['reply_id']) {
                                $database->query("UPDATE forum_replies SET attachments_count = GREATEST(0, attachments_count - 1) WHERE id = ?", [$attachment['reply_id']]);
                            }
                            
                            // 提交事务
                            $database->commit();
                            
                            $logger->info("管理员删除附件", ['attachment_id' => $attachmentId, 'filename' => $attachment['filename']]);
                            $successMessage = "附件删除成功！";
                        } catch (Exception $e) {
                            // 回滚事务
                            $database->rollback();
                            throw $e;
                        }
                    } else {
                        $errorMessage = "附件不存在！";
                    }
                } catch (Exception $e) {
                    $errorMessage = "删除失败：" . $e->getMessage();
                }
            }
            break;
            
        case 'batch_delete':
            $attachmentIds = array_map('intval', $_POST['attachment_ids'] ?? []);
            
            if (!empty($attachmentIds)) {
                try {
                    $deletedCount = 0;
                    
                    // 开启事务
                    $database->beginTransaction();
                    
                    foreach ($attachmentIds as $attachmentId) {
                        $attachment = $database->fetchOne("SELECT * FROM forum_attachments WHERE id = ?", [$attachmentId]);
                        
                        if ($attachment) {
                            // 删除物理文件
                            if (file_exists($attachment['file_path'])) {
                                unlink($attachment['file_path']);
                            }
                            
                            // 删除数据库记录
                            $database->query("DELETE FROM forum_attachments WHERE id = ?", [$attachmentId]);
                            
                            // 更新关联的帖子或回复的附件数量
                            if ($attachment['post_id']) {
                                $database->query("UPDATE forum_posts SET attachments_count = GREATEST(0, attachments_count - 1) WHERE id = ?", [$attachment['post_id']]);
                            }
                            if ($attachment['reply_id']) {
                                $database->query("UPDATE forum_replies SET attachments_count = GREATEST(0, attachments_count - 1) WHERE id = ?", [$attachment['reply_id']]);
                            }
                            
                            $deletedCount++;
                        }
                    }
                    
                    // 提交事务
                    $database->commit();
                    
                    $logger->info("管理员批量删除附件", ['count' => $deletedCount]);
                    $successMessage = "成功删除 {$deletedCount} 个附件！";
                } catch (Exception $e) {
                    // 回滚事务
                    $database->rollback();
                    $errorMessage = "批量删除失败：" . $e->getMessage();
                }
            }
            break;
    }
}

// 获取附件列表
$page = (int)($_GET['page'] ?? 1);
$limit = 50;
$offset = ($page - 1) * $limit;

$whereClause = "WHERE 1=1";
$params = [];

// 文件类型筛选
$fileType = $_GET['file_type'] ?? '';
if ($fileType) {
    $whereClause .= " AND file_type = ?";
    $params[] = $fileType;
}

// 搜索
$search = $_GET['search'] ?? '';
if ($search) {
    $whereClause .= " AND (filename LIKE ? OR upload_user LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

try {
    $attachments = $database->fetchAll("
        SELECT a.*, 
               p.title as post_title,
               r.content as reply_content
        FROM forum_attachments a
        LEFT JOIN forum_posts p ON a.post_id = p.id
        LEFT JOIN forum_replies r ON a.reply_id = r.id
        {$whereClause}
        ORDER BY a.upload_time DESC
        LIMIT {$limit} OFFSET {$offset}
    ", $params);
    
    $totalCount = $database->fetchValue("SELECT COUNT(*) FROM forum_attachments a {$whereClause}", $params);
    $totalPages = ceil($totalCount / $limit);
} catch (Exception $e) {
    $attachments = [];
    $totalCount = 0;
    $totalPages = 0;
}

// 获取统计信息
try {
    $stats = $database->fetchOne("
        SELECT 
            COUNT(*) as total_attachments,
            ROUND(SUM(file_size) / 1024 / 1024, 2) as total_size_mb,
            COUNT(CASE WHEN file_type = 'image' THEN 1 END) as image_count,
            COUNT(CASE WHEN file_type = 'audio' THEN 1 END) as audio_count,
            COUNT(CASE WHEN file_type = 'document' THEN 1 END) as document_count
        FROM forum_attachments
        WHERE status = 1
    ");
} catch (Exception $e) {
    $stats = [
        'total_attachments' => 0,
        'total_size_mb' => 0,
        'image_count' => 0,
        'audio_count' => 0,
        'document_count' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论坛附件管理 - 管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .file-icon {
            font-size: 1.5rem;
        }
        .attachment-preview {
            max-width: 50px;
            max-height: 50px;
            object-fit: cover;
            border-radius: 5px;
        }
        .btn-action {
            transition: all 0.2s ease;
        }
        .btn-action:hover {
            transform: scale(1.1);
        }
        .table-hover-row:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="admin.php">
                <i class="fa fa-shield me-2"></i>管理后台
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="admin.php?page=forum">
                    <i class="fa fa-arrow-left me-1"></i>返回论坛管理
                </a>
                <a class="nav-link" href="logout.php">
                    <i class="fa fa-sign-out me-1"></i>退出登录
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fa fa-paperclip me-2"></i>论坛附件管理</h2>
                <p class="text-muted">管理所有论坛附件，包括图片、音频和文档</p>
            </div>
        </div>

        <!-- 消息提示 -->
        <?php if (isset($successMessage)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fa fa-check-circle me-2"></i><?= htmlspecialchars($successMessage) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($errorMessage)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fa fa-exclamation-triangle me-2"></i><?= htmlspecialchars($errorMessage) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?= $stats['total_attachments'] ?></div>
                    <div>总附件数</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?= $stats['total_size_mb'] ?>MB<?= $stats['total_size_mb'] ?>MB</div>
                    <div>总大小</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?= $stats['image_count'] ?></div>
                    <div>图片文件</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?= $stats['audio_count'] ?></div>
                    <div>音频文件</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?= $stats['document_count'] ?></div>
                    <div>文档文件</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?= $totalPages ?></div>
                    <div>总页数</div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">搜索</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fa fa-search"></i></span>
                            <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="文件名或上传用户...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">文件类型</label>
                        <select class="form-select" name="file_type">
                            <option value="">全部类型</option>
                            <option value="image" <?= $fileType === 'image' ? 'selected' : '' ?>>图片</option>
                            <option value="audio" <?= $fileType === 'audio' ? 'selected' : '' ?>>音频</option>
                            <option value="document" <?= $fileType === 'document' ? 'selected' : '' ?>>文档</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary btn-action">
                                <i class="fa fa-search me-1"></i>搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">批量操作</label>
                        <div>
                            <button type="button" class="btn btn-danger btn-action" onclick="batchDelete()">
                                <i class="fa fa-trash me-1"></i>批量删除
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 附件列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fa fa-list me-2"></i>附件列表
                    <span class="badge bg-primary ms-2"><?= $totalCount ?> 个附件</span>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($attachments)): ?>
                    <div class="text-center py-5">
                        <i class="fa fa-inbox" style="font-size: 3rem; color: #dee2e6;"></i>
                        <h5 class="mt-3 text-muted">暂无附件</h5>
                        <p class="text-muted">用户上传的附件将在这里显示</p>
                    </div>
                <?php else: ?>
                    <form id="batchForm" method="POST">
                        <input type="hidden" name="action" value="batch_delete">
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAll" onchange="toggleAll(this)">
                                        </th>
                                        <th>预览</th>
                                        <th>文件信息</th>
                                        <th>类型</th>
                                        <th>大小</th>
                                        <th>上传者</th>
                                        <th>关联内容</th>
                                        <th>上传时间</th>
                                        <th>下载次数</th>
                                        <th width="120">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($attachments as $attachment): ?>
                                    <tr class="table-hover-row">
                                        <td>
                                            <input type="checkbox" name="attachment_ids[]" value="<?= $attachment['id'] ?>">
                                        </td>
                                        <td>
                                            <?php if ($attachment['file_type'] === 'image'): ?>
                                                <img src="<?= htmlspecialchars($attachment['file_path']) ?>" class="attachment-preview" alt="预览">
                                            <?php else: ?>
                                                <i class="file-icon <?= getFileTypeIcon($attachment['file_type']) ?>"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?= htmlspecialchars($attachment['filename']) ?></div>
                                            <small class="text-muted">ID: <?= $attachment['id'] ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= getFileTypeBadge($attachment['file_type']) ?>">
                                                <?= ucfirst($attachment['file_type']) ?>
                                            </span>
                                        </td>
                                        <td><?= formatFileSize($attachment['file_size']) ?></td>
                                        <td>
                                            <span class="badge bg-info"><?= htmlspecialchars($attachment['upload_user']) ?></span>
                                        </td>
                                        <td>
                                            <?php if ($attachment['post_id']): ?>
                                                <small class="text-primary">
                                                    <i class="fa fa-comment-o me-1"></i>
                                                    <?= htmlspecialchars(mb_substr($attachment['post_title'], 0, 30)) ?>...
                                                </small>
                                            <?php elseif ($attachment['reply_id']): ?>
                                                <small class="text-success">
                                                    <i class="fa fa-comments-o me-1"></i>
                                                    <?= htmlspecialchars(mb_substr($attachment['reply_content'], 0, 30)) ?>...
                                                </small>
                                            <?php else: ?>
                                                <small class="text-muted">未关联</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?= date('Y-m-d H:i', strtotime($attachment['upload_time'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= $attachment['download_count'] ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="forum_upload_api.php?action=download&id=<?= $attachment['id'] ?>" 
                                                   class="btn btn-outline-primary btn-action" title="下载" target="_blank">
                                                    <i class="fa fa-download"></i>
                                                </a>
                                                <button class="btn btn-outline-danger btn-action" 
                                                        onclick="deleteAttachment(<?= $attachment['id'] ?>, '<?= htmlspecialchars($attachment['filename']) ?>')" 
                                                        title="删除">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>&file_type=<?= urlencode($fileType) ?>&search=<?= urlencode($search) ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleAll(checkbox) {
            const checkboxes = document.querySelectorAll('input[name="attachment_ids[]"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        function deleteAttachment(id, filename) {
            if (confirm(`确定要删除附件 "${filename}" 吗？此操作不可撤销！`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_attachment">
                    <input type="hidden" name="attachment_id" value="${id}">
                    <input type="hidden" name="csrf_token" value="${document.querySelector('input[name="csrf_token"]').value}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function batchDelete() {
            const checked = document.querySelectorAll('input[name="attachment_ids[]"]:checked');
            if (checked.length === 0) {
                alert('请选择要删除的附件！');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${checked.length} 个附件吗？此操作不可撤销！`)) {
                document.getElementById('batchForm').submit();
            }
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card, .stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('opacity-100');
                    card.classList.remove('opacity-0', 'translate-y-4');
                }, 100 * index);
            });
        });
    </script>
</body>
</html>

<?php
function getFileTypeIcon($fileType) {
    switch ($fileType) {
        case 'image': return 'fa fa-file-image-o text-primary';
        case 'audio': return 'fa fa-file-audio-o text-success';
        case 'document': return 'fa fa-file-text-o text-warning';
        default: return 'fa fa-file-o text-secondary';
    }
}

function getFileTypeBadge($fileType) {
    switch ($fileType) {
        case 'image': return 'primary';
        case 'audio': return 'success';
        case 'document': return 'warning';
        default: return 'secondary';
    }
}

function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return round($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return round($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}
?>
    