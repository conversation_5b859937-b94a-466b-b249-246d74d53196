"""
论坛对话框模块 - 帖子详情、发帖、回复等对话框
"""

import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.gui.pyqt_ui.pyqt_design_system import DS
from src.utils.logger import get_logger


class PostDetailDialog(QDialog):
    """帖子详情对话框"""
    
    def __init__(self, post_data, parent=None):
        super().__init__(parent)
        self.post_data = post_data
        self.parent_widget = parent
        self.logger = get_logger()
        self.replies = []
        self._setup_ui()
        self._load_replies()
        
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("帖子详情")
        
        # 根据屏幕大小设置窗口尺寸 - 增加宽度10%
        screen = QApplication.desktop().screenGeometry()
        # 原来是0.8，增加10%后变为0.88 (0.8 * 1.1)
        window_width = min(1100, int(screen.width() * 0.88))  # 最大宽度也增加10% (1000 * 1.1)
        window_height = min(800, int(screen.height() * 0.8))
        self.resize(window_width, window_height)
        
        # 居中显示
        self.move(
            (screen.width() - window_width) // 2,
            (screen.height() - window_height) // 2
        )
        
        self.setStyleSheet(f"""
            QDialog {{
                background: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QTextEdit {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                padding: 15px;
                color: {DS.COLORS['text_primary'].name()};
                font-size: 14px;
                line-height: 1.6;
            }}
            QPushButton {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 8px 16px;
                color: {DS.COLORS['text_primary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 帖子头部信息
        self._create_post_header(main_layout)
        
        # 帖子内容
        self._create_post_content(main_layout)
        
        # 回复区域
        self._create_replies_section(main_layout)
        
        # 底部按钮
        self._create_bottom_buttons(main_layout)
        
    def _create_post_header(self, main_layout):
        """创建帖子头部信息"""
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(15)
        
        # 标题行
        title_layout = QHBoxLayout()
        title_layout.setSpacing(15)
        
        # 帖子类型图标
        post_type = self.post_data.get('post_type', 'discussion')
        type_icons = {
            'discussion': '💬',
            'question': '❓',
            'announcement': '📢',
            'feedback': '💡'
        }
        
        type_icon = QLabel(type_icons.get(post_type, '💬'))
        type_icon.setFont(QFont('Segoe UI Emoji', 18))
        title_layout.addWidget(type_icon)
        
        # 置顶标识
        if self.post_data.get('status') == 2:
            pin_label = QLabel('📌 置顶')
            pin_label.setStyleSheet(f"color: {DS.COLORS['neon_red'].name()}; font-weight: bold;")
            title_layout.addWidget(pin_label)
        
        # 标题
        title = self.post_data.get('title', '无标题')
        title_label = QLabel(title)
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        title_label.setWordWrap(True)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        header_layout.addLayout(title_layout)
        
        # 元信息行
        meta_layout = QHBoxLayout()
        meta_layout.setSpacing(30)
        
        # 作者
        author = self.post_data.get('author', '匿名')
        author_label = QLabel(f"👤 作者: {author}")
        author_label.setFont(DS.get_font('body_md'))
        author_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        meta_layout.addWidget(author_label)
        
        # 发布时间
        created_at = self.post_data.get('created_at', '')
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                time_str = dt.strftime('%Y年%m月%d日 %H:%M')
                time_label = QLabel(f"🕒 发布: {time_str}")
                time_label.setFont(DS.get_font('body_md'))
                time_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
                meta_layout.addWidget(time_label)
            except:
                pass
        
        # 统计信息
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)

        replies_count = self._safe_int(self.post_data.get('replies_count', 0))
        stats_layout.addWidget(QLabel(f"💬 {replies_count} 回复"))

        likes_count = self._safe_int(self.post_data.get('likes_count', 0))
        stats_layout.addWidget(QLabel(f"👍 {likes_count} 点赞"))

        views_count = self._safe_int(self.post_data.get('views_count', 0))
        stats_layout.addWidget(QLabel(f"👁️ {views_count} 浏览"))
        
        for i in range(stats_layout.count()):
            widget = stats_layout.itemAt(i).widget()
            if widget:
                widget.setFont(DS.get_font('body_sm'))
                widget.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        
        meta_layout.addLayout(stats_layout)
        meta_layout.addStretch()
        header_layout.addLayout(meta_layout)
        
        main_layout.addWidget(header_frame)
        
    def _create_post_content(self, main_layout):
        """创建帖子内容区域"""
        content_frame = QFrame()
        content_frame.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(10)
        
        # 内容标题
        content_title = QLabel("📄 帖子内容")
        content_title.setFont(DS.get_font('heading_md'))
        content_title.setStyleSheet(f"color: {DS.COLORS['neon_cyan'].name()}; font-weight: bold;")
        content_layout.addWidget(content_title)
        
        # 内容文本
        content_area = QTextEdit()
        content_area.setPlainText(self.post_data.get('content', ''))
        content_area.setReadOnly(True)
        content_area.setMinimumHeight(150)
        content_layout.addWidget(content_area)
        
        main_layout.addWidget(content_frame)

    def _safe_int(self, value, default=0):
        """安全的整数转换"""
        try:
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                return int(value) if value.isdigit() else default
            else:
                return default
        except (ValueError, TypeError):
            return default

    def _create_replies_section(self, main_layout):
        """创建回复区域"""
        replies_frame = QFrame()
        replies_frame.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        
        replies_layout = QVBoxLayout(replies_frame)
        replies_layout.setSpacing(15)
        
        # 回复标题
        replies_title_layout = QHBoxLayout()
        replies_title_layout.setSpacing(15)
        
        replies_title = QLabel("💬 回复列表")
        replies_title.setFont(DS.get_font('heading_md'))
        replies_title.setStyleSheet(f"color: {DS.COLORS['neon_cyan'].name()}; font-weight: bold;")
        replies_title_layout.addWidget(replies_title)
        
        replies_title_layout.addStretch()
        
        # 发表回复按钮
        reply_btn = QPushButton("✏️ 发表回复")
        reply_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['neon_green'].name()};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_green'].darker(110).name()};
            }}
        """)
        reply_btn.clicked.connect(self._show_reply_dialog)
        replies_title_layout.addWidget(reply_btn)
        
        replies_layout.addLayout(replies_title_layout)
        
        # 回复列表滚动区域
        self.replies_scroll = QScrollArea()
        self.replies_scroll.setWidgetResizable(True)
        self.replies_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.replies_scroll.setMinimumHeight(200)
        
        self.replies_content = QWidget()
        self.replies_list_layout = QVBoxLayout(self.replies_content)
        self.replies_list_layout.setContentsMargins(0, 0, 0, 0)
        self.replies_list_layout.setSpacing(10)
        self.replies_list_layout.setAlignment(Qt.AlignTop)
        
        self.replies_scroll.setWidget(self.replies_content)
        replies_layout.addWidget(self.replies_scroll)
        
        main_layout.addWidget(replies_frame)
        
    def _create_bottom_buttons(self, main_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        button_layout.addStretch()
        
        # 点赞按钮
        like_btn = QPushButton("👍 点赞")
        like_btn.clicked.connect(self._like_post)
        button_layout.addWidget(like_btn)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)
        
        main_layout.addLayout(button_layout)
        
    def _load_replies(self):
        """加载回复列表"""
        try:
            # 使用统一的API客户端获取URL
            try:
                try:
                    from utils.forum_api_client import get_forum_api_client
                except ImportError:
                    from src.utils.forum_api_client import get_forum_api_client
                api_client = get_forum_api_client()
                api_url = f"{api_client.base_url}/forum_api.php"
            except Exception:
                # 降级到硬编码URL
                api_url = "http://192.168.10.55:777/forum_api.php"
            
            # 请求回复数据
            params = {
                'action': 'get_replies',
                'post_id': self.post_data.get('id')
            }
            
            response = requests.get(api_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                self.replies = data.get('data', [])
                self._display_replies()
            else:
                self._show_replies_error(data.get('message', '获取回复失败'))
                
        except Exception as e:
            self._show_replies_error(f"加载回复失败: {str(e)}")
            
    def _display_replies(self):
        """显示回复列表"""
        # 清空现有回复
        while self.replies_list_layout.count():
            child = self.replies_list_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        if not self.replies:
            # 显示空状态
            empty_label = QLabel("暂无回复，快来抢沙发吧！")
            empty_label.setFont(DS.get_font('body_lg'))
            empty_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; padding: 40px;")
            empty_label.setAlignment(Qt.AlignCenter)
            self.replies_list_layout.addWidget(empty_label)
        else:
            # 显示回复
            for reply in self.replies:
                reply_widget = self._create_reply_widget(reply)
                self.replies_list_layout.addWidget(reply_widget)
        
        # 添加弹性空间
        self.replies_list_layout.addStretch()
        
    def _create_reply_widget(self, reply_data):
        """创建回复组件"""
        reply_frame = QFrame()
        reply_frame.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_primary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0;
            }}
        """)
        
        reply_layout = QVBoxLayout(reply_frame)
        reply_layout.setSpacing(10)
        
        # 回复头部
        header_layout = QHBoxLayout()
        header_layout.setSpacing(15)
        
        # 作者
        author = reply_data.get('author', '匿名')
        author_label = QLabel(f"👤 {author}")
        author_label.setFont(DS.get_font('body_md'))
        author_label.setStyleSheet(f"color: {DS.COLORS['neon_cyan'].name()}; font-weight: bold;")
        header_layout.addWidget(author_label)
        
        # 时间
        created_at = reply_data.get('created_at', '')
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                time_str = dt.strftime('%m-%d %H:%M')
                time_label = QLabel(f"🕒 {time_str}")
                time_label.setFont(DS.get_font('body_sm'))
                time_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
                header_layout.addWidget(time_label)
            except:
                pass
        
        header_layout.addStretch()
        
        # 点赞数 - 安全的类型转换
        likes_count_raw = reply_data.get('likes_count', 0)
        try:
            likes_count = int(likes_count_raw) if str(likes_count_raw).isdigit() else 0
        except (ValueError, TypeError):
            likes_count = 0

        if likes_count > 0:
            likes_label = QLabel(f"👍 {likes_count}")
            likes_label.setFont(DS.get_font('body_sm'))
            likes_label.setStyleSheet(f"color: {DS.COLORS['neon_green'].name()};")
            header_layout.addWidget(likes_label)
        
        reply_layout.addLayout(header_layout)
        
        # 回复内容
        content = reply_data.get('content', '')
        content_label = QLabel(content)
        content_label.setFont(DS.get_font('body_md'))
        content_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        content_label.setWordWrap(True)
        reply_layout.addWidget(content_label)
        
        return reply_frame
        
    def _show_replies_error(self, error_message):
        """显示回复加载错误"""
        error_label = QLabel(f"❌ {error_message}")
        error_label.setFont(DS.get_font('body_lg'))
        error_label.setStyleSheet(f"color: {DS.COLORS['neon_red'].name()}; padding: 40px;")
        error_label.setAlignment(Qt.AlignCenter)
        self.replies_list_layout.addWidget(error_label)
        
    def _show_reply_dialog(self):
        """显示回复对话框"""
        try:
            dialog = ReplyDialog(self.post_data, self)
            if dialog.exec_() == QDialog.Accepted:
                # 回复成功，重新加载回复列表
                self._load_replies()
        except Exception as e:
            if self.logger:
                self.logger.error(f"显示回复对话框失败: {e}")
                
    def _like_post(self):
        """点赞帖子"""
        try:
            # TODO: 实现点赞功能
            QMessageBox.information(self, "提示", "点赞功能开发中...")
        except Exception as e:
            if self.logger:
                self.logger.error(f"点赞失败: {e}")


class NewPostDialog(QDialog):
    """发帖对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.logger = get_logger()
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("发布新帖")
        self.resize(800, 600)
        
        # 居中显示
        screen = QApplication.desktop().screenGeometry()
        self.move(
            (screen.width() - self.width()) // 2,
            (screen.height() - self.height()) // 2
        )
        
        self.setStyleSheet(f"""
            QDialog {{
                background: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QLineEdit, QTextEdit, QComboBox {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 10px;
                color: {DS.COLORS['text_primary'].name()};
                font-size: 14px;
            }}
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
            QPushButton {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 10px 20px;
                color: {DS.COLORS['text_primary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("✏️ 发布新帖")
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        main_layout.addWidget(title_label)
        
        # 表单区域
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # 帖子类型
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "💬 讨论",
            "❓ 问答", 
            "💡 反馈"
        ])
        form_layout.addRow("帖子类型:", self.type_combo)
        
        # 标题
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("请输入帖子标题...")
        form_layout.addRow("标题:", self.title_edit)
        
        # 内容
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("请输入帖子内容...")
        self.content_edit.setMinimumHeight(300)
        form_layout.addRow("内容:", self.content_edit)
        
        main_layout.addLayout(form_layout)

        # 进度指示器（初始隐藏）
        self.progress_widget = QWidget()
        progress_layout = QHBoxLayout(self.progress_widget)
        progress_layout.setContentsMargins(0, 0, 0, 0)

        from PyQt5.QtWidgets import QProgressBar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 4px;
                background: {DS.COLORS['bg_tertiary'].name()};
                text-align: center;
                color: {DS.COLORS['text_primary'].name()};
                height: 20px;
            }}
            QProgressBar::chunk {{
                background: {DS.COLORS['neon_cyan'].name()};
                border-radius: 3px;
            }}
        """)

        self.progress_label = QLabel("正在发布帖子...")
        self.progress_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)
        self.progress_widget.hide()  # 初始隐藏

        main_layout.addWidget(self.progress_widget)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['bg_tertiary'].name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['glass_border'].name()};
            }}
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        submit_btn = QPushButton("发布帖子")
        submit_btn.setObjectName("submit_btn")  # 设置对象名用于查找
        submit_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['neon_green'].name()};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_green'].darker(110).name()};
            }}
            QPushButton:disabled {{
                background: #666666;
                color: #999999;
            }}
        """)
        submit_btn.clicked.connect(self._submit_post)
        button_layout.addWidget(submit_btn)
        
        main_layout.addLayout(button_layout)
        
    def _submit_post(self):
        """提交帖子"""
        try:
            # 验证输入
            title = self.title_edit.text().strip()
            content = self.content_edit.toPlainText().strip()

            if not title:
                QMessageBox.warning(self, "警告", "请输入帖子标题")
                return

            if not content:
                QMessageBox.warning(self, "警告", "请输入帖子内容")
                return

            # 获取帖子类型
            type_map = {
                "💬 讨论": "discussion",
                "❓ 问答": "question",
                "💡 反馈": "feedback"
            }
            post_type = type_map.get(self.type_combo.currentText(), "discussion")

            # 获取当前用户信息
            username = self._get_current_username()
            if not username:
                QMessageBox.warning(self, "警告", "无法获取用户信息，请重新登录")
                return

            # 显示加载状态
            self._set_loading_state(True)

            # 调用发帖API
            self._create_post_async(username, title, content, post_type)

        except Exception as e:
            if self.logger:
                self.logger.error(f"发帖失败: {e}")
            QMessageBox.critical(self, "错误", f"发帖失败: {str(e)}")
            self._set_loading_state(False)

    def _get_current_username(self):
        """获取当前登录用户名"""
        try:
            # 方法1：从父窗口获取安全管理器
            if hasattr(self.parent_widget, 'main_window') and self.parent_widget.main_window:
                main_window = self.parent_widget.main_window
                if hasattr(main_window, 'security_manager') and main_window.security_manager:
                    user_info = main_window.security_manager.logged_in_user
                    if user_info and user_info.get('username'):
                        username = user_info.get('username')
                        if self.logger:
                            self.logger.info(f"从安全管理器获取用户名: {username}")
                        return username

            # 方法2：直接从父窗口的main_window获取
            if hasattr(self.parent_widget, 'main_window'):
                main_window = self.parent_widget.main_window
                if hasattr(main_window, 'security_manager'):
                    security_manager = main_window.security_manager
                    if security_manager and hasattr(security_manager, 'logged_in_user'):
                        user_info = security_manager.logged_in_user
                        if user_info and user_info.get('username'):
                            username = user_info.get('username')
                            if self.logger:
                                self.logger.info(f"从主窗口安全管理器获取用户名: {username}")
                            return username

            # 方法3：从配置获取
            try:
                from utils.config_manager import ConfigManager
            except ImportError:
                from src.utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            config_user = config_manager.get('current_user', {}).get('username')
            if config_user and config_user != 'guest':
                if self.logger:
                    self.logger.info(f"从配置获取用户名: {config_user}")
                return config_user

            # 方法4：从认证缓存获取
            try:
                import os
                cache_file = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'temp', 'auth_cache.json')
                if os.path.exists(cache_file):
                    import json
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                        cached_user = cache_data.get('username')
                        if cached_user and cached_user != 'guest':
                            if self.logger:
                                self.logger.info(f"从缓存获取用户名: {cached_user}")
                            return cached_user
            except Exception:
                pass

            # 默认用户名
            if self.logger:
                self.logger.warning("无法获取用户名，使用默认值: guest")
            return 'guest'

        except Exception as e:
            if self.logger:
                self.logger.warning(f"获取用户名失败: {e}")
            return 'guest'

    def _set_loading_state(self, loading: bool):
        """设置加载状态"""
        # 禁用/启用输入控件
        self.title_edit.setEnabled(not loading)
        self.content_edit.setEnabled(not loading)
        self.type_combo.setEnabled(not loading)

        # 显示/隐藏进度指示器
        if loading:
            self.progress_widget.show()
        else:
            self.progress_widget.hide()

        # 更新按钮状态
        submit_btn = self.findChild(QPushButton, "submit_btn")
        cancel_btn = self.findChild(QPushButton, "cancel_btn")

        if submit_btn:
            if loading:
                submit_btn.setText("发布中...")
                submit_btn.setEnabled(False)
            else:
                submit_btn.setText("发布帖子")
                submit_btn.setEnabled(True)

        if cancel_btn:
            cancel_btn.setEnabled(not loading)

    def _create_post_async(self, username: str, title: str, content: str, post_type: str):
        """异步创建帖子"""
        from PyQt5.QtCore import QThread, pyqtSignal

        class PostCreator(QThread):
            success = pyqtSignal(dict)
            error = pyqtSignal(str)

            def __init__(self, username, title, content, post_type):
                super().__init__()
                self.username = username
                self.title = title
                self.content = content
                self.post_type = post_type

            def run(self):
                try:
                    try:
                        from utils.forum_api_client import get_forum_api_client
                    except ImportError:
                        from src.utils.forum_api_client import get_forum_api_client
                    # 获取主窗口引用
                    main_window = None
                    try:
                        # 从父页面获取主窗口
                        import sys
                        for obj in sys.modules.values():
                            if hasattr(obj, '__dict__'):
                                for attr_name, attr_value in obj.__dict__.items():
                                    if hasattr(attr_value, 'main_window'):
                                        main_window = attr_value.main_window
                                        break
                                if main_window:
                                    break
                    except:
                        pass

                    api_client = get_forum_api_client(main_window=main_window)

                    success, response = api_client.create_post(
                        self.username, self.title, self.content, self.post_type
                    )

                    if success:
                        self.success.emit(response)
                    else:
                        error_msg = response.get('message', '发帖失败')
                        self.error.emit(error_msg)

                except Exception as e:
                    self.error.emit(f"网络错误: {str(e)}")

        # 创建并启动线程
        self.post_creator = PostCreator(username, title, content, post_type)
        self.post_creator.success.connect(self._on_post_success)
        self.post_creator.error.connect(self._on_post_error)
        self.post_creator.start()

    def _on_post_success(self, response: dict):
        """发帖成功回调"""
        self._set_loading_state(False)

        post_id = response.get('data', {}).get('post_id', 0)
        title = self.title_edit.text().strip()

        # 创建成功对话框
        success_dialog = QMessageBox(self)
        success_dialog.setWindowTitle("发帖成功")
        success_dialog.setIcon(QMessageBox.Information)
        success_dialog.setText(f"🎉 帖子发布成功！")
        success_dialog.setInformativeText(f"标题：{title}\n帖子ID：{post_id}")
        success_dialog.setStandardButtons(QMessageBox.Ok)

        # 设置样式
        success_dialog.setStyleSheet(f"""
            QMessageBox {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QMessageBox QPushButton {{
                background-color: {DS.COLORS['neon_green'].name()};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {DS.COLORS['neon_green'].darker(110).name()};
            }}
        """)

        success_dialog.exec_()

        if self.logger:
            self.logger.info(f"发帖成功: 标题='{title}', ID={post_id}")

        self.accept()

    def _on_post_error(self, error_msg: str):
        """发帖失败回调"""
        self._set_loading_state(False)

        # 创建错误对话框
        error_dialog = QMessageBox(self)
        error_dialog.setWindowTitle("发帖失败")
        error_dialog.setIcon(QMessageBox.Critical)
        error_dialog.setText("❌ 发帖失败")
        error_dialog.setInformativeText(f"错误信息：{error_msg}")
        error_dialog.setStandardButtons(QMessageBox.Ok | QMessageBox.Retry)

        # 设置样式
        error_dialog.setStyleSheet(f"""
            QMessageBox {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QMessageBox QPushButton {{
                background-color: {DS.COLORS['bg_tertiary'].name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {DS.COLORS['neon_cyan'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
        """)

        result = error_dialog.exec_()

        # 如果用户选择重试
        if result == QMessageBox.Retry:
            self._submit_post()

        if self.logger:
            self.logger.error(f"发帖失败: {error_msg}")


class ReplyDialog(QDialog):
    """回复对话框"""
    
    def __init__(self, post_data, parent=None):
        super().__init__(parent)
        self.post_data = post_data
        self.parent_widget = parent
        self.logger = get_logger()
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("发表回复")
        self.resize(600, 400)
        
        # 居中显示
        screen = QApplication.desktop().screenGeometry()
        self.move(
            (screen.width() - self.width()) // 2,
            (screen.height() - self.height()) // 2
        )
        
        self.setStyleSheet(f"""
            QDialog {{
                background: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QTextEdit {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 15px;
                color: {DS.COLORS['text_primary'].name()};
                font-size: 14px;
            }}
            QTextEdit:focus {{
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
            QPushButton {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 10px 20px;
                color: {DS.COLORS['text_primary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # 标题
        title_label = QLabel(f"💬 回复: {self.post_data.get('title', '无标题')}")
        title_label.setFont(DS.get_font('heading_md'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        title_label.setWordWrap(True)
        main_layout.addWidget(title_label)
        
        # 回复内容
        content_label = QLabel("回复内容:")
        content_label.setFont(DS.get_font('body_md'))
        content_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        main_layout.addWidget(content_label)
        
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("请输入回复内容...")
        self.content_edit.setMinimumHeight(200)
        main_layout.addWidget(self.content_edit)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        submit_btn = QPushButton("发表回复")
        submit_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['neon_green'].name()};
                color: white;
                border: none;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_green'].darker(110).name()};
            }}
        """)
        submit_btn.clicked.connect(self._submit_reply)
        button_layout.addWidget(submit_btn)
        
        main_layout.addLayout(button_layout)
        
    def _submit_reply(self):
        """提交回复"""
        try:
            # 验证输入
            content = self.content_edit.toPlainText().strip()
            
            if not content:
                QMessageBox.warning(self, "警告", "请输入回复内容")
                return
            
            # TODO: 实现回复API调用
            QMessageBox.information(self, "提示", "回复功能开发中...")
            self.accept()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"回复失败: {e}")
            QMessageBox.critical(self, "错误", f"回复失败: {str(e)}")
