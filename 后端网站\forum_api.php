<?php
/**
 * 论坛API接口
 * 为前端论坛功能提供数据接口
 */

declare(strict_types=1);

// 引入核心类
require_once __DIR__ . '/includes/Database.php';
require_once __DIR__ . '/includes/Logger.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 初始化
try {
    $database = new Database();
    $logger = new Logger();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败',
        'error' => $e->getMessage()
    ]);
    exit;
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

// 验证用户身份（简单验证）
function validateUser($username, $database) {
    try {
        $user = $database->fetchOne("SELECT id, username, user_type, status FROM users WHERE username = ? AND status = 1", [$username]);
        return $user ?: false;
    } catch (Exception $e) {
        return false;
    }
}

// 响应函数
function sendResponse($success, $data = null, $message = '', $code = 200) {
    http_response_code($code);
    $response = ['success' => $success];
    if ($message) $response['message'] = $message;
    if ($data !== null) $response['data'] = $data;
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 处理请求
try {
    switch ($action) {
        case 'get_posts':
            // 获取帖子列表
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            $type = $_GET['type'] ?? '';
            
            $whereClause = "WHERE p.status IN (1, 2)"; // 1=正常, 2=置顶
            $params = [];
            
            if ($type && in_array($type, ['discussion', 'question', 'announcement', 'feedback'])) {
                $whereClause .= " AND p.post_type = ?";
                $params[] = $type;
            }
            
            $posts = $database->fetchAll("
                SELECT p.*, 
                       (SELECT COUNT(*) FROM forum_replies WHERE post_id = p.id AND status = 1) as replies_count,
                       (SELECT COUNT(*) FROM forum_likes WHERE target_type = 'post' AND target_id = p.id) as likes_count
                FROM forum_posts p 
                {$whereClause}
                ORDER BY 
                    CASE WHEN p.status = 2 THEN 0 ELSE 1 END,
                    p.created_at DESC 
                LIMIT {$limit} OFFSET {$offset}
            ", $params);
            
            // 获取总数
            $total = $database->fetchValue("SELECT COUNT(*) FROM forum_posts p {$whereClause}", $params);
            
            sendResponse(true, [
                'posts' => $posts,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            break;
            
        case 'get_post':
            // 获取单个帖子详情
            $postId = (int)($_GET['id'] ?? 0);
            if ($postId <= 0) {
                sendResponse(false, null, '帖子ID无效', 400);
            }
            
            $post = $database->fetchOne("
                SELECT p.*, 
                       (SELECT COUNT(*) FROM forum_replies WHERE post_id = p.id AND status = 1) as replies_count,
                       (SELECT COUNT(*) FROM forum_likes WHERE target_type = 'post' AND target_id = p.id) as likes_count
                FROM forum_posts p 
                WHERE p.id = ? AND p.status IN (1, 2)
            ", [$postId]);
            
            if (!$post) {
                sendResponse(false, null, '帖子不存在', 404);
            }
            
            // 增加浏览次数
            $database->query("UPDATE forum_posts SET views_count = views_count + 1 WHERE id = ?", [$postId]);
            $post['views_count']++;
            
            sendResponse(true, $post);
            break;
            
        case 'create_post':
            // 创建新帖子
            if ($method !== 'POST') {
                sendResponse(false, null, '请求方法错误', 405);
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            $username = trim($input['username'] ?? '');
            $title = trim($input['title'] ?? '');
            $content = trim($input['content'] ?? '');
            $postType = $input['post_type'] ?? 'discussion';
            $attachments = $input['attachments'] ?? [];

            // 验证参数
            if (empty($username) || empty($title) || empty($content)) {
                sendResponse(false, null, '参数不完整', 400);
            }

            if (!in_array($postType, ['discussion', 'question', 'announcement', 'feedback'])) {
                $postType = 'discussion';
            }

            // 验证用户
            $user = validateUser($username, $database);
            if (!$user) {
                sendResponse(false, null, '用户验证失败', 401);
            }

            // 插入帖子
            $postId = $database->insert('forum_posts', [
                'title' => $title,
                'content' => $content,
                'author' => $username,
                'author_qq' => $user['qq_number'] ?? null,
                'post_type' => $postType,
                'status' => 1,
                'likes_count' => 0,
                'replies_count' => 0,
                'views_count' => 0,
                'attachments_count' => 0
            ]);

            // 处理附件
            $attachmentCount = 0;
            if (!empty($attachments) && is_array($attachments)) {
                foreach ($attachments as $attachment) {
                    try {
                        // 检查附件信息
                        if (!isset($attachment['name']) || !isset($attachment['size'])) {
                            continue;
                        }

                        $attachmentData = [
                            'post_id' => $postId,
                            'filename' => $attachment['name'],
                            'file_size' => (int)$attachment['size'],
                            'file_type' => $attachment['type'] ?? 'file',
                            'upload_user' => $username,
                            'status' => 1
                        ];

                        // 如果有上传的文件信息，添加更多字段
                        if (isset($attachment['url'])) {
                            $attachmentData['file_url'] = $attachment['url'];
                        }
                        if (isset($attachment['file_id'])) {
                            $attachmentData['file_id'] = $attachment['file_id'];
                        }
                        if (isset($attachment['uploaded']) && $attachment['uploaded']) {
                            $attachmentData['upload_status'] = 'uploaded';
                        } else {
                            $attachmentData['upload_status'] = 'local';
                            $attachmentData['local_path'] = $attachment['path'] ?? '';
                        }

                        // 插入附件记录
                        $attachmentId = $database->insert('forum_attachments', $attachmentData);

                        if ($attachmentId) {
                            $attachmentCount++;
                            $logger->info("帖子附件添加", [
                                'post_id' => $postId,
                                'attachment_id' => $attachmentId,
                                'filename' => $attachment['name'],
                                'user' => $username
                            ]);
                        }

                    } catch (Exception $e) {
                        $logger->warning("处理附件失败", [
                            'post_id' => $postId,
                            'filename' => $attachment['name'] ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }

            // 更新帖子的附件数量
            if ($attachmentCount > 0) {
                $database->query("UPDATE forum_posts SET attachments_count = ? WHERE id = ?", [$attachmentCount, $postId]);
            }

            $logger->info("论坛帖子创建", [
                'post_id' => $postId,
                'author' => $username,
                'title' => $title,
                'attachments_count' => $attachmentCount
            ]);

            sendResponse(true, [
                'post_id' => $postId,
                'attachments_count' => $attachmentCount
            ], '帖子发布成功');
            break;
            
        case 'get_replies':
            // 获取帖子回复
            $postId = (int)($_GET['post_id'] ?? 0);
            if ($postId <= 0) {
                sendResponse(false, null, '帖子ID无效', 400);
            }
            
            $replies = $database->fetchAll("
                SELECT r.*, 
                       (SELECT COUNT(*) FROM forum_likes WHERE target_type = 'reply' AND target_id = r.id) as likes_count
                FROM forum_replies r 
                WHERE r.post_id = ? AND r.status = 1
                ORDER BY r.created_at ASC
            ", [$postId]);
            
            sendResponse(true, $replies);
            break;
            
        case 'create_reply':
            // 创建回复
            if ($method !== 'POST') {
                sendResponse(false, null, '请求方法错误', 405);
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            $username = trim($input['username'] ?? '');
            $postId = (int)($input['post_id'] ?? 0);
            $content = trim($input['content'] ?? '');
            $parentId = (int)($input['parent_id'] ?? 0) ?: null;
            
            // 验证参数
            if (empty($username) || $postId <= 0 || empty($content)) {
                sendResponse(false, null, '参数不完整', 400);
            }
            
            // 验证用户
            $user = validateUser($username, $database);
            if (!$user) {
                sendResponse(false, null, '用户验证失败', 401);
            }
            
            // 验证帖子存在
            $post = $database->fetchOne("SELECT id FROM forum_posts WHERE id = ? AND status IN (1, 2)", [$postId]);
            if (!$post) {
                sendResponse(false, null, '帖子不存在', 404);
            }
            
            // 插入回复
            $replyId = $database->insert('forum_replies', [
                'post_id' => $postId,
                'content' => $content,
                'author' => $username,
                'author_qq' => $user['qq_number'] ?? null,
                'parent_id' => $parentId,
                'status' => 1,
                'likes_count' => 0
            ]);
            
            // 更新帖子回复数
            $database->query("UPDATE forum_posts SET replies_count = replies_count + 1 WHERE id = ?", [$postId]);
            
            $logger->info("论坛回复创建", ['reply_id' => $replyId, 'post_id' => $postId, 'author' => $username]);
            
            sendResponse(true, ['reply_id' => $replyId], '回复发布成功');
            break;

        case 'delete_post':
            // 删除帖子
            if ($method !== 'POST') {
                sendResponse(false, null, '请求方法错误', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true);
            $username = trim($input['username'] ?? '');
            $postId = (int)($input['post_id'] ?? 0);

            // 验证参数
            if (empty($username) || $postId <= 0) {
                sendResponse(false, null, '参数不完整', 400);
            }

            // 验证用户
            $user = validateUser($username, $database);
            if (!$user) {
                sendResponse(false, null, '用户验证失败', 401);
            }

            // 验证帖子存在并检查权限
            $post = $database->fetchOne("SELECT id, author, title FROM forum_posts WHERE id = ? AND status IN (1, 2)", [$postId]);
            if (!$post) {
                sendResponse(false, null, '帖子不存在', 404);
            }

            // 检查是否是帖子作者或管理员
            if ($post['author'] !== $username && $user['user_type'] !== 'ADMIN') {
                sendResponse(false, null, '您只能删除自己发布的帖子', 403);
            }

            // 软删除帖子（设置状态为0）
            $database->query("UPDATE forum_posts SET status = 0, updated_at = NOW() WHERE id = ?", [$postId]);

            // 同时软删除相关回复
            $database->query("UPDATE forum_replies SET status = 0, updated_at = NOW() WHERE post_id = ?", [$postId]);

            // 删除相关点赞记录
            $database->query("DELETE FROM forum_likes WHERE target_type = 'post' AND target_id = ?", [$postId]);
            $database->query("DELETE FROM forum_likes WHERE target_type = 'reply' AND target_id IN (SELECT id FROM forum_replies WHERE post_id = ?)", [$postId]);

            // 删除相关附件记录
            $database->query("UPDATE forum_attachments SET status = 0 WHERE post_id = ?", [$postId]);

            $logger->info("论坛帖子删除", [
                'post_id' => $postId,
                'title' => $post['title'],
                'author' => $post['author'],
                'deleted_by' => $username
            ]);

            sendResponse(true, [
                'post_id' => $postId,
                'title' => $post['title']
            ], '帖子删除成功');
            break;

        case 'toggle_like':
            // 切换点赞状态
            if ($method !== 'POST') {
                sendResponse(false, null, '请求方法错误', 405);
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            $username = trim($input['username'] ?? '');
            $targetType = $input['target_type'] ?? ''; // 'post' or 'reply'
            $targetId = (int)($input['target_id'] ?? 0);
            
            // 验证参数
            if (empty($username) || !in_array($targetType, ['post', 'reply']) || $targetId <= 0) {
                sendResponse(false, null, '参数错误', 400);
            }
            
            // 验证用户
            $user = validateUser($username, $database);
            if (!$user) {
                sendResponse(false, null, '用户验证失败', 401);
            }
            
            // 检查是否已点赞
            $existingLike = $database->fetchOne("
                SELECT id FROM forum_likes 
                WHERE user_id = ? AND target_type = ? AND target_id = ?
            ", [$username, $targetType, $targetId]);
            
            if ($existingLike) {
                // 取消点赞
                $database->query("DELETE FROM forum_likes WHERE id = ?", [$existingLike['id']]);
                $database->query("UPDATE forum_{$targetType}s SET likes_count = likes_count - 1 WHERE id = ?", [$targetId]);
                $action_result = 'unliked';
            } else {
                // 添加点赞
                $database->insert('forum_likes', [
                    'user_id' => $username,
                    'target_type' => $targetType,
                    'target_id' => $targetId
                ]);
                $database->query("UPDATE forum_{$targetType}s SET likes_count = likes_count + 1 WHERE id = ?", [$targetId]);
                $action_result = 'liked';
            }
            
            // 获取最新点赞数
            $likesCount = $database->fetchValue("SELECT likes_count FROM forum_{$targetType}s WHERE id = ?", [$targetId]);
            
            sendResponse(true, [
                'action' => $action_result,
                'likes_count' => (int)$likesCount
            ]);
            break;
            
        case 'get_user_likes':
            // 获取用户点赞列表
            $username = $_GET['username'] ?? '';
            if (empty($username)) {
                sendResponse(false, null, '用户名不能为空', 400);
            }
            
            $likes = $database->fetchAll("
                SELECT target_type, target_id 
                FROM forum_likes 
                WHERE user_id = ?
            ", [$username]);
            
            $userLikes = [];
            foreach ($likes as $like) {
                $userLikes[] = $like['target_type'] . '_' . $like['target_id'];
            }
            
            sendResponse(true, $userLikes);
            break;
            
        case 'get_messages':
            // 获取用户消息
            $username = $_GET['username'] ?? '';
            if (empty($username)) {
                sendResponse(false, null, '用户名不能为空', 400);
            }
            
            $messages = $database->fetchAll("
                SELECT * FROM forum_messages 
                WHERE to_user = ? 
                ORDER BY created_at DESC 
                LIMIT 50
            ", [$username]);
            
            sendResponse(true, $messages);
            break;
            
        case 'get_stats':
            // 获取论坛统计信息
            $stats = $database->fetchOne("
                SELECT 
                    (SELECT COUNT(*) FROM forum_posts WHERE status IN (1, 2)) as total_posts,
                    (SELECT COUNT(*) FROM forum_replies WHERE status = 1) as total_replies,
                    (SELECT COUNT(DISTINCT author) FROM forum_posts) as total_users,
                    (SELECT COUNT(*) FROM forum_posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)) as today_posts
            ");
            
            sendResponse(true, $stats);
            break;
            
        default:
            sendResponse(false, null, '未知的操作', 400);
    }
    
} catch (Exception $e) {
    $logger->error("论坛API错误", ['action' => $action, 'error' => $e->getMessage()]);
    sendResponse(false, null, '服务器内部错误', 500);
}
?>
