<?php
/**
 * 软件授权管理类
 * 处理软件授权验证、设备管理等防盗版功能
 */

declare(strict_types=1);

class AuthManager
{
    private Database $database;
    private int $tokenExpireHours = 24; // 令牌有效期（小时）
    private Logger $logger;

    public function __construct(Database $database)
    {
        $this->database = $database;
        $this->logger = new Logger();
    }
    
    /**
     * 软件授权验证
     */
    public function authenticate(string $licenseKey, string $deviceId): array
    {
        try {
            // 查找授权
            $license = $this->database->fetchOne(
                "SELECT * FROM users WHERE username = ? AND status = 1",
                [$licenseKey]
            );

            if (!$license) {
                $this->logAuthAttempt($licenseKey, 'failed', '授权码不存在或已禁用', null, $deviceId);
                return [
                    'success' => false,
                    'code' => 'INVALID_LICENSE',
                    'message' => '无效的授权码'
                ];
            }

            // 检查授权是否过期
            if ($license['expire_time'] && strtotime($license['expire_time']) < time()) {
                $this->logAuthAttempt($licenseKey, 'failed', '授权已过期', (int)$license['id'], $deviceId);
                return [
                    'success' => false,
                    'code' => 'LICENSE_EXPIRED',
                    'message' => '授权已过期，请联系管理员续费'
                ];
            }

            // 检查设备限制
            if (!empty($deviceId)) {
                $deviceCheck = $this->checkDeviceLimit((int)$license['id'], $deviceId, (int)$license['max_devices']);
                if (!$deviceCheck['success']) {
                    $this->logAuthAttempt($licenseKey, 'failed', $deviceCheck['message'], (int)$license['id'], $deviceId);
                    return $deviceCheck;
                }
            }

            // 实现令牌轮换机制：新登录时使旧令牌失效
            $this->invalidateUserTokens((int)$license['id'], $deviceId);

            // 生成新的访问令牌
            $token = $this->generateToken();
            $expiresAt = date('Y-m-d H:i:s', time() + ($this->tokenExpireHours * 3600));

            // 保存新令牌
            $this->database->insert('access_tokens', [
                'user_id' => (int)$license['id'],
                'token' => $token,
                'device_id' => $deviceId,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 更新设备信息
            if (!empty($deviceId)) {
                $this->updateDeviceInfo((int)$license['id'], $deviceId);
            }

            // 记录成功授权
            $this->logAuthAttempt($licenseKey, 'success', '授权验证成功', (int)$license['id'], $deviceId);

            // 清理敏感信息
            unset($license['password']);

            return [
                'success' => true,
                'message' => '授权验证成功',
                'token' => $token,
                'license' => $license,
                'expires_at' => $expiresAt
            ];

        } catch (Exception $e) {
            $this->logger->error("授权验证异常", [
                'license_key' => substr($licenseKey, 0, 8) . '...',
                'device_id' => $deviceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'code' => 'AUTH_ERROR',
                'message' => '授权验证过程中发生错误'
            ];
        }
    }

    /**
     * 使用用户名密码进行授权验证
     */
    public function authenticateWithPassword(string $username, string $password, string $deviceId, string $qqNumber = ''): array
    {
        // 调用现有的login方法，并调整返回格式以匹配authenticate方法
        $result = $this->login($username, $password, $deviceId, $qqNumber);

        if ($result['success']) {
            // 调整返回格式，将user改为license以保持一致性
            return [
                'success' => true,
                'message' => $result['message'],
                'token' => $result['token'],
                'user' => $result['user'], // 保持user字段用于前端
                'license' => $result['user'], // 同时提供license字段用于兼容
                'expires_at' => date('Y-m-d H:i:s', time() + ($this->tokenExpireHours * 3600))
            ];
        }

        return $result;
    }

    /**
     * 用户登录（保留兼容性）
     */
    public function login(string $username, string $password, string $deviceId = '', string $qqNumber = ''): array
    {
        try {
            // 查找用户
            $user = $this->database->fetchOne(
                "SELECT * FROM users WHERE username = ? AND status = 1",
                [$username]
            );
            
            if (!$user) {
                $this->logLoginAttempt($username, 'failed', '用户不存在或已禁用');
                return [
                    'success' => false,
                    'code' => 'USER_NOT_FOUND',
                    'message' => '用户不存在或已禁用'
                ];
            }
            
            // 验证密码
            if (!password_verify($password, $user['password'])) {
                $this->logLoginAttempt($username, 'failed', '密码错误', (int)$user['id']);
                return [
                    'success' => false,
                    'code' => 'INVALID_PASSWORD',
                    'message' => '密码错误'
                ];
            }
            
            // 检查账户是否过期
            if ($user['expire_time'] && strtotime($user['expire_time']) < time()) {
                $this->logLoginAttempt($username, 'failed', '账户已过期', (int)$user['id']);
                return [
                    'success' => false,
                    'code' => 'USER_EXPIRED',
                    'message' => '账户已过期，请联系管理员续费'
                ];
            }
            
            // 检查设备限制
            if (!empty($deviceId)) {
                $deviceCheck = $this->checkDeviceLimit((int)$user['id'], $deviceId, (int)$user['max_devices']);
                if (!$deviceCheck['success']) {
                    $this->logLoginAttempt($username, 'failed', $deviceCheck['message'], (int)$user['id']);
                    return $deviceCheck;
                }
            }
            
            // 实现令牌轮换机制：新登录时使旧令牌失效
            $this->invalidateUserTokens((int)$user['id'], $deviceId);

            // 生成新的访问令牌
            $token = $this->generateToken();
            $expiresAt = date('Y-m-d H:i:s', time() + ($this->tokenExpireHours * 3600));

            // 保存新令牌
            $this->database->insert('access_tokens', [
                'user_id' => (int)$user['id'],
                'token' => $token,
                'device_id' => $deviceId,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 更新设备信息
            if (!empty($deviceId)) {
                $this->updateDeviceInfo((int)$user['id'], $deviceId);
            }

            // 更新用户的在线状态
            $updateData = [
                'last_login' => date('Y-m-d H:i:s'),
                'is_online' => 1,
                'device_id' => $deviceId
            ];

            $this->database->update('users', $updateData, 'id = ?', [(int)$user['id']]);

            // 检查QQ号状态
            if ($user['qq_number'] === 'PENDING') {
                // 如果QQ号为待填写状态，必须提供QQ号
                if (empty($qqNumber)) {
                    return [
                        'success' => false,
                        'code' => 'QQ_REQUIRED',
                        'message' => '首次登录必须填写QQ号'
                    ];
                }

                // 验证QQ号格式
                if (!preg_match('/^[0-9]{5,11}$/', $qqNumber)) {
                    return [
                        'success' => false,
                        'code' => 'QQ_INVALID',
                        'message' => 'QQ号格式不正确（5-11位数字）'
                    ];
                }

                // 更新QQ号
                $this->database->update('users',
                    ['qq_number' => $qqNumber],
                    'id = ?',
                    [(int)$user['id']]
                );
                $user['qq_number'] = $qqNumber;

            } elseif (!empty($qqNumber) && $qqNumber !== $user['qq_number']) {
                // 如果用户想更新QQ号
                $this->database->update('users',
                    ['qq_number' => $qqNumber],
                    'id = ?',
                    [(int)$user['id']]
                );
                $user['qq_number'] = $qqNumber;
            }

            // 记录成功登录
            $this->logLoginAttempt($username, 'success', '登录成功', (int)$user['id'], $deviceId);
            
            // 清理用户敏感信息
            unset($user['password']);
            
            return [
                'success' => true,
                'message' => '登录成功',
                'token' => $token,
                'user' => $user
            ];
            
        } catch (Exception $e) {
            $this->logger->error("用户登录异常", [
                'username' => $username,
                'device_id' => $deviceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'code' => 'LOGIN_ERROR',
                'message' => '登录过程中发生错误'
            ];
        }
    }
    
    /**
     * 验证访问令牌
     */
    public function verifyToken(string $token): array
    {
        try {
            $tokenData = $this->database->fetchOne(
                "SELECT t.*, u.* FROM access_tokens t 
                 JOIN users u ON t.user_id = u.id 
                 WHERE t.token = ? AND t.expires_at > ? AND u.status = 1",
                [$token, date('Y-m-d H:i:s')]
            );
            
            if (!$tokenData) {
                return [
                    'success' => false,
                    'code' => 'INVALID_TOKEN',
                    'message' => '无效或已过期的令牌'
                ];
            }
            
            // 检查用户是否过期
            if ($tokenData['expire_time'] && strtotime($tokenData['expire_time']) < time()) {
                return [
                    'success' => false,
                    'code' => 'USER_EXPIRED',
                    'message' => '用户账户已过期'
                ];
            }
            
            // 清理敏感信息
            unset($tokenData['password'], $tokenData['token']);
            
            return [
                'success' => true,
                'user' => $tokenData
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'code' => 'VERIFY_ERROR',
                'message' => '令牌验证过程中发生错误'
            ];
        }
    }
    
    /**
     * 心跳检测
     */
    public function heartbeat(string $token, string $deviceId = ''): array
    {
        $verifyResult = $this->verifyToken($token);
        
        if (!$verifyResult['success']) {
            return $verifyResult;
        }
        
        $user = $verifyResult['user'];
        
        // 记录心跳
        $this->database->insert('heartbeat_logs', [
            'user_id' => (int)$user['id'],
            'device_id' => $deviceId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? ''
        ]);
        
        return [
            'success' => true,
            'message' => '心跳正常'
        ];
    }
    
    /**
     * 用户登出
     */
    public function logout(string $token, string $deviceId = ''): array
    {
        try {
            // 首先验证令牌获取用户信息
            $verifyResult = $this->verifyToken($token);

            if ($verifyResult['success']) {
                $userId = (int)$verifyResult['user']['id'];
                $username = $verifyResult['user']['username'];

                // 如果提供了设备ID，删除对应的设备记录
                if (!empty($deviceId)) {
                    // 获取设备名称用于日志
                    $deviceInfo = $this->database->fetchOne(
                        "SELECT device_name FROM user_devices WHERE user_id = ? AND device_id = ?",
                        [$userId, $deviceId]
                    );
                    $deviceName = $deviceInfo['device_name'] ?? '未知设备';

                    // 删除设备记录（这会让设备从设备列表中消失）
                    $deviceDeleted = $this->database->delete(
                        'user_devices',
                        'user_id = ? AND device_id = ?',
                        [$userId, $deviceId]
                    );

                    // 删除该设备的所有令牌
                    $tokenDeleted = $this->database->delete(
                        'access_tokens',
                        'user_id = ? AND device_id = ?',
                        [$userId, $deviceId]
                    );

                    // 记录到授权日志
                    $this->logLoginAttempt($username, 'logout', '用户退出登录并解绑设备', $userId, $deviceId);

                    $this->logger->info("用户退出登录", [
                        'user_id' => $userId,
                        'username' => $username,
                        'device_id' => $deviceId,
                        'device_name' => $deviceName,
                        'device_deleted' => $deviceDeleted,
                        'token_deleted' => $tokenDeleted,
                        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                    ]);
                } else {
                    // 如果没有设备ID，只删除当前令牌
                    $tokenDeleted = $this->database->delete('access_tokens', 'token = ?', [$token]);

                    // 记录到授权日志
                    $this->logLoginAttempt($username, 'logout', '用户退出登录（仅删除令牌）', $userId);

                    $this->logger->info("用户退出登录（仅删除令牌）", [
                        'user_id' => $userId,
                        'username' => $username,
                        'token_deleted' => $tokenDeleted,
                        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                    ]);
                }
            } else {
                // 令牌无效，但仍然尝试删除令牌记录
                $this->database->delete('access_tokens', 'token = ?', [$token]);
            }

            return [
                'success' => true,
                'message' => '登出成功，设备已解绑'
            ];

        } catch (Exception $e) {
            // 尝试获取用户信息用于错误日志
            $username = 'unknown';
            try {
                $verifyResult = $this->verifyToken($token);
                if ($verifyResult['success']) {
                    $username = $verifyResult['user']['username'];
                }
            } catch (Exception $verifyException) {
                // 忽略验证异常，使用默认用户名
            }

            $this->logger->error("登出过程异常", [
                'username' => $username,
                'error' => $e->getMessage(),
                'device_id' => $deviceId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);

            return [
                'success' => false,
                'code' => 'LOGOUT_ERROR',
                'message' => '登出过程中发生错误'
            ];
        }
    }
    
    /**
     * 用户注册
     */
    public function register(string $username, string $password, string $email = ''): array
    {
        try {
            // 检查用户名是否已存在
            $existingUser = $this->database->fetchOne(
                "SELECT id FROM users WHERE username = ?",
                [$username]
            );
            
            if ($existingUser) {
                return [
                    'success' => false,
                    'code' => 'USERNAME_EXISTS',
                    'message' => '用户名已存在'
                ];
            }
            
            // 创建用户
            $userId = $this->database->insert('users', [
                'username' => $username,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'email' => $email,
                'user_type' => 'free',
                'max_devices' => 1,
                'status' => 1
            ]);
            
            return [
                'success' => true,
                'message' => '注册成功',
                'user_id' => $userId
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'code' => 'REGISTER_ERROR',
                'message' => '注册过程中发生错误'
            ];
        }
    }
    
    /**
     * 获取用户信息
     */
    public function getUserInfo(string $token): array
    {
        $verifyResult = $this->verifyToken($token);
        
        if (!$verifyResult['success']) {
            return $verifyResult;
        }
        
        return [
            'success' => true,
            'user' => $verifyResult['user']
        ];
    }
    
    /**
     * 获取授权信息
     */
    public function getLicenseInfo(string $token): array
    {
        $verifyResult = $this->verifyToken($token);

        if (!$verifyResult['success']) {
            return $verifyResult;
        }

        $license = $verifyResult['user'];

        // 获取设备列表
        $devices = $this->database->fetchAll(
            "SELECT device_id, device_name, device_info, last_login, status, created_at
             FROM user_devices WHERE user_id = ? ORDER BY last_login DESC",
            [(int)$license['id']]
        );

        return [
            'success' => true,
            'license' => [
                'license_key' => $license['username'],
                'user_type' => $license['user_type'],
                'max_devices' => $license['max_devices'],
                'expire_time' => $license['expire_time'],
                'status' => $license['status'],
                'devices' => $devices,
                'device_count' => count($devices)
            ]
        ];
    }

    /**
     * 绑定设备
     */
    public function bindDevice(string $token, string $deviceId, string $deviceName = ''): array
    {
        $verifyResult = $this->verifyToken($token);

        if (!$verifyResult['success']) {
            return $verifyResult;
        }

        $license = $verifyResult['user'];

        // 检查设备限制
        $deviceCheck = $this->checkDeviceLimit((int)$license['id'], $deviceId, (int)$license['max_devices']);
        if (!$deviceCheck['success']) {
            return $deviceCheck;
        }

        // 绑定设备
        $this->updateDeviceInfo((int)$license['id'], $deviceId, $deviceName);

        return [
            'success' => true,
            'message' => '设备绑定成功'
        ];
    }

    /**
     * 解绑设备
     */
    public function unbindDevice(string $token, string $deviceId): array
    {
        $verifyResult = $this->verifyToken($token);

        if (!$verifyResult['success']) {
            return $verifyResult;
        }

        $license = $verifyResult['user'];

        // 删除设备
        $deleted = $this->database->delete(
            'user_devices',
            'user_id = ? AND device_id = ?',
            [(int)$license['id'], $deviceId]
        );

        if ($deleted > 0) {
            // 删除该设备的所有令牌
            $this->database->delete(
                'access_tokens',
                'user_id = ? AND device_id = ?',
                [(int)$license['id'], $deviceId]
            );

            return [
                'success' => true,
                'message' => '设备解绑成功'
            ];
        } else {
            return [
                'success' => false,
                'code' => 'DEVICE_NOT_FOUND',
                'message' => '设备不存在'
            ];
        }
    }
    
    /**
     * 检查设备限制
     */
    private function checkDeviceLimit(int $userId, string $deviceId, int $maxDevices): array
    {
        // 检查设备是否已存在
        $existingDevice = $this->database->fetchOne(
            "SELECT id FROM user_devices WHERE user_id = ? AND device_id = ?",
            [$userId, $deviceId]
        );
        
        if ($existingDevice) {
            return ['success' => true]; // 设备已存在，允许登录
        }
        
        // 检查设备数量限制
        $deviceCount = $this->database->fetchValue(
            "SELECT COUNT(*) FROM user_devices WHERE user_id = ? AND status = 1",
            [$userId]
        );
        
        if ($deviceCount >= $maxDevices) {
            return [
                'success' => false,
                'code' => 'DEVICE_LIMIT_EXCEEDED',
                'message' => "设备数量已达上限（{$maxDevices}台），请先解绑其他设备"
            ];
        }
        
        return ['success' => true];
    }
    
    /**
     * 更新设备信息
     */
    private function updateDeviceInfo(int $userId, string $deviceId, string $deviceName = ''): void
    {
        $deviceInfo = [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];
        
        // 尝试更新现有设备
        $updated = $this->database->update(
            'user_devices',
            [
                'device_info' => json_encode($deviceInfo),
                'last_login' => date('Y-m-d H:i:s'),
                'status' => 1
            ],
            'user_id = ? AND device_id = ?',
            [$userId, $deviceId]
        );

        // 如果设备不存在，则创建新设备
        if ($updated === 0) {
            $this->database->insert('user_devices', [
                'user_id' => $userId,
                'device_id' => $deviceId,
                'device_name' => $deviceName ?: $this->generateDeviceName(),
                'device_info' => json_encode($deviceInfo),
                'last_login' => date('Y-m-d H:i:s'),
                'status' => 1
            ]);
        }
    }
    
    /**
     * 生成设备名称
     */
    private function generateDeviceName(): string
    {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        if (strpos($userAgent, 'Windows') !== false) {
            return 'Windows设备';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            return 'Mac设备';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            return 'Linux设备';
        } else {
            return '未知设备';
        }
    }
    
    /**
     * 记录授权尝试
     */
    private function logAuthAttempt(string $licenseKey, string $type, string $message, ?int $userId = null, string $deviceId = ''): void
    {
        $this->database->insert('login_logs', [
            'user_id' => $userId,
            'username' => substr($licenseKey, 0, 8) . '...',
            'login_type' => $type,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'device_id' => $deviceId,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }

    /**
     * 记录登录尝试
     */
    private function logLoginAttempt(string $username, string $type, string $message, ?int $userId = null, string $deviceId = ''): void
    {
        $this->database->insert('login_logs', [
            'user_id' => $userId,
            'username' => $username,
            'login_type' => $type,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'device_id' => $deviceId,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }
    
    /**
     * 使用户的旧令牌失效（令牌轮换机制）
     */
    private function invalidateUserTokens(int $userId, string $deviceId = ''): void
    {
        try {
            if ($deviceId) {
                // 只使该设备的令牌失效
                $this->database->delete(
                    'access_tokens',
                    'user_id = ? AND device_id = ?',
                    [$userId, $deviceId]
                );
                $this->logger->info("用户 {$userId} 设备 {$deviceId} 的旧令牌已失效");
            } else {
                // 使该用户所有设备的令牌失效
                $this->database->delete(
                    'access_tokens',
                    'user_id = ?',
                    [$userId]
                );
                $this->logger->info("用户 {$userId} 的所有旧令牌已失效");
            }
        } catch (Exception $e) {
            $this->logger->error("令牌失效操作失败", [
                'user_id' => $userId,
                'device_id' => $deviceId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 生成访问令牌
     */
    private function generateToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * 清理过期令牌
     */
    public function cleanupExpiredTokens(): int
    {
        try {
            $deleted = $this->database->delete(
                'access_tokens',
                'expires_at < ?',
                [date('Y-m-d H:i:s')]
            );

            if ($deleted > 0) {
                $this->logger->info("清理了 {$deleted} 个过期令牌");
            }

            return $deleted;
        } catch (Exception $e) {
            $this->logger->error("清理过期令牌失败", ['error' => $e->getMessage()]);
            return 0;
        }
    }
}
