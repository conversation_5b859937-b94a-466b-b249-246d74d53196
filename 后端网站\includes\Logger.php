<?php
/**
 * 日志记录类
 * 记录系统运行日志
 */

declare(strict_types=1);

class Logger
{
    private string $logFile;
    private string $logLevel;
    
    public function __construct(string $logFile = null, string $logLevel = 'INFO')
    {
        $this->logFile = $logFile ?: __DIR__ . '/../logs/system.log';
        $this->logLevel = $logLevel;
        
        // 确保日志目录存在
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * 记录信息日志
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('INFO', $message, $context);
    }
    
    /**
     * 记录警告日志
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('WARNING', $message, $context);
    }
    
    /**
     * 记录错误日志
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('ERROR', $message, $context);
    }
    
    /**
     * 记录调试日志
     */
    public function debug(string $message, array $context = []): void
    {
        if ($this->logLevel === 'DEBUG') {
            $this->log('DEBUG', $message, $context);
        }
    }

    /**
     * 记录数据库操作日志
     */
    public function database(string $operation, string $table, array $context = []): void
    {
        $this->log('INFO', "数据库操作: {$operation} - {$table}", array_merge([
            'operation' => $operation,
            'table' => $table
        ], $context));
    }

    /**
     * 记录用户操作日志
     */
    public function userAction(string $action, string $username, array $context = []): void
    {
        $this->log('INFO', "用户操作: {$action} - {$username}", array_merge([
            'action' => $action,
            'username' => $username
        ], $context));
    }

    /**
     * 记录API调用日志
     */
    public function apiCall(string $endpoint, string $method, array $context = []): void
    {
        $this->log('INFO', "API调用: {$method} {$endpoint}", array_merge([
            'endpoint' => $endpoint,
            'method' => $method
        ], $context));
    }

    /**
     * 记录安全事件日志
     */
    public function security(string $event, array $context = []): void
    {
        $this->log('WARNING', "安全事件: {$event}", array_merge([
            'security_event' => $event
        ], $context));
    }

    /**
     * 记录性能警告
     */
    public function performance(string $message, float $executionTime, array $context = []): void
    {
        $level = $executionTime > 5.0 ? 'ERROR' : ($executionTime > 2.0 ? 'WARNING' : 'INFO');
        $this->log($level, "性能监控: {$message}", array_merge([
            'execution_time' => $executionTime,
            'performance_issue' => $executionTime > 2.0
        ], $context));
    }
    
    /**
     * 写入日志
     */
    private function log(string $level, string $message, array $context = []): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $logEntry = [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'ip' => $ip,
            'user_agent' => substr($userAgent, 0, 100),
            'context' => $context
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        
        // 写入日志文件
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // 日志轮转（保持文件大小在合理范围内）
        $this->rotateLog();
    }
    
    /**
     * 日志轮转
     */
    private function rotateLog(): void
    {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        $maxSize = 10 * 1024 * 1024; // 10MB
        
        if (filesize($this->logFile) > $maxSize) {
            $backupFile = $this->logFile . '.' . date('Y-m-d-H-i-s') . '.bak';
            rename($this->logFile, $backupFile);
            
            // 只保留最近的5个备份文件
            $this->cleanupOldLogs();
        }
    }
    
    /**
     * 清理旧日志文件
     */
    private function cleanupOldLogs(): void
    {
        $logDir = dirname($this->logFile);
        $logBasename = basename($this->logFile);
        
        $backupFiles = glob($logDir . '/' . $logBasename . '.*.bak');
        
        if (count($backupFiles) > 5) {
            // 按修改时间排序
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件
            $filesToDelete = array_slice($backupFiles, 0, count($backupFiles) - 5);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * 获取最近的日志条目
     */
    public function getRecentLogs(int $limit = 100): array
    {
        if (!file_exists($this->logFile)) {
            return [];
        }
        
        $lines = file($this->logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $logs = [];
        
        // 获取最后N行
        $recentLines = array_slice($lines, -$limit);
        
        foreach ($recentLines as $line) {
            $logEntry = json_decode($line, true);
            if ($logEntry) {
                $logs[] = $logEntry;
            }
        }
        
        return array_reverse($logs); // 最新的在前面
    }
    
    /**
     * 清空日志文件
     */
    public function clearLogs(): bool
    {
        return file_put_contents($this->logFile, '') !== false;
    }
}
