#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱API模块
负责与各种临时邮箱服务的API交互
"""

import time
from typing import List, Dict, Any, Optional

# 尝试导入requests，如果失败则提供备用方案
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    requests = None

try:
    try:
        from src.utils.email_utils import extract_verification_code, parse_email_address
        from src.utils.logger import get_logger
    except ImportError:
        from utils.email_utils import extract_verification_code, parse_email_address
        from utils.logger import get_logger
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    try:
        from src.utils.email_utils import extract_verification_code, parse_email_address
        from src.utils.logger import get_logger
    except ImportError:
        from utils.email_utils import extract_verification_code, parse_email_address
        from utils.logger import get_logger


class EmailAPI:
    """邮箱API客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化邮箱API客户端

        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger()

        # 邮件历史缓存 - 用于保存所有获取过的邮件
        self.email_cache = {}  # {email_id: email_data}
        self.last_check_time = 0

        if not REQUESTS_AVAILABLE:
            self.logger.error("requests库未安装，邮箱API功能不可用")
            self.session = None
            return

        self.session = requests.Session()

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

        # 禁用SSL验证警告
        try:
            requests.packages.urllib3.disable_warnings()
        except:
            pass
    
    def get_emails(self) -> List[Dict[str, Any]]:
        """
        获取邮件列表

        Returns:
            邮件列表

        Raises:
            Exception: 当获取邮件失败时
        """
        if not REQUESTS_AVAILABLE:
            raise Exception('requests库未安装，请运行: pip install requests')

        if not self.config.get('temp_mail_address'):
            raise Exception('需要配置TempMail.Plus邮箱地址')

        temp_mail_address = self.config['temp_mail_address']
        self.logger.info(f"🔍 开始获取邮件，邮箱地址: {temp_mail_address}")

        email_info = parse_email_address(temp_mail_address)
        self.logger.info(f"📧 解析的邮箱信息: {email_info}")

        if not email_info['username']:
            raise Exception('邮箱地址格式不正确')

        # 尝试不同的API端点
        result = self._try_tempmail_plus_api(email_info['username'])
        self.logger.info(f"📬 API返回结果: {len(result) if result else 0} 封邮件")

        return result
    
    def _try_tempmail_plus_api(self, username: str) -> List[Dict[str, Any]]:
        """
        尝试TempMail.Plus API（基于油猴脚本的正确实现）

        Args:
            username: 邮箱用户名

        Returns:
            邮件列表
        """
        # 从配置中获取完整的邮箱地址和PIN码
        temp_mail_address = self.config.get('temp_mail_address', '')
        pin_code = self.config.get('temp_mail_pin', '')

        if not temp_mail_address:
            raise Exception('需要配置完整的TempMail.Plus邮箱地址')

        self.logger.info(f"使用邮箱地址: {temp_mail_address}")
        self.logger.info(f"使用PIN码: {pin_code if pin_code else '未设置'}")

        # 构建请求参数 - 优化为15封邮件，刚好占满页面
        params = {
            'email': temp_mail_address,
            'limit': 15  # 优化为15封，提高加载速度
        }

        if pin_code:
            params['epin'] = pin_code

        # 使用正确的API端点（按照JavaScript脚本）
        api_url = "https://tempmail.plus/api/mails"

        try:
            self.logger.info(f"🌐 请求邮件列表: {api_url}")
            self.logger.info(f"📝 请求参数: {params}")

            response = self.session.get(
                api_url,
                params=params,
                timeout=10,
                verify=False
            )

            self.logger.info(f"📡 API响应状态: {response.status_code}")
            self.logger.info(f"📄 响应内容前500字符: {response.text[:500]}")

            if response.ok:
                    try:
                        data = response.json()
                        self.logger.info(f"API返回数据结构: {list(data.keys())}")

                        # 按照JavaScript脚本的逻辑检查返回结果
                        if not data.get('result'):
                            self.logger.warning("API返回result为false，可能没有邮件")
                            return []

                        # 检查是否有邮件列表
                        mail_list = data.get('mail_list', [])
                        if not mail_list:
                            self.logger.info("没有找到邮件（mail_list为空）")
                            return []

                        self.logger.info(f"找到 {len(mail_list)} 封邮件")

                        # 获取所有邮件的详情
                        processed_emails = []
                        for mail_item in mail_list:
                            mail_id = mail_item.get('mail_id')
                            if mail_id:
                                self.logger.info(f"获取邮件详情: {mail_id}")
                                mail_detail = self._get_mail_details_js_style(temp_mail_address, str(mail_id), pin_code)
                                if mail_detail:
                                    processed_emails.append(mail_detail)
                                else:
                                    # 如果获取详情失败，使用列表中的基本信息
                                    time_str = mail_item.get('time', '')
                                    formatted_time = self._format_email_time(time_str)

                                    basic_mail = {
                                        'id': str(mail_id),
                                        'sender': mail_item.get('from_mail', '未知发件人'),
                                        'subject': mail_item.get('subject', '无主题'),
                                        'content': '(需要点击查看详情)',
                                        'time': formatted_time,
                                        'date': formatted_time,  # 兼容性字段
                                        'verification_code': ''
                                    }
                                    processed_emails.append(basic_mail)

                        return processed_emails


                    except Exception as e:
                        self.logger.error(f"解析API响应失败: {e}")
                        return []
            else:
                self.logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return []

        except Exception as e:
            self.logger.error(f"TempMail.Plus API请求异常: {e}")
            return []

    def _get_mail_details_js_style(self, email_address: str, mail_id: str, pin_code: str = '') -> Dict[str, Any]:
        """
        按照JavaScript脚本的方式获取邮件详情

        Args:
            email_address: 邮箱地址
            mail_id: 邮件ID
            pin_code: PIN码

        Returns:
            邮件详情字典
        """
        try:
            # 构建邮件详情API URL（按照JavaScript脚本的正确格式）
            detail_url = f"https://tempmail.plus/api/mails/{mail_id}"

            params = {
                'email': email_address
            }

            if pin_code:
                params['epin'] = pin_code

            self.logger.info(f"获取邮件详情: {mail_id}")
            self.logger.info(f"详情API URL: {detail_url}")
            self.logger.info(f"详情API参数: {params}")

            response = self.session.get(
                detail_url,
                params=params,
                timeout=10,
                verify=False
            )

            if response.ok:
                data = response.json()
                self.logger.info(f"邮件详情API响应: {data}")

                # 检查不同的数据结构
                if data.get('result'):
                    # 情况1：数据在mail字段中
                    if data.get('mail'):
                        mail_data = data['mail']
                    # 情况2：数据直接在根级别
                    elif data.get('text') or data.get('html') or data.get('subject'):
                        mail_data = data
                    else:
                        self.logger.warning(f"邮件详情数据结构未知: {data}")
                        return None

                    # 按照JavaScript脚本的格式处理邮件数据
                    content = mail_data.get('text', mail_data.get('html', ''))

                    # 处理时间格式
                    date_str = mail_data.get('date', mail_data.get('time', ''))
                    formatted_time = self._format_email_time(date_str)

                    processed_mail = {
                        'id': mail_id,
                        'sender': mail_data.get('from', mail_data.get('from_mail', '未知发件人')),
                        'subject': mail_data.get('subject', '无主题'),
                        'content': content,
                        'time': formatted_time,
                        'date': formatted_time,  # 兼容性字段
                        'verification_code': self._extract_verification_code(content)
                    }

                    self.logger.info(f"成功获取邮件详情: {processed_mail['subject']}")
                    return processed_mail
                else:
                    self.logger.warning(f"邮件详情API返回失败: {data}")
                    return None
            else:
                self.logger.error(f"邮件详情API请求失败: {response.status_code}")
                self.logger.error(f"响应内容: {response.text[:200]}")
                return None

        except Exception as e:
            self.logger.error(f"获取邮件详情异常: {e}")
            return None



    def _extract_verification_code(self, text: str) -> str:
        """
        从邮件文本中提取验证码

        Args:
            text: 邮件文本内容

        Returns:
            验证码字符串，如果未找到则返回空字符串
        """
        if not text:
            return ''

        # 常见的验证码模式
        patterns = [
            r'验证码[：:]\s*([A-Za-z0-9]{4,8})',
            r'verification code[：:]\s*([A-Za-z0-9]{4,8})',
            r'code[：:]\s*([A-Za-z0-9]{4,8})',
            r'([A-Za-z0-9]{6})',  # 6位数字或字母
            r'([0-9]{4,8})',      # 4-8位数字
        ]

        for pattern in patterns:
            import re
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return ''

    def _format_email_time(self, date_str: str) -> str:
        """
        格式化邮件时间

        Args:
            date_str: 原始时间字符串，如 'Fri, 27 Jun 2025 10:01:15 +0000'

        Returns:
            格式化后的时间字符串
        """
        if not date_str:
            return '未知时间'

        try:
            from datetime import datetime
            import re

            # 处理RFC2822格式：'Fri, 27 Jun 2025 10:01:15 +0000'
            if ',' in date_str and '+' in date_str:
                # 移除星期几和时区信息进行简化处理
                clean_date = re.sub(r'^[A-Za-z]+,\s*', '', date_str)  # 移除 'Fri, '
                clean_date = re.sub(r'\s*[+-]\d{4}$', '', clean_date)  # 移除 ' +0000'

                # 尝试解析：'27 Jun 2025 10:01:15'
                try:
                    dt = datetime.strptime(clean_date, '%d %b %Y %H:%M:%S')
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass

            # 如果已经是标准格式，直接返回
            if re.match(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', date_str):
                return date_str

            # 其他格式尝试
            return date_str

        except Exception as e:
            self.logger.debug(f"时间格式化失败: {e}, 原始时间: {date_str}")
            return date_str
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试邮箱API连接
        
        Returns:
            测试结果字典
        """
        result = {
            'success': False,
            'message': '',
            'email_count': 0,
            'response_time': 0
        }
        
        start_time = time.time()
        
        try:
            emails = self.get_emails()
            result['success'] = True
            result['email_count'] = len(emails)
            result['message'] = f"成功连接，获取到 {len(emails)} 封邮件"
            
        except Exception as e:
            result['message'] = f"连接失败: {str(e)}"
        
        result['response_time'] = round(time.time() - start_time, 2)
        
        return result

    def clear_cache(self):
        """清理邮件缓存"""
        self.email_cache.clear()
        self.logger.info("邮件缓存已清理")

    def get_cache_size(self) -> int:
        """获取缓存中的邮件数量"""
        return len(self.email_cache)

    def _cleanup_old_cache(self, max_emails: int = 15):
        """清理过旧的缓存邮件，保留最新的15封邮件（优化性能）"""
        if len(self.email_cache) > max_emails:
            # 按时间排序，保留最新的邮件
            cached_emails = list(self.email_cache.values())
            try:
                cached_emails.sort(key=lambda x: x.get('date', ''), reverse=True)
                # 保留最新的邮件
                keep_emails = cached_emails[:max_emails]

                # 重建缓存
                self.email_cache = {email['id']: email for email in keep_emails if email.get('id')}
                self.logger.info(f"清理旧缓存，保留最新的 {len(self.email_cache)} 封邮件")
            except Exception as e:
                self.logger.error(f"清理缓存失败: {e}")
                # 如果排序失败，简单地清理一半
                cache_items = list(self.email_cache.items())
                self.email_cache = dict(cache_items[:max_emails])
                self.logger.info(f"简单清理缓存，保留 {len(self.email_cache)} 封邮件")
