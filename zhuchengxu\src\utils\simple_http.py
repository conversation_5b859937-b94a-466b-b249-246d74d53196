#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单HTTP客户端 - 无外部依赖
专为exe打包设计，只使用Python标准库
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import socket
import ssl
from typing import Dict, Any, Optional, Tuple


class SimpleHTTPClient:
    """简单的HTTP客户端，无外部依赖"""
    
    def __init__(self, timeout: int = 10, user_agent: str = "SimpleHTTP/1.0"):
        self.timeout = timeout
        self.user_agent = user_agent
        self.default_headers = {
            'User-Agent': user_agent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
    
    def _create_request(self, url: str, method: str = 'GET', 
                       data: Optional[bytes] = None, 
                       headers: Optional[Dict[str, str]] = None) -> urllib.request.Request:
        """创建HTTP请求"""
        req = urllib.request.Request(url, data=data, method=method)
        
        # 添加默认头部
        for key, value in self.default_headers.items():
            req.add_header(key, value)
        
        # 添加自定义头部
        if headers:
            for key, value in headers.items():
                req.add_header(key, value)
        
        return req
    
    def _send_request(self, req: urllib.request.Request) -> Tuple[int, Dict[str, Any]]:
        """发送HTTP请求"""
        try:
            # 创建SSL上下文（忽略证书验证，适用于内网环境）
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            with urllib.request.urlopen(req, timeout=self.timeout, context=ssl_context) as response:
                status_code = response.getcode()
                content_type = response.headers.get('Content-Type', '')
                
                # 读取响应数据
                data = response.read()
                
                # 尝试解码
                try:
                    text = data.decode('utf-8')
                except UnicodeDecodeError:
                    text = data.decode('gbk', errors='ignore')
                
                # 尝试解析JSON
                if 'application/json' in content_type or text.strip().startswith('{'):
                    try:
                        json_data = json.loads(text)
                        return status_code, json_data
                    except json.JSONDecodeError:
                        return status_code, {'error': 'Invalid JSON response', 'text': text}
                else:
                    return status_code, {'text': text}
                    
        except urllib.error.HTTPError as e:
            # HTTP错误（4xx, 5xx）
            try:
                error_data = e.read().decode('utf-8')
                try:
                    error_json = json.loads(error_data)
                    return e.code, error_json
                except json.JSONDecodeError:
                    return e.code, {'error': f'HTTP {e.code}', 'text': error_data}
            except:
                return e.code, {'error': f'HTTP {e.code}: {e.reason}'}
                
        except urllib.error.URLError as e:
            # 网络错误
            return 0, {'error': f'Network error: {e.reason}'}
            
        except socket.timeout:
            # 超时
            return 0, {'error': 'Request timeout'}
            
        except Exception as e:
            # 其他错误
            return 0, {'error': f'Request failed: {str(e)}'}
    
    def get(self, url: str, params: Optional[Dict[str, str]] = None,
            headers: Optional[Dict[str, str]] = None, timeout: Optional[int] = None) -> Tuple[int, Dict[str, Any]]:
        """发送GET请求"""
        # 处理查询参数
        if params:
            query_string = urllib.parse.urlencode(params)
            separator = '&' if '?' in url else '?'
            url = f"{url}{separator}{query_string}"

        # 设置超时
        if timeout:
            old_timeout = self.timeout
            self.timeout = timeout

        req = self._create_request(url, 'GET', headers=headers)
        result = self._send_request(req)

        # 恢复原超时设置
        if timeout:
            self.timeout = old_timeout

        return result
    
    def post(self, url: str, data: Optional[Dict[str, Any]] = None,
             json_data: Optional[Dict[str, Any]] = None,
             headers: Optional[Dict[str, str]] = None, timeout: Optional[int] = None) -> Tuple[int, Dict[str, Any]]:
        """发送POST请求"""
        request_headers = headers or {}
        request_data = None

        if json_data:
            # JSON数据
            request_data = json.dumps(json_data, ensure_ascii=False).encode('utf-8')
            request_headers['Content-Type'] = 'application/json; charset=utf-8'
        elif data:
            # 表单数据
            request_data = urllib.parse.urlencode(data).encode('utf-8')
            request_headers['Content-Type'] = 'application/x-www-form-urlencoded'

        # 设置超时
        if timeout:
            old_timeout = self.timeout
            self.timeout = timeout

        req = self._create_request(url, 'POST', data=request_data, headers=request_headers)
        result = self._send_request(req)

        # 恢复原超时设置
        if timeout:
            self.timeout = old_timeout

        return result
    
    def put(self, url: str, data: Optional[Dict[str, Any]] = None,
            json_data: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None) -> Tuple[int, Dict[str, Any]]:
        """发送PUT请求"""
        request_headers = headers or {}
        request_data = None
        
        if json_data:
            request_data = json.dumps(json_data, ensure_ascii=False).encode('utf-8')
            request_headers['Content-Type'] = 'application/json; charset=utf-8'
        elif data:
            request_data = urllib.parse.urlencode(data).encode('utf-8')
            request_headers['Content-Type'] = 'application/x-www-form-urlencoded'
        
        req = self._create_request(url, 'PUT', data=request_data, headers=request_headers)
        return self._send_request(req)
    
    def delete(self, url: str, headers: Optional[Dict[str, str]] = None) -> Tuple[int, Dict[str, Any]]:
        """发送DELETE请求"""
        req = self._create_request(url, 'DELETE', headers=headers)
        return self._send_request(req)


# 创建全局实例
http_client = SimpleHTTPClient()

# 便捷函数
def get(url: str, params: Optional[Dict[str, str]] = None, 
        headers: Optional[Dict[str, str]] = None, timeout: int = 10) -> Tuple[int, Dict[str, Any]]:
    """发送GET请求的便捷函数"""
    client = SimpleHTTPClient(timeout=timeout)
    return client.get(url, params, headers)

def post(url: str, data: Optional[Dict[str, Any]] = None,
         json_data: Optional[Dict[str, Any]] = None,
         headers: Optional[Dict[str, str]] = None, timeout: int = 10) -> Tuple[int, Dict[str, Any]]:
    """发送POST请求的便捷函数"""
    client = SimpleHTTPClient(timeout=timeout)
    return client.post(url, data, json_data, headers)

def test_connection(url: str, timeout: int = 5) -> bool:
    """测试连接是否可用"""
    try:
        status_code, _ = get(url, timeout=timeout)
        return status_code > 0
    except:
        return False


if __name__ == "__main__":
    # 简单测试
    print("🧪 测试SimpleHTTPClient...")
    
    # 测试GET请求
    print("1. 测试GET请求...")
    status, data = get("https://httpbin.org/get", timeout=5)
    print(f"   状态码: {status}")
    print(f"   响应: {data}")
    
    # 测试POST请求
    print("2. 测试POST请求...")
    status, data = post("https://httpbin.org/post", 
                       json_data={"test": "data"}, timeout=5)
    print(f"   状态码: {status}")
    print(f"   响应: {data}")
    
    print("✅ 测试完成")
