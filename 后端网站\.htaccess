# Augment认证系统 - Apache配置

# 启用重写引擎
RewriteEngine On

# Favicon重定向
RewriteRule ^favicon\.ico$ favicon.php [L]

# 安全设置
<Files "*.php">
    # 防止直接访问配置文件
    <FilesMatch "^(config|includes)/">
        Order Deny,Allow
        Deny from all
    </FilesMatch>
</Files>

# 防止访问敏感文件
<FilesMatch "\.(log|sql|md)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 防止访问隐藏文件
<FilesMatch "^\.">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/x-icon "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>
