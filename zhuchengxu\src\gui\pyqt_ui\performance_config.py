#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt性能优化配置
Performance Configuration for PyQt UI
"""

class PerformanceConfig:
    """PyQt性能优化配置类"""
    
    # 定时器优化配置 - 大幅降低刷新频率
    TIMER_INTERVALS = {
        'time_update': 30000,       # 时间更新：30秒（降低频率）
        'mailbox_refresh': 30000,   # 邮箱刷新：30秒（大幅降低）
        'system_info': 60000,       # 系统信息：60秒
        'status_update': 45000,     # 状态更新：45秒
        'performance_check': 120000, # 性能检查：2分钟
    }
    
    # UI更新优化配置
    UI_OPTIMIZATION = {
        'enable_stylesheet_cache': True,    # 启用样式表缓存
        'enable_data_diff_check': True,     # 启用数据差异检查
        'batch_ui_updates': True,           # 批量UI更新
        'lazy_loading': True,               # 延迟加载
        'reduce_redraws': True,             # 减少重绘
    }
    
    # 内存优化配置 - 更激进的内存管理
    MEMORY_OPTIMIZATION = {
        'clear_unused_widgets': True,      # 清理未使用的组件
        'limit_history_items': 10,         # 限制历史项目数量（减少到10）
        'garbage_collect_interval': 30,    # 垃圾回收间隔（减少到30秒）
        'cache_size_limit': 30,            # 缓存大小限制（减少到30MB）
        'auto_cleanup_cache': True,        # 自动清理缓存
        'max_page_cache': 3,               # 最大页面缓存数量
    }
    
    # 网络请求优化
    NETWORK_OPTIMIZATION = {
        'request_timeout': 10,              # 请求超时：10秒
        'max_retries': 2,                   # 最大重试次数：2次
        'connection_pool_size': 5,          # 连接池大小
        'enable_request_cache': True,       # 启用请求缓存
    }
    
    # 动画和效果优化
    ANIMATION_OPTIMIZATION = {
        'enable_animations': True,          # 启用动画
        'animation_duration': 200,          # 动画持续时间（毫秒）
        'reduce_transparency': False,       # 减少透明效果
        'disable_shadows': False,           # 禁用阴影效果
    }
    
    @classmethod
    def get_timer_interval(cls, timer_name: str) -> int:
        """
        获取定时器间隔
        
        Args:
            timer_name: 定时器名称
            
        Returns:
            间隔时间（毫秒）
        """
        return cls.TIMER_INTERVALS.get(timer_name, 5000)
    
    @classmethod
    def is_optimization_enabled(cls, optimization_name: str) -> bool:
        """
        检查优化选项是否启用
        
        Args:
            optimization_name: 优化选项名称
            
        Returns:
            是否启用
        """
        for category in [cls.UI_OPTIMIZATION, cls.MEMORY_OPTIMIZATION, 
                        cls.NETWORK_OPTIMIZATION, cls.ANIMATION_OPTIMIZATION]:
            if optimization_name in category:
                return category[optimization_name]
        return False
    
    @classmethod
    def get_optimization_value(cls, optimization_name: str, default=None):
        """
        获取优化配置值
        
        Args:
            optimization_name: 优化选项名称
            default: 默认值
            
        Returns:
            配置值
        """
        for category in [cls.UI_OPTIMIZATION, cls.MEMORY_OPTIMIZATION, 
                        cls.NETWORK_OPTIMIZATION, cls.ANIMATION_OPTIMIZATION,
                        cls.TIMER_INTERVALS]:
            if optimization_name in category:
                return category[optimization_name]
        return default


class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self):
        self.start_time = None
        self.operation_times = {}
    
    def start_operation(self, operation_name: str):
        """开始监控操作"""
        import time
        self.operation_times[operation_name] = time.time()
    
    def end_operation(self, operation_name: str) -> float:
        """结束监控操作并返回耗时"""
        import time
        if operation_name in self.operation_times:
            duration = time.time() - self.operation_times[operation_name]
            del self.operation_times[operation_name]
            return duration
        return 0.0
    
    def log_performance(self, operation_name: str, duration: float, threshold: float = 0.1):
        """记录性能日志"""
        if duration > threshold:
            print(f"⚠️ 性能警告: {operation_name} 耗时 {duration:.3f}s (阈值: {threshold}s)")
        else:
            print(f"✅ 性能正常: {operation_name} 耗时 {duration:.3f}s")


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


def performance_decorator(operation_name: str, threshold: float = 0.1):
    """性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            performance_monitor.start_operation(operation_name)
            try:
                result = func(*args, **kwargs)
                duration = performance_monitor.end_operation(operation_name)
                performance_monitor.log_performance(operation_name, duration, threshold)
                return result
            except Exception as e:
                performance_monitor.end_operation(operation_name)
                raise e
        return wrapper
    return decorator


# 性能优化工具函数
def optimize_widget_updates(widget):
    """优化组件更新"""
    if PerformanceConfig.is_optimization_enabled('reduce_redraws'):
        widget.setUpdatesEnabled(False)
        return lambda: widget.setUpdatesEnabled(True)
    return lambda: None


def should_update_data(old_data, new_data) -> bool:
    """检查是否需要更新数据"""
    if not PerformanceConfig.is_optimization_enabled('enable_data_diff_check'):
        return True
    
    # 简单的数据比较
    if old_data is None and new_data is None:
        return False
    if old_data is None or new_data is None:
        return True
    
    # 对于列表类型的数据
    if isinstance(old_data, list) and isinstance(new_data, list):
        if len(old_data) != len(new_data):
            return True
        # 简单的内容比较（可以根据需要优化）
        return str(old_data) != str(new_data)
    
    return old_data != new_data


def get_cached_stylesheet(cache_key: str, generator_func):
    """获取缓存的样式表"""
    if not PerformanceConfig.is_optimization_enabled('enable_stylesheet_cache'):
        return generator_func()
    
    if not hasattr(get_cached_stylesheet, '_cache'):
        get_cached_stylesheet._cache = {}
    
    if cache_key not in get_cached_stylesheet._cache:
        get_cached_stylesheet._cache[cache_key] = generator_func()
    
    return get_cached_stylesheet._cache[cache_key]
