#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL修复工具
解决SSL证书验证问题
"""

import ssl
import os
import sys
import types


def create_dummy_ca_file() -> str:
    """
    创建一个有效的CA证书文件
    
    Returns:
        CA证书文件路径
    """
    ca_content = """-----BEGIN CERTIFICATE-----
MIIFazCCA1OgAwIBAgIRAIIQz7DSQONZRGPgu2OCiwAwDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMTUwNjA0MTEwNDM4
WhcNMzUwNjA0MTEwNDM4WjBPMQswCQYDVQQGEwJVUzEpMCcGA1UEChMgSW50ZXJu
ZXQgU2VjdXJpdHkgUmVzZWFyY2ggR3JvdXAxFTATBgNVBAMTDElTUkcgUm9vdCBY
MTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAK3oJHP0FDfzm54rVygc
h77ct984kIxuPOZXoHj3dcKi/vVqbvYATyjb3miGbESTtrFj/RQSa78f0uoxmyF+
0TM8ukj13Xnfs7j/EvEhmkvBioZxaUpmZmyPfjxwv60pIgbz5MDmgK7iS4+3mX6U
A5/TR5d8mUgjU+g4rk8Kb4Mu0UlXjIB0ttov0DiNewNwIRt18jA8+o+u3dpjq+sW
T8KOEUt+zwvo/7V3LvSye0rgTBIlDHCNAymg4VMk7BPZ7hm/ELNKjD+Jo2FR3qyH
B5T0Y3HsLuJvW5iB4YlcNHlsdu87kGJ55tukmi8mxdAQ4Q7e2RCOFvu396j3x+UC
B5iPNgiV5+I3lg02dZ77DnKxHZu8A/lJBdiB3QW0KtZB6awBdpUKD9jf1b0SHzUv
KBds0pjBqAlkd25HN7rOrFleaJ1/ctaJxQZBKT5ZPt0m9STJEadao0xAH0ahmbWn
OlFuhjuefXKnEgV4We0+UXgVCwOPjdAvBbI+e0ocS3MFEvzG6uBQE3xDk3SzynTn
jh8BCNAw1FtxNrQHusEwMFxIt4I7mKZ9YIqioymCzLq9gwQbooMDQaHWBfEbwrbw
qHyGO0aoSCqI3Haadr8faqU9GY/rOPNk3sgrDQoo//fb4hVC1CLQJ13hef4Y53CI
rU7m2Ys6xt0nUW7/vGT1M0NPAgMBAAGjQjBAMA4GA1UdDwEB/wQEAwIBBjAPBgNV
HRMBAf8EBTADAQH/MB0GA1UdDgQWBBR5tFnme7bl5AFzgAiIyBpY9umbbjANBgkq
hkiG9w0BAQsFAAOCAgEAVR9YqbyyqFDQDLHYGmkgJykIrGF1XIpu+ILlaS/V9lZL
ubhzEFnTIZd+50xx+7LSYK05qAvqFyFWhfFQDlnrzuBZ6brJFe+GnY+EgPbk6ZGQ
3BebYhtF8GaV0nxvwuo77x/Py9auJ/GpsMiu/X1+mvoiBOv/2X/qkSsisRcOj/KK
NFtY2PwByVS5uCbMiogziUwthDyC3+6WVwW6LLv3xLfHTjuCvjHIInNzktHCgKQ5
ORAzI4JMPJ+GslWYHb4phowim57iaztXOoJwTdwJx4nLCgdNbOhdjsnvzqvHu7Ur
TkXWStAmzOVyyghqpZXjFaH3pO3JLF+l+/+sKAIuvtd7u+Nxe5AW0wdeRlN8NwdC
jNPElpzVmbUq4JUagEiuTDkHzsxHpFKVK7q4+63SM1N95R1NbdWhscdCb+ZAJzVc
oyi3B43njTOQ5yOf+1CceWxG1bQVs5ZufpsMljq4Ui0/1lvh+wjChP4kqKOJ2qxq
4RgqsahDYVvTH9w7jXbyLeiNdd8XM2w9U/t7y0Ff/9yi0GE44Za4rF2LN9d11TPA
mRGunUHBcnWEvgJBQl9nJEiU0Zsnvgc/ubhPgXRR4Xq37Z0j4r7g1SgEEzwxA57d
emyPxgcYxn/eR44/KJ4EBs+lVDR3veyJm+kXQ99b21/+jh5Xos1AnX5iItreGCc=
-----END CERTIFICATE-----"""

    ca_file_path = os.path.join(os.path.dirname(__file__), 'dummy_ca.pem')
    try:
        with open(ca_file_path, 'w') as f:
            f.write(ca_content)
        return ca_file_path
    except:
        return None


def fix_ssl_issues():
    """修复SSL相关问题"""
    # 设置环境变量禁用SSL验证
    os.environ['PYTHONHTTPSVERIFY'] = '0'
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    
    # 修复certifi问题
    try:
        import certifi
        # 创建有效的CA文件
        dummy_ca_path = create_dummy_ca_file()

        # 如果certifi没有where属性，添加修复
        if not hasattr(certifi, 'where'):
            def _certifi_where():
                return dummy_ca_path
            certifi.where = _certifi_where
        else:
            # 如果有where属性但返回None，也修复
            original_where = certifi.where
            def _certifi_where():
                try:
                    result = original_where()
                    if result is None or not os.path.exists(result):
                        return dummy_ca_path
                    return result
                except:
                    return dummy_ca_path
            certifi.where = _certifi_where
    except ImportError:
        # 如果没有certifi，创建一个模拟的certifi模块
        certifi = types.ModuleType('certifi')
        dummy_ca_path = create_dummy_ca_file()
        def _certifi_where():
            return dummy_ca_path
        certifi.where = _certifi_where
        sys.modules['certifi'] = certifi

    # 禁用urllib3警告
    try:
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    except:
        pass

    # 禁用SSL验证（临时解决方案）
    try:
        ssl._create_default_https_context = ssl._create_unverified_context
    except:
        pass


def check_ssl_status() -> dict:
    """
    检查SSL相关库的状态
    
    Returns:
        SSL状态信息字典
    """
    status = {
        'ssl_available': True,
        'certifi_available': False,
        'urllib3_available': False,
        'requests_available': False,
        'ssl_context_fixed': False
    }
    
    try:
        import ssl
        status['ssl_available'] = True
        status['ssl_context_fixed'] = hasattr(ssl, '_create_unverified_context')
    except ImportError:
        status['ssl_available'] = False
    
    try:
        import certifi
        status['certifi_available'] = True
    except ImportError:
        status['certifi_available'] = False
    
    try:
        import urllib3
        status['urllib3_available'] = True
    except ImportError:
        status['urllib3_available'] = False
    
    try:
        import requests
        status['requests_available'] = True
    except ImportError:
        status['requests_available'] = False
    
    return status
