#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
提供统一的日志记录功能
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional, List, Callable
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Logger:
    """统一日志管理器"""
    
    def __init__(self, name: str = "AugmentTool", log_file: Optional[str] = None):
        """
        初始化日志器
        
        Args:
            name: 日志器名称
            log_file: 日志文件路径，如果为None则不写入文件
        """
        self.name = name
        self.log_file = log_file
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
        
        # 实时日志回调函数列表
        self.realtime_callbacks: List[Callable[[str], None]] = []
        
        # 内存日志缓存
        self.memory_logs: List[str] = []
        self.max_memory_logs = 1000

        # 日志去重和限制
        self._last_log_message = ""
        self._duplicate_count = 0
        self._max_duplicate_logs = 3  # 最多显示3条重复日志
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if self.log_file:
            try:
                # 确保日志目录存在
                log_dir = os.path.dirname(self.log_file)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir)
                
                file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
                file_handler.setLevel(logging.DEBUG)
                file_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                file_handler.setFormatter(file_formatter)
                self.logger.addHandler(file_handler)
            except Exception as e:
                print(f"无法创建日志文件处理器: {e}")
    
    def add_realtime_callback(self, callback: Callable[[str], None]):
        """
        添加实时日志回调函数
        
        Args:
            callback: 回调函数，接收日志消息字符串
        """
        if callback not in self.realtime_callbacks:
            self.realtime_callbacks.append(callback)
    
    def remove_realtime_callback(self, callback: Callable[[str], None]):
        """
        移除实时日志回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        if callback in self.realtime_callbacks:
            self.realtime_callbacks.remove(callback)
    
    def _log_with_callback(self, level: LogLevel, message: str):
        """
        记录日志并调用回调函数

        Args:
            level: 日志级别
            message: 日志消息
        """
        # 检查是否为重复日志
        if self._is_duplicate_log(message):
            return

        # 记录到标准日志
        getattr(self.logger, level.value.lower())(message)

        # 添加到内存缓存
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}"

        # 对于DEBUG级别的长消息进行截断
        if level == LogLevel.DEBUG and len(formatted_message) > 200:
            formatted_message = formatted_message[:200] + "... [截断]"

        self.memory_logs.append(formatted_message)

        # 限制内存日志数量
        if len(self.memory_logs) > self.max_memory_logs:
            self.memory_logs = self.memory_logs[-self.max_memory_logs:]

        # 调用实时回调
        for callback in self.realtime_callbacks:
            try:
                callback(formatted_message)
            except Exception as e:
                # 避免回调函数错误影响日志记录
                self.logger.error(f"实时日志回调函数错误: {e}")

    def _is_duplicate_log(self, message: str) -> bool:
        """
        检查是否为重复日志

        Args:
            message: 日志消息

        Returns:
            是否为重复日志
        """
        # 简化消息用于比较（去除时间戳等变化部分）
        simplified_message = self._simplify_message(message)

        if simplified_message == self._last_log_message:
            self._duplicate_count += 1
            if self._duplicate_count > self._max_duplicate_logs:
                return True  # 跳过重复日志
        else:
            # 如果有重复日志被跳过，记录一条汇总信息
            if self._duplicate_count > self._max_duplicate_logs:
                summary_message = f"[重复日志] 上一条消息重复了 {self._duplicate_count - self._max_duplicate_logs} 次"
                timestamp = datetime.now().strftime('%H:%M:%S')
                formatted_summary = f"[{timestamp}] {summary_message}"
                self.memory_logs.append(formatted_summary)

                # 调用回调
                for callback in self.realtime_callbacks:
                    try:
                        callback(formatted_summary)
                    except:
                        pass

            self._last_log_message = simplified_message
            self._duplicate_count = 1

        return False

    def _simplify_message(self, message: str) -> str:
        """
        简化消息用于重复检测

        Args:
            message: 原始消息

        Returns:
            简化后的消息
        """
        # 移除时间戳、状态码等变化的部分
        import re

        # 移除时间戳
        message = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '', message)
        # 移除HTTP状态码
        message = re.sub(r'状态码: \d+', '状态码: XXX', message)
        # 移除响应头中的时间
        message = re.sub(r"'Date': '[^']*'", "'Date': 'XXX'", message)
        # 移除HTML内容
        message = re.sub(r'<html>.*?</html>', '<html>...</html>', message, flags=re.DOTALL)

        return message.strip()
    
    def debug(self, message: str):
        """记录调试信息"""
        self._log_with_callback(LogLevel.DEBUG, message)
    
    def info(self, message: str):
        """记录一般信息"""
        self._log_with_callback(LogLevel.INFO, message)
    
    def warning(self, message: str):
        """记录警告信息"""
        self._log_with_callback(LogLevel.WARNING, message)
    
    def error(self, message: str):
        """记录错误信息"""
        self._log_with_callback(LogLevel.ERROR, message)
    
    def critical(self, message: str):
        """记录严重错误信息"""
        self._log_with_callback(LogLevel.CRITICAL, message)
    
    def log(self, message: str, level: LogLevel = LogLevel.INFO):
        """
        通用日志记录方法
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        self._log_with_callback(level, message)
    
    def get_memory_logs(self) -> List[str]:
        """
        获取内存中的日志
        
        Returns:
            日志消息列表
        """
        return self.memory_logs.copy()
    
    def clear_memory_logs(self):
        """清空内存日志"""
        self.memory_logs.clear()
    
    def set_level(self, level: LogLevel):
        """
        设置日志级别
        
        Args:
            level: 日志级别
        """
        log_level = getattr(logging, level.value)
        self.logger.setLevel(log_level)
        
        # 更新所有处理器的级别
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                # 控制台处理器
                handler.setLevel(log_level)


# 全局日志器实例
_global_logger: Optional[Logger] = None


def get_logger(name: str = "AugmentTool", log_file: Optional[str] = None) -> Logger:
    """
    获取全局日志器实例
    
    Args:
        name: 日志器名称
        log_file: 日志文件路径
        
    Returns:
        日志器实例
    """
    global _global_logger
    if _global_logger is None:
        _global_logger = Logger(name, log_file)
    return _global_logger


def setup_logging(log_file: Optional[str] = None, level: LogLevel = LogLevel.INFO):
    """
    设置全局日志配置
    
    Args:
        log_file: 日志文件路径
        level: 日志级别
    """
    logger = get_logger(log_file=log_file)
    logger.set_level(level)
