<?php
/**
 * 安全配置类
 * 集中管理系统安全设置
 */

declare(strict_types=1);

class SecurityConfig
{
    // 管理员密码 - 请修改为更强的密码
    public const ADMIN_PASSWORD = 'AugmentAdmin2025!@#';
    
    // 会话安全设置
    public const SESSION_TIMEOUT = 3600; // 1小时
    public const MAX_LOGIN_ATTEMPTS = 5;
    public const LOGIN_LOCKOUT_TIME = 900; // 15分钟
    
    // API安全设置
    public const API_RATE_LIMIT = 100; // 每分钟最大请求数
    public const TOKEN_EXPIRE_TIME = 86400; // 24小时
    
    // 密码安全设置
    public const MIN_PASSWORD_LENGTH = 8;
    public const PASSWORD_REQUIRE_SPECIAL = true;
    public const PASSWORD_REQUIRE_NUMBER = true;
    public const PASSWORD_REQUIRE_UPPERCASE = true;
    
    /**
     * 初始化安全设置
     */
    public static function init(): void
    {
        // 设置安全的会话配置
        if (session_status() === PHP_SESSION_NONE) {
            ini_set('session.cookie_httponly', '1');
            ini_set('session.cookie_secure', '0'); // HTTPS环境请设为1
            ini_set('session.use_strict_mode', '1');
            ini_set('session.cookie_samesite', 'Strict');
            session_start();
        }
        
        // 设置错误报告
        error_reporting(E_ALL);
        ini_set('display_errors', '0');
        ini_set('log_errors', '1');
    }
    
    /**
     * 设置安全响应头
     */
    public static function setSecurityHeaders(): void
    {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' cdn.jsdelivr.net; style-src \'self\' \'unsafe-inline\' cdn.jsdelivr.net; img-src \'self\' data:;');
    }
    
    /**
     * 验证密码强度
     */
    public static function validatePassword(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < self::MIN_PASSWORD_LENGTH) {
            $errors[] = "密码长度至少{self::MIN_PASSWORD_LENGTH}位";
        }
        
        if (self::PASSWORD_REQUIRE_UPPERCASE && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "密码必须包含大写字母";
        }
        
        if (self::PASSWORD_REQUIRE_NUMBER && !preg_match('/[0-9]/', $password)) {
            $errors[] = "密码必须包含数字";
        }
        
        if (self::PASSWORD_REQUIRE_SPECIAL && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "密码必须包含特殊字符";
        }
        
        return $errors;
    }
    
    /**
     * 清理输入数据
     */
    public static function sanitizeInput(string $input, int $maxLength = 255): string
    {
        $input = trim($input);
        $input = substr($input, 0, $maxLength);
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 验证IP地址
     */
    public static function validateIP(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * 生成CSRF令牌
     */
    public static function generateCSRFToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * 验证CSRF令牌
     */
    public static function validateCSRFToken(string $token): bool
    {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * 记录安全事件
     */
    public static function logSecurityEvent(string $event, array $data = []): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'data' => $data
        ];
        
        error_log("SECURITY: " . json_encode($logData));
    }
}
