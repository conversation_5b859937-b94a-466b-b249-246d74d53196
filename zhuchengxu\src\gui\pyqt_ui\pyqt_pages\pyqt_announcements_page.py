"""
PyQt公告页面 - 现代化设计
参考后端网站的公告系统，实现完整的公告展示功能
"""

import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import os

try:
    from src.gui.pyqt_ui.pyqt_design_system import DS
    from src.gui.pyqt_ui.pyqt_components import NeonButton, GlassWidget as GlassFrame
    COMPONENTS_AVAILABLE = True
except ImportError:
    COMPONENTS_AVAILABLE = False

from src.utils.logger import get_logger


class AnnouncementCard(QFrame):
    """公告卡片组件"""
    
    clicked = pyqtSignal(dict)
    
    def __init__(self, announcement_data):
        super().__init__()
        self.announcement_data = announcement_data
        self.logger = get_logger()
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI - 按照PyQt布局最佳实践"""
        # 设置卡片基本属性
        self.setMinimumHeight(120)  # 使用最小高度而不是固定高度
        self.setMaximumHeight(150)  # 设置最大高度
        self.setCursor(Qt.PointingHandCursor)

        # 设置尺寸策略 - 水平扩展，垂直固定
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # 根据公告类型设置样式
        type_colors = {
            'info': '#00bcd4',
            'warning': '#ff9800',
            'success': '#4caf50',
            'error': '#f44336',
            'update': '#9c27b0'
        }

        announcement_type = self.announcement_data.get('type', 'info')
        accent_color = type_colors.get(announcement_type, '#00bcd4')

        self.setStyleSheet(f"""
            AnnouncementCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2d2d2d,
                    stop:1 #1a1a1a);
                border: 1px solid #444444;
                border-left: 4px solid {accent_color};
                border-radius: 12px;
                margin: 5px;
                padding: 5px;
            }}
            AnnouncementCard:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3d3d3d,
                    stop:1 #2d2d2d);
                border: 1px solid {accent_color};
            }}
        """)
        
        # 主布局 - 垂直布局管理器
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 12, 15, 12)  # 调整边距
        layout.setSpacing(10)  # 增加间距

        # 头部布局 - 水平布局管理器
        header_layout = QHBoxLayout()
        header_layout.setSpacing(12)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # 类型图标
        type_icons = {
            'info': '📢',
            'warning': '⚠️',
            'success': '✅', 
            'error': '❌',
            'update': '🔄'
        }
        
        icon_label = QLabel(type_icons.get(announcement_type, '📢'))
        icon_label.setFont(QFont('Segoe UI Emoji', 16))
        header_layout.addWidget(icon_label)
        
        # 标题
        title = self.announcement_data.get('title', '无标题')
        title_label = QLabel(title)
        title_font = QFont("Microsoft YaHei UI", 14, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #ffffff; font-weight: bold;")
        header_layout.addWidget(title_label)
        
        # 优先级标签
        priority = self.announcement_data.get('priority', 0)
        if priority > 80:
            priority_label = QLabel('🔥 重要')
            priority_label.setStyleSheet(f"""
                background: {DS.COLORS['neon_red'].name()};
                color: white;
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
            """)
            header_layout.addWidget(priority_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # 内容预览
        content = self.announcement_data.get('content', '')
        preview = content[:100] + '...' if len(content) > 100 else content
        content_label = QLabel(preview)
        content_font = QFont("Microsoft YaHei UI", 10)
        content_label.setFont(content_font)
        content_label.setStyleSheet("color: #cccccc;")
        content_label.setWordWrap(True)
        layout.addWidget(content_label)

        # 图片预览（如果有图片）
        images = self.announcement_data.get('images', [])
        if images and len(images) > 0:
            image_info_layout = QHBoxLayout()
            image_info_layout.setContentsMargins(0, 5, 0, 0)

            # 图片图标
            image_icon = QLabel("🖼️")
            image_icon.setFont(QFont("Segoe UI Emoji", 12))
            image_icon.setStyleSheet("color: #00bcd4;")
            image_info_layout.addWidget(image_icon)

            # 图片信息
            image_count = len(images)
            image_text = f"包含 {image_count} 张图片" if image_count > 1 else "包含图片"
            image_label = QLabel(image_text)
            image_font = QFont("Microsoft YaHei UI", 9)
            image_label.setFont(image_font)
            image_label.setStyleSheet("color: #00bcd4;")
            image_info_layout.addWidget(image_label)

            image_info_layout.addStretch()
            layout.addLayout(image_info_layout)
        
        # 底部信息 - 水平布局管理器
        footer_layout = QHBoxLayout()
        footer_layout.setSpacing(15)
        footer_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        
        # 创建时间
        created_at = self.announcement_data.get('created_at', '')
        if created_at:
            try:
                # 解析时间并格式化
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                time_str = dt.strftime('%Y-%m-%d %H:%M')
                time_label = QLabel(f"📅 {time_str}")
                time_font = QFont("Microsoft YaHei UI", 9)
                time_label.setFont(time_font)
                time_label.setStyleSheet("color: #999999;")
                footer_layout.addWidget(time_label)
            except:
                pass
        
        # 点击次数
        click_count = self.announcement_data.get('click_count', 0)
        if click_count > 0:
            click_label = QLabel(f"👁️ {click_count}")
            click_font = QFont("Microsoft YaHei UI", 9)
            click_label.setFont(click_font)
            click_label.setStyleSheet("color: #999999;")
            footer_layout.addWidget(click_label)
        
        footer_layout.addStretch()
        layout.addLayout(footer_layout)
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.announcement_data)
        super().mousePressEvent(event)


class AnnouncementDetailDialog(QDialog):
    """公告详情对话框"""
    
    def __init__(self, announcement_data, parent=None):
        super().__init__(parent)
        self.announcement_data = announcement_data
        self.logger = get_logger()
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI - 使用更大的窗口尺寸"""
        self.setWindowTitle("公告详情")

        # 根据屏幕大小动态设置窗口尺寸 - 增大窗口
        screen = QApplication.desktop().screenGeometry()
        window_width = min(1200, int(screen.width() * 0.85))   # 增大到1200px或屏幕宽度85%
        window_height = min(900, int(screen.height() * 0.85))  # 增大到900px或屏幕高度85%

        self.resize(window_width, window_height)

        # 设置最小尺寸
        self.setMinimumSize(800, 600)  # 增大最小尺寸

        # 居中显示
        self.move(
            (screen.width() - window_width) // 2,
            (screen.height() - window_height) // 2
        )

        self.setStyleSheet("""
            QDialog {
                background: #1a1a1a;
                border: 1px solid #444444;
                border-radius: 15px;
                color: #ffffff;
            }
            QTextEdit {
                background: #2d2d2d;
                border: 1px solid #444444;
                border-radius: 8px;
                padding: 15px;
                color: #ffffff;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        
        # 主布局 - 垂直布局，内容贴合边框
        main_layout = QVBoxLayout(self)

        # 设置很小的边距，让内容贴合窗口边框
        main_layout.setContentsMargins(15, 15, 15, 15)  # 只保留最小边距
        main_layout.setSpacing(15)

        # 标题区域
        title_layout = QVBoxLayout()
        title_layout.setSpacing(10)

        # 标题
        title = self.announcement_data.get('title', '无标题')
        title_label = QLabel(title)
        title_font = QFont("Microsoft YaHei UI", 20, QFont.Bold)  # 增大标题字体
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #ffffff; font-weight: bold; padding: 10px 0;")
        title_label.setWordWrap(True)
        title_layout.addWidget(title_label)

        # 元信息
        meta_layout = QHBoxLayout()
        meta_layout.setSpacing(30)

        # 类型
        announcement_type = self.announcement_data.get('type', 'info')
        type_names = {
            'info': '📢 信息',
            'warning': '⚠️ 警告',
            'success': '✅ 成功',
            'error': '❌ 错误',
            'update': '🔄 更新'
        }
        type_label = QLabel(type_names.get(announcement_type, '📢 信息'))
        type_font = QFont("Microsoft YaHei UI", 14)  # 增大字体
        type_label.setFont(type_font)
        type_label.setStyleSheet("color: #00bcd4; font-weight: bold;")
        meta_layout.addWidget(type_label)

        # 时间
        created_at = self.announcement_data.get('created_at', '')
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                time_str = dt.strftime('%Y年%m月%d日 %H:%M')
                time_label = QLabel(f"📅 {time_str}")
                time_font = QFont("Microsoft YaHei UI", 14)  # 增大字体
                time_label.setFont(time_font)
                time_label.setStyleSheet("color: #cccccc;")
                meta_layout.addWidget(time_label)
            except:
                pass

        meta_layout.addStretch()
        title_layout.addLayout(meta_layout)
        main_layout.addLayout(title_layout)

        # 内容区域 - 左右分栏布局
        content_layout = QHBoxLayout()

        # 设置适度的分栏间距
        content_layout.setSpacing(20)  # 固定20px间距，简单有效

        # 检查是否有图片
        images = self.announcement_data.get('images', [])
        has_images = images and len(images) > 0

        if has_images:
            # 左侧图片区域
            image_widget = self._create_image_panel(images)
            content_layout.addWidget(image_widget, 1)  # 占1/3空间

            # 右侧文字区域
            text_widget = self._create_text_panel()
            content_layout.addWidget(text_widget, 2)  # 占2/3空间
        else:
            # 没有图片时，文字占满整个区域
            text_widget = self._create_text_panel()
            content_layout.addWidget(text_widget, 1)

        main_layout.addLayout(content_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.addStretch()

        close_btn = QPushButton("关闭")
        close_btn.setMinimumSize(100, 35)  # 增大按钮尺寸
        close_btn_font = QFont("Microsoft YaHei UI", 12)  # 增大按钮字体
        close_btn.setFont(close_btn_font)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #00bcd4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #00acc1;
            }
            QPushButton:pressed {
                background: #0097a7;
            }
        """)
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        main_layout.addLayout(button_layout)

    def _create_image_panel(self, images):
        """创建左侧图片面板"""
        # 图片面板容器
        image_panel = QWidget()
        image_panel.setStyleSheet("""
            QWidget {
                background: #2d2d2d;
                border: 1px solid #444444;
                border-radius: 10px;
                padding: 15px;
            }
        """)

        panel_layout = QVBoxLayout(image_panel)
        panel_layout.setContentsMargins(15, 15, 15, 15)  # 适度内边距
        panel_layout.setSpacing(12)

        # 图片标题
        image_title = QLabel(f"📷 公告图片 ({len(images)}张)")
        image_title_font = QFont("Microsoft YaHei UI", 16, QFont.Bold)  # 增大字体
        image_title.setFont(image_title_font)
        image_title.setStyleSheet("color: #00bcd4; font-weight: bold; padding-bottom: 10px;")
        panel_layout.addWidget(image_title)

        # 图片滚动区域
        image_scroll = QScrollArea()
        image_scroll.setWidgetResizable(True)
        image_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        image_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        image_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #1a1a1a;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #00bcd4;
                border-radius: 4px;
                min-height: 20px;
            }
        """)

        # 图片容器
        image_container = QWidget()
        image_layout = QVBoxLayout(image_container)
        image_layout.setSpacing(15)
        image_layout.setContentsMargins(5, 5, 5, 5)

        # 显示每张图片
        for i, image_data in enumerate(images):
            image_widget = self._create_compact_image_widget(image_data, i)
            if image_widget:
                image_layout.addWidget(image_widget)

        # 添加弹性空间
        image_layout.addStretch()

        image_scroll.setWidget(image_container)
        panel_layout.addWidget(image_scroll)

        return image_panel

    def _create_text_panel(self):
        """创建右侧文字面板"""
        # 文字面板容器
        text_panel = QWidget()
        text_panel.setStyleSheet("""
            QWidget {
                background: #2d2d2d;
                border: 1px solid #444444;
                border-radius: 10px;
                padding: 15px;
            }
        """)

        panel_layout = QVBoxLayout(text_panel)
        panel_layout.setContentsMargins(20, 15, 20, 15)  # 适度内边距，充分利用空间
        panel_layout.setSpacing(12)

        # 内容标题
        content_title = QLabel("📄 公告内容")
        content_title_font = QFont("Microsoft YaHei UI", 16, QFont.Bold)  # 增大字体
        content_title.setFont(content_title_font)
        content_title.setStyleSheet("color: #00bcd4; font-weight: bold; padding-bottom: 10px;")
        panel_layout.addWidget(content_title)

        # 内容文本区域
        content_area = QTextEdit()
        content_area.setPlainText(self.announcement_data.get('content', ''))
        content_area.setReadOnly(True)

        # 设置更大的字体
        content_font = QFont("Microsoft YaHei UI", 14)  # 从12增大到14
        content_area.setFont(content_font)

        # 设置文本样式 - 减少内边距，充分利用空间
        content_area.setStyleSheet("""
            QTextEdit {
                background: #1a1a1a;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 15px;
                color: #ffffff;
                font-size: 14px;
                line-height: 1.8;
            }
        """)

        # 设置文本编辑器的属性
        content_area.setLineWrapMode(QTextEdit.WidgetWidth)  # 自动换行
        content_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        content_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        panel_layout.addWidget(content_area)

        return text_panel

    def _create_compact_image_widget(self, image_data, index):
        """创建紧凑型图片组件（用于左侧面板）"""
        try:
            # 图片数据结构：{'filename': '...', 'data': 'base64...', 'type': '...', 'size': ...}
            if not isinstance(image_data, dict):
                return None

            filename = image_data.get('filename', f'image_{index}.jpg')
            base64_data = image_data.get('data', '')
            file_type = image_data.get('type', 'image/jpeg')
            file_size = image_data.get('size', 0)

            if not base64_data:
                return None

            # 创建图片容器
            image_frame = QFrame()
            image_frame.setStyleSheet("""
                QFrame {
                    background: #1a1a1a;
                    border: 1px solid #555555;
                    border-radius: 8px;
                    padding: 10px;
                }
                QFrame:hover {
                    border-color: #00bcd4;
                }
            """)

            frame_layout = QVBoxLayout(image_frame)
            frame_layout.setSpacing(8)
            frame_layout.setContentsMargins(10, 10, 10, 10)

            # 文件信息
            info_layout = QHBoxLayout()
            info_layout.setContentsMargins(0, 0, 0, 0)

            # 文件名（简化显示）
            display_name = filename if len(filename) <= 20 else filename[:17] + "..."
            filename_label = QLabel(f"📎 {display_name}")
            filename_font = QFont("Microsoft YaHei UI", 11, QFont.Bold)  # 增大字体
            filename_label.setFont(filename_font)
            filename_label.setStyleSheet("color: #ffffff;")
            info_layout.addWidget(filename_label)

            info_layout.addStretch()

            # 文件大小
            if file_size > 0:
                size_text = self._format_file_size(file_size)
                size_label = QLabel(size_text)
                size_font = QFont("Microsoft YaHei UI", 10)  # 增大字体
                size_label.setFont(size_font)
                size_label.setStyleSheet("color: #cccccc;")
                info_layout.addWidget(size_label)

            frame_layout.addLayout(info_layout)

            # 尝试显示图片
            try:
                import base64
                from PyQt5.QtGui import QPixmap

                # 解码base64图片数据
                image_bytes = base64.b64decode(base64_data)

                # 创建QPixmap
                pixmap = QPixmap()
                if pixmap.loadFromData(image_bytes):
                    # 缩放图片到合适大小（左侧面板用较小尺寸）
                    max_width = 250
                    max_height = 150

                    if pixmap.width() > max_width or pixmap.height() > max_height:
                        pixmap = pixmap.scaled(max_width, max_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)

                    # 创建图片标签
                    image_label = QLabel()
                    image_label.setPixmap(pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    image_label.setStyleSheet("""
                        QLabel {
                            border: 1px solid #666666;
                            background: #000000;
                            border-radius: 4px;
                            padding: 5px;
                        }
                    """)

                    # 添加点击事件查看大图
                    image_label.mousePressEvent = lambda event: self._show_full_image(base64_data, filename)
                    image_label.setCursor(Qt.PointingHandCursor)

                    frame_layout.addWidget(image_label)

                    # 添加查看大图提示
                    tip_label = QLabel("💡 点击查看大图")
                    tip_font = QFont("Microsoft YaHei UI", 9)  # 增大字体
                    tip_label.setFont(tip_font)
                    tip_label.setStyleSheet("color: #999999; padding-top: 5px;")
                    tip_label.setAlignment(Qt.AlignCenter)
                    frame_layout.addWidget(tip_label)

                else:
                    # 图片加载失败
                    error_label = QLabel("❌ 图片加载失败")
                    error_label.setStyleSheet("color: #ff6b6b; padding: 20px; font-size: 12px;")
                    error_label.setAlignment(Qt.AlignCenter)
                    frame_layout.addWidget(error_label)

            except Exception as e:
                # 显示错误信息
                error_label = QLabel(f"❌ 图片显示错误")
                error_label.setStyleSheet("color: #ff6b6b; padding: 20px; font-size: 12px;")
                error_label.setAlignment(Qt.AlignCenter)
                frame_layout.addWidget(error_label)

            return image_frame

        except Exception as e:
            print(f"❌ 创建紧凑图片组件失败: {e}")
            return None

    def _create_image_widget(self, image_data, index):
        """创建图片显示组件"""
        try:
            # 图片数据结构：{'filename': '...', 'data': 'base64...', 'type': '...', 'size': ...}
            if not isinstance(image_data, dict):
                return None

            filename = image_data.get('filename', f'image_{index}.jpg')
            base64_data = image_data.get('data', '')
            file_type = image_data.get('type', 'image/jpeg')
            file_size = image_data.get('size', 0)

            if not base64_data:
                return None

            # 创建图片容器
            image_frame = QFrame()
            image_frame.setStyleSheet("""
                QFrame {
                    background: #2d2d2d;
                    border: 1px solid #444444;
                    border-radius: 8px;
                    padding: 10px;
                }
            """)

            frame_layout = QVBoxLayout(image_frame)
            frame_layout.setSpacing(8)

            # 图片信息
            info_layout = QHBoxLayout()
            info_layout.setContentsMargins(0, 0, 0, 0)

            # 文件名
            filename_label = QLabel(f"📎 {filename}")
            filename_font = QFont("Microsoft YaHei UI", 10, QFont.Bold)
            filename_label.setFont(filename_font)
            filename_label.setStyleSheet("color: #ffffff;")
            info_layout.addWidget(filename_label)

            info_layout.addStretch()

            # 文件大小
            if file_size > 0:
                size_text = self._format_file_size(file_size)
                size_label = QLabel(size_text)
                size_font = QFont("Microsoft YaHei UI", 9)
                size_label.setFont(size_font)
                size_label.setStyleSheet("color: #cccccc;")
                info_layout.addWidget(size_label)

            frame_layout.addLayout(info_layout)

            # 尝试显示图片
            try:
                import base64
                from PyQt5.QtGui import QPixmap

                # 解码base64图片数据
                image_bytes = base64.b64decode(base64_data)

                # 创建QPixmap
                pixmap = QPixmap()
                if pixmap.loadFromData(image_bytes):
                    # 缩放图片到合适大小
                    max_width = 400
                    max_height = 200

                    if pixmap.width() > max_width or pixmap.height() > max_height:
                        pixmap = pixmap.scaled(max_width, max_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)

                    # 创建图片标签
                    image_label = QLabel()
                    image_label.setPixmap(pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    image_label.setStyleSheet("border: 1px solid #555555; background: #1a1a1a;")

                    # 添加点击事件查看大图
                    image_label.mousePressEvent = lambda event: self._show_full_image(base64_data, filename)
                    image_label.setCursor(Qt.PointingHandCursor)

                    frame_layout.addWidget(image_label)

                    # 添加查看大图提示
                    tip_label = QLabel("💡 点击图片查看大图")
                    tip_font = QFont("Microsoft YaHei UI", 8)
                    tip_label.setFont(tip_font)
                    tip_label.setStyleSheet("color: #999999;")
                    tip_label.setAlignment(Qt.AlignCenter)
                    frame_layout.addWidget(tip_label)

                else:
                    # 图片加载失败
                    error_label = QLabel("❌ 图片加载失败")
                    error_label.setStyleSheet("color: #ff6b6b; padding: 20px;")
                    error_label.setAlignment(Qt.AlignCenter)
                    frame_layout.addWidget(error_label)

            except Exception as e:
                # 显示错误信息
                error_label = QLabel(f"❌ 图片显示错误: {str(e)}")
                error_label.setStyleSheet("color: #ff6b6b; padding: 20px;")
                error_label.setAlignment(Qt.AlignCenter)
                frame_layout.addWidget(error_label)

            return image_frame

        except Exception as e:
            print(f"❌ 创建图片组件失败: {e}")
            return None

    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes >= 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        elif size_bytes >= 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes} B"

    def _show_full_image(self, base64_data, filename):
        """显示大图"""
        try:
            import base64
            from PyQt5.QtGui import QPixmap

            # 创建大图对话框
            dialog = QDialog(self)
            dialog.setWindowTitle(f"查看图片 - {filename}")
            dialog.setModal(True)
            dialog.resize(800, 600)
            dialog.setStyleSheet("""
                QDialog {
                    background: #1a1a1a;
                    border: 1px solid #444444;
                }
            """)

            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)

            # 图片显示
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("border: none; background: #000000;")

            image_label = QLabel()
            image_label.setAlignment(Qt.AlignCenter)
            image_label.setStyleSheet("background: #000000;")

            # 解码并显示图片
            image_bytes = base64.b64decode(base64_data)
            pixmap = QPixmap()
            if pixmap.loadFromData(image_bytes):
                image_label.setPixmap(pixmap)
            else:
                image_label.setText("❌ 图片加载失败")
                image_label.setStyleSheet("color: #ff6b6b; background: #1a1a1a;")

            scroll_area.setWidget(image_label)
            layout.addWidget(scroll_area)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec_()

        except Exception as e:
            print(f"❌ 显示大图失败: {e}")


class AnnouncementLoader(QThread):
    """公告加载线程"""
    
    announcements_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, api_url):
        super().__init__()
        self.api_url = api_url
        
    def run(self):
        """运行加载任务"""
        try:
            # 构建API请求URL
            url = f"{self.api_url}?action=get_announcements"
            
            # 添加日志记录
            print(f"🔄 请求公告数据: {url}")
            
            # 发送请求，增加超时时间和更多异常处理
            try:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
            except requests.exceptions.ConnectionError:
                print(f"❌ 连接失败: 无法连接到服务器 {url}")
                self.error_occurred.emit("无法连接到服务器，请检查网络连接或服务器地址")
                return
            except requests.exceptions.Timeout:
                print(f"❌ 请求超时: 服务器响应超时 {url}")
                self.error_occurred.emit("服务器响应超时，请稍后再试")
                return
            except requests.exceptions.HTTPError as e:
                print(f"❌ HTTP错误: {e}")
                self.error_occurred.emit(f"HTTP错误: {e}")
                return
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                self.error_occurred.emit(f"网络请求异常: {e}")
                return
            
            # 解析响应
            try:
                data = response.json()
            except Exception as e:
                print(f"❌ JSON解析错误: {e}")
                self.error_occurred.emit(f"服务器返回的数据格式错误: {e}")
                return
                
            if data.get('success'):
                announcements = data.get('data', [])
                print(f"✅ 成功获取 {len(announcements)} 条公告")

                # 处理公告数据，确保图片数据正确解析
                processed_announcements = []
                for announcement in announcements:
                    # 处理图片数据
                    images = announcement.get('images', [])
                    if isinstance(images, str):
                        # 如果images是字符串，尝试解析JSON
                        try:
                            import json
                            images = json.loads(images)
                        except:
                            images = []

                    # 确保images是列表
                    if not isinstance(images, list):
                        images = []

                    announcement['images'] = images
                    processed_announcements.append(announcement)

                self.announcements_loaded.emit(processed_announcements)
            else:
                error_msg = data.get('message', '获取公告失败')
                print(f"❌ API返回错误: {error_msg}")
                self.error_occurred.emit(error_msg)
                
        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_occurred.emit(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"数据解析失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_occurred.emit(error_msg)
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_occurred.emit(error_msg)


class PyQtAnnouncementsPage(QWidget):
    """PyQt公告页面"""

    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.logger = get_logger()
        self.announcements = []
        self.loader_thread = None

        # 设置基本样式
        self.setStyleSheet("""
            QWidget {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
            QPushButton {
                background-color: #2d2d2d;
                border: 1px solid #444444;
                border-radius: 6px;
                padding: 8px 16px;
                color: #ffffff;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border-color: #00bcd4;
            }
            QScrollArea {
                border: none;
                background: transparent;
            }
        """)

        self._setup_ui()
        self._load_announcements()
        
    def _setup_ui(self):
        """设置UI - 响应式布局"""
        # 主布局 - 使用相对边距
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)  # 减少固定边距
        main_layout.setSpacing(15)  # 适中间距

        # 设置最小尺寸确保响应式
        self.setMinimumSize(600, 400)

        # 头部
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)

        # 标题
        title_label = QLabel("📢 系统公告")
        title_font = QFont("Microsoft YaHei UI", 24, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #ffffff; font-weight: bold;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self._load_announcements)
        header_layout.addWidget(refresh_btn)

        main_layout.addLayout(header_layout)
        
        # 统计信息
        self.stats_label = QLabel("正在加载...")
        stats_font = QFont("Microsoft YaHei UI", 12)
        self.stats_label.setFont(stats_font)
        self.stats_label.setStyleSheet("color: #cccccc;")
        main_layout.addWidget(self.stats_label)

        # 滚动区域 - 按照教程正确设置
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)  # 关键：允许widget自动调整大小
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #2d2d2d;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #00bcd4;
                border-radius: 4px;
                min-height: 20px;
            }
        """)

        # 滚动内容容器 - 按照教程创建独立widget
        self.scroll_content = QWidget()
        self.scroll_content.setStyleSheet("background: transparent;")

        # 公告布局 - 垂直布局管理器
        self.announcements_layout = QVBoxLayout(self.scroll_content)
        self.announcements_layout.setContentsMargins(10, 10, 10, 10)
        self.announcements_layout.setSpacing(15)
        self.announcements_layout.setAlignment(Qt.AlignTop)  # 顶部对齐

        # 设置滚动区域的widget - 关键步骤
        scroll_area.setWidget(self.scroll_content)
        main_layout.addWidget(scroll_area)

        # 状态标签
        self.status_label = QLabel("正在加载公告...")
        status_font = QFont("Microsoft YaHei UI", 14)
        self.status_label.setFont(status_font)
        self.status_label.setStyleSheet("color: #cccccc; padding: 40px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.status_label)
        
    def _load_announcements(self):
        """加载公告数据"""
        if self.loader_thread and self.loader_thread.isRunning():
            return

        # 更新状态
        self.status_label.setText("正在加载公告...")
        self.status_label.show()

        # 清空现有公告
        self._clear_announcements()

        # 获取API URL
        api_url = self._get_api_url()

        # 创建加载线程
        self.loader_thread = AnnouncementLoader(api_url)
        self.loader_thread.announcements_loaded.connect(self._on_announcements_loaded)
        self.loader_thread.error_occurred.connect(self._on_load_error)
        self.loader_thread.start()

        if self.logger:
            self.logger.info("开始加载系统公告")
            
    def _clear_announcements(self):
        """清空公告列表 - 安全的清理方法"""
        # 安全地清理所有子控件
        while self.announcements_layout.count():
            child = self.announcements_layout.takeAt(0)
            if child.widget():
                widget = child.widget()
                widget.setParent(None)  # 先移除父级关系
                widget.deleteLater()    # 然后删除控件
            elif child.layout():
                # 如果是布局，递归清理
                self._clear_layout(child.layout())

    def _clear_layout(self, layout):
        """递归清理布局"""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.layout():
                self._clear_layout(child.layout())
                
    def _on_announcements_loaded(self, announcements):
        """公告加载完成"""
        self.announcements = announcements
        self._display_announcements()
        
        if self.logger:
            self.logger.info(f"公告加载完成，共 {len(announcements)} 条")
            
    def _on_load_error(self, error_message):
        """公告加载失败"""
        # 确保状态标签可见
        self.status_label.setText(f"加载失败: {error_message}")
        self.status_label.show()
        
        # 更新统计信息
        self.stats_label.setText("加载失败 | 点击刷新按钮重试")
        
        # 添加重试按钮（如果不存在）
        if not hasattr(self, 'retry_button'):
            self.retry_button = QPushButton("🔄 重试")
            self.retry_button.setStyleSheet("""
                QPushButton {
                    background-color: #2d2d2d;
                    border: 1px solid #00bcd4;
                    border-radius: 6px;
                    padding: 8px 16px;
                    color: #ffffff;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #3d3d3d;
                    border-color: #00bcd4;
                }
            """)
            self.retry_button.clicked.connect(self._load_announcements)
            
            # 添加到布局中（添加到状态标签下方）
            parent_layout = self.status_label.parent().layout()
            parent_layout.addWidget(self.retry_button, alignment=Qt.AlignCenter)
        else:
            # 确保按钮可见
            self.retry_button.show()
        
        # 记录日志
        if self.logger:
            self.logger.error(f"公告加载失败: {error_message}")
            
        # 清空现有公告
        self._clear_announcements()
        
        # 显示错误提示
        error_widget = QWidget()
        error_layout = QVBoxLayout(error_widget)
        error_layout.setAlignment(Qt.AlignCenter)
        
        # 错误图标
        error_icon = QLabel("⚠️")
        error_icon.setFont(QFont("Segoe UI Emoji", 48))
        error_icon.setAlignment(Qt.AlignCenter)
        error_layout.addWidget(error_icon)
        
        # 错误标题
        error_title = QLabel("无法连接到服务器")
        error_title.setFont(QFont("Microsoft YaHei UI", 16, QFont.Bold))
        error_title.setStyleSheet("color: #ff9800;")
        error_title.setAlignment(Qt.AlignCenter)
        error_layout.addWidget(error_title)
        
        # 错误详情
        error_details = QLabel(f"详细信息: {error_message}")
        error_details.setFont(QFont("Microsoft YaHei UI", 10))
        error_details.setStyleSheet("color: #cccccc;")
        error_details.setAlignment(Qt.AlignCenter)
        error_details.setWordWrap(True)
        error_layout.addWidget(error_details)
        
        # 建议
        suggestions = QLabel("请检查网络连接或联系管理员")
        suggestions.setFont(QFont("Microsoft YaHei UI", 12))
        suggestions.setStyleSheet("color: #ffffff;")
        suggestions.setAlignment(Qt.AlignCenter)
        error_layout.addWidget(suggestions)
        
        # 添加到公告布局
        self.announcements_layout.addWidget(error_widget)

    def _display_announcements(self):
        """显示公告列表 - 按照PyQt布局最佳实践"""
        # 隐藏状态标签
        self.status_label.hide()

        # 清空现有公告
        self._clear_announcements()

        if not self.announcements:
            # 显示空状态
            empty_label = QLabel("暂无公告")
            empty_font = QFont("Microsoft YaHei UI", 14)
            empty_label.setFont(empty_font)
            empty_label.setStyleSheet("color: #cccccc; padding: 40px;")
            empty_label.setAlignment(Qt.AlignCenter)
            empty_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            self.announcements_layout.addWidget(empty_label)
        else:
            # 显示公告卡片
            for announcement in self.announcements:
                card = AnnouncementCard(announcement)
                card.clicked.connect(self._show_announcement_detail)
                self.announcements_layout.addWidget(card)

        # 添加弹性空间 - 确保内容顶部对齐
        self.announcements_layout.addStretch()

        # 更新统计信息
        self.stats_label.setText(f"共 {len(self.announcements)} 条公告")

        # 强制更新滚动区域 - 关键步骤
        self.scroll_content.updateGeometry()
        self.scroll_content.update()
        
    def _show_announcement_detail(self, announcement_data):
        """显示公告详情"""
        try:
            dialog = AnnouncementDetailDialog(announcement_data, self)
            dialog.exec_()
            
            # 标记公告为已读（增加点击次数）
            self._mark_announcement_read(announcement_data.get('id'))
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"显示公告详情失败: {e}")
                
    def _mark_announcement_read(self, announcement_id):
        """标记公告为已读"""
        if not announcement_id:
            return
            
        try:
            # 获取API URL
            api_url = self._get_api_url()
            
            # 发送已读标记请求
            data = {
                'action': 'mark_announcement_read',
                'announcement_id': announcement_id
            }
            
            response = requests.post(api_url, json=data, timeout=5)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    if self.logger:
                        self.logger.info(f"公告已读标记成功: {announcement_id}")
                        
        except Exception as e:
            if self.logger:
                self.logger.warning(f"标记公告已读失败: {e}")

    def _get_api_url(self):
        """获取API URL
        优先从安全管理器获取，然后是配置管理器，最后是默认值
        """
        # 默认API地址
        default_api_url = "http://127.0.0.1:777/api.php"
        
        try:
            # 首先尝试读取专用的公告配置文件
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR', '')
            if config_dir:
                announcements_config_path = os.path.join(config_dir, 'announcements_config.json')
                if os.path.exists(announcements_config_path):
                    try:
                        import json
                        with open(announcements_config_path, 'r', encoding='utf-8') as f:
                            announcements_config = json.load(f)
                            
                        api_endpoint = announcements_config.get('api_endpoint')
                        if api_endpoint:
                            if self.logger:
                                self.logger.info(f"从公告专用配置文件获取API地址: {api_endpoint}")
                            print(f"📢 公告页面: 使用专用配置的API地址 - {api_endpoint}")
                            return api_endpoint
                    except Exception as e:
                        if self.logger:
                            self.logger.error(f"读取公告配置文件失败: {e}")
                        print(f"⚠️ 公告页面: 读取专用配置失败 - {e}")
            
            # 优先从安全管理器获取服务器地址
            if self.main_window and hasattr(self.main_window, 'security_manager') and self.main_window.security_manager:
                server_url = self.main_window.security_manager.get_server_address()
                if server_url:
                    # 确保URL以api.php结尾
                    if not server_url.endswith('/api.php'):
                        if server_url.endswith('/'):
                            server_url += 'api.php'
                        else:
                            server_url += '/api.php'
                    
                    # 记录日志
                    if self.logger:
                        self.logger.info(f"从安全管理器获取服务器地址: {server_url}")
                    print(f"📢 公告页面: 使用安全管理器的API地址 - {server_url}")
                    return server_url
            
            # 然后从配置管理器获取
            if self.main_window and hasattr(self.main_window, 'config_manager'):
                auth_server_url = self.main_window.config_manager.get('auth_server_url')
                if auth_server_url:
                    # 确保URL以api.php结尾
                    if not auth_server_url.endswith('/api.php'):
                        if auth_server_url.endswith('/'):
                            auth_server_url += 'api.php'
                        else:
                            auth_server_url += '/api.php'
                    
                    # 记录日志
                    if self.logger:
                        self.logger.info(f"从配置管理器获取服务器地址: {auth_server_url}")
                    print(f"📢 公告页面: 使用配置管理器的API地址 - {auth_server_url}")
                    return auth_server_url
        
        except Exception as e:
            if self.logger:
                self.logger.error(f"获取API URL失败: {e}")
            print(f"⚠️ 公告页面: 获取API URL失败 - {e}")
        
        # 返回默认值
        if self.logger:
            self.logger.warning(f"使用默认API URL: {default_api_url}")
        print(f"📢 公告页面: 使用默认API地址 - {default_api_url}")
        return default_api_url
