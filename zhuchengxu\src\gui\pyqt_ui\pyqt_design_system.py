#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt版本的2025年现代化设计系统
Modern 2025 Design System for PyQt - 定义所有UI组件的颜色、字体、间距等设计规范
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QFont, QPalette, QLinearGradient


class PyQtDesignSystem:
    """PyQt版本的2025年现代化设计系统"""
    
    # 🎨 颜色系统 - 2025年流行色彩
    COLORS = {
        # 主色调 - 深色主题
        'bg_primary': QColor('#0a0a0f'),      # 主背景 - 深空蓝
        'bg_secondary': QColor('#1a1a2e'),    # 次背景 - 深紫蓝
        'bg_tertiary': QColor('#16213e'),     # 第三背景 - 深蓝
        
        # 文本颜色
        'text_primary': QColor('#ffffff'),     # 主文本 - 纯白
        'text_secondary': QColor('#b8c5d6'),  # 次文本 - 浅蓝灰
        'text_tertiary': QColor('#8892a6'),   # 第三文本 - 中蓝灰
        'text_disabled': QColor('#4a5568'),   # 禁用文本 - 深灰

        # 边框颜色
        'border': QColor('#374151'),          # 边框 - 深灰蓝
        'border_light': QColor('#4b5563'),    # 浅边框 - 中灰蓝
        'border_dark': QColor('#1f2937'),     # 深边框 - 深灰
        
        # 霓虹色彩 - 2025年流行霓虹色
        'neon_cyan': QColor('#00d4ff'),       # 霓虹青 - 主要强调色
        'neon_purple': QColor('#a855f7'),     # 霓虹紫 - 次要强调色
        'neon_pink': QColor('#ec4899'),       # 霓虹粉 - 警告/错误色
        'neon_red': QColor('#ff4757'),        # 霓虹红 - 错误/删除色
        'neon_green': QColor('#10b981'),      # 霓虹绿 - 成功色
        'neon_orange': QColor('#f59e0b'),     # 霓虹橙 - 提醒色
        'neon_blue': QColor('#3b82f6'),       # 霓虹蓝 - 信息色
        'neon_yellow': QColor('#fbbf24'),     # 霓虹黄 - 高亮色
        
        # 玻璃拟态效果
        'glass_bg': QColor(255, 255, 255, 13),      # 玻璃背景 - 5%透明度
        'glass_border': QColor(255, 255, 255, 26),  # 玻璃边框 - 10%透明度
        'glass_shadow': QColor(0, 0, 0, 77),        # 玻璃阴影 - 30%透明度
        
        # 渐变色彩
        'gradient_primary': [QColor('#667eea'), QColor('#764ba2')],    # 主渐变
        'gradient_secondary': [QColor('#f093fb'), QColor('#f5576c')],  # 次渐变
        'gradient_accent': [QColor('#4facfe'), QColor('#00f2fe')],     # 强调渐变
        
        # 状态颜色
        'success': QColor('#10b981'),         # 成功
        'warning': QColor('#f59e0b'),         # 警告
        'error': QColor('#ef4444'),           # 错误
        'info': QColor('#3b82f6'),            # 信息
    }
    
    # 🔤 字体系统 - 大气风格，更大更有气势
    FONTS = {
        'primary': QFont('Microsoft YaHei UI', 16),      # 主字体 - 大气版本
        'secondary': QFont('Segoe UI', 16),              # 次字体 - 大气版本
        'monospace': QFont('Consolas', 14),              # 等宽字体 - 大气版本
        'display': QFont('Microsoft YaHei UI', 42, QFont.Bold),  # 显示字体 - 超大气势
        'heading_xl': QFont('Microsoft YaHei UI', 32, QFont.Bold),  # 超大标题 - 威严庄重
        'heading_lg': QFont('Microsoft YaHei UI', 26, QFont.Bold),  # 大标题 - 醒目突出
        'heading_md': QFont('Microsoft YaHei UI', 22, QFont.Bold),  # 中标题 - 清晰有力
        'heading_sm': QFont('Microsoft YaHei UI', 18, QFont.Bold),  # 小标题 - 稳重大方
        'body_lg': QFont('Microsoft YaHei UI', 16),      # 大正文 - 易读舒适
        'body_md': QFont('Microsoft YaHei UI', 14),      # 中正文 - 标准大小
        'body_sm': QFont('Microsoft YaHei UI', 12),      # 小正文 - 精致细腻
        'caption': QFont('Microsoft YaHei UI', 11),      # 说明文字 - 简洁明快
    }
    
    # 📏 间距系统 (像素) - 大气风格，更宽敞的布局
    SPACING = {
        'xs': 10,     # 超小间距 - 大气版本
        'sm': 18,     # 小间距 - 大气版本
        'md': 30,     # 中间距 - 大气版本
        'lg': 45,     # 大间距 - 大气版本
        'xl': 60,     # 超大间距 - 大气版本
        '2xl': 80,    # 2倍超大间距 - 大气版本
        '3xl': 120,   # 3倍超大间距 - 大气版本
    }
    
    # 🔲 圆角系统 (像素)
    BORDER_RADIUS = {
        'none': 0,
        'sm': 4,
        'md': 8,
        'lg': 12,
        'xl': 16,
        '2xl': 24,
        'full': 9999,  # 完全圆角
    }
    
    # 🌫️ 阴影系统 (注意：QSS不支持box-shadow，这些值用于QGraphicsDropShadowEffect)
    SHADOWS = {
        'sm': {'blur': 2, 'offset': (0, 1), 'color': QColor(0, 0, 0, 13)},
        'md': {'blur': 6, 'offset': (0, 4), 'color': QColor(0, 0, 0, 26)},
        'lg': {'blur': 15, 'offset': (0, 10), 'color': QColor(0, 0, 0, 26)},
        'xl': {'blur': 25, 'offset': (0, 20), 'color': QColor(0, 0, 0, 26)},
        '2xl': {'blur': 50, 'offset': (0, 25), 'color': QColor(0, 0, 0, 64)},
        'neon': {'blur': 20, 'offset': (0, 0), 'color': QColor(0, 212, 255, 128)},
    }
    
    # 🎨 组件特定样式
    COMPONENT_STYLES = {
        'button': {
            'height': 40,
            'padding_x': 16,
            'padding_y': 8,
            'border_radius': BORDER_RADIUS['md'],
        },
        'input': {
            'height': 40,
            'padding_x': 12,
            'padding_y': 8,
            'border_radius': BORDER_RADIUS['sm'],
        },
        'card': {
            'padding': SPACING['lg'],
            'border_radius': BORDER_RADIUS['lg'],
        },
        'modal': {
            'padding': SPACING['xl'],
            'border_radius': BORDER_RADIUS['xl'],
        },
    }
    
    @classmethod
    def get_color(cls, color_name: str, fallback: QColor = None) -> QColor:
        """
        获取颜色值
        
        Args:
            color_name: 颜色名称
            fallback: 备用颜色
            
        Returns:
            QColor对象
        """
        if fallback is None:
            fallback = QColor('#ffffff')
        return cls.COLORS.get(color_name, fallback)
    
    @classmethod
    def get_font(cls, font_name: str) -> QFont:
        """
        获取字体
        
        Args:
            font_name: 字体名称
            
        Returns:
            QFont对象
        """
        return cls.FONTS.get(font_name, cls.FONTS['primary'])
    
    @classmethod
    def get_spacing(cls, size: str) -> int:
        """
        获取间距值
        
        Args:
            size: 间距大小
            
        Returns:
            间距像素值
        """
        return cls.SPACING.get(size, cls.SPACING['md'])
    
    @classmethod
    def create_gradient(cls, gradient_name: str) -> QLinearGradient:
        """
        创建渐变
        
        Args:
            gradient_name: 渐变名称
            
        Returns:
            QLinearGradient对象
        """
        colors = cls.COLORS.get(f'gradient_{gradient_name}', cls.COLORS['gradient_primary'])
        gradient = QLinearGradient(0, 0, 1, 1)
        gradient.setCoordinateMode(QLinearGradient.ObjectBoundingMode)
        gradient.setColorAt(0, colors[0])
        gradient.setColorAt(1, colors[1])
        return gradient
    
    @classmethod
    def create_dark_palette(cls) -> QPalette:
        """
        创建深色主题调色板
        
        Returns:
            QPalette对象
        """
        palette = QPalette()
        
        # 窗口背景
        palette.setColor(QPalette.Window, cls.COLORS['bg_primary'])
        palette.setColor(QPalette.WindowText, cls.COLORS['text_primary'])
        
        # 基础背景
        palette.setColor(QPalette.Base, cls.COLORS['bg_secondary'])
        palette.setColor(QPalette.AlternateBase, cls.COLORS['bg_tertiary'])
        
        # 文本
        palette.setColor(QPalette.Text, cls.COLORS['text_primary'])
        palette.setColor(QPalette.BrightText, cls.COLORS['neon_cyan'])
        
        # 按钮
        palette.setColor(QPalette.Button, cls.COLORS['bg_secondary'])
        palette.setColor(QPalette.ButtonText, cls.COLORS['text_primary'])
        
        # 高亮
        palette.setColor(QPalette.Highlight, cls.COLORS['neon_cyan'])
        palette.setColor(QPalette.HighlightedText, cls.COLORS['bg_primary'])
        
        # 链接
        palette.setColor(QPalette.Link, cls.COLORS['neon_blue'])
        palette.setColor(QPalette.LinkVisited, cls.COLORS['neon_purple'])
        
        return palette

    @classmethod
    def create_shadow_effect(cls, shadow_name: str):
        """
        创建阴影效果

        Args:
            shadow_name: 阴影名称

        Returns:
            QGraphicsDropShadowEffect对象
        """
        from PyQt5.QtWidgets import QGraphicsDropShadowEffect

        shadow_config = cls.SHADOWS.get(shadow_name, cls.SHADOWS['md'])

        shadow_effect = QGraphicsDropShadowEffect()
        shadow_effect.setBlurRadius(shadow_config['blur'])
        shadow_effect.setColor(shadow_config['color'])
        shadow_effect.setOffset(shadow_config['offset'][0], shadow_config['offset'][1])

        return shadow_effect


# 创建全局设计系统实例
DS = PyQtDesignSystem()
