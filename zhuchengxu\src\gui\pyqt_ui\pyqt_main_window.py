#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt版本的现代化主窗口 - 重构版
简洁、高效、功能完整的主界面
"""

import sys
import os
import webbrowser
from datetime import datetime
import gc

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout,
                             QVBoxLayout, QLabel, QPushButton, QSizePolicy, QGridLayout, QFrame, QScrollArea,
                             QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QThread
from PyQt5.QtGui import QFont

# 导入设计系统和组件
from .pyqt_design_system import DS
from .pyqt_components import GlassWidget, NeonButton, ModernSidebar
# 公告页面将在需要时动态导入

# 导入工具模块
try:
    from src.utils.config_manager import ConfigManager
    from src.utils.logger import get_logger
except ImportError:
    try:
        from utils.config_manager import ConfigManager
        from utils.logger import get_logger
    except ImportError:
        # 简化版配置管理器
        class ConfigManager:
            def __init__(self):
                self.config = {}
            def get(self, key, default=None):
                return self.config.get(key, default)
            def set(self, key, value):
                self.config[key] = value

        # 简化版日志
        class Logger:
            def info(self, msg): print(f"INFO: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")

        def get_logger():
            return Logger()


class PyQtMainWindow(QMainWindow):
    """PyQt版本的现代化主窗口 - 重构版"""

    def __init__(self):
        # 初始化配置和日志（在创建QWidget之前）
        super().__init__()
        
        # 初始化配置管理器
        try:
            self.config_manager = ConfigManager()
        except Exception as e:
            print(f"⚠️ 配置管理器初始化失败: {e}")
        
        # 设置窗口实例引用
        self._instance_reference = self
        
        # 定义简单日志类，用作备用
        class SimpleLogger:
            def __init__(self):
                pass
                
            def info(self, msg):
                print(f"INFO: {msg}")
                
            def warning(self, msg):
                print(f"WARNING: {msg}")
                
            def error(self, msg):
                print(f"ERROR: {msg}")
                
            def debug(self, msg):
                print(f"DEBUG: {msg}")
                
            def setLevel(self, level):
                print(f"Set log level to: {level}")
        
        # 初始化日志记录器
        try:
            from src.utils.logger import get_logger
            import os
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'logs')
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, 'pyqt_main_window.log')
            self.logger = get_logger("PyQtMainWindow", log_file)
            # 启用更详细的性能日志
            self.logger.setLevel('DEBUG')  # 设置为DEBUG级别以记录性能信息
            print("📝 日志系统已初始化，开启性能监控")
        except Exception as e:
            print(f"⚠️ 日志系统初始化失败: {e}")
            self.logger = SimpleLogger()
            print("⚠️ 使用简易日志记录器")
        
        # 设置安全管理器    
        self.security_manager = None

        # 先初始化应用（确保QApplication存在）
        self._setup_application()

        # 设置窗口基础属性
        self.setAttribute(Qt.WA_DeleteOnClose)
        self.setWindowTitle("续杯工具")
        self.setMinimumSize(1280, 720)
        
        # 记录当前页面ID
        self.current_page_id = None
        self.preloaded_modules = {}
        self.precreated_pages = {}
        
        # 设置异常处理器
        self._setup_exception_handler()

        # 设置窗口
        self._setup_window()

        # 更高效地加载重型组件
        QTimer.singleShot(10, self._init_heavy_components)
        
        # 创建UI组件
        self._create_ui()

        # 设置窗口过渡动画
        self._setup_window_animations()
        
        # 预加载性能优化
        QTimer.singleShot(100, self._preload_optimization)
        
        # 初始化定时器
        self._setup_timers()
        
        # 居中显示窗口
        self._center_window()
        
        self.logger.info("✅ 主窗口初始化完成")
        
    def _force_refresh_home_page(self):
        """强制刷新首页实例，确保使用最新设计"""
        try:
            self.logger.info("🔄 强制刷新首页实例...")
            
            # 删除预创建的首页实例
            if hasattr(self, 'precreated_pages') and 'home' in self.precreated_pages:
                del self.precreated_pages['home']
                
            # 从缓存中删除首页
            if hasattr(self, 'cache_manager') and self.cache_manager:
                try:
                    self.cache_manager.remove_page_cache('home')
                except Exception as e:
                    self.logger.warning(f"清除首页缓存失败: {e}")
                    
            # 创建新的首页实例
            home_page = self._create_home_content()
            
            # 添加到预创建页面
            if hasattr(self, 'precreated_pages'):
                self.precreated_pages['home'] = home_page
                self.logger.info("✅ 已更新预创建的首页实例")
                
            # 添加到缓存
            if hasattr(self, 'cache_manager') and self.cache_manager:
                import time
                self.cache_manager.cache_page('home', home_page, {
                    'creation_time': time.time(),
                    'page_type': 'home',
                    'is_precreated': True
                })
                self.logger.info("✅ 已更新首页缓存")
                
        except Exception as e:
            self.logger.error(f"❌ 强制刷新首页失败: {e}")

    def _preload_optimization(self):
        """预加载性能优化 - 在UI显示后立即启动"""
        try:
            self.logger.info("⚡ 启动预加载性能优化...")
            
            # 确保缓存系统已初始化
            if not hasattr(self, 'cache_manager') or self.cache_manager is None:
                from src.utils.smart_cache_manager import get_cache_manager
                self.cache_manager = get_cache_manager()
                self.logger.info("✅ 智能缓存系统已初始化")

            # 强制刷新首页实例，确保使用最新设计
            self._force_refresh_home_page()
            
            # 启动模块预加载
            QTimer.singleShot(500, self._preload_page_modules)
            
            # 预创建页面实例
            QTimer.singleShot(1000, self._precreate_page_instances)
            
            # 后台服务初始化
            QTimer.singleShot(1500, self._init_background_services)

            # 多进程初始化（更长延迟）
            QTimer.singleShot(3000, self._init_multi_process)

        except Exception as e:
            self.logger.error(f"❌ 预加载优化失败: {e}")

    def _setup_exception_handler(self):
        """设置全局异常处理器 - 防止程序崩溃"""
        import sys

        def handle_exception(exc_type, exc_value, exc_traceback):
            """全局异常处理器"""
            if issubclass(exc_type, KeyboardInterrupt):
                # 允许Ctrl+C正常退出
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            # 记录异常
            import traceback
            error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            self.logger.error(f"❌ 未处理的异常: {error_msg}")

            # 显示用户友好的错误消息
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(
                    self,
                    "程序错误",
                    f"程序遇到了一个错误，但会继续运行。\n\n错误类型: {exc_type.__name__}\n错误信息: {str(exc_value)}\n\n详细信息已记录到日志文件。"
                )
            except:
                print(f"❌ 严重错误: {exc_type.__name__}: {exc_value}")

        # 设置异常处理器
        sys.excepthook = handle_exception
        self.logger.info("✅ 全局异常处理器已设置")

    def _init_heavy_components(self):
        """延迟初始化重型组件 - 避免阻塞界面显示"""
        try:
            self.logger.info("🔄 开始加载重型组件...")

            # 确保缓存系统已初始化
            if not hasattr(self, 'cache_manager') or self.cache_manager is None:
            from src.utils.smart_cache_manager import get_cache_manager
            self.cache_manager = get_cache_manager()
                self.logger.info("✅ 智能缓存系统已初始化")

            # UI性能优化器
            from src.utils.ui_performance_optimizer import get_ui_optimizer
            self.ui_optimizer = get_ui_optimizer()

            # 初始化性能监控
            try:
                from src.utils.performance_monitor import PerformanceMonitor
                self.performance_monitor = PerformanceMonitor()
                self.logger.info("✅ 性能监控系统已启动")
            except Exception as monitor_error:
                self.logger.warning(f"⚠️ 性能监控系统初始化失败: {monitor_error}")

            self.logger.info("✅ 重型组件加载完成")

        except Exception as e:
            self.logger.warning(f"⚠️ 重型组件加载失败: {e}")
            # 保持默认值，确保程序能继续运行

    def _init_background_services(self):
        """延迟初始化后台服务 - 在界面显示后进行"""
        try:
            print("🔄 开始初始化后台服务...")

            # 启动性能优化定时器
            self._setup_performance_timers()

            # 启动异步预加载（在后台进行）
            QTimer.singleShot(1000, self._start_async_preloading)

            # 预加载页面模块（关键优化）
            QTimer.singleShot(2000, self._preload_page_modules)

            # 初始化多进程管理
            QTimer.singleShot(3000, self._init_multi_process)

            print("✅ 后台服务初始化完成")

        except Exception as e:
            print(f"⚠️ 后台服务初始化失败: {e}")

    def _start_async_preloading(self):
        """启动异步预加载"""
        try:
            # 显示预加载状态
            self.preload_status = "🔄 后台预加载中..."

            # 更新状态栏
            if hasattr(self, 'preload_label'):
                self.preload_label.setText("🔄 后台预加载中...")
            if hasattr(self, 'status_label'):
                self.status_label.setText("✅ 界面就绪")

            # 使用QTimer延迟启动预加载，避免阻塞界面显示
            QTimer.singleShot(100, self._do_async_preloading)

            print("🚀 异步预加载已启动，界面优先显示")
        except Exception as e:
            print(f"⚠️ 异步预加载启动失败: {e}")

    def _do_async_preloading(self):
        """执行异步预加载"""
        try:
            import threading

            def preload_worker():
                try:
                    print("🔄 开始后台预加载...")

                    # 导入并执行预加载
                    from src.utils.memory_preloader import preload_to_memory
                    memory_preloader = preload_to_memory()

                    # 预加载完成后更新状态
                    QTimer.singleShot(0, self._on_preload_complete)

                except Exception as e:
                    print(f"❌ 后台预加载失败: {e}")
                    QTimer.singleShot(0, lambda: self._on_preload_error(str(e)))

            # 在后台线程中执行预加载
            preload_thread = threading.Thread(target=preload_worker, daemon=True)
            preload_thread.start()

        except Exception as e:
            print(f"❌ 异步预加载执行失败: {e}")

    def _on_preload_complete(self):
        """预加载完成回调"""
        try:
            self.preload_status = "✅ 预加载完成"
            print("🎉 后台预加载完成！程序性能已优化")

            # 更新状态栏（如果有的话）
            if hasattr(self, 'status_label'):
                self.status_label.setText("✅ 预加载完成 | 高性能模式")
            if hasattr(self, 'preload_label'):
                self.preload_label.setText("✅ 预加载完成")

        except Exception as e:
            print(f"⚠️ 预加载完成回调失败: {e}")

    def _on_preload_error(self, error_msg):
        """预加载错误回调"""
        try:
            self.preload_status = f"⚠️ 预加载失败: {error_msg}"
            print(f"⚠️ 后台预加载失败: {error_msg}")

            # 更新状态栏
            if hasattr(self, 'status_label'):
                self.status_label.setText("⚠️ 预加载失败 | 标准模式")
            if hasattr(self, 'preload_label'):
                self.preload_label.setText("⚠️ 预加载失败")

        except Exception as e:
            print(f"⚠️ 预加载错误回调失败: {e}")

    def _preload_page_modules(self):
        """预加载所有页面模块 - 关键性能优化"""
        try:
            print("🚀 开始预加载页面模块...")

            # 预加载页面模块类
            self.preloaded_modules = {}

            # 邮箱页面
            try:
                from src.gui.pyqt_ui.pyqt_pages.pyqt_mailbox_page import PyQtMailboxPage
                self.preloaded_modules['PyQtMailboxPage'] = PyQtMailboxPage
                print("✅ 预加载邮箱页面模块")
            except Exception as e:
                print(f"⚠️ 预加载邮箱页面模块失败: {e}")

            # VSCode页面
            try:
                from src.gui.pyqt_ui.pyqt_pages.pyqt_vscode_page import PyQtVSCodePage
                self.preloaded_modules['PyQtVSCodePage'] = PyQtVSCodePage
                print("✅ 预加载VSCode页面模块")
            except Exception as e:
                print(f"⚠️ 预加载VSCode页面模块失败: {e}")

            # 配置页面
            try:
                from src.gui.pyqt_ui.pyqt_pages.pyqt_config_page import PyQtConfigPage
                self.preloaded_modules['PyQtConfigPage'] = PyQtConfigPage
                print("✅ 预加载配置页面模块")
            except Exception as e:
                print(f"⚠️ 预加载配置页面模块失败: {e}")
                
            # 公告页面
            try:
                from src.gui.pyqt_ui.pyqt_pages.pyqt_announcements_page import PyQtAnnouncementsPage
                self.preloaded_modules['PyQtAnnouncementsPage'] = PyQtAnnouncementsPage
                print("✅ 预加载公告页面模块")
            except Exception as e:
                print(f"⚠️ 预加载公告页面模块失败: {e}")
                
            # 论坛页面
            try:
                try:
                    # 优先尝试加载重构版
                    from src.gui.pyqt_ui.pyqt_pages.forum_refactored import RefactoredForumPage
                    self.preloaded_modules['RefactoredForumPage'] = RefactoredForumPage
                    print("✅ 预加载重构论坛页面模块")
                except ImportError:
                    # 降级到原版
                    from src.gui.pyqt_ui.pyqt_pages.pyqt_forum_page import PyQtForumPage
                    self.preloaded_modules['PyQtForumPage'] = PyQtForumPage
                    print("✅ 预加载原版论坛页面模块")
            except Exception as e:
                print(f"⚠️ 预加载论坛页面模块失败: {e}")

        except Exception as e:
            print(f"⚠️ 预加载页面模块失败: {e}")
            import traceback
            print(traceback.format_exc())

    def _precreate_page_instances(self):
        """预创建页面实例 - 终极性能优化"""
        try:
            print("🚀 开始预创建页面实例...")

            # 预创建的页面实例
            if not hasattr(self, 'precreated_pages'):
            self.precreated_pages = {}

            # 定义需要预创建的页面列表 - 增加更多常用页面
            pages_to_create = [
                ('vscode', 'PyQtVSCodePage'),
                ('config', 'PyQtConfigPage'),
                # 不再在这里创建首页，已经在_force_refresh_home_page中创建
                ('mailbox', 'PyQtMailboxPage'),
                ('announcements', 'PyQtAnnouncementsPage'),
                ('forum', 'RefactoredForumPage')
                # 不创建account_database页面，它需要特殊处理
            ]

            # 遍历预创建页面
            for page_id, class_name in pages_to_create:
                try:
                    # 跳过已创建的页面
                    if page_id in self.precreated_pages:
                        print(f"⏩ 跳过已创建的页面: {page_id}")
                        continue
                        
                    # 跳过账号库页面
                    if page_id == 'account_database':
                        print("⏩ 跳过账号库页面 - 需要特殊处理")
                        continue
                        
                    # 获取预加载的模块类
                    if class_name and class_name in self.preloaded_modules:
                        PageClass = self.preloaded_modules.get(class_name)
                        if PageClass:
                            # 创建页面实例
                            if page_id == 'mailbox':
                                # 邮箱页面需要特殊处理
                                config_manager = getattr(self, 'config_manager', None)
                                if hasattr(self, 'security_manager') and self.security_manager:
                                    config_manager = getattr(self.security_manager, 'config_manager', None)
                                page_instance = PageClass(config_manager=config_manager)
                            else:
                                # 其他页面标准初始化
                                page_instance = PageClass()
                                
                            # 存储到缓存
                            self.precreated_pages[page_id] = page_instance
                            print(f"✅ 预创建{page_id}页面实例")
                            
                            # 同时添加到缓存管理器
                            import time
                            if hasattr(self, 'cache_manager'):
                                self.cache_manager.cache_page(page_id, page_instance, {
                                    'creation_time': time.time(),
                                    'page_type': page_id,
                                    'is_precreated': True
                                })
                                print(f"✅ {page_id}页面已添加到缓存管理器")
            except Exception as e:
                    print(f"⚠️ 预创建{page_id}页面失败: {e}")

            print(f"🎉 页面实例预创建完成，共 {len(self.precreated_pages)} 个页面")

        except Exception as e:
            print(f"⚠️ 预创建页面实例失败: {e}")
            import traceback
            print(traceback.format_exc())

    def _init_multi_process(self):
        """初始化线程池管理"""
        try:
            print("🚀 初始化线程池管理...")

            from src.utils.thread_pool_manager import init_page_thread_pools
            init_page_thread_pools()

            print("✅ 线程池管理初始化完成")

        except Exception as e:
            print(f"⚠️ 线程池管理初始化失败: {e}")

    def _setup_performance_timers(self):
        """设置性能优化定时器"""
        try:
            from src.utils.performance_optimizer import get_optimizer, optimize_runtime
            self.optimizer = get_optimizer()

            # 时间更新定时器 - 进一步降低频率
            self.timer = QTimer()
            self.timer.timeout.connect(self._update_time)
            self.timer.start(60000)  # 60秒更新一次
            self._update_time()

            # 性能监控定时器 - 降低频率
            self.perf_timer = QTimer()
            self.perf_timer.timeout.connect(self._performance_check)
            self.perf_timer.start(120000)  # 2分钟检查一次

            print("✅ 性能优化定时器已启动")
        except Exception as e:
            print(f"⚠️ 性能优化定时器启动失败: {e}")
            # 降级到基础定时器
            self._setup_basic_timers()

    def _setup_basic_timers(self):
        """基础定时器（降级方案）"""
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_time)
        self.timer.start(60000)  # 60秒更新一次
        self._update_time()

    def _performance_check(self):
        """性能检查和优化"""
        try:
            from src.utils.performance_optimizer import optimize_runtime
            stats = optimize_runtime()

            # 获取UI性能统计
            ui_stats = self.ui_optimizer.get_performance_stats()
            cache_stats = self.cache_manager.get_cache_stats()

            # 更新状态栏性能信息
            if hasattr(self, 'memory_label') and "rss" in stats:
                memory_mb = int(stats["rss"])
                cpu_percent = int(stats.get("cpu_percent", 0))
                cache_count = cache_stats['cached_pages']
                self.memory_label.setText(f"💾 {memory_mb}MB | ⚡ {cpu_percent}% | 📦 {cache_count}")

            # 如果内存使用过高，清理缓存
            if stats and "rss" in stats and stats["rss"] > 200:  # 提高到200MB
                print(f"⚠️ 内存使用过高: {stats['rss']:.1f}MB，开始清理缓存")
                # 只清理部分缓存，不是全部
                if hasattr(self.cache_manager, '_force_cleanup'):
                    self.cache_manager._force_cleanup()

            # 检查缓存使用情况
            if cache_stats['usage_percent'] > 90:  # 提高到90%
                print(f"⚠️ 缓存使用率过高: {cache_stats['usage_percent']:.1f}%")
                if hasattr(self.cache_manager, '_force_cleanup'):
                    self.cache_manager._force_cleanup()

        except Exception as e:
            print(f"⚠️ 性能检查失败: {e}")

    def set_security_manager(self, security_manager):
        """设置安全管理器"""
        self.security_manager = security_manager
        self.logger.info("安全管理器已设置")

    def _setup_sidebar_menu(self):
        """设置侧边栏菜单"""
        menu_items = [
            ('home', '🏠', '首页', True),
            ('mailbox', '📧', '查看邮箱', False),
            ('vscode', '🛠️', '清理工具', False),
            ('forum', '💬', '论坛交流', False),
            ('config', '⚙️', '系统配置', False),
            ('announcements', '📢', '系统公告', False),
            ('account_database', '📊', '账号库', False),
        ]

        for item_id, icon, text, active in menu_items:
            self.sidebar.add_menu_item(item_id, icon, text, active)

    def _setup_application(self):
        """设置应用程序"""
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        app.setApplicationName("Augment续杯工具")
        app.setApplicationVersion("v2025")
        app.setOrganizationName("Augment Code")

        try:
            app.setPalette(DS.create_dark_palette())
            print(f"🎨 设置调色板成功")
            # 样式表将在窗口创建后设置
        except Exception as e:
            print(f"❌ 设置主题失败: {e}")
            import traceback
            traceback.print_exc()

        self.app = app

    def _setup_window(self):
        """设置窗口属性 - 响应式设计"""
        self.setWindowTitle("🚀 Augment续杯工具")

        # 现在可以安全地设置样式表
        try:
            global_style = self._get_global_stylesheet()
            print(f"🎨 设置窗口样式表...")
            self.app.setStyleSheet(global_style)
            print(f"✅ 窗口样式表设置成功")
        except Exception as e:
            print(f"❌ 设置窗口样式表失败: {e}")
            import traceback
            traceback.print_exc()

        # 响应式窗口尺寸设置
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget()
        screen_geometry = desktop.screenGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # 根据屏幕尺寸动态计算窗口大小
        if screen_width >= 1920:  # 大屏幕
            window_width = int(screen_width * 0.75)  # 75%屏幕宽度
            window_height = int(screen_height * 0.8)  # 80%屏幕高度
            min_width = 1400
            min_height = 900
        elif screen_width >= 1366:  # 中等屏幕
            window_width = int(screen_width * 0.85)  # 85%屏幕宽度
            window_height = int(screen_height * 0.85)  # 85%屏幕高度
            min_width = 1200
            min_height = 800
        else:  # 小屏幕
            window_width = int(screen_width * 0.95)  # 95%屏幕宽度
            window_height = int(screen_height * 0.9)   # 90%屏幕高度
            min_width = 1024
            min_height = 768

        # 设置窗口尺寸
        self.setMinimumSize(min_width, min_height)
        self.resize(window_width, window_height)
        self._center_window()

        self.logger.info(f"窗口尺寸设置: {window_width}x{window_height} (屏幕: {screen_width}x{screen_height})")

        # 简化的窗口样式
        try:
            window_style = f"""
                QMainWindow {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {DS.COLORS['bg_primary'].name()},
                        stop:1 {DS.COLORS['bg_secondary'].name()});
                }}
            """
            print(f"🎨 设置窗口样式表...")
            self.setStyleSheet(window_style)
            print(f"✅ 窗口样式表设置成功")
        except Exception as e:
            print(f"❌ 设置窗口样式失败: {e}")
            import traceback
            traceback.print_exc()

    def _center_window(self):
        """窗口居中"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def _create_ui(self):
        """创建用户界面 - 响应式设计"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 响应式边距
        main_layout = QVBoxLayout(central_widget)

        # 根据窗口大小动态调整边距
        window_width = self.width()
        if window_width >= 1600:
            margin = 15
            spacing = 15
        elif window_width >= 1200:
            margin = 10
            spacing = 10
        else:
            margin = 5
            spacing = 8

        main_layout.setContentsMargins(margin, margin, margin, margin)
        main_layout.setSpacing(spacing)

        # 顶部栏
        self._create_header(main_layout)

        # 主内容区域
        self._create_main_content(main_layout)

        # 底部状态栏已删除

        # QQ群信息已移除

    def _create_header(self, parent_layout):
        """创建顶部栏"""
        header = GlassWidget(glass_opacity=0.08, blur_radius=15)
        header.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)

        # 设置更小的高度
        header.setFixedHeight(35)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(15, 6, 15, 6)  # 减小内边距
        layout.setSpacing(12)

        # 左侧：退出登录按钮
        logout_btn = NeonButton("🚪 退出登录", 'red', 'outline')
        logout_btn.setFont(DS.get_font('body_sm'))
        logout_btn.setFixedHeight(28)
        logout_btn.setMinimumWidth(100)
        logout_btn.clicked.connect(self._logout)
        logout_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {DS.COLORS['neon_red'].name()};
                border: 1px solid {DS.COLORS['neon_red'].name()};
                border-radius: 6px;
                padding: 4px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DS.COLORS['neon_red'].darker(400).name()};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: {DS.COLORS['neon_red'].darker(300).name()};
            }}
        """)

        # 中间：留空，更简洁的设计

        # 右侧：时间显示
        self.time_label = QLabel()
        self.time_label.setFont(DS.get_font('body_sm'))
        self.time_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_green'].name()};
                background-color: {DS.COLORS['bg_tertiary'].name()};
                padding: 3px 8px;
                border-radius: 4px;
                font-weight: bold;
            }}
        """)

        layout.addWidget(logout_btn)
        layout.addStretch()
        layout.addWidget(self.time_label)

        parent_layout.addWidget(header)

    def _create_main_content(self, parent_layout):
        """创建主内容区域"""
        content_container = QHBoxLayout()
        content_container.setSpacing(15)

        # 左侧：侧边栏 - 缩短宽度
        self.sidebar = ModernSidebar(width=200)
        self.sidebar.item_clicked.connect(self._on_menu_clicked)
        self._setup_sidebar_menu()
        content_container.addWidget(self.sidebar)

        # 右侧：主要内容
        self.main_content = GlassWidget(glass_opacity=0.06, blur_radius=15)
        self.main_content.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        content_layout = QVBoxLayout(self.main_content)

        # 优化的响应式内容边距 - 最大化空间利用
        window_width = self.width()
        if window_width >= 1600:
            content_margin = 15  # 大幅减少边距
            content_spacing = 20
        elif window_width >= 1200:
            content_margin = 10  # 减少边距
            content_spacing = 15
        else:
            content_margin = 5   # 最小边距
            content_spacing = 10

        content_layout.setContentsMargins(content_margin, content_margin, content_margin, content_margin)
        content_layout.setSpacing(content_spacing)
        # 设置内容区域居中对齐，确保按钮完全居中
        content_layout.setAlignment(Qt.AlignCenter)

        # 主要内容显示区域 - 使用完整的首页内容
        home_content = self._create_home_content()
        content_layout.addWidget(home_content)

        content_container.addWidget(self.main_content, 1)
        parent_layout.addLayout(content_container)



    # 底部状态栏已删除

    # QQ卡片功能已移除

    # QQ卡片相关功能已移除

    def _setup_window_animations(self):
        """设置窗口动画"""
        # 窗口淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(800)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)

        # 启动淡入动画
        QTimer.singleShot(100, self._play_window_entrance)

    def _play_window_entrance(self):
        """播放窗口入场动画"""
        self.setWindowOpacity(0.0)
        self.fade_animation.start()

    def _logout(self):
        """退出登录 - 学习现代2025界面原理"""
        from .modern_logout_dialog import ModernLogoutDialog

        try:
            # 显示现代化退出确认对话框
            if ModernLogoutDialog.show_logout_dialog(self):
                self.logger.info("🚪 用户选择退出登录...")

                # 如果有安全管理器，执行登出操作（学习现代2025界面）
                if hasattr(self, 'security_manager') and self.security_manager:
                    try:
                        self.logger.info("🔄 正在执行登出操作...")

                        # 调用安全管理器的登出方法
                        logout_result = self.security_manager.logout()

                        if logout_result:
                            self.logger.info("✅ 服务器端登出成功")
                        else:
                            self.logger.warning("⚠️ 服务器端登出失败，但将继续清理本地数据")

                    except Exception as e:
                        self.logger.error(f"❌ 登出过程异常: {e}")
                else:
                    self.logger.warning("⚠️ 未找到安全管理器，仅清理本地数据")

                # 关闭程序并返回登录页面
                self.logger.info("🔄 正在关闭程序并返回登录页面...")
                self._on_closing()

            else:
                self.logger.info("❌ 用户取消退出登录")

        except Exception as e:
            self.logger.error(f"❌ 退出登录失败: {e}")
            # 即使出错也尝试关闭
            self._on_closing()

    def _on_closing(self):
        """窗口关闭事件 - VSCode启发的完整资源清理版本"""
        try:
            self.logger.info("🚪 程序正在关闭...")

            # 1. 优先保存重要状态
            if hasattr(self, 'config_manager'):
                try:
                    if hasattr(self.config_manager, 'save'):
                        self.logger.info("💾 保存配置...")
                        self.config_manager.save()
                    elif hasattr(self.config_manager, 'save_config'):
                        self.logger.info("💾 保存配置...")
                        self.config_manager.save_config()
                except Exception as e:
                    self.logger.warning(f"⚠️ 保存配置失败: {e}")

            # 2. 停止所有定时器
            self.logger.info("⏱️ 停止所有定时器...")
            all_timers = []
            if hasattr(self, 'timer'):
                all_timers.append(self.timer)
            if hasattr(self, 'system_timer'):
                all_timers.append(self.system_timer)
            if hasattr(self, 'performance_timer'):
                all_timers.append(self.performance_timer)
            
            for timer in all_timers:
                if hasattr(timer, 'stop'):
                    timer.stop()
            self.logger.info(f"✅ 已停止 {len(all_timers)} 个定时器")
            
            # 3. 页面特定清理（如果有当前页面）
            if hasattr(self, 'current_page_id') and self.current_page_id:
                self.logger.info(f"📄 清理当前页面: {self.current_page_id}")
                self._handle_page_leave()
                
            # 4. 使用VSCode风格彻底清理线程池
            self.logger.info("🧵 清理线程池...")
            try:
                from src.utils.thread_pool_manager import cleanup_thread_pools
                cleanup_thread_pools()
                self.logger.info("✅ 所有线程池已清理")
            except Exception as e:
                self.logger.warning(f"⚠️ 清理线程池失败: {e}")
                
            # 5. 释放预加载的内存资源
            self.logger.info("🧠 释放预加载内存资源...")
            try:
                from src.utils.memory_preloader import dispose_preloader_resources
                dispose_preloader_resources()
                self.logger.info("✅ 预加载内存资源已释放")
            except Exception as e:
                self.logger.warning(f"⚠️ 内存资源释放失败: {e}")
                
            # 6. 执行性能优化器内存释放
            self.logger.info("🚀 执行最终内存优化...")
            try:
                from src.utils.performance_optimizer import get_optimizer
                optimizer = get_optimizer()
                if optimizer:
                    optimizer.optimize_memory()
                    self.logger.info("✅ 最终内存优化完成")
            except Exception as e:
                self.logger.warning(f"⚠️ 最终内存优化失败: {e}")
            
            # 7. 强制垃圾回收
            self.logger.info("🗑️ 执行最终垃圾回收...")
            try:
                import gc
                collected = gc.collect()
                self.logger.info(f"✅ 垃圾回收完成，清理了 {collected} 个对象")
            except Exception as e:
                self.logger.warning(f"⚠️ 垃圾回收失败: {e}")
                
            # 8. 关闭窗口和退出
            self.logger.info("👋 窗口关闭完成，程序退出")
            self.close()

            # 9. 退出应用程序
            import sys
            sys.exit(0)

        except Exception as e:
            self.logger.error(f"❌ 关闭程序时出错: {e}")
            import sys
            sys.exit(0)

    def _cleanup_processes(self):
        """清理所有线程池"""
        try:
            from src.utils.thread_pool_manager import cleanup_thread_pools
            cleanup_thread_pools()
            print("✅ 所有线程池已清理")
        except Exception as e:
            print(f"⚠️ 清理线程池失败: {e}")

    def _setup_timers(self):
        """设置定时器 - 性能优化版本"""
        # 时间更新定时器 - 降低频率到5秒
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_time)
        self.timer.start(5000)  # 从1秒改为5秒
        self._update_time()  # 立即更新一次

        # 系统信息更新定时器已删除

    def _update_time(self):
        """更新时间显示"""
        now = datetime.now()
        time_str = now.strftime("%H:%M:%S")
        detail_time_str = now.strftime("%Y-%m-%d %H:%M:%S")

        if hasattr(self, 'time_label'):
            self.time_label.setText(f"🕒 {time_str}")
        if hasattr(self, 'detail_time_label'):
            self.detail_time_label.setText(f"📅 {detail_time_str}")

    def _update_system_info(self):
        """更新系统信息"""
        try:
            import psutil

            # 获取内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used = memory.used / (1024**3)  # GB
            memory_total = memory.total / (1024**3)  # GB

            # 获取CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)

            # 更新显示
            if hasattr(self, 'memory_info'):
                self.memory_info.setText(f"💾 内存: {memory_used:.1f}GB/{memory_total:.1f}GB ({memory_percent:.1f}%)")

            if hasattr(self, 'cpu_info'):
                self.cpu_info.setText(f"⚡ CPU: {cpu_percent:.1f}%")

            # 根据系统状态更新颜色
            if hasattr(self, 'system_status'):
                if memory_percent > 90 or cpu_percent > 90:
                    self.system_status.setText("🔴 系统负载较高")
                    self.system_status.setStyleSheet(f"color: {DS.COLORS['neon_red'].name()};")
                elif memory_percent > 70 or cpu_percent > 70:
                    self.system_status.setText("🟡 系统负载中等")
                    self.system_status.setStyleSheet(f"color: {DS.COLORS['neon_yellow'].name()};")
                else:
                    self.system_status.setText("🟢 系统运行正常")
                    self.system_status.setStyleSheet(f"color: {DS.COLORS['neon_green'].name()};")

        except ImportError:
            # 如果没有psutil，显示简单信息
            if hasattr(self, 'memory_info'):
                self.memory_info.setText("💾 内存: 信息不可用")
            if hasattr(self, 'cpu_info'):
                self.cpu_info.setText("⚡ CPU: 信息不可用")
        except Exception as e:
            self.logger.warning(f"更新系统信息失败: {e}")

    def _on_menu_clicked(self, item_id):
        """菜单点击处理 - 修复页面重叠问题"""
        self.logger.info(f"🔄 点击菜单: {item_id}")

        # 如果是当前页面，直接返回
        if self.current_page_id == item_id:
            self.logger.info(f"已在页面 {item_id}，跳过切换")
            return

        # 使用缓存机制快速切换页面
        self._switch_to_page(item_id)

        # 检查是否有页面重叠
        after_count = self.main_content.layout().count()
        if after_count > 1:
            self.logger.warning(f"⚠️ 检测到页面重叠！布局中有 {after_count} 个组件")
            # 强制清理多余组件
            self._force_clean_layout()

    def _switch_to_page(self, page_id):
        """高性能页面切换 - 使用缓存和异步加载"""
        try:
            self.logger.info(f"🔄 开始切换到页面: {page_id}")

            # 记录切换开始时间
            import time
            start_time = time.perf_counter()

            # 处理离开当前页面的清理工作
            self.logger.info("🧹 处理页面离开清理...")
            self._handle_page_leave()

            # 清除当前内容
            self.logger.info("🗑️ 清除当前内容...")
            self._clear_main_content()

            # 账号库页面特殊处理 - 直接创建新实例，不使用缓存
            if page_id == 'account_database':
                self.logger.info("⭐ 账号库页面特殊处理 - 直接创建新实例")
                
                # 从缓存系统中彻底移除账号库页面（如果存在）
                # 1. 从缓存管理器中删除
                if hasattr(self, 'cache_manager') and self.cache_manager:
                    try:
                        self.cache_manager.remove_page_cache('account_database')
                        self.logger.info("✅ 已从缓存中移除账号库页面")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 移除账号库页面缓存失败: {e}")
                
                # 2. 从预创建页面中删除
                if hasattr(self, 'precreated_pages') and 'account_database' in self.precreated_pages:
                    del self.precreated_pages['account_database']
                    self.logger.info("✅ 已从预创建页面中移除账号库页面")
                
                # 直接创建新的账号库页面
                page_widget = self._create_account_database_page_widget()
                if page_widget:
                    # 显示页面
                    self.main_content.layout().addWidget(page_widget)
                    page_widget.show()  # 确保页面可见
                    self.current_page_id = page_id
                    # 计算耗时
                    elapsed = (time.perf_counter() - start_time) * 1000
                    self.logger.info(f"✅ 账号库页面创建完成，耗时: {elapsed:.1f}ms")
                    return
                    
            # 1. 首先检查是否有预创建的页面实例
            if hasattr(self, 'precreated_pages') and page_id in self.precreated_pages:
                try:
                    precreated_page = self.precreated_pages[page_id]
                    # 验证预创建页面是否有效
                    _ = precreated_page.objectName()
                    
                    # 确保页面没有父组件
                    if precreated_page.parent():
                        precreated_page.setParent(None)
                        
                    # 显示预创建页面
                    self.main_content.layout().addWidget(precreated_page)
                    precreated_page.show()
                    self.current_page_id = page_id
                    
                    # 如果是邮箱页面，恢复自动刷新
                    if page_id == 'mailbox' and hasattr(precreated_page, 'resume_auto_refresh'):
                        precreated_page.resume_auto_refresh()
                        
                    # 计算耗时
                    elapsed = (time.perf_counter() - start_time) * 1000
                    self.logger.info(f"⚡ 从预创建实例加载页面 {page_id}，超快速切换耗时: {elapsed:.1f}ms")
                    return
                except (RuntimeError, AttributeError) as e:
                    # 预创建页面已失效，从字典中移除
                    self.logger.warning(f"⚠️ 预创建页面 {page_id} 已失效: {e}，将使用缓存")
                    del self.precreated_pages[page_id]

            # 2. 其次检查智能缓存
            cached_page = None
            if self.cache_manager:
                try:
                    cached_page = self.cache_manager.get_cached_page(page_id)
                except Exception as e:
                    self.logger.warning(f"⚠️ 缓存检查失败: {e}")
                    cached_page = None

            if cached_page:
                try:
                    # 验证缓存页面是否仍然有效
                    _ = cached_page.objectName()

                    # 确保缓存页面没有父组件
                    if cached_page.parent():
                        cached_page.setParent(None)

                    # 从缓存加载页面
                    self.main_content.layout().addWidget(cached_page)
                    cached_page.show()  # 确保页面可见
                    self.current_page_id = page_id

                    # 如果是邮箱页面，恢复自动刷新
                    if page_id == 'mailbox' and hasattr(cached_page, 'resume_auto_refresh'):
                        cached_page.resume_auto_refresh()

                    elapsed = (time.perf_counter() - start_time) * 1000
                    self.logger.info(f"✅ 从智能缓存加载页面 {page_id}，耗时: {elapsed:.1f}ms")
                    return

                except RuntimeError as e:
                    # 缓存的页面已被删除，清理缓存并重新创建
                    self.logger.warning(f"⚠️ 缓存页面 {page_id} 已失效: {e}")
                    if self.cache_manager:
                        try:
                            self.cache_manager.remove_page_cache(page_id)
                        except Exception as cache_error:
                            self.logger.warning(f"⚠️ 清理缓存失败: {cache_error}")

            # 3. 异步创建新页面
            self._create_page_async(page_id, start_time)

        except Exception as e:
            self.logger.error(f"❌ 页面切换失败: {e}")
            self._show_error_page(f"页面切换失败: {e}")

    def _handle_page_leave(self):
        """处理离开当前页面的清理工作 - VSCode启发的高效内存管理版本"""
        try:
            # 获取当前显示的页面
            current_widget = None
            if self.main_content.layout().count() > 0:
                item = self.main_content.layout().itemAt(0)
                if item:
                    current_widget = item.widget()

            if current_widget:
                # 记录页面类型，用于日志
                page_type = current_widget.__class__.__name__
                self.logger.info(f"🔄 页面离开: {page_type}")
                
                # 安全地执行页面清理任务
                cleanup_methods = [
                    ('_stop_background_monitoring', '📧 停止后台监控'),
                    ('pause_auto_refresh', '⏸️ 暂停自动刷新'),
                    ('save_page_state', '💾 保存页面状态'),
                    ('stop_all_timers', '⏱️ 停止所有计时器'),
                    ('cleanup_temp_data', '🧹 清理临时数据'),
                    ('cancel_pending_requests', '🌐 取消挂起的网络请求'),
                    ('on_page_leaving', '👋 通知页面即将隐藏')
                ]

                # 安全地执行所有可用的清理任务
                for method_name, description in cleanup_methods:
                    if hasattr(current_widget, method_name):
                        try:
                            self.logger.info(description)
                            method = getattr(current_widget, method_name)
                            if callable(method):
                                method()
                        except Exception as e:
                            self.logger.warning(f"⚠️ {method_name}失败: {e}")

                # 始终触发页面隐藏事件（确保即使没有以上方法也执行这个基本步骤）
                if hasattr(current_widget, 'hideEvent'):
                    from PyQt5.QtGui import QHideEvent
                    current_widget.hideEvent(QHideEvent())
                    self.logger.info("🎭 触发隐藏事件")
                    
                # 推荐页面垃圾回收
                import gc
                gc.collect()
                
                self.logger.info(f"✅ 页面 {self.current_page_id} 离开处理完成")

        except Exception as e:
            self.logger.warning(f"⚠️ 页面离开处理失败: {e}")
            import traceback
            self.logger.debug(traceback.format_exc())

    def _create_page_async(self, page_id, start_time):
        """异步创建页面（优化版）- 不再显示加载提示"""
        try:
            # 设置状态栏提示
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"正在加载 {page_id} 页面...")
                
            # 记录日志
            self.logger.info(f"🚀 开始异步创建页面: {page_id}")

            # 不再显示加载弹窗，直接创建页面
            self._create_page_delayed(page_id, start_time)
        except Exception as e:
            self.logger.error(f"❌ 异步页面创建失败: {e}")

    def _create_page_delayed(self, page_id, start_time, loading_widget=None):
        """延迟创建页面（优化版）"""
        try:
            import time
            import threading

            # 兼容旧代码，处理loading_widget参数
            if loading_widget is not None:
            loading_widget.setParent(None)

            # 记录创建开始时间
            create_start_time = time.perf_counter()
                
            # 创建页面
            page_widget = None

            # 在单独线程中创建页面的函数
            def thread_create_page():
                nonlocal page_widget
                try:
                    # 根据页面类型创建对应页面
            if page_id == 'home':
                page_widget = self._create_home_content()
            elif page_id == 'mailbox':
                page_widget = self._create_mailbox_page_widget()
            elif page_id == 'vscode':
                page_widget = self._create_vscode_page_widget()
            elif page_id == 'forum':
                page_widget = self._create_forum_page_widget()
            elif page_id == 'config':
                page_widget = self._create_config_page_widget()
            elif page_id == 'announcements':
                page_widget = self._create_announcements_page_widget()
            elif page_id == 'account_database':
                page_widget = self._create_account_database_page_widget()
            else:
                page_widget = self._create_coming_soon_widget(page_id)

                    # 记录创建耗时
                    create_time = (time.perf_counter() - create_start_time) * 1000
                    self.logger.info(f"⏱️ 页面 {page_id} 实例创建耗时: {create_time:.1f}ms")
                    
                except Exception as e:
                    self.logger.error(f"❌ 线程中创建页面失败: {e}")
                
            # 在UI线程中显示页面的函数
            def ui_show_page():
                nonlocal page_widget
                try:
            if page_widget:
                # 确保页面没有父组件
                if page_widget.parent():
                    page_widget.setParent(None)

                # 添加到智能缓存
                        if self.cache_manager:
                self.cache_manager.cache_page(page_id, page_widget, {
                    'creation_time': time.time(),
                    'page_type': page_id
                })

                # 显示页面
                self.main_content.layout().addWidget(page_widget)
                page_widget.show()  # 确保页面可见
                self.current_page_id = page_id

                        # 如果是邮箱页面，恢复自动刷新
                        if page_id == 'mailbox' and hasattr(page_widget, 'resume_auto_refresh'):
                            page_widget.resume_auto_refresh()
    
                # 计算总耗时
                elapsed = (time.perf_counter() - start_time) * 1000
                        self.logger.info(f"✅ 页面 {page_id} 创建并显示完成，总耗时: {elapsed:.1f}ms")
                        
                        # 更新状态栏
                        if hasattr(self, 'status_label'):
                            self.status_label.setText(f"页面加载完成")
                    else:
                        # 页面创建失败，显示错误页面
                        self._show_error_page(f"页面 {page_id} 创建失败")

        except Exception as e:
                    self.logger.error(f"❌ 显示页面失败: {e}")
                    self._show_error_page(f"页面显示失败: {e}")
            
            # 对于简单页面，直接在主线程创建
            if page_id in ['home', 'coming_soon']:
                thread_create_page()  # 直接在主线程创建
                ui_show_page()  # 然后显示
            else:
                # 对于复杂页面，在单独线程中创建，避免UI卡顿
                create_thread = threading.Thread(target=thread_create_page)
                create_thread.daemon = True
                create_thread.start()
                
                # 使用QTimer定时检查线程是否完成
                from PyQt5.QtCore import QTimer
                self.page_create_timer = QTimer()
                self.page_create_timer.setInterval(50)  # 50ms检查一次
                
                def check_page_ready():
                    if not create_thread.is_alive() and page_widget is not None:
                        # 线程完成且页面已创建
                        self.page_create_timer.stop()
                        ui_show_page()
                    elif not create_thread.is_alive() and page_widget is None:
                        # 线程完成但页面创建失败
                        self.page_create_timer.stop()
                        self._show_error_page(f"页面 {page_id} 创建失败")
                        
                self.page_create_timer.timeout.connect(check_page_ready)
                self.page_create_timer.start()
                
        except Exception as e:
            self.logger.error(f"❌ 创建页面过程失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            self._show_error_page(f"页面创建失败: {e}")

    def _create_loading_widget(self, page_id):
        """创建加载提示组件"""
        loading_widget = QWidget()
        loading_layout = QVBoxLayout(loading_widget)
        loading_layout.setAlignment(Qt.AlignCenter)

        # 加载图标
        loading_label = QLabel("🔄")
        loading_label.setFont(QFont("Segoe UI Emoji", 48))
        loading_label.setAlignment(Qt.AlignCenter)
        loading_layout.addWidget(loading_label)

        # 加载文本
        text_label = QLabel(f"正在加载 {self._get_page_name(page_id)}...")
        text_label.setFont(DS.get_font('heading_lg'))
        text_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        text_label.setAlignment(Qt.AlignCenter)
        loading_layout.addWidget(text_label)

        return loading_widget

    def _get_page_name(self, page_id):
        """获取页面显示名称"""
        page_names = {
            'home': '首页',
            'mailbox': '邮箱管理',
            'vscode': 'VSCode清理',
            'forum': '社区论坛',
            'config': '系统配置',
            'announcements': '系统公告'
        }
        return page_names.get(page_id, page_id)

    def _create_mailbox_page_widget(self):
        """创建邮箱页面组件 - 使用预加载模块"""
        try:
            # 使用预加载的模块类
            PyQtMailboxPage = self.preloaded_modules.get('PyQtMailboxPage')
            if not PyQtMailboxPage:
                # 降级到动态导入
                from src.gui.pyqt_ui.pyqt_pages.pyqt_mailbox_page import PyQtMailboxPage

            # 获取配置管理器
            config_manager = getattr(self, 'config_manager', None)
            if hasattr(self, 'security_manager') and self.security_manager:
                config_manager = getattr(self.security_manager, 'config_manager', None)

            # 创建邮箱页面（使用预加载的类）
            mailbox_page = PyQtMailboxPage(config_manager=config_manager)

            # 设置为实例变量以便管理
            self.mailbox_page = mailbox_page

            return mailbox_page

        except Exception as e:
            self.logger.error(f"❌ 创建邮箱页面失败: {e}")
            return self._create_error_widget(f"邮箱页面创建失败: {e}")

    def _create_vscode_page_widget(self):
        """创建VSCode页面组件 - 使用预创建实例"""
        try:
            # 优先使用预创建的实例
            if hasattr(self, 'precreated_pages') and 'vscode' in self.precreated_pages:
                vscode_page = self.precreated_pages['vscode']
                print("⚡ 使用预创建的VSCode页面实例")
                return vscode_page

            # 降级到使用预加载的模块类
            PyQtVSCodePage = self.preloaded_modules.get('PyQtVSCodePage')
            if not PyQtVSCodePage:
                # 最后降级到动态导入
                from src.gui.pyqt_ui.pyqt_pages.pyqt_vscode_page import PyQtVSCodePage

            vscode_page = PyQtVSCodePage()
            return vscode_page
        except Exception as e:
            self.logger.error(f"❌ 创建VSCode页面失败: {e}")
            return self._create_error_widget(f"VSCode页面创建失败: {e}")



    def _create_config_page_widget(self):
        """创建配置页面组件 - 使用预创建实例"""
        try:
            # 优先使用预创建的实例
            if hasattr(self, 'precreated_pages') and 'config' in self.precreated_pages:
                config_page = self.precreated_pages['config']
                print("⚡ 使用预创建的配置页面实例")
                return config_page

            # 降级到使用预加载的模块类
            PyQtConfigPage = self.preloaded_modules.get('PyQtConfigPage')
            if not PyQtConfigPage:
                # 最后降级到动态导入
                from src.gui.pyqt_ui.pyqt_pages.pyqt_config_page import PyQtConfigPage

            config_page = PyQtConfigPage()
            return config_page
        except Exception as e:
            self.logger.error(f"❌ 创建配置页面失败: {e}")
            return self._create_error_widget(f"配置页面创建失败: {e}")

    def _create_announcements_page_widget(self):
        """创建公告页面组件 - 使用预加载模块"""
        try:
            # 使用预加载的模块类
            PyQtAnnouncementsPage = self.preloaded_modules.get('PyQtAnnouncementsPage')
            if not PyQtAnnouncementsPage:
                # 降级到动态导入
                from src.gui.pyqt_ui.pyqt_pages.pyqt_announcements_page import PyQtAnnouncementsPage

            announcements_page = PyQtAnnouncementsPage(main_window=self)
            return announcements_page
        except Exception as e:
            self.logger.error(f"❌ 创建公告页面失败: {e}")
            return self._create_error_widget(f"公告页面创建失败: {e}")

    def _create_forum_page_widget(self):
        """创建论坛页面组件 - 优先使用重构版本"""
        try:
            # 优先使用重构的论坛页面（最新版本）
            try:
                from src.gui.pyqt_ui.pyqt_pages.forum_refactored import RefactoredForumPage
                forum_page = RefactoredForumPage(main_window=self)
                self.logger.info("✅ 使用重构的论坛页面（推荐）")
                return forum_page
            except ImportError:
                pass

            # 降级到API对接的原版论坛页面
            PyQtForumPage = self.preloaded_modules.get('PyQtForumPage')
            if PyQtForumPage:
                forum_page = PyQtForumPage(main_window=self)
                self.logger.info("⚠️ 降级使用原版论坛页面")
                return forum_page

            # 最后降级到动态导入
            try:
                from src.gui.pyqt_ui.pyqt_pages.pyqt_forum_page import PyQtForumPage
                forum_page = PyQtForumPage(main_window=self)
                self.logger.info("⚠️ 动态导入原版论坛页面")
                return forum_page
            except:
                from src.gui.pyqt_ui.pyqt_pages.forum_ui_refactored import RefactoredForumPage
                forum_page = RefactoredForumPage(main_window=self)
                self.logger.info("⚠️ 动态导入旧版重构论坛页面")
                return forum_page

        except Exception as e:
            self.logger.error(f"❌ 创建论坛页面失败: {e}")
            return self._create_error_widget(f"论坛页面创建失败: {e}")



    def _create_coming_soon_widget(self, page_id):
        """创建开发中页面组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)

        # 图标
        icon_label = QLabel("🚧")
        icon_label.setFont(QFont("Segoe UI Emoji", 64))
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(f"{self._get_page_name(page_id)} 功能开发中")
        title_label.setFont(DS.get_font('heading_xl'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 描述
        desc_label = QLabel("该功能正在开发中，敬请期待...")
        desc_label.setFont(DS.get_font('body_lg'))
        desc_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)

        return widget

    def _create_error_widget(self, error_message):
        """创建错误页面组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)

        # 错误图标
        icon_label = QLabel("❌")
        icon_label.setFont(QFont("Segoe UI Emoji", 64))
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # 错误信息
        error_label = QLabel(error_message)
        error_label.setFont(DS.get_font('body_lg'))
        error_label.setStyleSheet(f"color: {DS.COLORS['neon_red'].name()};")
        error_label.setAlignment(Qt.AlignCenter)
        error_label.setWordWrap(True)
        layout.addWidget(error_label)

        return widget

    def _show_home_page(self):
        """显示首页"""
        try:
            # 清除当前内容
            self._clear_main_content()

            # 创建首页内容
            home_widget = self._create_home_content()
            self.main_content.layout().addWidget(home_widget)

            self.logger.info("✅ 首页已加载")

        except Exception as e:
            self.logger.error(f"❌ 加载首页失败: {e}")
            self._show_error_page(f"首页加载失败: {e}")

    def _show_announcements_page(self):
        """显示系统公告页面"""
        try:
            # 清除当前内容
            self._clear_main_content()

            # 创建公告页面 - 优先使用预加载的模块类
            PyQtAnnouncementsPage = self.preloaded_modules.get('PyQtAnnouncementsPage')
            if PyQtAnnouncementsPage:
                self.logger.info("使用预加载的公告页面模块")
            else:
                # 降级到动态导入
                self.logger.info("动态导入公告页面模块")
                from src.gui.pyqt_ui.pyqt_pages.pyqt_announcements_page import PyQtAnnouncementsPage
            
            announcements_page = PyQtAnnouncementsPage(main_window=self)
            self.main_content.layout().addWidget(announcements_page)

            self.logger.info("✅ 系统公告页面已加载")

        except Exception as e:
            self.logger.error(f"❌ 加载系统公告页面失败: {e}")
            self._show_error_page(f"系统公告页面加载失败: {e}")
            # 记录详细堆栈信息
            import traceback
            self.logger.error(f"堆栈信息: {traceback.format_exc()}")

    def _create_account_database_page_widget(self):
        """创建账号库页面组件 - 修复线程安全问题"""
        try:
            self.logger.info("🔄 正在加载账号库页面...")
            
            # 检查当前线程
            from PyQt5.QtCore import QThread
            current_thread = QThread.currentThread()
            self.logger.info(f"当前线程: {current_thread.objectName() or '主线程'}")

            # 导入账号库页面
            from .pyqt_pages.pyqt_account_database_page import PyQtAccountDatabasePage

            # 创建账号库页面 - 明确传递self作为父组件
            account_database_page = PyQtAccountDatabasePage(config_manager=self.config_manager, parent=self)

            # 强制设置窗口标志
            account_database_page.setWindowFlags(Qt.Widget)
            
            # 额外检查确保它没有错误的父组件
            if account_database_page.parent() != self:
                self.logger.warning(f"⚠️ 账号库页面父组件设置异常: {account_database_page.parent()}")
                # 强制重新设置父组件
                account_database_page.setParent(self)

            # 额外检查线程是否正确
            if account_database_page.thread() != current_thread:
                self.logger.error(f"❌ 账号库页面在错误的线程中！")
                
            # 强制移除所有可能的定时器和非主线程对象关联
            if hasattr(account_database_page, 'refresh_timer'):
                try:
                    account_database_page.refresh_timer.stop()
                    account_database_page.refresh_timer.deleteLater()
                    account_database_page.refresh_timer = QTimer(account_database_page)  # 重新创建在正确的线程
                    account_database_page.refresh_timer.timeout.connect(account_database_page.refresh_accounts)
                    account_database_page.refresh_timer.start(60000)  # 每分钟刷新一次
                    self.logger.info("✅ 账号库页面定时器已重新创建")
                except Exception as e:
                    self.logger.warning(f"⚠️ 重置定时器失败: {e}")

            self.logger.info(f"✅ 账号库页面已加载 - 父组件类型: {type(account_database_page.parent()).__name__}")
            return account_database_page

        except Exception as e:
            self.logger.error(f"❌ 加载账号库页面失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            self._show_error_page(f"账号库页面加载失败: {e}")
            return None

    def _create_home_content(self):
        """创建极简首页内容 - 快速加载"""
        # 主容器
        home_widget = QWidget()
        home_layout = QVBoxLayout(home_widget)
        home_layout.setContentsMargins(50, 50, 50, 50)
        home_layout.setSpacing(30)
        home_layout.setAlignment(Qt.AlignCenter)

        # 欢迎标题
        welcome_label = QLabel("🚀 Augment续杯工具")
        welcome_label.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("color: #00d4ff; margin: 20px;")
        home_layout.addWidget(welcome_label)

        # 简单描述
        desc_label = QLabel("高效的开发工具集合")
        desc_label.setFont(QFont("Microsoft YaHei", 14))
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #888; margin-bottom: 30px;")
        home_layout.addWidget(desc_label)

        # 快速开始按钮 - 改为自动注册
        start_btn = QPushButton("🚀 自动注册")
        start_btn.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        start_btn.setFixedSize(300, 60)
        # 改为连接到自动注册方法
        start_btn.clicked.connect(self._start_auto_register)
        start_btn.setStyleSheet("""
            QPushButton {
                background: #00d4ff;
                color: white;
                border: none;
                border-radius: 30px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #33ddff;
            }
        """)
        home_layout.addWidget(start_btn, 0, Qt.AlignCenter)

        return home_widget









    def _create_navigation_section(self):
        """创建功能导航区域"""
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(15)

        # 标题
        title_label = QLabel("🎯 主要功能")
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        nav_layout.addWidget(title_label)

        # 功能网格
        grid_widget = QWidget()
        grid_layout = QHBoxLayout(grid_widget)
        grid_layout.setSpacing(20)

        # 功能按钮数据
        functions = [
            {
                'icon': '📧',
                'title': '邮箱管理',
                'desc': '临时邮箱创建与管理',
                'action': lambda: self._on_menu_clicked('mailbox'),
                'color': DS.COLORS['neon_cyan']
            },
            {
                'icon': '🛠️',
                'title': '清理工具',
                'desc': '清理各种编辑器缓存文件',
                'action': lambda: self._on_menu_clicked('vscode'),
                'color': DS.COLORS['neon_orange']
            },
            {
                'icon': '📢',
                'title': '系统公告',
                'desc': '查看最新系统通知',
                'action': lambda: self._on_menu_clicked('announcements'),
                'color': DS.COLORS['neon_purple']
            },
            {
                'icon': '⚙️',
                'title': '系统配置',
                'desc': '个性化设置选项',
                'action': lambda: self._on_menu_clicked('config'),
                'color': DS.COLORS['neon_green']
            }
        ]

        for func in functions:
            card = self._create_function_card(func)
            grid_layout.addWidget(card)

        nav_layout.addWidget(grid_widget)
        return nav_widget

    def _create_function_card(self, func_data):
        """创建功能卡片"""
        card = QPushButton()
        card.setFixedSize(200, 120)
        card.setCursor(Qt.PointingHandCursor)
        card.clicked.connect(func_data['action'])

        # 卡片样式
        card.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border: none;
                border-radius: 10px;
                text-align: left;
                padding: 15px;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].lighter(115).name()};
                border: 2px solid {func_data['color'].name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['bg_tertiary'].darker(110).name()};
            }}
        """)

        # 卡片内容
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(10, 10, 10, 10)
        card_layout.setSpacing(8)

        # 图标
        icon_label = QLabel(func_data['icon'])
        icon_label.setFont(QFont("Segoe UI Emoji", 24))
        icon_label.setAlignment(Qt.AlignLeft)
        card_layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(func_data['title'])
        title_label.setFont(DS.get_font('heading_sm'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        card_layout.addWidget(title_label)

        # 描述
        desc_label = QLabel(func_data['desc'])
        desc_label.setFont(DS.get_font('body_sm'))
        desc_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        desc_label.setWordWrap(True)
        card_layout.addWidget(desc_label)

        return card

    def _create_account_database_page(self):
        """创建账号库页面（用于系统状态区域）"""
        try:
            from .pyqt_pages.pyqt_account_database_page import PyQtAccountDatabasePage
            return PyQtAccountDatabasePage(self.config_manager)
        except Exception as e:
            self.logger.error(f"创建账号库页面失败: {e}")
            return self._create_system_status_section()

    def _create_system_status_section(self):
        """创建账号库区域（简化版）"""
        status_widget = QWidget()
        status_layout = QVBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(15)

        # 标题
        title_label = QLabel("📊 账号库")
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        status_layout.addWidget(title_label)

        # 状态卡片容器
        status_container = QWidget()
        status_container.setStyleSheet(f"""
            QWidget {{
                background: {DS.COLORS['bg_secondary'].name()};
                border-radius: 10px;
                border: none;
            }}
        """)

        container_layout = QHBoxLayout(status_container)
        container_layout.setContentsMargins(20, 15, 20, 15)
        container_layout.setSpacing(30)

        # 简化的账号库信息
        status_items = [
            {
                'icon': '📊',
                'title': '账号库',
                'value': '管理账号',
                'color': DS.COLORS['neon_cyan']
            }
        ]

        for item in status_items:
            status_card = self._create_status_card(item)
            container_layout.addWidget(status_card)

        status_layout.addWidget(status_container)

        return status_widget

    def _create_status_card(self, item_data):
        """创建状态卡片"""
        card = QWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(5)
        card_layout.setAlignment(Qt.AlignCenter)

        # 图标
        icon_label = QLabel(item_data['icon'])
        icon_label.setFont(QFont("Segoe UI Emoji", 20))
        icon_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(item_data['title'])
        title_label.setFont(DS.get_font('body_sm'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)

        # 值
        value_label = QLabel(item_data['value'])
        value_label.setFont(DS.get_font('body_md'))
        value_label.setStyleSheet(f"color: {item_data['color'].name()}; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)

        return card

    def _create_quick_info_section(self):
        """创建快速信息区域"""
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(15)

        # 标题
        title_label = QLabel("ℹ️ 快速信息")
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        info_layout.addWidget(title_label)

        # 信息容器
        info_container = QWidget()
        info_container.setStyleSheet(f"""
            QWidget {{
                background: {DS.COLORS['bg_secondary'].name()};
                border-radius: 10px;
                border: none;
            }}
        """)

        container_layout = QVBoxLayout(info_container)
        container_layout.setContentsMargins(20, 15, 20, 15)
        container_layout.setSpacing(10)

        # 信息项目
        info_items = [
            "🎯 当前版本：PyQt v2.0 - 高性能版本",
            "🚀 性能提升：相比tkinter版本提升300%",
            "💡 新功能：系统公告、现代化界面、流畅动画",
            "🔧 技术栈：PyQt5 + 现代化设计系统",
            "📞 技术支持：QQ群 735821698"
        ]

        for item in info_items:
            info_label = QLabel(item)
            info_label.setFont(DS.get_font('body_md'))
            info_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; padding: 5px 0;")
            container_layout.addWidget(info_label)

        info_layout.addWidget(info_container)
        return info_widget

    def _create_hero_section(self):
        """创建英雄区域"""
        hero_widget = QWidget()
        hero_layout = QVBoxLayout(hero_widget)
        hero_layout.setContentsMargins(60, 80, 60, 80)
        hero_layout.setSpacing(30)
        hero_layout.setAlignment(Qt.AlignCenter)

        # 主标题
        title_label = QLabel("🚀 Augment 续杯工具")
        title_font = QFont("Microsoft YaHei", 42, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_cyan'].name()};
                background: transparent;
                padding: 20px;
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
            }}
        """)

        # 副标题
        subtitle_label = QLabel("专业的开发工具集合 • 提升效率 • 简化流程")
        subtitle_font = QFont("Microsoft YaHei", 18)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_secondary'].name()};
                background: transparent;
                padding: 10px;
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
                opacity: 0.9;
            }}
        """)

        # 描述文本
        desc_label = QLabel("集成邮箱管理、VSCode清理、系统优化等多种实用功能\n让开发工作更加高效便捷")
        desc_font = QFont("Microsoft YaHei", 14)
        desc_label.setFont(desc_font)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_primary'].name()};
                background: transparent;
                padding: 15px;
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
                line-height: 1.6;
            }}
        """)

        # 主要操作按钮
        action_buttons = self._create_hero_buttons()

        hero_layout.addWidget(title_label)
        hero_layout.addWidget(subtitle_label)
        hero_layout.addWidget(desc_label)
        hero_layout.addWidget(action_buttons)

        # 英雄区域背景样式 - 简化版本，去掉边框
        hero_widget.setStyleSheet(f"""
            QWidget {{
                background: {DS.COLORS['bg_secondary'].name()};
                border-radius: 15px;
                border: none;
            }}
        """)

        return hero_widget

    def _create_hero_buttons(self):
        """创建英雄区域的按钮"""
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setAlignment(Qt.AlignCenter)
        button_layout.setSpacing(20)

        # 主要按钮
        primary_btn = QPushButton("🚀 开始使用")
        primary_btn.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        primary_btn.setFixedSize(180, 50)
        primary_btn.clicked.connect(lambda: self._show_page('mailbox'))
        primary_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['bg_primary'].name()};
                border: none;
                border-radius: 20px;
                font-weight: bold;
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].lighter(110).name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['neon_cyan'].darker(110).name()};
            }}
        """)

        # 次要按钮
        secondary_btn = QPushButton("📖 查看功能")
        secondary_btn.setFont(QFont("Microsoft YaHei", 14))
        secondary_btn.setFixedSize(150, 50)
        secondary_btn.clicked.connect(self._scroll_to_features)
        secondary_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['bg_tertiary'].name()};
                color: {DS.COLORS['neon_cyan'].name()};
                border: none;
                border-radius: 20px;
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].lighter(120).name()};
            }}
        """)

        button_layout.addWidget(primary_btn)
        button_layout.addWidget(secondary_btn)

        return button_container

    def _create_quick_actions(self):
        """创建快速操作区域"""
        quick_widget = QWidget()
        quick_layout = QVBoxLayout(quick_widget)
        quick_layout.setContentsMargins(30, 30, 30, 30)
        quick_layout.setSpacing(20)

        # 标题
        title_label = QLabel("⚡ 快速操作")
        title_font = QFont("Microsoft YaHei", 24, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_green'].name()};
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
                padding: 10px 0;
            }}
        """)

        # 快速操作按钮网格
        actions_grid = QGridLayout()
        actions_grid.setSpacing(15)

        # 定义快速操作
        quick_actions = [
            ("📧", "邮箱管理", "查看和管理临时邮箱", lambda: self._show_page('mailbox')),
            ("🧹", "清理工具", "清理各种编辑器缓存文件", lambda: self._show_page('vscode')),
            ("⚙️", "系统配置", "配置应用程序设置", lambda: self._show_page('config')),
            ("🚀", "注册助手", "快速注册Augment", self._start_auto_register),
        ]

        for i, (icon, title, desc, callback) in enumerate(quick_actions):
            action_card = self._create_quick_action_card(icon, title, desc, callback)
            row, col = i // 2, i % 2
            actions_grid.addWidget(action_card, row, col)

        quick_layout.addWidget(title_label)
        quick_layout.addLayout(actions_grid)

        # 快速操作区域样式 - 简化版本
        quick_widget.setStyleSheet(f"""
            QWidget {{
                background: {DS.COLORS['bg_secondary'].name()};
                border-radius: 10px;
                border: none;
            }}
        """)

        return quick_widget

    def _create_quick_action_card(self, icon, title, desc, callback):
        """创建快速操作卡片"""
        card = QPushButton()
        card.setFixedSize(280, 100)
        card.clicked.connect(callback)

        # 设置卡片文本（简化版本，使用按钮的原生文本显示）
        card.setText(f"{icon}  {title}\n{desc}")

        # 设置卡片样式 - 简化版本，去掉边框
        card.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border: none;
                border-radius: 8px;
                text-align: left;
                padding: 15px;
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
                font-size: 13px;
                color: {DS.COLORS['text_primary'].name()};
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].lighter(120).name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['bg_tertiary'].darker(110).name()};
            }}
        """)

        return card

    def _create_feature_grid(self):
        """创建功能网格"""
        feature_widget = QWidget()
        feature_layout = QVBoxLayout(feature_widget)
        feature_layout.setContentsMargins(30, 30, 30, 30)
        feature_layout.setSpacing(25)

        # 标题
        title_label = QLabel("🛠️ 核心功能")
        title_font = QFont("Microsoft YaHei", 24, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_purple'].name()};
                font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
                padding: 10px 0;
            }}
        """)

        # 功能网格
        grid_layout = QGridLayout()
        grid_layout.setSpacing(20)

        # 定义功能特性
        features = [
            ("📧", "邮箱管理", "临时邮箱创建与管理\n自动验证码提取", DS.COLORS['neon_cyan']),
            ("🧹", "VSCode清理", "清理缓存和临时文件\n释放磁盘空间", DS.COLORS['neon_green']),
            ("⚙️", "系统配置", "个性化设置管理\n一键配置导入导出", DS.COLORS['neon_yellow']),
            ("🚀", "自动化工具", "批量操作自动化\n提升工作效率", DS.COLORS['neon_purple']),
            ("🔒", "安全保护", "数据加密存储\n隐私信息保护", DS.COLORS['neon_red']),
            ("📊", "数据统计", "使用情况分析\n性能监控报告", DS.COLORS['neon_blue']),
        ]

        for i, (icon, title, desc, color) in enumerate(features):
            feature_card = self._create_feature_card(icon, title, desc, color)
            row, col = i // 3, i % 3
            grid_layout.addWidget(feature_card, row, col)

        feature_layout.addWidget(title_label)
        feature_layout.addLayout(grid_layout)

        # 功能区域样式 - 简化版本
        feature_widget.setStyleSheet(f"""
            QWidget {{
                background: {DS.COLORS['bg_secondary'].name()};
                border-radius: 10px;
                border: none;
            }}
        """)

        return feature_widget







    def _scroll_to_features(self):
        """滚动到功能区域"""
        # 这里可以实现滚动到功能区域的逻辑
        pass

    def _create_main_register_button(self):
        """创建完全居中的自动注册按钮"""
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)

        # 左侧弹性空间
        button_layout.addStretch()

        # 只保留自动注册按钮
        register_btn = QPushButton("自动注册")
        register_btn.setFont(QFont("Microsoft YaHei UI", 16, QFont.Bold))
        register_btn.setFixedSize(200, 50)
        register_btn.clicked.connect(self._start_auto_register)

        # 简洁的样式
        register_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['neon_cyan'].name()};
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].lighter(120).name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['neon_cyan'].darker(120).name()};
            }}
        """)

        button_layout.addWidget(register_btn)

        # 右侧弹性空间
        button_layout.addStretch()

        return button_container

    def _create_function_cards(self):
        """创建功能卡片"""
        cards_widget = QWidget()
        cards_layout = QHBoxLayout(cards_widget)
        cards_layout.setSpacing(30)

        # 功能卡片数据
        functions = [
            {
                "icon": "🚀",
                "title": "快速注册",
                "description": "自动打开网站并点击登录",
                "action": self._start_auto_register
            },
            {
                "icon": "📧",
                "title": "邮箱管理",
                "description": "查看临时邮箱，获取验证码",
                "action": lambda: self._on_menu_clicked('mailbox')
            },
            {
                "icon": "🛠️",
                "title": "VSCode清理",
                "description": "清理缓存，重置机器码",
                "action": lambda: self._on_menu_clicked('vscode')
            },
            {
                "icon": "📊",
                "title": "系统监控",
                "description": "查看系统状态和性能",
                "action": lambda: self._on_menu_clicked('monitor')
            }
        ]

        for func in functions:
            card = self._create_function_card(func)
            cards_layout.addWidget(card)

        return cards_widget

    def _create_function_card(self, func_data):
        """创建单个功能卡片"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setFixedSize(200, 150)
        card.setCursor(Qt.PointingHandCursor)

        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        card_layout.setSpacing(10)

        # 图标
        icon_label = QLabel(func_data["icon"])
        icon_label.setFont(QFont("Microsoft YaHei UI", 32))
        icon_label.setAlignment(Qt.AlignCenter)

        # 标题
        title_label = QLabel(func_data["title"])
        title_label.setFont(QFont("Microsoft YaHei UI", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)

        # 描述
        desc_label = QLabel(func_data.get("description", func_data.get("desc", "")))
        desc_label.setFont(QFont("Microsoft YaHei UI", 10))
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)

        card_layout.addWidget(icon_label)
        card_layout.addWidget(title_label)
        card_layout.addWidget(desc_label)

        # 样式
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 12px;
            }}
            QFrame:hover {{
                background-color: {DS.COLORS['glass_bg'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
            QLabel {{
                background: transparent;
                color: {DS.COLORS['text_primary'].name()};
            }}
        """)

        # 点击事件
        def mousePressEvent(event):
            if event.button() == Qt.LeftButton:
                func_data["action"]()

        card.mousePressEvent = mousePressEvent

        return card

    def _show_coming_soon_page(self, page_name):
        """显示开发中页面"""
        try:
            # 清除当前内容
            self._clear_main_content()

            # 创建开发中页面
            coming_soon_widget = QWidget()
            layout = QVBoxLayout(coming_soon_widget)
            layout.setAlignment(Qt.AlignCenter)
            layout.setSpacing(20)

            # 图标
            icon_label = QLabel("🚧")
            icon_font = QFont("Microsoft YaHei", 64)
            icon_label.setFont(icon_font)
            icon_label.setAlignment(Qt.AlignCenter)

            # 标题
            title_label = QLabel("功能开发中")
            title_font = QFont("Microsoft YaHei", 24, QFont.Bold)
            title_label.setFont(title_font)
            title_label.setAlignment(Qt.AlignCenter)

            # 描述
            page_names = {
                'config': '系统配置',
                'announcements': '系统公告',
                'forum': '论坛交流',
                'account_database': '账号库'
            }
            page_display_name = page_names.get(page_name, page_name)
            desc_label = QLabel(f"'{page_display_name}' 功能正在开发中，敬请期待...")
            desc_font = QFont("Microsoft YaHei", 16)
            desc_label.setFont(desc_font)
            desc_label.setAlignment(Qt.AlignCenter)

            # 返回按钮
            back_button = QPushButton("🏠 返回首页")
            back_button.setFont(QFont("Microsoft YaHei UI", 14))
            back_button.clicked.connect(lambda: self._on_menu_clicked('home'))
            back_button.setFixedSize(150, 40)

            layout.addWidget(icon_label)
            layout.addWidget(title_label)
            layout.addWidget(desc_label)
            layout.addWidget(back_button)

            # 样式
            coming_soon_widget.setStyleSheet(f"""
                QWidget {{
                    background: transparent;
                }}
                QLabel {{
                    color: {DS.COLORS['text_primary']};
                    background: transparent;
                }}
                QPushButton {{
                    background-color: {DS.COLORS['neon_cyan']};
                    color: {DS.COLORS['bg_primary']};
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {DS.COLORS['neon_green']};
                }}
            """)

            self.main_content.layout().addWidget(coming_soon_widget)

            self.logger.info(f"✅ 开发中页面已显示: {page_display_name}")

        except Exception as e:
            self.logger.error(f"❌ 显示开发中页面失败: {e}")

    def _start_manual_register(self):
        """启动手动注册助手"""
        try:
            self.logger.info("👤 用户点击手动注册助手")

            # 导入邮箱生成模块
            try:
                from src.email_service.email_generator import generate_random_email
            except ImportError:
                # 简单的邮箱生成
                import random
                import string
                def generate_random_email(domain="hwsyyds.xyz"):
                    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                    return f"{username}@{domain}"

            # 生成邮箱
            email = generate_random_email("hwsyyds.xyz")

            # 复制到剪贴板
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(email)

            # 打开浏览器 - 全屏模式
            self._open_browser_fullscreen("https://augmentcode.com")

            # 显示成功消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "手动注册助手",
                f"✅ 操作完成！\n\n📧 邮箱: {email}\n📋 已复制到剪贴板\n🌐 浏览器已全屏打开\n\n请在网站上使用此邮箱注册"
            )

            self.logger.info(f"✅ 手动注册助手完成，邮箱: {email}")

        except Exception as e:
            self.logger.error(f"❌ 手动注册助手失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"手动注册助手失败: {e}")

    def _open_browser_fullscreen(self, url):
        """打开浏览器并全屏显示"""
        try:
            # 获取配置的浏览器类型
            config_file = os.path.join(os.path.expanduser("~"), ".augment_config.json")
            browser_type = "默认浏览器"

            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    browser_type = config.get("browser_type", "默认浏览器")
                except:
                    pass

            import subprocess
            import platform

            system = platform.system()

            if browser_type == "Chrome":
                if system == "Windows":
                    subprocess.Popen([
                        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                        "--start-fullscreen",
                        url
                    ])
                elif system == "Darwin":  # macOS
                    subprocess.Popen([
                        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                        "--start-fullscreen",
                        url
                    ])
                else:  # Linux
                    subprocess.Popen(["google-chrome", "--start-fullscreen", url])

            elif browser_type == "Firefox":
                if system == "Windows":
                    subprocess.Popen([
                        "C:\\Program Files\\Mozilla Firefox\\firefox.exe",
                        "-fullscreen",
                        url
                    ])
                elif system == "Darwin":  # macOS
                    subprocess.Popen([
                        "/Applications/Firefox.app/Contents/MacOS/firefox",
                        "-fullscreen",
                        url
                    ])
                else:  # Linux
                    subprocess.Popen(["firefox", "-fullscreen", url])

            elif browser_type == "Edge":
                if system == "Windows":
                    subprocess.Popen([
                        "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe",
                        "--start-fullscreen",
                        url
                    ])
                else:
                    # Edge在其他系统上使用默认浏览器
                    import webbrowser
                    webbrowser.open(url)
            else:
                # 默认浏览器 - 尝试全屏
                import webbrowser
                webbrowser.open(url)

            self.logger.info(f"✅ 浏览器已全屏打开: {browser_type}")

        except Exception as e:
            self.logger.error(f"❌ 打开浏览器失败: {e}")
            # 回退到普通方式
            import webbrowser
            webbrowser.open(url)

    def _show_monitor_page(self):
        """显示系统监控页面"""
        try:
            from src.gui.pyqt_ui.pyqt_pages.pyqt_monitor_page import SystemMonitorPage

            # 清除当前内容
            self._clear_main_content()

            # 创建并添加监控页面
            self.monitor_page = SystemMonitorPage()
            self.main_content.layout().addWidget(self.monitor_page)

            self.logger.info("✅ 系统监控页面已加载")

        except Exception as e:
            self.logger.error(f"❌ 加载系统监控页面失败: {e}")
            self._show_error_page(f"系统监控页面加载失败: {e}")

    def _show_mailbox_page(self):
        """显示邮箱查看页面 - 学习modern_mailbox_page.py的设计"""
        try:
            from src.gui.pyqt_ui.pyqt_pages.pyqt_mailbox_page import PyQtMailboxPage

            # 清除当前内容
            self._clear_main_content()

            # 创建并添加邮箱页面，传入配置管理器
            config_manager = getattr(self, 'config_manager', None)
            if hasattr(self, 'security_manager') and self.security_manager:
                config_manager = getattr(self.security_manager, 'config_manager', None)

            self.mailbox_page = PyQtMailboxPage(config_manager=config_manager)
            self.main_content.layout().addWidget(self.mailbox_page)

            self.logger.info("✅ 邮箱查看页面已加载")

        except Exception as e:
            self.logger.error(f"❌ 加载邮箱查看页面失败: {e}")
            self._show_error_page(f"邮箱查看页面加载失败: {e}")

    def _show_vscode_page(self):
        """显示VSCode清理页面 - 学习enhanced_vscode_page.py的设计"""
        try:
            from src.gui.pyqt_ui.pyqt_pages.pyqt_vscode_page import PyQtVSCodePage

            # 清除当前内容
            self._clear_main_content()

            # 创建并添加VSCode页面
            self.vscode_page = PyQtVSCodePage()
            self.main_content.layout().addWidget(self.vscode_page)

            self.logger.info("✅ VSCode清理页面已加载")

        except Exception as e:
            self.logger.error(f"❌ 加载VSCode清理页面失败: {e}")
            self._show_error_page(f"VSCode清理页面加载失败: {e}")

    def _show_config_page(self):
        """显示配置管理页面"""
        try:
            # 清除当前内容
            self._clear_main_content()

            # 导入配置页面
            from src.gui.pyqt_ui.pyqt_pages.pyqt_config_page import PyQtConfigPage

            # 创建配置页面
            config_page = PyQtConfigPage()
            self.main_content.layout().addWidget(config_page)

            self.logger.info("✅ 配置管理页面已加载")

        except Exception as e:
            self.logger.error(f"❌ 加载配置管理页面失败: {e}")
            self._show_error_page(f"配置管理页面加载失败: {e}")

    def _clear_main_content(self):
        """清除主内容区域 - 修复页面重叠问题"""
        try:
            # 强制清除所有子组件
            layout = self.main_content.layout()
            if layout:
                # 清除所有子组件
                while layout.count():
                    item = layout.takeAt(0)
                    if item:
                        widget = item.widget()
                        if widget:
                            # 如果是邮箱页面，暂停自动刷新
                            if (self.current_page_id == 'mailbox' and
                                hasattr(widget, 'pause_auto_refresh')):
                                try:
                                    widget.pause_auto_refresh()
                                    self.logger.info("⏸️ 已暂停邮箱页面自动刷新")
                                except Exception as e:
                                    self.logger.warning(f"暂停邮箱自动刷新失败: {e}")

                            # 移除组件但不删除（保留缓存）
                            widget.setParent(None)
                            widget.hide()
                            # 不使用deleteLater()，避免删除缓存的页面

                # 确保布局为空
                remaining_count = layout.count()
                self.logger.info(f"🧹 清理完成，布局中剩余组件数: {remaining_count}")

                # 如果还有剩余组件，强制清理
                if remaining_count > 0:
                    self.logger.warning(f"⚠️ 发现 {remaining_count} 个未清理的组件，强制清理")
                    for i in range(remaining_count):
                        item = layout.itemAt(0)
                        if item:
                            layout.removeItem(item)
                            if item.widget():
                                item.widget().setParent(None)

            # 强制更新界面
            self.main_content.update()

            # 重置当前页面ID
            self.current_page_id = None

        except Exception as e:
            self.logger.error(f"❌ 内容区域清理失败: {e}")
            # 降级清理方案
            try:
                layout = self.main_content.layout()
                if layout:
                    for i in reversed(range(layout.count())):
                        item = layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            layout.removeWidget(widget)
                            widget.setParent(None)
                            widget.hide()
            except Exception as e2:
                self.logger.error(f"❌ 降级清理也失败: {e2}")

    def _force_clean_layout(self):
        """强制清理布局，解决页面重叠问题"""
        try:
            layout = self.main_content.layout()
            if layout and layout.count() > 1:
                self.logger.warning(f"🧹 强制清理重叠页面，当前组件数: {layout.count()}")

                # 保留最后一个组件（最新的页面）
                widgets_to_remove = []
                for i in range(layout.count() - 1):  # 保留最后一个
                    item = layout.itemAt(i)
                    if item and item.widget():
                        widgets_to_remove.append(item.widget())

                # 移除多余组件（但不删除，保留缓存）
                for widget in widgets_to_remove:
                    layout.removeWidget(widget)
                    widget.setParent(None)
                    widget.hide()
                    # 不删除widget，保持缓存有效

                self.logger.info(f"✅ 强制清理完成，移除了 {len(widgets_to_remove)} 个重叠组件")

        except Exception as e:
            self.logger.error(f"❌ 强制清理布局失败: {e}")

    def _show_error_page(self, error_message):
        """显示错误页面"""
        error_label = QLabel(f"❌ {error_message}")
        error_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_red'].name()};
                font-size: 16px;
                padding: 20px;
                background: transparent;
            }}
        """)
        error_label.setAlignment(Qt.AlignCenter)
        self.main_content.layout().addWidget(error_label)

    # QQ群相关功能已移除

    def resizeEvent(self, event):
        """窗口大小改变事件 - 响应式布局更新"""
        super().resizeEvent(event)

        # 延迟更新布局，避免频繁调用
        if hasattr(self, '_resize_timer'):
            self._resize_timer.stop()

        self._resize_timer = QTimer()
        self._resize_timer.setSingleShot(True)
        self._resize_timer.timeout.connect(self._update_responsive_layout)
        self._resize_timer.start(100)  # 100ms延迟

    def _update_responsive_layout(self):
        """更新响应式布局"""
        try:
            window_width = self.width()
            window_height = self.height()

            # 更新主布局边距
            if hasattr(self, 'centralWidget') and self.centralWidget():
                main_layout = self.centralWidget().layout()
                if main_layout:
                    if window_width >= 1600:
                        margin = 15
                        spacing = 15
                    elif window_width >= 1200:
                        margin = 10
                        spacing = 10
                    else:
                        margin = 5
                        spacing = 8

                    main_layout.setContentsMargins(margin, margin, margin, margin)
                    main_layout.setSpacing(spacing)

            # 更新内容区域边距 - 优化空间利用
            if hasattr(self, 'main_content') and self.main_content:
                content_layout = self.main_content.layout()
                if content_layout:
                    if window_width >= 1600:
                        content_margin = 15  # 大幅减少边距
                        content_spacing = 20
                    elif window_width >= 1200:
                        content_margin = 10  # 减少边距
                        content_spacing = 15
                    else:
                        content_margin = 5   # 最小边距
                        content_spacing = 10

                    content_layout.setContentsMargins(content_margin, content_margin,
                                                    content_margin, content_margin)
                    content_layout.setSpacing(content_spacing)

            self.logger.info(f"响应式布局已更新: {window_width}x{window_height}")

        except Exception as e:
            self.logger.error(f"更新响应式布局失败: {e}")

    def _get_global_stylesheet(self):
        """获取全局样式表 - 缓存优化版本"""
        # 使用缓存避免重复计算
        if not hasattr(self, '_cached_stylesheet'):
            self._cached_stylesheet = f"""
                QWidget {{
                    font-family: "Microsoft YaHei", "SimHei", "Arial", sans-serif;
                    color: {DS.COLORS['text_primary'].name()};
                }}

                QLabel {{
                    background: transparent;
                }}

                QPushButton {{
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: 500;
                }}

                QPushButton:hover {{
                    background-color: {DS.COLORS['bg_tertiary'].name()};
                }}

                QScrollBar:vertical {{
                    background: {DS.COLORS['bg_secondary'].name()};
                    width: 12px;
                    border-radius: 6px;
                }}

                QScrollBar::handle:vertical {{
                    background: {DS.COLORS['neon_cyan'].name()};
                    border-radius: 6px;
                    min-height: 20px;
                }}

                QScrollBar::handle:vertical:hover {{
                    background: {DS.COLORS['neon_cyan'].lighter(120).name()};
                }}
            """
        return self._cached_stylesheet

    @staticmethod
    def run():
        """运行应用程序"""
        try:
            # 创建主窗口
            window = PyQtMainWindow()
            window.show()

            # 运行应用程序
            return window.app.exec_()

        except Exception as e:
            print(f"应用程序运行失败: {e}")
            import traceback
            traceback.print_exc()
            return 1





        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(DS.SPACING['lg'])

        # 侧边栏 - 恢复原来的宽度
        self.sidebar = ModernSidebar(width=320)
        self.sidebar.item_clicked.connect(self._on_sidebar_item_clicked)
        content_layout.addWidget(self.sidebar)

        # 右侧内容区域 - 更大的玻璃效果
        self.content_area = GlassWidget(glass_opacity=0.05, blur_radius=20)
        content_layout.addWidget(self.content_area, 1)  # 占据剩余空间

        # 内容区域布局 - 优化边距，最大化空间利用
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(DS.SPACING['sm'], DS.SPACING['sm'],
                                             DS.SPACING['sm'], DS.SPACING['sm'])
        self.content_layout.setSpacing(DS.SPACING['xs'])

        # 主要内容显示区域 - 响应式设计
        welcome_widget = QWidget()
        welcome_layout = QVBoxLayout(welcome_widget)
        welcome_layout.setAlignment(Qt.AlignCenter)
        welcome_layout.setSpacing(DS.SPACING['lg'])  # 减少间距

        # 主标题 - 适中字体
        title_label = QLabel("🚀 Augment续杯工具")
        title_label.setFont(DS.get_font('heading_xl'))  # 使用较小字体
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_cyan'].name()};
                background: transparent;
                font-weight: bold;
            }}
        """)
        title_label.setAlignment(Qt.AlignCenter)

        # 副标题 - 适中字体
        subtitle_label = QLabel("企业级开发工具套件")
        subtitle_label.setFont(DS.get_font('body_lg'))  # 使用较小字体
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_secondary'].name()};
                background: transparent;
            }}
        """)
        subtitle_label.setAlignment(Qt.AlignCenter)

        # 状态信息 - 适中字体
        status_label = QLabel("✅ 系统运行正常 | 准备就绪")
        status_label.setFont(DS.get_font('body_md'))  # 使用较小字体
        status_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_green'].name()};
                background: transparent;
                font-weight: 500;
            }}
        """)
        status_label.setAlignment(Qt.AlignCenter)

        welcome_layout.addWidget(title_label)
        welcome_layout.addWidget(subtitle_label)
        welcome_layout.addWidget(status_label)

        self.content_layout.addWidget(welcome_widget)

        parent_layout.addWidget(content_widget, 1)  # 占据剩余空间
    
    def _create_bottom_bar(self, parent_layout):
        """创建底部信息栏"""
        bottom_bar = GlassWidget(glass_opacity=0.06, blur_radius=12)
        bottom_bar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        bottom_bar.setMinimumHeight(50)

        bottom_layout = QVBoxLayout(bottom_bar)
        bottom_layout.setContentsMargins(DS.SPACING['lg'], DS.SPACING['sm'],
                                       DS.SPACING['lg'], DS.SPACING['sm'])
        bottom_layout.setSpacing(DS.SPACING['xs'])

        # 第一行：主要状态信息
        status_row = QHBoxLayout()

        # 系统状态
        self.status_label = QLabel("🔄 启动中...")
        self.status_label.setFont(DS.get_font('body_md'))
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_green'].name()};
                background: transparent;
            }}
        """)

        # 预加载状态
        self.preload_label = QLabel("🔄 准备预加载...")
        self.preload_label.setFont(DS.get_font('body_sm'))
        self.preload_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_purple'].name()};
                background: transparent;
            }}
        """)

        # 内存使用情况
        self.memory_label = QLabel("💾 内存: 正常")
        self.memory_label.setFont(DS.get_font('body_sm'))
        self.memory_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_secondary'].name()};
                background: transparent;
            }}
        """)

        # 网络状态
        self.network_label = QLabel("🌐 网络: 已连接")
        self.network_label.setFont(DS.get_font('body_sm'))
        self.network_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['neon_cyan'].name()};
                background: transparent;
            }}
        """)

        status_row.addWidget(self.status_label)
        status_row.addWidget(self.preload_label)
        status_row.addStretch()
        status_row.addWidget(self.memory_label)
        status_row.addWidget(self.network_label)

        # 第二行：版本和帮助信息
        info_row = QHBoxLayout()

        # 版本信息
        version_info = QLabel("🔧 PyQt版本 v2.0 | 性能提升300%")
        version_info.setFont(DS.get_font('caption'))
        version_info.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_tertiary'].name()};
                background: transparent;
            }}
        """)

        # 帮助信息
        help_info = QLabel("💡 按F1获取帮助 | Ctrl+Q退出")
        help_info.setFont(DS.get_font('caption'))
        help_info.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_tertiary'].name()};
                background: transparent;
            }}
        """)

        info_row.addWidget(version_info)
        info_row.addStretch()
        info_row.addWidget(help_info)

        bottom_layout.addLayout(status_row)
        bottom_layout.addLayout(info_row)

        parent_layout.addWidget(bottom_bar)

    # QQ群信息功能已移除
    
    def _setup_navigation(self):
        """设置导航菜单"""
        # 添加菜单项
        menu_items = [
            ('home', '🏠', '首页', True),
            ('mailbox', '📧', '邮箱管理', False),
            ('vscode', '🛠️', 'VSCode清理', False),
            ('cursor', '🎯', 'Cursor清理', False),
            ('editors', '🛠️', '其他编辑器', False),
            ('forum', '💬', '论坛交流', False),
            ('config', '⚙️', '配置设置', False),
            ('announcements', '📢', '系统公告', False),
            ('account_database', '📊', '账号库', False),
            ('help', '❓', '使用帮助', False),
        ]

        for item_id, icon, text, active in menu_items:
            self.sidebar.add_menu_item(item_id, icon, text, active)
    
    def _on_sidebar_item_clicked(self, item_id):
        """侧边栏项目点击处理 - 安全的页面切换"""
        try:
            self.logger.info(f"点击了菜单项: {item_id}")

            # 如果是当前页面，直接返回
            if self.current_page_id == item_id:
                self.logger.info(f"已在页面 {item_id}，跳过切换")
                return

            # 使用缓存机制快速切换页面
            self._switch_to_page(item_id)

            # 检查是否有页面重叠
            after_count = self.main_content.layout().count()
            if after_count > 1:
                self.logger.warning(f"⚠️ 检测到页面重叠！布局中有 {after_count} 个组件")
                # 强制清理多余组件
                self._force_clean_layout()

        except Exception as e:
            self.logger.error(f"❌ 页面切换失败: {e}")
            import traceback
            traceback.print_exc()
            # 显示错误提示但不崩溃
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "页面切换错误", f"切换到 {item_id} 页面时发生错误:\n{str(e)}")
            except:
                pass

    
    def _update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"🕒 {current_time}")
    

    
    def set_security_manager(self, security_manager):
        """设置安全管理器"""
        self.security_manager = security_manager
        self.logger.info("安全管理器已设置")

        # 更新用户信息显示
        if security_manager and hasattr(security_manager, 'current_user'):
            user_info = security_manager.current_user
            if user_info:
                username = user_info.get('username', '未知用户')
                user_type = user_info.get('user_type', 'basic')
                self.user_info_label.setText(f"👤 {username} ({user_type})")
                self.user_info_label.setStyleSheet(f"""
                    QLabel {{
                        color: {DS.COLORS['neon_green'].name()};
                        background: transparent;
                    }}
                """)

        # 更新QQ群信息
        if security_manager and hasattr(security_manager, 'config_manager'):
            config = security_manager.config_manager
            qq_number = config.get('qq_group_number', '735821698')
            qq_name = config.get('qq_group_name', 'Augment续杯工具交流群')

            if hasattr(self, 'qq_number_label'):
                self.qq_number_label.setText(f"群号: {qq_number}")
            if hasattr(self, 'qq_name_label'):
                self.qq_name_label.setText(f"群名: {qq_name}")

            self.qq_group_number = qq_number
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("PyQt GUI启动成功")
            print("🎨 PyQt GUI 已启动")
            print("✨ 特性: 高性能 + 现代化设计 + 流畅动画")
            print("🚀 PyQt版本提供更好的性能和用户体验")
            
            self.show()
            return self.app.exec_()
            
        except Exception as e:
            self.logger.error(f"程序运行出错: {e}")
            raise

    def _start_auto_register(self):
        """启动自动注册流程"""
        try:
            self.logger.info("🚀 用户点击快速注册按钮")

            # 尝试导入自动注册模块
            try:
                from src.automation.auto_register import auto_register, simple_auto_register

                # 优先尝试完整版自动注册
                if auto_register:
                    success = auto_register.start_auto_register()
                    if success:
                        self.logger.info("✅ 完整版自动注册已启动")
                        return

                # 回退到简化版
                self.logger.info("🔄 使用简化版注册流程...")
                simple_auto_register.start_simple_register()

            except ImportError as e:
                self.logger.warning(f"自动注册模块导入失败: {e}")
                # 最简单的回退方案
                self._fallback_register()

        except Exception as e:
            self.logger.error(f"❌ 启动注册流程失败: {e}")
            self._fallback_register()

    def _fallback_register(self):
        """回退注册方案 - 直接打开浏览器"""
        try:
            import webbrowser
            target_url = "https://augmentcode.com"
            self.logger.info(f"🌐 正在打开浏览器访问: {target_url}")
            webbrowser.open(target_url)
            self.logger.info("✅ 浏览器已打开，请手动完成注册")
        except Exception as e:
            self.logger.error(f"❌ 打开浏览器失败: {e}")
