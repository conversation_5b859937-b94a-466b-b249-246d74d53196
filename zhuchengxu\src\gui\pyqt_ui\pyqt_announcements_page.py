"""
PyQt系统公告页面 - 现代化设计
"""

import sys
import os
import json
import requests
from datetime import datetime
from typing import List, Dict, Optional

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QScrollArea, QFrame, QTextEdit,
                             QSizePolicy, QSpacerItem, QApplication)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

# 导入设计系统和组件
from .pyqt_design_system import DS
from .pyqt_components import NeonButton, GlassWidget


class AnnouncementCard(QFrame):
    """公告卡片组件"""
    
    clicked = pyqtSignal(dict)  # 点击信号，传递公告数据
    
    def __init__(self, announcement_data: Dict, parent=None):
        super().__init__(parent)
        self.announcement_data = announcement_data
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        self.setFixedHeight(120)
        self.setCursor(Qt.PointingHandCursor)
        
        # 根据公告类型设置样式
        type_colors = {
            'info': DS.COLORS['neon_cyan'],
            'warning': DS.COLORS['neon_orange'], 
            'success': DS.COLORS['neon_green'],
            'error': DS.COLORS['neon_red'],
            'update': DS.COLORS['neon_purple']
        }
        
        announcement_type = self.announcement_data.get('type', 'info')
        accent_color = type_colors.get(announcement_type, DS.COLORS['neon_cyan'])
        
        self.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border: none;
                border-left: 4px solid {accent_color.name()};
                border-radius: 8px;
                margin: 5px 0;
            }}
            QFrame:hover {{
                background: {DS.COLORS['bg_tertiary'].lighter(110).name()};
            }}
        """)
        
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(12)
        
        # 左侧：类型图标
        icon_label = QLabel(self._get_type_icon(announcement_type))
        icon_label.setFont(QFont("Segoe UI Emoji", 24))
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # 中间：内容区域
        content_layout = QVBoxLayout()
        content_layout.setSpacing(4)
        
        # 标题
        title_label = QLabel(self.announcement_data.get('title', ''))
        title_label.setFont(DS.get_font('heading_sm'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        title_label.setWordWrap(True)
        content_layout.addWidget(title_label)
        
        # 内容预览
        content = self.announcement_data.get('content', '')
        preview = content[:80] + '...' if len(content) > 80 else content
        content_label = QLabel(preview)
        content_label.setFont(DS.get_font('body_sm'))
        content_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        content_label.setWordWrap(True)
        content_layout.addWidget(content_label)
        
        # 时间和统计
        meta_layout = QHBoxLayout()
        meta_layout.setSpacing(15)
        
        # 创建时间
        created_at = self.announcement_data.get('created_at', '')
        if created_at:
            time_label = QLabel(f"📅 {created_at}")
            time_label.setFont(DS.get_font('caption'))
            time_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
            meta_layout.addWidget(time_label)
        
        # 点击次数
        click_count = self.announcement_data.get('click_count', 0)
        click_label = QLabel(f"👁️ {click_count}")
        click_label.setFont(DS.get_font('caption'))
        click_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        meta_layout.addWidget(click_label)
        
        meta_layout.addStretch()
        content_layout.addLayout(meta_layout)
        
        layout.addLayout(content_layout)
        
        # 右侧：优先级标识
        priority = self.announcement_data.get('priority', 0)
        if priority > 50:  # 高优先级
            priority_label = QLabel("🔥")
            priority_label.setFont(QFont("Segoe UI Emoji", 16))
            priority_label.setFixedSize(30, 30)
            priority_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(priority_label)
        
    def _get_type_icon(self, announcement_type: str) -> str:
        """获取公告类型图标"""
        icons = {
            'info': '📢',
            'warning': '⚠️',
            'success': '✅',
            'error': '❌',
            'update': '🔄'
        }
        return icons.get(announcement_type, '📢')
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.announcement_data)
        super().mousePressEvent(event)


class AnnouncementDetailDialog(QWidget):
    """公告详情对话框"""
    
    def __init__(self, announcement_data: Dict, parent=None):
        super().__init__(parent)
        self.announcement_data = announcement_data
        self.setWindowTitle("公告详情")
        self.setFixedSize(600, 500)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        self.setStyleSheet(f"""
            QWidget {{
                background: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel(self.announcement_data.get('title', ''))
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; padding: 10px 0;")
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        # 元信息
        meta_layout = QHBoxLayout()
        
        # 类型
        type_label = QLabel(f"类型: {self.announcement_data.get('type', 'info')}")
        type_label.setFont(DS.get_font('body_sm'))
        type_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        meta_layout.addWidget(type_label)
        
        # 时间
        created_at = self.announcement_data.get('created_at', '')
        if created_at:
            time_label = QLabel(f"发布时间: {created_at}")
            time_label.setFont(DS.get_font('body_sm'))
            time_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
            meta_layout.addWidget(time_label)
        
        meta_layout.addStretch()
        layout.addLayout(meta_layout)
        
        # 内容
        content_area = QTextEdit()
        content_area.setPlainText(self.announcement_data.get('content', ''))
        content_area.setReadOnly(True)
        content_area.setFont(DS.get_font('body_md'))
        content_area.setStyleSheet(f"""
            QTextEdit {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                padding: 15px;
                color: {DS.COLORS['text_primary'].name()};
            }}
        """)
        layout.addWidget(content_area)
        
        # 关闭按钮
        close_btn = NeonButton("关闭", 'cyan', 'filled')
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)


class AnnouncementLoader(QThread):
    """公告加载线程"""
    
    announcements_loaded = pyqtSignal(list)  # 公告加载完成信号
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, api_url: str):
        super().__init__()
        self.api_url = api_url
        
    def run(self):
        """运行加载任务"""
        try:
            # 构建API请求URL
            url = f"{self.api_url}?action=get_announcements"
            
            # 发送请求
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            if data.get('success'):
                announcements = data.get('data', [])
                self.announcements_loaded.emit(announcements)
            else:
                self.error_occurred.emit(data.get('message', '获取公告失败'))
                
        except requests.RequestException as e:
            self.error_occurred.emit(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            self.error_occurred.emit(f"数据解析失败: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"未知错误: {str(e)}")


class PyQtAnnouncementsPage(QWidget):
    """PyQt系统公告页面"""
    
    def __init__(self, parent=None, main_window=None):
        super().__init__(parent)
        self.main_window = main_window
        self.logger = main_window.logger if main_window else None
        
        # 数据
        self.announcements = []
        self.loader_thread = None
        
        # 创建UI
        self._setup_ui()
        
        # 加载公告数据
        self._load_announcements()
        
    def _setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 页面标题
        self._create_header(main_layout)
        
        # 公告列表区域
        self._create_announcements_area(main_layout)
        
        # 底部操作区域
        self._create_footer(main_layout)

    def _create_header(self, parent_layout):
        """创建页面标题"""
        header_layout = QHBoxLayout()

        # 标题
        title_label = QLabel("📢 系统公告")
        title_label.setFont(DS.get_font('heading_xl'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 刷新按钮
        refresh_btn = NeonButton("🔄 刷新", 'cyan', 'outline')
        refresh_btn.clicked.connect(self._load_announcements)
        header_layout.addWidget(refresh_btn)

        parent_layout.addLayout(header_layout)

    def _create_announcements_area(self, parent_layout):
        """创建公告列表区域"""
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background: transparent;
            }}
            QScrollBar:vertical {{
                background: {DS.COLORS['bg_tertiary'].name()};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background: {DS.COLORS['neon_cyan'].name()};
                border-radius: 4px;
                min-height: 20px;
            }}
        """)

        # 公告容器
        self.announcements_container = QWidget()
        self.announcements_layout = QVBoxLayout(self.announcements_container)
        self.announcements_layout.setContentsMargins(0, 0, 0, 0)
        self.announcements_layout.setSpacing(10)

        # 加载状态标签
        self.status_label = QLabel("正在加载公告...")
        self.status_label.setFont(DS.get_font('body_md'))
        self.status_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; padding: 40px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.announcements_layout.addWidget(self.status_label)

        self.scroll_area.setWidget(self.announcements_container)
        parent_layout.addWidget(self.scroll_area)

    def _create_footer(self, parent_layout):
        """创建底部操作区域"""
        footer_layout = QHBoxLayout()

        # 统计信息
        self.stats_label = QLabel("共 0 条公告")
        self.stats_label.setFont(DS.get_font('body_sm'))
        self.stats_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        footer_layout.addWidget(self.stats_label)

        footer_layout.addStretch()

        # 帮助按钮
        help_btn = NeonButton("❓ 帮助", 'purple', 'outline')
        help_btn.clicked.connect(self._show_help)
        footer_layout.addWidget(help_btn)

        parent_layout.addLayout(footer_layout)

    def _get_api_url(self):
        """获取API地址 - 从登录界面保存的地址"""
        try:
            # 优先从安全管理器获取（最新的登录地址）
            if (self.main_window and
                hasattr(self.main_window, 'security_manager') and
                self.main_window.security_manager):
                server_address = self.main_window.security_manager.get_server_address()
                if server_address:
                    self.logger.info(f"从安全管理器获取API地址: {server_address}")
                    return server_address

            # 从配置文件获取用户保存的地址
            from utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            auth_server_url = config_manager.get('auth_server_url')

            if auth_server_url:
                self.logger.info(f"从配置文件获取API地址: {auth_server_url}")
                return auth_server_url

            # 默认地址
            default_url = 'http://127.0.0.1:81/api.php'
            self.logger.warning(f"使用默认API地址: {default_url}")
            return default_url

        except Exception as e:
            self.logger.error(f"获取API地址失败: {e}")
            return 'http://127.0.0.1:81/api.php'

    def _load_announcements(self):
        """加载公告数据"""
        if self.loader_thread and self.loader_thread.isRunning():
            return

        # 更新状态
        self.status_label.setText("正在加载公告...")
        self.status_label.show()

        # 清空现有公告
        self._clear_announcements()

        # 获取API URL - 从登录界面保存的地址
        api_url = self._get_api_url()

        # 启动加载线程
        self.loader_thread = AnnouncementLoader(api_url)
        self.loader_thread.announcements_loaded.connect(self._on_announcements_loaded)
        self.loader_thread.error_occurred.connect(self._on_load_error)
        self.loader_thread.start()

        if self.logger:
            self.logger.info("开始加载系统公告")

    def _on_announcements_loaded(self, announcements: List[Dict]):
        """公告加载完成"""
        self.announcements = announcements
        self._display_announcements()

        if self.logger:
            self.logger.info(f"成功加载 {len(announcements)} 条公告")

    def _on_load_error(self, error_message: str):
        """加载错误处理"""
        self.status_label.setText(f"加载失败: {error_message}")

        # 显示示例数据
        self._load_sample_announcements()

        if self.logger:
            self.logger.error(f"公告加载失败: {error_message}")

    def _load_sample_announcements(self):
        """加载示例公告数据"""
        sample_announcements = [
            {
                'id': 1,
                'title': '🎉 欢迎使用Augment续杯工具',
                'content': '感谢您使用Augment续杯工具！如有问题请加入QQ群：735821698 获取技术支持。\n\n主要功能：\n• 在线认证系统\n• 邮箱管理\n• VSCode清理\n• 系统配置\n• 论坛交流',
                'type': 'info',
                'priority': 100,
                'click_count': 1234,
                'created_at': '2025-06-28 10:00:00'
            },
            {
                'id': 2,
                'title': '🔧 系统维护通知',
                'content': '系统将在今晚22:00-23:00进行例行维护，期间可能影响服务使用，请提前做好准备。\n\n维护内容：\n• 数据库优化\n• 系统更新\n• 安全补丁\n• 性能优化',
                'type': 'warning',
                'priority': 90,
                'click_count': 567,
                'created_at': '2025-06-27 15:30:00'
            },
            {
                'id': 3,
                'title': '✨ 新功能上线',
                'content': '新增公告系统功能，管理员可以实时发布重要通知，用户界面将自动更新显示最新公告。\n\n新功能特性：\n• 实时公告推送\n• 多种公告类型\n• 优先级排序\n• 点击统计',
                'type': 'success',
                'priority': 80,
                'click_count': 890,
                'created_at': '2025-06-26 09:15:00'
            }
        ]

        self.announcements = sample_announcements
        self._display_announcements()

    def _display_announcements(self):
        """显示公告列表"""
        # 隐藏状态标签
        self.status_label.hide()

        # 清空现有公告
        self._clear_announcements()

        if not self.announcements:
            # 显示空状态
            empty_label = QLabel("暂无公告")
            empty_label.setFont(DS.get_font('body_lg'))
            empty_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; padding: 40px;")
            empty_label.setAlignment(Qt.AlignCenter)
            self.announcements_layout.addWidget(empty_label)
        else:
            # 显示公告卡片
            for announcement in self.announcements:
                card = AnnouncementCard(announcement)
                card.clicked.connect(self._show_announcement_detail)
                self.announcements_layout.addWidget(card)

        # 添加弹性空间
        self.announcements_layout.addStretch()

        # 更新统计信息
        self.stats_label.setText(f"共 {len(self.announcements)} 条公告")

    def _clear_announcements(self):
        """清空公告列表"""
        while self.announcements_layout.count():
            child = self.announcements_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def _show_announcement_detail(self, announcement_data: Dict):
        """显示公告详情"""
        detail_dialog = AnnouncementDetailDialog(announcement_data, self)
        detail_dialog.show()

        # 标记公告为已读（增加点击次数）
        self._mark_announcement_read(announcement_data.get('id'))

    def _mark_announcement_read(self, announcement_id: int):
        """标记公告为已读"""
        if not announcement_id:
            return

        try:
            # 获取API URL - 从登录界面保存的地址
            api_url = self._get_api_url()

            # 发送标记请求
            data = {'action': 'mark_announcement_read', 'announcement_id': announcement_id}
            response = requests.post(api_url, json=data, timeout=5)

            if self.logger:
                self.logger.info(f"标记公告 {announcement_id} 为已读")

        except Exception as e:
            if self.logger:
                self.logger.error(f"标记公告已读失败: {e}")

    def _show_help(self):
        """显示帮助信息"""
        help_text = """
系统公告页面使用说明：

📢 功能介绍：
• 查看系统最新公告和通知
• 支持多种公告类型（信息、警告、成功、错误、更新）
• 按优先级和时间排序显示
• 点击公告查看详细内容

🔄 操作说明：
• 点击"刷新"按钮获取最新公告
• 点击公告卡片查看详细内容
• 高优先级公告会显示火焰图标

❓ 如需帮助：
• 加入QQ群：735821698
• 访问官方网站获取更多信息
        """

        from PyQt5.QtWidgets import QMessageBox
        msg = QMessageBox(self)
        msg.setWindowTitle("帮助信息")
        msg.setText(help_text)
        msg.setIcon(QMessageBox.Information)
        msg.exec_()
