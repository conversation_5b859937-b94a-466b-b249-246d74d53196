<?php
// 调试admin.php页面问题
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Admin.php 调试测试</h1>";

try {
    echo "<h2>1. 检查admin.php文件</h2>";
    if (file_exists('admin.php')) {
        echo "✓ admin.php 文件存在<br>";
        $fileSize = filesize('admin.php');
        echo "文件大小: " . number_format($fileSize) . " 字节<br>";
    } else {
        echo "✗ admin.php 文件不存在<br>";
        exit;
    }
    
    echo "<h2>2. 检查PHP语法</h2>";
    $output = [];
    $returnCode = 0;
    exec('php -l admin.php 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✓ PHP语法检查通过<br>";
    } else {
        echo "✗ PHP语法错误:<br>";
        foreach ($output as $line) {
            echo htmlspecialchars($line) . "<br>";
        }
    }
    
    echo "<h2>3. 检查依赖文件</h2>";
    $dependencies = [
        'includes/Database.php',
        'includes/AuthManager.php', 
        'includes/Logger.php',
        'includes/SecurityConfig.php',
        'config/database.php'
    ];
    
    foreach ($dependencies as $file) {
        if (file_exists($file)) {
            echo "✓ {$file} 存在<br>";
        } else {
            echo "✗ {$file} 不存在<br>";
        }
    }
    
    echo "<h2>4. 测试包含文件</h2>";
    try {
        require_once 'includes/Database.php';
        echo "✓ Database.php 包含成功<br>";
    } catch (Exception $e) {
        echo "✗ Database.php 包含失败: " . $e->getMessage() . "<br>";
    }
    
    try {
        require_once 'includes/Logger.php';
        echo "✓ Logger.php 包含成功<br>";
    } catch (Exception $e) {
        echo "✗ Logger.php 包含失败: " . $e->getMessage() . "<br>";
    }
    
    try {
        require_once 'includes/SecurityConfig.php';
        echo "✓ SecurityConfig.php 包含成功<br>";
    } catch (Exception $e) {
        echo "✗ SecurityConfig.php 包含失败: " . $e->getMessage() . "<br>";
    }
    
    try {
        require_once 'config/database.php';
        echo "✓ database.php 配置包含成功<br>";
    } catch (Exception $e) {
        echo "✗ database.php 配置包含失败: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>5. 测试Session</h2>";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
        echo "✓ Session 启动成功<br>";
    } else {
        echo "✓ Session 已经启动<br>";
    }
    
    echo "<h2>6. 模拟登录</h2>";
    $_SESSION['admin_id'] = 2;
    $_SESSION['admin_username'] = 'admin';
    echo "✓ 模拟管理员登录成功<br>";
    
    echo "<h2>7. 测试数据库连接</h2>";
    if (isset($config)) {
        try {
            $database = new Database($config['host'], $config['database'], $config['username'], $config['password']);
            echo "✓ 数据库连接成功<br>";
        } catch (Exception $e) {
            echo "✗ 数据库连接失败: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>8. 创建上传目录</h2>";
    $uploadDir = __DIR__ . '/uploads/announcements/';
    if (!is_dir($uploadDir)) {
        if (mkdir($uploadDir, 0755, true)) {
            echo "✓ 成功创建上传目录: {$uploadDir}<br>";
        } else {
            echo "✗ 创建上传目录失败<br>";
        }
    } else {
        echo "✓ 上传目录已存在<br>";
    }
    
    echo "<h2>9. 测试完成</h2>";
    echo "现在可以尝试访问: <a href='admin.php?page=announcements' target='_blank'>admin.php?page=announcements</a><br>";
    
} catch (Exception $e) {
    echo "<h2>错误</h2>";
    echo "错误信息: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    echo "错误堆栈: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>
