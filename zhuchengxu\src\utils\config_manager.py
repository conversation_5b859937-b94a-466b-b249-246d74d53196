#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
用于读取和管理配置文件
"""

import json
import os
from typing import Any, Dict, Optional

try:
    from .logger import get_logger
except ImportError:
    import logging
    def get_logger():
        return logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
        """
        self.logger = get_logger()

        # 确定配置文件的绝对路径 - 直接使用main.py中设置的环境变量
        if not os.path.isabs(config_file):
            # 直接使用main.py中设置的配置目录环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                self.config_file = os.path.join(config_dir, config_file)
                self.logger.info(f"📁 使用main.py设置的配置目录: {self.config_file}")
            else:
                # 如果环境变量未设置，使用当前工作目录作为备选
                cwd = os.getcwd()
                self.config_file = os.path.join(cwd, config_file)
                self.logger.warning(f"⚠️ 环境变量未设置，使用当前工作目录: {self.config_file}")
        else:
            self.config_file = config_file
            self.logger.info(f"📁 使用绝对路径配置文件: {self.config_file}")
        
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                self.logger.info(f"✅ 配置文件加载成功: {self.config_file}")
            else:
                self.logger.warning(f"⚠️ 配置文件不存在: {self.config_file}")
                self._config = {}
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ 配置文件格式错误: {e}")
            self._config = {}
        except Exception as e:
            self.logger.error(f"❌ 加载配置文件失败: {e}")
            self._config = {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'auth_config.enabled'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
        except Exception as e:
            self.logger.error(f"获取配置失败 {key}: {e}")
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        try:
            keys = key.split('.')
            config = self._config
            
            # 创建嵌套字典结构
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置最终值
            config[keys[-1]] = value
            
            # 保存到文件
            self._save_config()
            
        except Exception as e:
            self.logger.error(f"设置配置失败 {key}: {e}")
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
            
            self.logger.debug(f"配置已保存: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def reload(self):
        """重新加载配置文件"""
        self._load_config()
    
    def has(self, key: str) -> bool:
        """
        检查配置键是否存在
        
        Args:
            key: 配置键
            
        Returns:
            是否存在
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return False
            
            return True
        except:
            return False


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config(key: str, default: Any = None) -> Any:
    """快捷方法：获取配置值"""
    return get_config_manager().get(key, default)


def set_config(key: str, value: Any):
    """快捷方法：设置配置值"""
    get_config_manager().set(key, value)


def has_config(key: str) -> bool:
    """快捷方法：检查配置是否存在"""
    return get_config_manager().has(key)
