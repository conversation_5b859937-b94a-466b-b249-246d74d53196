#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存管理器 - 自动管理页面缓存和内存使用
"""

import time
import threading
import weakref
from typing import Dict, Any, Optional, List
from PyQt5.QtCore import QObject, pyqtSignal, QTimer


class SmartCacheManager(QObject):
    """智能缓存管理器"""
    
    # 缓存状态信号
    cache_cleaned = pyqtSignal(int, int)  # 清理前数量, 清理后数量
    memory_warning = pyqtSignal(str, dict)
    
    def __init__(self, max_cache_size: int = 50):
        super().__init__()
        self.max_cache_size = max_cache_size  # MB
        self.page_cache = {}
        self.cache_metadata = {}
        self.access_times = {}
        self.cache_lock = threading.RLock()
        
        # 缓存策略配置 - 更激进的缓存策略
        self.CACHE_CONFIG = {
            'max_pages': 10,             # 增加到10个页面 (原来5个)
            'ttl_seconds': 1800,         # 延长到30分钟 (原来10分钟)
            'cleanup_interval': 300,     # 延长清理间隔到5分钟 (原来2分钟)
            'memory_check_interval': 120, # 延长内存检查到2分钟 (原来1分钟)
            'core_pages': ['home', 'mailbox', 'vscode', 'forum', 'announcements'] # 核心页面列表
        }
        
        # 启动自动清理
        self._setup_auto_cleanup()
        print("✅ 智能缓存管理器已启动 - 高性能模式")
    
    def _setup_auto_cleanup(self):
        """设置自动清理定时器"""
        # 缓存清理定时器
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self._auto_cleanup)
        self.cleanup_timer.start(self.CACHE_CONFIG['cleanup_interval'] * 1000)
        
        # 内存检查定时器
        self.memory_timer = QTimer()
        self.memory_timer.timeout.connect(self._check_memory_usage)
        self.memory_timer.start(self.CACHE_CONFIG['memory_check_interval'] * 1000)
    
    def cache_page(self, page_id: str, page_widget, metadata: Dict[str, Any] = None):
        """缓存页面"""
        try:
            with self.cache_lock:
                current_time = time.time()
                
                # 检查缓存数量限制 - 更宽松的策略
                if len(self.page_cache) >= self.CACHE_CONFIG['max_pages']:
                    # 只有在真正需要时才驱逐
                    if page_id not in self.page_cache:  # 新页面才驱逐
                        self._evict_oldest_page()
                
                # 直接存储对象引用（强引用），确保页面不会被意外删除
                self.page_cache[page_id] = page_widget
                self.cache_metadata[page_id] = {
                    'created_time': current_time,
                    'last_access': current_time,
                    'access_count': 1,
                    'size_estimate': self._estimate_widget_size(page_widget),
                    'metadata': metadata or {}
                }
                self.access_times[page_id] = current_time
                
                print(f"📦 页面已缓存: {page_id}")
                
        except Exception as e:
            print(f"❌ 缓存页面失败: {e}")
    
    def get_cached_page(self, page_id: str):
        """获取缓存的页面"""
        try:
            with self.cache_lock:
                # 账号库页面特殊处理 - 不应该从缓存中获取
                if page_id == 'account_database':
                    print(f"⚠️ 账号库页面不应从缓存获取，返回None")
                    return None
                
                if page_id not in self.page_cache:
                    return None
                
                # 直接获取缓存的对象
                page_widget = self.page_cache[page_id]

                # 检查对象是否仍然有效
                try:
                    # 尝试访问对象属性来验证有效性
                    _ = page_widget.objectName()
                    
                    # 检查线程安全 - 只有在同一线程中才能使用
                    from PyQt5.QtCore import QThread
                    current_thread = QThread.currentThread()
                    if page_widget.thread() != current_thread:
                        print(f"⚠️ 线程不匹配：缓存页面 {page_id} 在不同线程中，返回None")
                        # 不返回该页面，避免线程安全问题
                        return None
                except RuntimeError:
                    # 对象已被删除，清理缓存
                    self._remove_cache_entry(page_id)
                    return None
                
                # 更新访问信息
                current_time = time.time()
                if page_id in self.cache_metadata:
                    self.cache_metadata[page_id]['last_access'] = current_time
                    self.cache_metadata[page_id]['access_count'] += 1
                self.access_times[page_id] = current_time
                
                print(f"📦 从缓存获取页面: {page_id}")
                return page_widget
                
        except Exception as e:
            print(f"❌ 获取缓存页面失败: {e}")
            return None
    
    def remove_page_cache(self, page_id: str):
        """移除页面缓存"""
        try:
            with self.cache_lock:
                self._remove_cache_entry(page_id)
                print(f"🗑️ 已移除页面缓存: {page_id}")
                
        except Exception as e:
            print(f"❌ 移除页面缓存失败: {e}")
    
    def _remove_cache_entry(self, page_id: str):
        """移除缓存条目（内部方法）"""
        self.page_cache.pop(page_id, None)
        self.cache_metadata.pop(page_id, None)
        self.access_times.pop(page_id, None)
    
    def _evict_oldest_page(self):
        """智能驱逐页面 - 优先驱逐非核心页面"""
        if not self.access_times:
            return

        # 核心页面优先级（不轻易驱逐）
        core_pages = self.CACHE_CONFIG.get('core_pages', ['home', 'mailbox', 'vscode'])

        # 先尝试驱逐非核心页面
        non_core_pages = {k: v for k, v in self.access_times.items() if k not in core_pages}
        if non_core_pages:
            oldest_page = min(non_core_pages.items(), key=lambda x: x[1])[0]
        else:
            # 如果只有核心页面，驱逐最旧的
            oldest_page = min(self.access_times.items(), key=lambda x: x[1])[0]

        self._remove_cache_entry(oldest_page)
        print(f"🗑️ 智能驱逐页面: {oldest_page}")
    
    def _auto_cleanup(self):
        """自动清理过期缓存"""
        try:
            with self.cache_lock:
                current_time = time.time()
                expired_pages = []
                
                for page_id, metadata in self.cache_metadata.items():
                    # 核心页面不主动清理，即使超过TTL
                    if page_id in self.CACHE_CONFIG.get('core_pages', []):
                        continue
                        
                    # 检查TTL
                    if current_time - metadata['created_time'] > self.CACHE_CONFIG['ttl_seconds']:
                        expired_pages.append(page_id)
                    
                    # 检查对象是否还有效
                    elif page_id in self.page_cache:
                        try:
                            page_widget = self.page_cache[page_id]
                            _ = page_widget.objectName()  # 测试对象有效性
                        except (RuntimeError, AttributeError):
                            expired_pages.append(page_id)
                
                # 清理过期页面
                cleaned_count = 0
                for page_id in expired_pages:
                    self._remove_cache_entry(page_id)
                    cleaned_count += 1
                
                if cleaned_count > 0:
                    print(f"🧹 自动清理了 {cleaned_count} 个过期页面缓存")
                    self.cache_cleaned.emit(len(self.page_cache) + cleaned_count, len(self.page_cache))
                
        except Exception as e:
            print(f"❌ 自动清理缓存失败: {e}")
    
    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            # 估算缓存占用的内存
            total_size = sum(
                metadata.get('size_estimate', 0) 
                for metadata in self.cache_metadata.values()
            )
            
            if total_size > self.max_cache_size * 1024 * 1024:  # 转换为字节
                warning_msg = f"缓存内存使用过高: {total_size / 1024 / 1024:.1f}MB"
                self.memory_warning.emit(warning_msg, {
                    'current_size_mb': total_size / 1024 / 1024,
                    'max_size_mb': self.max_cache_size,
                    'cached_pages': len(self.page_cache)
                })
                
                # 强制清理一些缓存
                self._force_cleanup()
                
        except Exception as e:
            print(f"❌ 内存使用检查失败: {e}")
    
    def _force_cleanup(self):
        """强制清理缓存 - 保留核心页面"""
        try:
            with self.cache_lock:
                # 保留核心页面和最近访问的页面
                core_pages = self.CACHE_CONFIG.get('core_pages', ['home', 'mailbox', 'vscode'])
                
                # 如果页面数量超过限制，开始清理
                if len(self.page_cache) > self.CACHE_CONFIG['max_pages']:
                    # 按访问时间排序
                    sorted_pages = sorted(
                        self.access_times.items(), 
                        key=lambda x: x[1], 
                        reverse=True
                    )
                    
                    # 获取要保留的页面（核心页面+最近访问的页面）
                    pages_to_keep = []
                    for page_id, _ in sorted_pages:
                        if page_id in core_pages or len(pages_to_keep) < 3:  # 保留核心页面和最近3个页面
                            pages_to_keep.append(page_id)
                    
                    # 计算要删除的页面
                    pages_to_remove = [
                        page_id for page_id in self.page_cache.keys() 
                        if page_id not in pages_to_keep
                    ]
                    
                    # 删除非核心页面
                    for page_id in pages_to_remove:
                        self._remove_cache_entry(page_id)
                    
                    if pages_to_remove:
                        print(f"🧹 强制清理了 {len(pages_to_remove)} 个页面缓存，保留 {len(pages_to_keep)} 个页面")
                
        except Exception as e:
            print(f"❌ 强制清理缓存失败: {e}")
    
    def _estimate_widget_size(self, widget) -> int:
        """估算组件大小（字节）"""
        try:
            # 简单的大小估算
            base_size = 1024  # 基础大小 1KB
            
            if hasattr(widget, 'children'):
                child_count = len(widget.children())
                base_size += child_count * 512  # 每个子组件 512 字节
            
            if hasattr(widget, 'size'):
                size = widget.size()
                pixel_count = size.width() * size.height()
                base_size += pixel_count * 4  # 每像素 4 字节（RGBA）
            
            return base_size
            
        except:
            return 1024  # 默认 1KB
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            total_size = sum(
                metadata.get('size_estimate', 0) 
                for metadata in self.cache_metadata.values()
            )
            
            return {
                'cached_pages': len(self.page_cache),
                'total_size_mb': total_size / 1024 / 1024,
                'max_size_mb': self.max_cache_size,
                'usage_percent': (total_size / (self.max_cache_size * 1024 * 1024)) * 100,
                'pages': list(self.page_cache.keys())
            }
    
    def clear_all_cache(self):
        """清空所有缓存"""
        with self.cache_lock:
            cache_count = len(self.page_cache)
            self.page_cache.clear()
            self.cache_metadata.clear()
            self.access_times.clear()
            print(f"🧹 已清空所有缓存 ({cache_count} 个页面)")
    
    def shutdown(self):
        """关闭缓存管理器"""
        if hasattr(self, 'cleanup_timer'):
            self.cleanup_timer.stop()
        if hasattr(self, 'memory_timer'):
            self.memory_timer.stop()
        self.clear_all_cache()
        print("✅ 智能缓存管理器已关闭")


# 全局缓存管理器实例
_cache_manager = None

def get_cache_manager() -> SmartCacheManager:
    """获取全局缓存管理器"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = SmartCacheManager()
    return _cache_manager

def cache_page(page_id: str, page_widget, metadata: Dict[str, Any] = None):
    """缓存页面"""
    manager = get_cache_manager()
    manager.cache_page(page_id, page_widget, metadata)

def get_cached_page(page_id: str):
    """获取缓存页面"""
    manager = get_cache_manager()
    return manager.get_cached_page(page_id)

def remove_page_cache(page_id: str):
    """移除页面缓存"""
    manager = get_cache_manager()
    manager.remove_page_cache(page_id)

def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计"""
    manager = get_cache_manager()
    return manager.get_cache_stats()
