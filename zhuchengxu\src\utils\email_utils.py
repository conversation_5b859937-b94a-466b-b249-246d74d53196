#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱工具模块
负责邮箱生成、验证码提取等功能
"""

import re
import random
import string
from typing import Optional, List, Dict, Any


def generate_random_email(domain: str = "hwsyyds.xyz") -> str:
    """
    生成随机邮箱地址
    
    Args:
        domain: 邮箱域名
        
    Returns:
        随机邮箱地址
    """
    # 生成12位随机字符串
    random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
    return f"{random_string}@{domain}"


def extract_verification_code(content: str) -> Optional[str]:
    """
    从邮件内容中提取验证码（专门匹配6位数字）

    Args:
        content: 邮件内容

    Returns:
        验证码字符串，如果未找到则返回None
    """
    if not content:
        return None

    # 验证码匹配模式，专门匹配6位数字（简化版本）
    patterns = [
        # 高优先级：有明确验证码关键词的
        r'验证码[：:\s]*(\d{6})',        # 中文验证码后跟6位数字
        r'verification code[：:\s]*(\d{6})',  # 英文验证码后跟6位数字
        r'code[：:\s]*(\d{6})',         # code后跟6位数字
        r'pin[：:\s]*(\d{6})',          # pin后跟6位数字
        r'otp[：:\s]*(\d{6})',          # otp后跟6位数字

        # 使用与油猴脚本相同的正则表达式
        r'(?<![a-zA-Z@.])\b(\d{6})\b',  # 6位数字，前后不能是字母、@或点号
    ]

    for pattern in patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            code = match.group(1)
            # 验证是否确实是6位数字
            if len(code) == 6 and code.isdigit():
                return code

    return None


def _is_likely_not_verification_code(content: str, code: str) -> bool:
    """
    检查6位数字是否可能不是验证码

    Args:
        content: 邮件内容
        code: 6位数字代码

    Returns:
        如果可能不是验证码则返回True
    """
    # 检查是否在价格上下文中
    if re.search(rf'[\$￥€£]\s*{code}', content):
        return True

    # 检查是否在日期上下文中（虽然我们的正则应该已经排除了8位日期）
    if re.search(rf'{code}\d{{2}}', content):  # 如果后面还有数字，可能是日期的一部分
        return True

    # 检查是否在电话号码中
    if re.search(rf'\d{code}', content) or re.search(rf'{code}\d', content):
        return True

    return False


def validate_email_format(email: str) -> bool:
    """
    验证邮箱格式是否正确
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否为有效邮箱格式
    """
    if not email or not isinstance(email, str):
        return False
    
    # 简单的邮箱格式验证
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def parse_email_address(email: str) -> Dict[str, str]:
    """
    解析邮箱地址，分离用户名和域名
    
    Args:
        email: 邮箱地址
        
    Returns:
        包含username和domain的字典
    """
    if not validate_email_format(email):
        return {'username': '', 'domain': ''}
    
    username, domain = email.split('@', 1)
    return {
        'username': username,
        'domain': domain
    }


def format_email_display(email_data: Dict[str, Any]) -> Dict[str, str]:
    """
    格式化邮件显示数据

    Args:
        email_data: 邮件数据字典

    Returns:
        格式化后的显示数据字典
    """
    subject = email_data.get('subject', '(无主题)')
    sender = email_data.get('sender', '(未知发件人)')
    verification_code = email_data.get('verification_code', '')
    content = email_data.get('content', '')
    date = email_data.get('date', '(未知时间)')

    # 截断过长的主题（用于列表显示）
    display_subject = subject
    if len(subject) > 30:
        display_subject = subject[:27] + "..."

    # 截断过长的发件人（用于列表显示）
    display_sender = sender
    if len(sender) > 20:
        display_sender = sender[:17] + "..."

    # 清理内容
    clean_content = clean_email_content(content) if content else '(无内容)'

    return {
        'subject': display_subject,
        'sender': display_sender,
        'time': date,
        'content': clean_content,
        'verification_code': verification_code if verification_code and verification_code != '无验证码' else None,
        'full_subject': subject,
        'full_sender': sender
    }


def extract_sender_info(sender: str) -> Dict[str, str]:
    """
    提取发件人信息
    
    Args:
        sender: 发件人字符串
        
    Returns:
        包含name和email的字典
    """
    if not sender:
        return {'name': '未知发件人', 'email': ''}
    
    # <AUTHOR> <EMAIL>" 或 "<EMAIL>"
    match = re.match(r'^(.+?)\s*<(.+?)>$', sender.strip())
    if match:
        name = match.group(1).strip().strip('"\'')
        email = match.group(2).strip()
        return {'name': name, 'email': email}
    else:
        # 如果是纯邮箱地址
        if validate_email_format(sender.strip()):
            return {'name': sender.strip(), 'email': sender.strip()}
        else:
            return {'name': sender.strip(), 'email': ''}


def clean_email_content(content: str) -> str:
    """
    清理邮件内容，移除HTML标签和多余空白
    
    Args:
        content: 原始邮件内容
        
    Returns:
        清理后的内容
    """
    if not content:
        return ''
    
    # 移除HTML标签
    content = re.sub(r'<[^>]+>', '', content)
    
    # 移除多余的空白字符
    content = re.sub(r'\s+', ' ', content)
    
    # 移除首尾空白
    content = content.strip()
    
    return content


def is_verification_email(email_data: Dict[str, Any]) -> bool:
    """
    判断是否为验证邮件
    
    Args:
        email_data: 邮件数据
        
    Returns:
        是否为验证邮件
    """
    subject = email_data.get('subject', '').lower()
    content = email_data.get('content', '').lower()
    
    # 验证邮件关键词
    verification_keywords = [
        'verification', 'verify', 'confirm', 'activate',
        '验证', '确认', '激活', 'code', '验证码'
    ]
    
    # 检查主题或内容是否包含验证关键词
    for keyword in verification_keywords:
        if keyword in subject or keyword in content:
            return True
    
    # 检查是否包含验证码
    verification_code = email_data.get('verification_code')
    if verification_code and verification_code != '无验证码':
        return True
    
    return False


def sort_emails_by_priority(emails: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    按优先级排序邮件（验证邮件优先）
    
    Args:
        emails: 邮件列表
        
    Returns:
        排序后的邮件列表
    """
    def email_priority(email):
        # 验证邮件优先级最高
        if is_verification_email(email):
            return 0
        # 其他邮件
        return 1
    
    return sorted(emails, key=email_priority)
