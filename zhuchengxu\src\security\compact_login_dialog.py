#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧凑版登录对话框
确保登录按钮可见
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import re


class CompactLoginDialog:
    """紧凑版登录对话框"""
    
    def __init__(self, parent=None):
        self.result = None
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.root.title("Augment续杯工具 - 用户登录")
        # 先设置一个临时大小，稍后会动态调整
        self.root.geometry("480x600")
        self.root.resizable(True, True)
        self.root.minsize(450, 500)

        # 设置窗口样式
        self.root.configure(bg='#f8f9fa')

        # 居中显示
        if parent:
            self.root.transient(parent)
            self.root.grab_set()

        # 服务器确认状态
        self.server_confirmed = False
        self.saved_server_url = None

        # 加载保存的服务器地址
        self._load_saved_server()

        self._create_widgets()
        self._center_window()

    def _load_saved_server(self):
        """加载保存的服务器地址"""
        try:
            try:
                from src.utils.config_manager import ConfigManager
            except ImportError:
                from utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            self.saved_server_url = config_manager.get('auth_server_url', '')
        except:
            self.saved_server_url = ''

    def _save_server_url(self, url):
        """保存服务器地址"""
        try:
            try:
                from src.utils.config_manager import ConfigManager
            except ImportError:
                from utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            config_manager.set('auth_server_url', url)
            self.saved_server_url = url
        except Exception as e:
            print(f"保存服务器地址失败: {e}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主容器 - 减少底部边距
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(15, 5))
        
        # 简化的头部
        self._create_simple_header(main_frame)
        
        # 服务器配置
        self._create_server_section(main_frame)
        
        # 登录表单
        self._create_login_form(main_frame)

        # 登录按钮（重点）
        self._create_login_button(main_frame)
        
        # 绑定事件
        self._bind_events()
    
    def _create_simple_header(self, parent):
        """创建简化头部"""
        header_frame = tk.Frame(parent, bg='#f8f9fa')
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 标题
        title_label = tk.Label(header_frame, text="🔐 Augment续杯工具",
                              font=('Microsoft YaHei UI', 16, 'bold'),
                              fg='#212529', bg='#f8f9fa')
        title_label.pack()
        
        subtitle_label = tk.Label(header_frame, text="用户登录认证",
                                 font=('Microsoft YaHei UI', 10),
                                 fg='#6c757d', bg='#f8f9fa')
        subtitle_label.pack(pady=(3, 0))
    
    def _create_server_section(self, parent):
        """创建服务器配置区域"""
        server_frame = tk.LabelFrame(parent, text=" 🌐 服务器配置 ",
                                   font=('Microsoft YaHei UI', 10, 'bold'),
                                   fg='#495057', bg='#f8f9fa')
        server_frame.pack(fill=tk.X, pady=(0, 10))

        # 服务器地址标签和提示
        addr_label_frame = tk.Frame(server_frame, bg='#f8f9fa')
        addr_label_frame.pack(fill=tk.X, padx=10, pady=(8, 3))

        tk.Label(addr_label_frame, text="服务器地址:",
                font=('Microsoft YaHei UI', 10),
                fg='#495057', bg='#f8f9fa').pack(side=tk.LEFT)

        # 如果有保存的地址，显示提示
        if self.saved_server_url:
            tk.Label(addr_label_frame, text=f"(上次使用: {self.saved_server_url})",
                    font=('Microsoft YaHei UI', 8),
                    fg='#6c757d', bg='#f8f9fa').pack(side=tk.LEFT, padx=(10, 0))

        # 地址输入和确认
        addr_frame = tk.Frame(server_frame, bg='#f8f9fa')
        addr_frame.pack(fill=tk.X, padx=10, pady=(0, 8))

        # 默认为空，用户需要手动输入
        self.server_var = tk.StringVar(value="")
        self.server_entry = tk.Entry(addr_frame, textvariable=self.server_var,
                                    font=('Microsoft YaHei UI', 10))
        self.server_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=4)

        # 如果有保存的地址，添加"使用上次"按钮
        if self.saved_server_url:
            use_last_btn = tk.Button(addr_frame, text="使用上次",
                                   font=('Microsoft YaHei UI', 8),
                                   fg='#007bff', bg='#e3f2fd',
                                   command=self._use_last_server)
            use_last_btn.pack(side=tk.RIGHT, padx=(5, 5), ipady=2, ipadx=6)

        self.confirm_button = tk.Button(addr_frame, text="确认",
                                    font=('Microsoft YaHei UI', 9),
                                    fg='#28a745', bg='#e8f5e9',
                                    command=self._confirm_server)
        self.confirm_button.pack(side=tk.RIGHT, padx=(5, 0), ipady=2, ipadx=8)

        # 状态显示
        if self.saved_server_url:
            initial_status = f"请输入服务器地址并点击确认，或使用上次地址: {self.saved_server_url}"
        else:
            initial_status = "请输入服务器地址并点击确认"

        self.status_var = tk.StringVar(value=initial_status)
        self.status_label = tk.Label(server_frame, textvariable=self.status_var,
                                    font=('Microsoft YaHei UI', 9),
                                    fg='#6c757d', bg='#f8f9fa',
                                    wraplength=400, justify='left')
        self.status_label.pack(fill=tk.X, padx=10, pady=(0, 8))
    
    def _create_login_form(self, parent):
        """创建登录表单"""
        form_frame = tk.LabelFrame(parent, text=" 👤 登录信息 ",
                                 font=('Microsoft YaHei UI', 10, 'bold'),
                                 fg='#495057', bg='#f8f9fa')
        form_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 用户名
        tk.Label(form_frame, text="用户名:",
                font=('Microsoft YaHei UI', 10),
                fg='#495057', bg='#f8f9fa').pack(anchor=tk.W, padx=10, pady=(8, 3))
        
        self.username_var = tk.StringVar()
        self.username_entry = tk.Entry(form_frame, textvariable=self.username_var,
                                      font=('Microsoft YaHei UI', 10))
        self.username_entry.pack(fill=tk.X, padx=10, pady=(0, 8), ipady=4)
        
        # 密码
        tk.Label(form_frame, text="密码:",
                font=('Microsoft YaHei UI', 10),
                fg='#495057', bg='#f8f9fa').pack(anchor=tk.W, padx=10, pady=(0, 3))

        self.password_var = tk.StringVar()
        self.password_entry = tk.Entry(form_frame, textvariable=self.password_var,
                                      font=('Microsoft YaHei UI', 10), show="*")
        self.password_entry.pack(fill=tk.X, padx=10, pady=(0, 8), ipady=4)

        # QQ号（必填）
        tk.Label(form_frame, text="QQ号（必填）:",
                font=('Microsoft YaHei UI', 10),
                fg='#495057', bg='#f8f9fa').pack(anchor=tk.W, padx=10, pady=(0, 3))

        self.qq_var = tk.StringVar()
        self.qq_entry = tk.Entry(form_frame, textvariable=self.qq_var,
                                font=('Microsoft YaHei UI', 10))
        self.qq_entry.pack(fill=tk.X, padx=10, pady=(0, 8), ipady=4)

        # QQ号说明
        qq_help = tk.Label(form_frame, text="⚠️ 首次登录必须填写QQ号，方便后续技术支持",
                          font=('Microsoft YaHei UI', 8),
                          fg='#dc3545', bg='#f8f9fa')
        qq_help.pack(anchor=tk.W, padx=10, pady=(0, 8))
        
        # 选项
        options_frame = tk.Frame(form_frame, bg='#f8f9fa')
        options_frame.pack(fill=tk.X, padx=10, pady=(0, 8))
        
        self.remember_var = tk.BooleanVar(value=True)
        remember_check = tk.Checkbutton(options_frame, text="记住服务器",
                                       variable=self.remember_var,
                                       font=('Microsoft YaHei UI', 9),
                                       fg='#495057', bg='#f8f9fa')
        remember_check.pack(side=tk.LEFT)


    def _create_login_button(self, parent):
        """创建登录按钮区域"""
        button_frame = tk.Frame(parent, bg='#f8f9fa')
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 适中大小的登录按钮
        self.login_button = tk.Button(button_frame, text="🔐 立即登录",
                                     font=('Microsoft YaHei UI', 12, 'bold'),
                                     fg='white', bg='#007bff',
                                     activeforeground='white', activebackground='#0056b3',
                                     relief='flat', borderwidth=0,
                                     cursor='hand2',
                                     command=self._on_login)
        self.login_button.pack(fill=tk.X, pady=(0, 10), ipady=8)


        
        # 次要按钮
        secondary_frame = tk.Frame(button_frame, bg='#f8f9fa')
        secondary_frame.pack(fill=tk.X)
        
        self.cancel_button = tk.Button(secondary_frame, text="取消",
                                      font=('Microsoft YaHei UI', 10),
                                      fg='#6c757d', bg='#e9ecef',
                                      command=self._on_cancel)
        self.cancel_button.pack(side=tk.LEFT, ipady=6, ipadx=15)
        
        help_button = tk.Button(secondary_frame, text="❓ 帮助",
                               font=('Microsoft YaHei UI', 10),
                               fg='#007bff', bg='#f8f9fa',
                               command=self._show_help)
        help_button.pack(side=tk.RIGHT, ipady=6, ipadx=15)

        # 显眼的QQ群提示（热更新支持）
        qq_frame = tk.Frame(button_frame, bg='#fff3cd', relief='solid', borderwidth=1)
        qq_frame.pack(fill=tk.X, pady=(10, 0))

        # 获取QQ群号（热更新）
        qq_number = self._get_qq_config()
        self.qq_label = tk.Label(qq_frame, text=f"💬 获取服务器地址和账号，请加Q群: {qq_number}",
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           fg='#856404', bg='#fff3cd')
        self.qq_label.pack(pady=8)

        # 安全提示区域 - 更紧凑的布局
        security_frame = tk.LabelFrame(button_frame, text=" 🛡️ 安全提示 ",
                                     font=('Microsoft YaHei UI', 9, 'bold'),
                                     fg='#495057', bg='#f8f9fa')
        security_frame.pack(fill=tk.X, pady=(10, 0))

        # 使用更紧凑的提示信息
        security_tips = [
            "🔐 3层安全验证 🔒 数据加密传输",
            "⚠️ 避免公共网络 🚫 保护登录凭据"
        ]

        for tip in security_tips:
            tip_label = tk.Label(security_frame, text=tip,
                               font=('Microsoft YaHei UI', 8),
                               fg='#6c757d', bg='#f8f9fa')
            tip_label.pack(anchor=tk.W, padx=10, pady=2)

        # 快捷键提示
        shortcut_frame = tk.Frame(button_frame, bg='#f8f9fa')
        shortcut_frame.pack(fill=tk.X, pady=(8, 0))

        shortcut_label = tk.Label(shortcut_frame, text="⌨️ 快捷键: Enter-登录 | Esc-取消",
                                font=('Microsoft YaHei UI', 8),
                                fg='#868e96', bg='#f8f9fa')
        shortcut_label.pack()

        # 版本信息
        version_frame = tk.Frame(button_frame, bg='#f8f9fa')
        version_frame.pack(fill=tk.X, pady=(5, 0))

        version_label = tk.Label(version_frame, text="Augment续杯工具 v1.0.0 | 安全认证版本",
                               font=('Microsoft YaHei UI', 8),
                               fg='#adb5bd', bg='#f8f9fa')
        version_label.pack()

        # 底部装饰线 - 紧贴底部
        separator = tk.Frame(button_frame, height=1, bg='#dee2e6')
        separator.pack(fill=tk.X, pady=(8, 0))
    
    def _bind_events(self):
        """绑定事件"""
        self.root.bind('<Return>', lambda e: self._on_login())
        self.root.bind('<Escape>', lambda e: self._on_cancel())
        
        # 设置焦点
        self.username_entry.focus()
    
    def _center_window(self):
        """窗口居中并完美贴合内容"""
        # 强制更新所有组件的布局
        self.root.update_idletasks()

        # 获取内容的实际需要大小
        req_width = self.root.winfo_reqwidth()
        req_height = self.root.winfo_reqheight()

        # 设置固定宽度，高度完美贴合内容
        width = 480
        height = req_height + 20  # 添加少量边距

        # 计算居中位置
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)

        # 应用完美贴合的尺寸
        self.root.geometry(f"{width}x{height}+{x}+{y}")

        # 再次更新确保布局正确
        self.root.update_idletasks()

        print(f"🎯 完美贴合: 内容需要{req_height}px，设置为{height}px")

    def _get_qq_config(self):
        """获取QQ群配置（热更新）"""
        try:
            # 尝试从服务器获取QQ配置
            server_url = self.server_var.get().strip()
            if server_url:
                import requests

                # 构造API地址
                if not server_url.endswith('/api.php'):
                    api_url = server_url.rstrip('/') + '/api.php'
                else:
                    api_url = server_url

                # 获取QQ配置
                response = requests.get(f"{api_url}?action=get_qq_config", timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        qq_config = data.get('data', {})
                        qq_number = qq_config.get('qq_group_number', '735821698')

                        # 更新QQ群号显示
                        if hasattr(self, 'qq_label'):
                            self.qq_label.config(text=f"💬 获取服务器地址和账号，请加Q群: {qq_number}")

                        return qq_number
        except:
            pass  # 忽略错误，使用默认值

        return '735821698'  # 默认QQ群号

    def _use_last_server(self):
        """使用上次保存的服务器地址"""
        if self.saved_server_url:
            self.server_var.set(self.saved_server_url)
            self.status_var.set(f"已填入上次地址: {self.saved_server_url}，请点击确认")

    def _save_confirmed_server(self, server_url):
        """保存确认成功的服务器地址"""
        try:
            # 保存到配置文件
            self._save_server_url(server_url)
            print(f"✅ 服务器地址已保存: {server_url}")
        except Exception as e:
            print(f"⚠️ 保存服务器地址失败: {e}")

    def _validate_server_url(self, url):
        """验证服务器地址"""
        pattern = r'^https?://[\w\.-]+(:\d+)?(/.*)?$'
        return re.match(pattern, url) is not None

    def _validate_qq_number(self, qq):
        """验证QQ号格式"""
        if not qq:
            return True  # 空值是允许的

        # QQ号应该是5-11位数字
        return qq.isdigit() and 5 <= len(qq) <= 11
    
    def _confirm_server(self):
        """确认服务器并获取配置"""
        server_url = self.server_var.get().strip()
        if not server_url:
            self.status_var.set("❌ 请输入服务器地址")
            return

        if not self._validate_server_url(server_url):
            self.status_var.set("❌ 地址格式不正确")
            return

        self.status_var.set("🔄 验证服务器中...")
        self.confirm_button.config(state="disabled", text="...")

        def confirm_in_thread():
            try:
                import requests
                import hashlib
                import time
                import json

                # 创建会话以保持cookie（解决会话丢失问题）
                session = requests.Session()
                session.headers.update({
                    'User-Agent': 'AugmentTool/1.0.0',
                    'Content-Type': 'application/json'
                })

                # 第1层：基础连接测试
                self.root.after(0, lambda: self.status_var.set("🔄 第1层：验证服务器连接..."))

                # 处理URL，避免重复的/api.php
                if server_url.endswith('/api.php'):
                    api_url = server_url
                else:
                    api_url = f"{server_url.rstrip('/')}/api.php"

                # 测试服务器是否响应
                try:
                    response = session.get(api_url + "?action=ping", timeout=5)

                    if response.status_code != 200:
                        self.root.after(0, lambda: self.status_var.set("❌ 服务器无响应"))
                        return

                    # 尝试解析响应
                    try:
                        ping_result = response.json()
                    except Exception as e:
                        print(f"⚠️ Ping响应解析失败: {str(e)}")
                        print(f"📄 原始响应: {response.text[:100]}...")

                except Exception as e:
                    print(f"❌ 连接失败: {str(e)}")
                    self.root.after(0, lambda: self.status_var.set("❌ 无法连接到服务器"))
                    return

                # 第2层：密钥交换和协议验证
                self.root.after(0, lambda: self.status_var.set("🔄 第2层：密钥交换验证..."))

                # 生成安全的客户端随机数
                import secrets
                client_nonce = secrets.token_hex(16)  # 32字符的安全随机数

                # 请求服务器公钥和挑战
                handshake_data = {
                    "action": "handshake",
                    "client_nonce": client_nonce,
                    "protocol_version": "1.0"
                }

                server_session_id = None
                try:
                    response = session.post(api_url, json=handshake_data, timeout=10)  # 使用session保持cookie

                    if response.status_code == 200:
                        try:
                            result = response.json()
                            # 检查result是否为None或非字典
                            if result is None or not isinstance(result, dict):
                                self.root.after(0, lambda: self.status_var.set("❌ 服务器响应格式错误"))
                                return
                        except ValueError as e:
                            print(f"❌ 握手响应JSON解析失败: {str(e)}")
                            print(f"📄 原始响应: {response.text[:200]}...")
                            self.root.after(0, lambda: self.status_var.set("❌ 服务器响应解析失败"))
                            return

                        if not result.get('success'):
                            error_code = result.get('code', 'UNKNOWN')
                            if error_code == 'INVALID_NONCE':
                                self.root.after(0, lambda: self.status_var.set("❌ 客户端随机数无效"))
                            elif error_code == 'UNSUPPORTED_PROTOCOL':
                                self.root.after(0, lambda: self.status_var.set("❌ 协议版本不支持"))
                            else:
                                self.root.after(0, lambda: self.status_var.set("❌ 服务器握手失败"))
                            return

                        # 从data字段中获取握手数据
                        data = result.get('data', {})
                        server_nonce = data.get('server_nonce')
                        server_challenge = data.get('challenge')
                        expires_in = data.get('expires_in', 300)  # 默认5分钟
                        server_session_id = data.get('session_id')  # 获取会话ID

                        if not server_nonce or not server_challenge:
                            self.root.after(0, lambda: self.status_var.set("❌ 密钥交换失败"))
                            return

                        # 记录握手信息用于调试
                        print(f"🔐 握手成功: SessionID={server_session_id}, ExpiresIn={expires_in}s")

                    else:
                        self.root.after(0, lambda: self.status_var.set(f"❌ 握手协议错误: HTTP {response.status_code}"))
                        return
                except requests.exceptions.Timeout:
                    self.root.after(0, lambda: self.status_var.set("❌ 密钥交换超时"))
                    return
                except requests.exceptions.ConnectionError:
                    self.root.after(0, lambda: self.status_var.set("❌ 无法连接到服务器"))
                    return
                except Exception as e:
                    error_msg = f"❌ 握手异常: {str(e)[:20]}"
                    self.root.after(0, lambda msg=error_msg: self.status_var.set(msg))
                    return

                # 第3层：身份验证和权限确认
                self.root.after(0, lambda: self.status_var.set("🔄 第3层：身份验证确认..."))

                # 计算挑战响应
                challenge_response = hashlib.sha256(
                    f"{client_nonce}{server_nonce}{server_challenge}".encode()
                ).hexdigest()

                # 验证客户端身份 - 包含会话ID用于恢复
                auth_data = {
                    "action": "verify_client",
                    "client_nonce": client_nonce,
                    "challenge_response": challenge_response,
                    "client_version": "1.0.0",
                    "session_id": server_session_id  # 传递会话ID用于备份恢复
                }

                try:
                    response = session.post(api_url, json=auth_data, timeout=10)  # 使用session保持cookie

                    if response.status_code == 200:
                        try:
                            result = response.json()
                            # 检查result是否为None或非字典
                            if result is None or not isinstance(result, dict):
                                self.root.after(0, lambda: self.status_var.set("❌ 验证响应格式错误"))
                                return
                        except ValueError as e:
                            print(f"❌ 验证响应JSON解析失败: {str(e)}")
                            print(f"📄 原始响应: {response.text[:200]}...")
                            self.root.after(0, lambda: self.status_var.set("❌ 验证响应解析失败"))
                            return

                        if result.get('success'):
                            # 从data字段中获取验证数据
                            data = result.get('data', {})
                            if data.get('authenticated'):
                                # 获取服务器信息
                                server_info = data.get('server_info', {})
                                server_name = server_info.get('name', '未知服务器')
                                server_version = server_info.get('version', '未知版本')
                                session_source = server_info.get('session_source', 'unknown')

                                # 显示详细的成功信息
                                success_msg = f"✅ 安全连接成功 - {server_name} v{server_version}"
                                if session_source == 'backup':
                                    success_msg += " (会话已恢复)"

                                self.root.after(0, lambda: self.status_var.set(success_msg))

                                # 记录成功信息
                                verification_details = data.get('verification_details', {})
                                print(f"🎉 验证成功: Source={session_source}, Age={verification_details.get('session_age', 'N/A')}s")

                                # 第4层：获取QQ群配置
                                self.root.after(0, lambda: self.status_var.set("🔄 第4层：获取QQ群配置..."))

                                try:
                                    qq_response = session.get(api_url + "?action=get_qq_config", timeout=5)
                                    if qq_response.status_code == 200:
                                        qq_data = qq_response.json()
                                        if qq_data.get('success'):
                                            qq_config = qq_data.get('data', {})
                                            qq_number = qq_config.get('qq_group_number', '735821698')
                                            qq_name = qq_config.get('qq_group_name', 'QQ交流群')

                                            # 更新QQ群信息显示
                                            self.root.after(0, lambda: self._update_qq_display(qq_number, qq_name))

                                            final_msg = f"✅ 服务器确认完成 - {qq_name} ({qq_number})"
                                            self.root.after(0, lambda: self.status_var.set(final_msg))
                                            print(f"🎉 QQ配置获取成功: {qq_name} ({qq_number})")
                                        else:
                                            print("⚠️ QQ配置获取失败，使用默认配置")
                                            self.root.after(0, lambda: self.status_var.set("✅ 服务器确认完成 (QQ配置获取失败)"))
                                    else:
                                        print("⚠️ QQ配置API请求失败，使用默认配置")
                                        self.root.after(0, lambda: self.status_var.set("✅ 服务器确认完成 (QQ配置不可用)"))
                                except Exception as qq_e:
                                    print(f"⚠️ QQ配置获取异常: {qq_e}")
                                    self.root.after(0, lambda: self.status_var.set("✅ 服务器确认完成 (QQ配置异常)"))

                                # 服务器确认成功，保存服务器地址
                                self.root.after(0, lambda: self._save_confirmed_server(server_url))
                                self.server_confirmed = True
                            else:
                                # 验证失败，但握手成功
                                self.root.after(0, lambda: self.status_var.set("❌ 客户端验证失败"))
                        else:
                            error_code = result.get('code', 'UNKNOWN')
                            debug_info = result.get('debug_info', {})

                            if error_code == 'NO_HANDSHAKE':
                                self.root.after(0, lambda: self.status_var.set("❌ 握手会话丢失，请重试"))
                            elif error_code == 'HANDSHAKE_EXPIRED':
                                session_age = debug_info.get('session_age', 'N/A')
                                self.root.after(0, lambda: self.status_var.set(f"❌ 握手会话过期 ({session_age}s)"))
                            elif error_code == 'CHALLENGE_FAILED':
                                self.root.after(0, lambda: self.status_var.set("❌ 挑战验证失败"))
                                # 记录调试信息
                                expected = debug_info.get('expected_prefix', 'N/A')
                                received = debug_info.get('received_prefix', 'N/A')
                                print(f"🔍 挑战验证失败: Expected={expected}, Received={received}")
                            elif error_code == 'CLIENT_FINGERPRINT_MISMATCH':
                                self.root.after(0, lambda: self.status_var.set("❌ 客户端环境变化"))
                            elif error_code == 'NONCE_MISMATCH':
                                self.root.after(0, lambda: self.status_var.set("❌ 随机数不匹配"))
                            elif error_code == 'UNSUPPORTED_CLIENT':
                                supported_versions = result.get('supported_versions', [])
                                self.root.after(0, lambda: self.status_var.set(f"❌ 客户端版本不支持"))
                                print(f"🔍 支持的版本: {supported_versions}")
                            else:
                                self.root.after(0, lambda: self.status_var.set(f"❌ 验证失败: {error_code}"))
                    else:
                        # 详细的HTTP错误处理
                        error_msg = f"❌ 身份验证错误: HTTP {response.status_code}"
                        try:
                            error_detail = response.json()
                            if error_detail.get('message'):
                                error_msg = f"❌ {error_detail['message']}"
                        except:
                            pass
                        self.root.after(0, lambda: self.status_var.set(error_msg))

                except requests.exceptions.Timeout:
                    self.root.after(0, lambda: self.status_var.set("❌ 验证超时，请检查网络"))
                except requests.exceptions.ConnectionError:
                    self.root.after(0, lambda: self.status_var.set("❌ 验证连接失败"))
                except Exception as e:
                    error_msg = f"❌ 验证异常: {str(e)[:30]}"
                    error_detail = str(e)
                    self.root.after(0, lambda msg=error_msg: self.status_var.set(msg))
                    print(f"🔍 验证异常详情: {error_detail}")

            except Exception as e:
                error_msg = f"❌ 连接测试异常: {str(e)[:20]}"
                self.root.after(0, lambda msg=error_msg: self.status_var.set(msg))
            finally:
                self.root.after(0, lambda: self.confirm_button.config(state="normal", text="确认"))

        threading.Thread(target=confirm_in_thread, daemon=True).start()

    def _update_qq_display(self, qq_number, qq_name):
        """更新QQ群信息显示"""
        try:
            # 更新QQ标签显示
            if hasattr(self, 'qq_label'):
                new_text = f"💬 获取服务器地址和账号，请加Q群: {qq_number}"
                self.qq_label.config(text=new_text)
                print(f"✅ QQ标签已更新: {qq_name} ({qq_number})")
        except Exception as e:
            print(f"⚠️ 更新QQ显示失败: {e}")

    def _on_login(self):
        """处理登录"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        server_url = self.server_var.get().strip()
        qq_number = self.qq_var.get().strip()

        if not server_url:
            self.status_var.set("❌ 请输入服务器地址并点击确认")
            self.server_entry.focus()
            return

        if not self._validate_server_url(server_url):
            self.status_var.set("❌ 服务器地址格式不正确")
            self.server_entry.focus()
            return

        # 检查服务器是否已确认
        if not self.server_confirmed:
            self.status_var.set("❌ 请先点击确认按钮验证服务器")
            self.confirm_button.focus()
            return

        if not username:
            self.status_var.set("❌ 请输入用户名")
            self.username_entry.focus()
            return

        if not password:
            self.status_var.set("❌ 请输入密码")
            self.password_entry.focus()
            return

        # QQ号必填验证
        if not qq_number:
            self.status_var.set("❌ 请输入QQ号")
            self.qq_entry.focus()
            return

        # 验证QQ号格式
        if not self._validate_qq_number(qq_number):
            self.status_var.set("❌ QQ号格式不正确（5-11位数字）")
            self.qq_entry.focus()
            return

        # 确保URL格式正确
        if not server_url.endswith('/api.php'):
            server_url = server_url.rstrip('/') + '/api.php'
        
        # 显示登录中状态
        self.login_button.config(state="disabled", text="🔄 登录中...")
        self.status_var.set("🔄 正在验证...")
        
        self.result = {
            'action': 'login',
            'username': username,
            'password': password,
            'server': server_url,
            'qq_number': qq_number,
            'remember': self.remember_var.get()
        }
        
        # 延迟关闭
        self.root.after(500, self.root.destroy)
    
    def _on_cancel(self):
        """处理取消"""
        self.result = {'action': 'cancel'}
        self.root.destroy()
    
    def _show_help(self):
        """显示帮助"""
        # 获取当前QQ群号
        qq_number = self._get_qq_config()

        help_text = f"""🔐 登录帮助

服务器地址说明:
• 需要管理员提供具体的服务器地址
• 格式通常为: http://IP地址:端口号
• 如果不知道地址，请联系管理员

使用步骤:
1. 向管理员获取服务器地址
2. 输入服务器地址并点击"确认"
3. 输入管理员提供的用户名和密码
4. 点击"立即登录"

重要提醒:
• 服务器地址由管理员配置和提供
• 用户账号需要管理员创建
• 请勿随意尝试其他地址

⚠️ 如有配置问题等，请加Q群: {qq_number}"""

        messagebox.showinfo("登录帮助", help_text)
    
    def show(self):
        """显示对话框"""
        self.root.mainloop()
        return self.result


def test_dialog():
    """测试对话框"""
    dialog = CompactLoginDialog()
    result = dialog.show()
    print("登录结果:", result)


if __name__ == "__main__":
    test_dialog()
