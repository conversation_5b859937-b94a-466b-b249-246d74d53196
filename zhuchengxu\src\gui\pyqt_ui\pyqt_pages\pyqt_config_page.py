#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt配置管理页面 - 学习modern_config_page.py的设计
"""

import os
import json
from typing import Dict, Any

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QScrollArea,
    QLabel, QLineEdit, QPushButton, QCheckBox, QSpinBox, QComboBox,
    QFrame, QSizePolicy, QGroupBox, QFormLayout, QTextEdit, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.utils.logger import get_logger


class ConfigDesignSystem:
    """配置页面设计系统"""
    
    # 现代化配色方案
    COLORS = {
        'bg_primary': '#0a0a0f',      # 深空黑
        'bg_secondary': '#1a1a2e',    # 深紫蓝
        'bg_tertiary': '#16213e',     # 深蓝灰
        'glass_bg': '#1d1d31',        # 玻璃效果背景
        'glass_border': '#2a2a3e',    # 玻璃效果边框
        
        # 霓虹色彩
        'neon_purple': '#8b5cf6',     # 霓虹紫
        'neon_green': '#00ff88',      # 霓虹绿
        'neon_cyan': '#00f5ff',       # 霓虹青
        'neon_orange': '#ff8800',     # 霓虹橙
        
        # 文字颜色
        'text_primary': '#ffffff',    # 主要文字
        'text_secondary': '#b8bcc8',  # 次要文字
        'text_muted': '#6c7293',      # 弱化文字
    }
    
    # 字体系统
    FONTS = {
        'primary': 'Microsoft YaHei UI',
        'secondary': 'Consolas',
    }
    
    # 尺寸系统
    SIZES = {
        'title': 20,
        'heading': 16,
        'body': 14,
        'small': 12,
        'tiny': 10,
    }
    
    # 间距系统
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 16,
        'lg': 24,
        'xl': 32,
    }


class PyQtConfigPage(QWidget):
    """
    PyQt配置管理页面 - 学习modern_config_page.py的设计
    
    功能特点：
    1. 邮箱配置管理
    2. 自动化参数设置
    3. 系统配置选项
    4. 配置保存和加载
    """
    
    # 定义信号
    config_changed = pyqtSignal(str, object)  # 配置变更信号
    config_saved = pyqtSignal()               # 配置保存信号
    
    def __init__(self, parent=None, config_manager=None):
        """
        初始化配置页面
        
        Args:
            parent: 父窗口
            config_manager: 配置管理器
        """
        super().__init__(parent)
        
        # 核心组件初始化
        self.logger = get_logger()
        self.config_manager = config_manager
        self.ds = ConfigDesignSystem()  # 设计系统实例
        
        # 配置数据
        self.config_data = {}
        self.config_widgets = {}  # 存储配置控件的引用
        
        # 初始化界面
        self._init_ui()
        self._apply_styles()
        self._load_config()
    
    # ==================== UI初始化方法 ====================
    
    def _init_ui(self):
        """
        初始化用户界面 - 学习modern_config_page.py的布局
        """
        # 设置页面策略：完全填充父容器
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(self.ds.SPACING['sm'], self.ds.SPACING['sm'], 
                                     self.ds.SPACING['sm'], self.ds.SPACING['sm'])
        main_layout.setSpacing(self.ds.SPACING['sm'])
        
        # 构建界面组件
        self._create_page_header(main_layout)
        self._create_scrollable_content(main_layout)
    
    def _create_page_header(self, parent_layout):
        """
        创建页面标题区域 - 学习modern_config_page.py的标题设计
        """
        # 标题容器
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setFixedHeight(100)
        
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(self.ds.SPACING['lg'], self.ds.SPACING['md'], 
                                       self.ds.SPACING['lg'], self.ds.SPACING['md'])
        header_layout.setSpacing(self.ds.SPACING['xs'])
        
        # 主标题
        title_label = QLabel("⚙️ 配置设置")
        title_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['title'], QFont.Bold))
        title_label.setObjectName("titleLabel")
        
        # 副标题
        subtitle_label = QLabel("配置邮箱服务和自动化参数")
        subtitle_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        subtitle_label.setObjectName("subtitleLabel")
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
    
    def _create_scrollable_content(self, parent_layout):
        """
        创建可滚动的内容区域
        """
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setObjectName("configScrollArea")
        
        # 滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(self.ds.SPACING['md'], self.ds.SPACING['md'],
                                       self.ds.SPACING['md'], self.ds.SPACING['md'])
        scroll_layout.setSpacing(self.ds.SPACING['lg'])
        
        # 创建配置区域
        self._create_email_config(scroll_layout)
        self._create_automation_config(scroll_layout)
        self._create_system_config(scroll_layout)
        self._create_action_buttons(scroll_layout)
        
        # 添加弹性空间
        scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_content)
        parent_layout.addWidget(scroll_area)
    
    def _create_email_config(self, parent_layout):
        """
        创建邮箱配置区域 - 学习modern_config_page.py的邮箱配置
        """
        # 区域标题
        section_title = QLabel("📧 邮箱配置")
        section_title.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['heading'], QFont.Bold))
        section_title.setObjectName("sectionTitle")
        
        # 配置组
        email_group = QGroupBox()
        email_group.setObjectName("configGroup")
        email_layout = QFormLayout(email_group)
        email_layout.setSpacing(self.ds.SPACING['md'])
        
        # 邮箱域名
        self._create_config_field(
            email_layout, "邮箱域名:", "email_domain", 
            "hwsyyds.xyz", "用于生成随机邮箱的域名"
        )
        
        # TempMail.Plus邮箱地址
        self._create_config_field(
            email_layout, "TempMail.Plus邮箱地址:", "temp_mail_address",
            "", "在TempMail.Plus网站获取的邮箱地址"
        )

        # TempMail.Plus Pin码
        self._create_config_field(
            email_layout, "TempMail.Plus Pin码:", "temp_mail_pin",
            "", "TempMail.Plus邮箱的Pin码（如果需要）"
        )
        
        parent_layout.addWidget(section_title)
        parent_layout.addWidget(email_group)
    
    def _create_automation_config(self, parent_layout):
        """
        创建自动化配置区域
        """
        # 区域标题
        section_title = QLabel("🤖 自动化配置")
        section_title.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['heading'], QFont.Bold))
        section_title.setObjectName("sectionTitle")
        
        # 配置组
        auto_group = QGroupBox()
        auto_group.setObjectName("configGroup")
        auto_layout = QFormLayout(auto_group)
        auto_layout.setSpacing(self.ds.SPACING['md'])
        
        # 浏览器类型
        browser_combo = QComboBox()
        browser_combo.addItems(["默认浏览器", "Chrome", "Firefox", "Edge"])
        browser_combo.setObjectName("browserCombo")
        self.config_widgets["browser_type"] = browser_combo
        auto_layout.addRow("浏览器类型:", browser_combo)
        
        # 启用详细日志
        verbose_checkbox = QCheckBox("启用详细日志记录")
        verbose_checkbox.setObjectName("verboseCheck")
        self.config_widgets["verbose_logging"] = verbose_checkbox
        auto_layout.addRow(verbose_checkbox)
        
        parent_layout.addWidget(section_title)
        parent_layout.addWidget(auto_group)
    
    def _create_system_config(self, parent_layout):
        """
        创建系统配置区域
        """
        # 区域标题
        section_title = QLabel("🔧 系统配置")
        section_title.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['heading'], QFont.Bold))
        section_title.setObjectName("sectionTitle")
        
        # 配置组
        system_group = QGroupBox()
        system_group.setObjectName("configGroup")
        system_layout = QFormLayout(system_group)
        system_layout.setSpacing(self.ds.SPACING['md'])
        
        # 保存窗口位置
        save_position_checkbox = QCheckBox("记住窗口位置和大小")
        save_position_checkbox.setObjectName("savePositionCheck")
        self.config_widgets["save_window_position"] = save_position_checkbox
        system_layout.addRow(save_position_checkbox)
        
        parent_layout.addWidget(section_title)
        parent_layout.addWidget(system_group)
    
    def _create_config_field(self, layout, label_text, config_key, default_value, help_text):
        """
        创建配置字段 - 学习modern_config_page.py的字段创建
        """
        # 创建输入框
        line_edit = QLineEdit()
        line_edit.setText(default_value)
        line_edit.setPlaceholderText(help_text)
        line_edit.setObjectName("configLineEdit")
        
        # 保存控件引用
        self.config_widgets[config_key] = line_edit
        
        # 添加到布局
        layout.addRow(label_text, line_edit)
    
    def _create_action_buttons(self, parent_layout):
        """
        创建操作按钮区域 - 学习modern_config_page.py的按钮设计
        """
        # 按钮容器
        button_frame = QFrame()
        button_frame.setObjectName("buttonFrame")
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, self.ds.SPACING['lg'], 0, 0)
        button_layout.setSpacing(self.ds.SPACING['md'])
        
        # 保存按钮
        save_button = QPushButton("💾 保存配置")
        save_button.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body'], QFont.Bold))
        save_button.setObjectName("saveButton")
        save_button.clicked.connect(self._save_config)
        
        # 重置按钮
        reset_button = QPushButton("🔄 重置配置")
        reset_button.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        reset_button.setObjectName("resetButton")
        reset_button.clicked.connect(self._reset_config)
        
        # 导入导出按钮
        import_button = QPushButton("📥 导入配置")
        import_button.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        import_button.setObjectName("importButton")
        import_button.clicked.connect(self._import_config)
        
        export_button = QPushButton("📤 导出配置")
        export_button.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        export_button.setObjectName("exportButton")
        export_button.clicked.connect(self._export_config)
        
        button_layout.addWidget(save_button)
        button_layout.addWidget(reset_button)
        button_layout.addStretch()
        button_layout.addWidget(import_button)
        button_layout.addWidget(export_button)
        
        parent_layout.addWidget(button_frame)

    # ==================== 样式应用方法 ====================

    def _apply_styles(self):
        """
        应用样式表 - 现代化2025设计风格（性能优化版本）
        """
        # 使用缓存避免重复计算样式表
        if hasattr(self, '_cached_styles'):
            self.setStyleSheet(self._cached_styles)
            return

        # 确保颜色值是字符串格式
        colors = self.ds.COLORS
        fonts = self.ds.FONTS

        style = f"""
        /* 主窗口样式 */
        QWidget {{
            background-color: {colors['bg_primary']};
            color: {colors['text_primary']};
            font-family: '{fonts['primary']}';
        }}

        /* 标题样式 */
        QLabel#titleLabel {{
            color: {colors['neon_purple']};
            font-weight: bold;
            background: transparent;
            border: none;
        }}

        QLabel#subtitleLabel {{
            color: {colors['text_secondary']};
            background: transparent;
            border: none;
        }}

        QLabel#sectionTitle {{
            color: {colors['text_primary']};
            font-weight: bold;
            background: transparent;
            border: none;
            padding: 8px 0px;
        }}

        /* 滚动区域样式 */
        QScrollArea#configScrollArea {{
            background-color: {colors['bg_primary']};
            border: none;
        }}

        QScrollArea#configScrollArea QScrollBar:vertical {{
            background-color: {colors['bg_tertiary']};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollArea#configScrollArea QScrollBar::handle:vertical {{
            background-color: {colors['neon_cyan']};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollArea#configScrollArea QScrollBar::handle:vertical:hover {{
            background-color: {colors['neon_green']};
        }}

        /* 配置组样式 */
        QGroupBox#configGroup {{
            background-color: {colors['bg_secondary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 8px;
            padding-top: 15px;
            margin-top: 10px;
        }}

        /* 输入框样式 */
        QLineEdit#configLineEdit {{
            background-color: {colors['bg_tertiary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
        }}

        QLineEdit#configLineEdit:focus {{
            border-color: {colors['neon_cyan']};
            background-color: {colors['glass_bg']};
        }}

        /* 复选框样式 */
        QCheckBox#verboseCheck, QCheckBox#savePositionCheck {{
            color: {colors['text_primary']};
            background: transparent;
            spacing: 8px;
        }}

        QCheckBox#verboseCheck::indicator, QCheckBox#savePositionCheck::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {colors['glass_border']};
            border-radius: 4px;
            background-color: {colors['bg_tertiary']};
        }}

        QCheckBox#verboseCheck::indicator:checked, QCheckBox#savePositionCheck::indicator:checked {{
            background-color: {colors['neon_green']};
            border-color: {colors['neon_green']};
        }}

        /* 下拉框样式 */
        QComboBox#browserCombo, QComboBox#themeCombo {{
            background-color: {colors['bg_tertiary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
        }}

        QComboBox#browserCombo:hover, QComboBox#themeCombo:hover {{
            border-color: {colors['neon_cyan']};
        }}

        QComboBox#browserCombo::drop-down, QComboBox#themeCombo::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox#browserCombo QAbstractItemView, QComboBox#themeCombo QAbstractItemView {{
            background-color: {colors['bg_secondary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            selection-background-color: {colors['neon_cyan']}33;
        }}



        /* 按钮样式 */
        QPushButton#saveButton {{
            background-color: {colors['neon_green']};
            color: {colors['bg_primary']};
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: bold;
            font-size: 14px;
        }}

        QPushButton#saveButton:hover {{
            background-color: {colors['neon_cyan']};
        }}

        QPushButton#resetButton {{
            background-color: {colors['neon_orange']};
            color: {colors['bg_primary']};
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: bold;
            font-size: 14px;
        }}

        QPushButton#resetButton:hover {{
            background-color: {colors['neon_purple']};
        }}

        QPushButton#importButton, QPushButton#exportButton {{
            background-color: {colors['bg_tertiary']};
            color: {colors['text_primary']};
            border: 2px solid {colors['neon_purple']};
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: bold;
            font-size: 14px;
        }}

        QPushButton#importButton:hover, QPushButton#exportButton:hover {{
            background-color: {colors['neon_purple']};
            color: {colors['bg_primary']};
        }}

        /* 框架样式 */
        QFrame {{
            background-color: {colors['bg_secondary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 8px;
        }}

        QFrame#buttonFrame {{
            background: transparent;
            border: none;
        }}
        """

        # 缓存样式表以提高性能
        self._cached_styles = style
        self.setStyleSheet(style)

    # ==================== 配置管理方法 ====================

    def _load_config(self):
        """
        加载配置数据 - 直接从主程序根目录config.json加载
        """
        try:
            # 🔧 使用与main.py一致的路径检测逻辑
            import sys
            # 直接使用main.py中设置的环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                config_file = os.path.join(config_dir, "config.json")
            else:
                # 备选方案：使用sys.argv[0]
                if sys.argv and len(sys.argv) > 0:
                    argv_path = os.path.abspath(sys.argv[0])
                    if argv_path.endswith('.exe'):
                        config_file = os.path.join(os.path.dirname(argv_path), "config.json")
                    else:
                        config_file = os.path.join(os.path.dirname(argv_path), "config.json")
                else:
                    config_file = os.path.join(os.getcwd(), "config.json")
            self.logger.info(f"📁 加载配置文件: {config_file}")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)

                self.logger.info("✅ 配置文件加载成功")
                self.logger.info(f"📧 当前邮箱配置: {self.config_data.get('temp_mail_address', '未配置')}")
            else:
                # 使用默认配置
                self.config_data = self._get_default_config()
                self.logger.info("📝 使用默认配置")

            # 应用配置到界面
            self._apply_config_to_widgets()

        except Exception as e:
            self.logger.error(f"❌ 加载配置失败: {e}")
            self.config_data = self._get_default_config()
            self._apply_config_to_widgets()

    def _get_default_config(self):
        """
        获取默认配置 - 根据实际需求调整
        """
        return {
            # 邮箱配置
            "email_domain": "hwsyyds.xyz",
            "temp_mail_address": "",
            "temp_mail_pin": "",

            # 自动化配置
            "browser_type": "默认浏览器",
            "verbose_logging": True,

            # 系统配置
            "save_window_position": True,
        }

    def _apply_config_to_widgets(self):
        """
        将配置应用到界面控件
        """
        try:
            for config_key, widget in self.config_widgets.items():
                if config_key in self.config_data:
                    value = self.config_data[config_key]

                    if isinstance(widget, QLineEdit):
                        widget.setText(str(value))
                    elif isinstance(widget, QCheckBox):
                        widget.setChecked(bool(value))
                    elif isinstance(widget, QSpinBox):
                        widget.setValue(int(value))
                    elif isinstance(widget, QComboBox):
                        index = widget.findText(str(value))
                        if index >= 0:
                            widget.setCurrentIndex(index)

            self.logger.debug("✅ 配置已应用到界面控件")

        except Exception as e:
            self.logger.error(f"❌ 应用配置到控件失败: {e}")

    def _save_config(self):
        """
        保存配置 - 学习modern_config_page.py的保存逻辑
        """
        try:
            # 从界面控件收集配置
            for config_key, widget in self.config_widgets.items():
                if isinstance(widget, QLineEdit):
                    self.config_data[config_key] = widget.text()
                elif isinstance(widget, QCheckBox):
                    self.config_data[config_key] = widget.isChecked()
                elif isinstance(widget, QSpinBox):
                    self.config_data[config_key] = widget.value()
                elif isinstance(widget, QComboBox):
                    self.config_data[config_key] = widget.currentText()

            # 🔧 使用与main.py一致的路径检测逻辑
            import sys
            # 直接使用main.py中设置的环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                config_file = os.path.join(config_dir, "config.json")
            else:
                # 备选方案：使用sys.argv[0]
                if sys.argv and len(sys.argv) > 0:
                    argv_path = os.path.abspath(sys.argv[0])
                    if argv_path.endswith('.exe'):
                        config_file = os.path.join(os.path.dirname(argv_path), "config.json")
                    else:
                        config_file = os.path.join(os.path.dirname(argv_path), "config.json")
                else:
                    config_file = os.path.join(os.getcwd(), "config.json")
            self.logger.info(f"💾 保存配置到: {config_file}")

            # 先读取现有配置，然后更新
            existing_config = {}
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        existing_config = json.load(f)
                except:
                    pass

            # 更新配置
            existing_config.update(self.config_data)

            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=4, ensure_ascii=False)

            # 发送信号
            self.config_saved.emit()

            # 显示成功消息
            QMessageBox.information(
                self,
                "配置保存",
                "✅ 配置已成功保存！\n\n部分设置可能需要重启应用程序才能生效。"
            )

            self.logger.info("✅ 配置保存成功")

        except Exception as e:
            self.logger.error(f"❌ 保存配置失败: {e}")
            QMessageBox.warning(
                self,
                "保存失败",
                f"❌ 保存配置失败：\n{e}"
            )

    def _reset_config(self):
        """
        重置配置到默认值
        """
        reply = QMessageBox.question(
            self,
            "重置配置",
            "⚠️ 确定要重置所有配置到默认值吗？\n\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.config_data = self._get_default_config()
                self._apply_config_to_widgets()

                QMessageBox.information(
                    self,
                    "重置完成",
                    "✅ 配置已重置到默认值！\n\n请点击保存按钮保存更改。"
                )

                self.logger.info("✅ 配置已重置到默认值")

            except Exception as e:
                self.logger.error(f"❌ 重置配置失败: {e}")
                QMessageBox.warning(self, "重置失败", f"❌ 重置配置失败：\n{e}")

    def _import_config(self):
        """
        导入配置文件
        """
        from PyQt5.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "导入配置文件",
            "",
            "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)

                # 验证配置格式
                default_config = self._get_default_config()
                for key in default_config.keys():
                    if key not in imported_config:
                        imported_config[key] = default_config[key]

                self.config_data = imported_config
                self._apply_config_to_widgets()

                QMessageBox.information(
                    self,
                    "导入成功",
                    "✅ 配置文件导入成功！\n\n请点击保存按钮保存更改。"
                )

                self.logger.info(f"✅ 配置文件导入成功: {file_path}")

            except Exception as e:
                self.logger.error(f"❌ 导入配置失败: {e}")
                QMessageBox.warning(self, "导入失败", f"❌ 导入配置失败：\n{e}")

    def _export_config(self):
        """
        导出配置文件
        """
        from PyQt5.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出配置文件",
            "augment_config.json",
            "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                # 从界面收集当前配置
                current_config = {}
                for config_key, widget in self.config_widgets.items():
                    if isinstance(widget, QLineEdit):
                        current_config[config_key] = widget.text()
                    elif isinstance(widget, QCheckBox):
                        current_config[config_key] = widget.isChecked()
                    elif isinstance(widget, QSpinBox):
                        current_config[config_key] = widget.value()
                    elif isinstance(widget, QComboBox):
                        current_config[config_key] = widget.currentText()

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(current_config, f, indent=2, ensure_ascii=False)

                QMessageBox.information(
                    self,
                    "导出成功",
                    f"✅ 配置文件导出成功！\n\n保存位置：{file_path}"
                )

                self.logger.info(f"✅ 配置文件导出成功: {file_path}")

            except Exception as e:
                self.logger.error(f"❌ 导出配置失败: {e}")
                QMessageBox.warning(self, "导出失败", f"❌ 导出配置失败：\n{e}")

    # ==================== 公共接口方法 ====================

    def get_config(self, key, default=None):
        """
        获取配置值
        """
        return self.config_data.get(key, default)

    def set_config(self, key, value):
        """
        设置配置值
        """
        self.config_data[key] = value
        self.config_changed.emit(key, value)

    def refresh_config(self):
        """
        刷新配置
        """
        self._load_config()
