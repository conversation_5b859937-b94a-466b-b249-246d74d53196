[Setup]
; 基本信息
AppName=Augment续杯工具
AppVersion=1.0.0
AppPublisher=Augment Code
DefaultDirName={autopf}\Augment续杯工具
DefaultGroupName=Augment续杯工具
AllowNoIcons=yes

; 输出设置
OutputDir=installer_output
OutputBaseFilename=Augment续杯工具_v2.0_安装程序
Compression=lzma2/ultra64
SolidCompression=yes
WizardStyle=modern

; 系统要求
MinVersion=6.1sp1
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "Create a desktop shortcut"; GroupDescription: "Additional options:"; Flags: checkedonce

[Files]
; 主程序文件 - 根据实际编译输出调整路径
Source: "Release\main.dist\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
; 开始菜单
Name: "{group}\Augment续杯工具"; Filename: "{app}\main.exe"
Name: "{group}\卸载Augment续杯工具"; Filename: "{uninstallexe}"

; 桌面图标
Name: "{autodesktop}\Augment续杯工具"; Filename: "{app}\main.exe"; Tasks: desktopicon

[Run]
; 安装完成后询问是否运行
Filename: "{app}\main.exe"; Description: "立即启动Augment续杯工具"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; 卸载时清理配置文件
Type: files; Name: "{app}\config.json"
Type: files; Name: "{app}\*.log"
Type: dirifempty; Name: "{app}"
