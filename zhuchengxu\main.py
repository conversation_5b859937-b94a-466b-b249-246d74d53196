#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
续杯工具
主程序入口文件 - 认证后启动PyQt
"""

import sys
import os
import time

# 立即设置环境变量，确保在导入任何其他模块前完成
def init_config_environment():
    """在程序最开始阶段初始化配置环境"""
    try:
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 设置配置目录环境变量（使用当前脚本目录）
        os.environ['AUGMENT_CONFIG_DIR'] = script_dir
        
        print(f"🔧 初始化: 已设置AUGMENT_CONFIG_DIR = {script_dir}")
        
        # 确保配置目录存在
        if not os.path.exists(script_dir):
            os.makedirs(script_dir, exist_ok=True)
            print(f"📁 初始化: 创建配置目录")
            
        return True
    except Exception as e:
        print(f"❌ 初始化配置环境失败: {e}")
        return False

# 立即执行初始化配置环境
init_config_environment()

# 🔧 首先设置配置文件路径环境变量（必须在导入其他模块之前）
print(f"🔍 调试信息: sys.executable = {sys.executable}")
print(f"🔍 调试信息: hasattr(sys, '_MEIPASS') = {hasattr(sys, '_MEIPASS')}")
if hasattr(sys, '_MEIPASS'):
    print(f"🔍 调试信息: sys._MEIPASS = {sys._MEIPASS}")

# 🔧 强制设置配置目录环境变量，确保所有子进程都能继承
def setup_config_dir():
    """设置配置目录环境变量 - 强制使用exe同目录"""
    # 检查环境变量是否已经设置
    if 'AUGMENT_CONFIG_DIR' in os.environ and os.environ['AUGMENT_CONFIG_DIR']:
        real_config_dir = os.environ['AUGMENT_CONFIG_DIR']
        print(f"✅ 使用已设置的环境变量: AUGMENT_CONFIG_DIR = {real_config_dir}")
        return real_config_dir

    # 🔧 直接强制使用exe同目录，不管什么环境
    print(f"🔍 sys.executable = {sys.executable}")
    print(f"🔍 sys.argv[0] = {sys.argv[0] if sys.argv else 'None'}")
    print(f"🔍 当前工作目录 = {os.getcwd()}")

    # 强制使用exe同目录的策略
    real_config_dir = None

    # 方法1：如果sys.argv[0]是exe文件，使用其目录
    if sys.argv and len(sys.argv) > 0:
        argv_path = os.path.abspath(sys.argv[0])
        if argv_path.endswith('.exe'):
            real_config_dir = os.path.dirname(argv_path)
            print(f"✅ 使用sys.argv[0]的exe目录: {real_config_dir}")
        else:
            # 如果不是exe文件，使用当前脚本所在目录
            script_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            real_config_dir = script_dir
            print(f"✅ 使用脚本所在目录: {real_config_dir}")

    # 方法2：如果方法1失败，使用当前工作目录
    if not real_config_dir:
        real_config_dir = os.getcwd()
        print(f"✅ 使用当前工作目录: {real_config_dir}")

    # 🔧 强制设置环境变量
    os.environ['AUGMENT_CONFIG_DIR'] = real_config_dir
    print(f"🔧 强制设置环境变量: AUGMENT_CONFIG_DIR = {real_config_dir}")

    # 确保配置目录存在
    if not os.path.exists(real_config_dir):
        os.makedirs(real_config_dir, exist_ok=True)
        print(f"📁 创建配置目录: {real_config_dir}")

    return real_config_dir

# 立即设置配置目录
try:
    config_dir = setup_config_dir()
    print(f"🔧 初始设置: 配置目录 = {config_dir}")
except Exception as e:
    print(f"❌ 设置配置目录失败: {e}")
    import traceback
    traceback.print_exc()

# 🔧 验证最终设置
final_env = os.environ.get('AUGMENT_CONFIG_DIR')
print(f"🔍 最终环境变量: AUGMENT_CONFIG_DIR = {final_env}")
if 'Temp' in final_env or 'onefile' in final_env.lower():
    print(f"❌ 警告：环境变量仍指向临时目录！")
else:
    print(f"✅ 环境变量设置正确，指向真实目录")

# 修复打包后的模块路径问题
if hasattr(sys, '_MEIPASS'):
    # Nuitka打包环境
    sys.path.insert(0, sys._MEIPASS)

    # 创建符号链接或复制目录来解决导入问题
    import shutil
    temp_dir = sys._MEIPASS

    # 在临时目录创建模块链接
    modules_to_link = ['utils', 'config', 'gui', 'security', 'automation', 'email_service']
    for module_name in modules_to_link:
        src_path = os.path.join(temp_dir, 'src', module_name)
        link_path = os.path.join(temp_dir, module_name)

        if os.path.exists(src_path) and not os.path.exists(link_path):
            try:
                # 在Windows上创建目录链接
                if os.name == 'nt':
                    import subprocess
                    subprocess.run(['mklink', '/D', link_path, src_path],
                                 shell=True, capture_output=True)
                else:
                    os.symlink(src_path, link_path)
            except:
                # 如果链接失败，尝试复制
                try:
                    shutil.copytree(src_path, link_path)
                except:
                    pass
else:
    # 开发环境 - 添加当前目录到路径
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 本地PIL库路径配置
lib_path = os.path.join(os.path.dirname(__file__), 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))



# 启动画面已移除 - 混合打包启动很快，不需要加载动画

def copy_release_config():
    """从Release/main.dist/复制配置文件到当前目录"""
    try:
        print("📋 检查并复制Release目录配置文件...")
        
        # 获取脚本所在目录作为源目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 获取配置目录作为目标目录
        target_dir = os.environ.get('AUGMENT_CONFIG_DIR', script_dir)
        
        print(f"📂 脚本目录: {script_dir}")
        print(f"📂 配置目录: {target_dir}")
        
        # 定义Release目录的配置文件路径
        release_dir = os.path.join(script_dir, 'Release', 'main.dist')
        
        # 检查Release目录是否存在
        if not os.path.exists(release_dir):
            print(f"⚠️ 未找到Release目录: {release_dir}")
            return
            
        # 需要复制的配置文件列表
        config_files = [
            'accounts.json',
            'auth_cache.json',
            'config.json'
        ]
        
        copied_count = 0
        
        # 复制单个文件
        for config_file in config_files:
            src_path = os.path.join(release_dir, config_file)
            dst_path = os.path.join(target_dir, config_file)
            
            # 检查源文件是否存在
            if os.path.exists(src_path):
                # 如果目标文件已存在且不是第一次运行，不要覆盖
                if os.path.exists(dst_path):
                    print(f"✅ {config_file} 已存在于配置目录，跳过复制")
                    continue
                    
                # 复制文件
                import shutil
                try:
                    shutil.copy2(src_path, dst_path)
                    copied_count += 1
                    print(f"✅ 已复制 {config_file} 到配置目录")
                except Exception as e:
                    print(f"❌ 复制 {config_file} 失败: {e}")
            else:
                print(f"⚠️ Release目录中未找到 {config_file}")
        
        # 需要复制的目录列表
        config_dirs = [
            'registered_accounts',
            'data'
        ]
        
        # 复制整个目录
        for dir_name in config_dirs:
            src_dir = os.path.join(release_dir, dir_name)
            dst_dir = os.path.join(target_dir, dir_name)
            
            # 检查源目录是否存在
            if os.path.exists(src_dir) and os.path.isdir(src_dir):
                # 如果目标目录不存在，则创建
                if not os.path.exists(dst_dir):
                    os.makedirs(dst_dir, exist_ok=True)
                    print(f"📁 已在配置目录创建子目录: {dir_name}")
                
                # 复制目录内的所有文件
                for file_name in os.listdir(src_dir):
                    src_file = os.path.join(src_dir, file_name)
                    dst_file = os.path.join(dst_dir, file_name)
                    
                    # 只复制文件，不复制子目录
                    if os.path.isfile(src_file):
                        if not os.path.exists(dst_file):
                            import shutil
                            try:
                                shutil.copy2(src_file, dst_file)
                                copied_count += 1
                                print(f"✅ 已复制 {dir_name}/{file_name}")
                            except Exception as e:
                                print(f"❌ 复制 {dir_name}/{file_name} 失败: {e}")
                
            elif not os.path.exists(src_dir):
                print(f"⚠️ Release目录中未找到 {dir_name} 目录")
                
        # 确保data目录存在
        data_dir = os.path.join(target_dir, 'data')
        if not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)
            print(f"📁 已在配置目录创建data目录")
            
        # 确保registered_accounts目录存在
        accounts_dir = os.path.join(target_dir, 'registered_accounts')
        if not os.path.exists(accounts_dir):
            os.makedirs(accounts_dir, exist_ok=True)
            print(f"📁 已在配置目录创建registered_accounts目录")
        
        # 检查环境变量是否已设置
        config_dir_env = os.environ.get('AUGMENT_CONFIG_DIR', '')
        if config_dir_env and os.path.exists(config_dir_env):
            print(f"✅ 环境变量AUGMENT_CONFIG_DIR已设置: {config_dir_env}")
        else:
            print("⚠️ 环境变量AUGMENT_CONFIG_DIR未设置或路径不存在，将使用默认配置目录")
                
        if copied_count > 0:
            print(f"🎉 成功复制 {copied_count} 个配置文件")
        else:
            print("ℹ️ 没有需要复制的配置文件")
            
    except Exception as e:
        print(f"❌ 复制配置文件失败: {e}")
        import traceback
        traceback.print_exc()


def ensure_server_config():
    """确保服务器配置正确设置"""
    try:
        print("🔧 检查服务器配置...")
        
        # 使用环境变量获取配置目录
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR', os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(config_dir, 'config.json')
        print(f"📂 使用配置目录: {config_dir}")
        print(f"📄 配置文件路径: {config_path}")
        
        # 默认配置
        default_server = "http://127.0.0.1:777"
        
        # 读取现有配置
        config_data = {}
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    import json
                    config_data = json.load(f)
                print(f"✅ 读取现有配置文件: {config_path}")
            except Exception as e:
                print(f"⚠️ 读取配置文件失败: {e}")
                config_data = {}
        else:
            print(f"⚠️ 配置文件不存在，将创建新文件: {config_path}")
        
        # 检查auth_server_url配置
        if not config_data.get('auth_server_url'):
            config_data['auth_server_url'] = default_server
            print(f"⚠️ 未设置服务器地址，使用默认值: {default_server}")
        else:
            print(f"✅ 服务器地址已配置: {config_data['auth_server_url']}")
            
        # 确保auth_config部分存在
        if 'auth_config' not in config_data:
            config_data['auth_config'] = {
                'enabled': True,
                'require_auth_for_start': False,  # 设为False允许无需认证启动
                'server': config_data.get('auth_server_url', default_server)
            }
            print("📝 创建认证配置部分")
            
        # 保存配置文件
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            print(f"💾 保存配置文件: {config_path}")
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 设置服务器配置失败: {e}")
        import traceback
        traceback.print_exc()


def show_help_info():
    """显示帮助信息"""
    help_text = """
╭───────────────────────────────────────────────────╮
│ 🚀 Augment续杯工具 - 使用指南                      │
╰───────────────────────────────────────────────────╯

📋 基本信息:
  • 当前版本: v2.0
  • 配置文件已自动从Release目录复制
  • 服务器地址已设置为配置文件中指定值

🔧 如何配置服务器地址:
  1. 打开config.json文件
  2. 修改"auth_server_url"字段的值
  3. 保存文件并重启程序

📢 公告页面连接问题:
  • 如果公告页面无法连接，请检查服务器地址
  • 确保服务器已启动并可访问
  • 检查网络连接是否正常

💡 技术支持:
  • QQ群: 735821698
  • 访问"帮助"->"关于"获取更多信息
"""
    print(help_text)


def check_config_path_problems():
    """检查并修复配置路径问题"""
    try:
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR', '')
        if config_dir and config_dir.endswith('config.json'):
            print(f"⚠️ 发现错误的配置路径: {config_dir}")
            # 修正错误的配置路径（从文件路径改为目录路径）
            correct_dir = os.path.dirname(config_dir)
            os.environ['AUGMENT_CONFIG_DIR'] = correct_dir
            print(f"✅ 已修正配置目录: {correct_dir}")
            return True
    except Exception as e:
        print(f"❌ 检查配置路径失败: {e}")
    return False


def force_config_path():
    """强制设置配置路径，确保所有模块都使用正确的配置目录"""
    try:
        # 获取已设置的配置目录
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR', '')
        if not config_dir:
            print("⚠️ 强制设置配置路径: AUGMENT_CONFIG_DIR未设置，重新设置")
            # 重新设置
            init_config_environment()
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR', '')
            
        # 打印设置状态
        print(f"🔍 当前配置目录: {config_dir}")
        
        # 强制设置系统环境变量（可能有些模块直接使用这些变量）
        os.environ['APPDATA'] = config_dir
        os.environ['LOCALAPPDATA'] = config_dir
        os.environ['USERPROFILE'] = config_dir
        
        # 写入一个标记文件，表明这是配置目录
        marker_file = os.path.join(config_dir, '.augment_config_dir')
        try:
            with open(marker_file, 'w') as f:
                f.write(f"AUGMENT_CONFIG_DIR={config_dir}\n")
                f.write(f"TIMESTAMP={time.time()}\n")
        except:
            pass
            
        return config_dir
    except Exception as e:
        print(f"❌ 强制设置配置路径失败: {e}")
        return None


def ensure_announcements_config():
    """确保公告页面配置正确"""
    try:
        print("📢 检查公告页面配置...")
        
        # 获取配置目录
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR', '')
        if not config_dir:
            print("⚠️ 公告配置: AUGMENT_CONFIG_DIR未设置")
            return False
            
        config_path = os.path.join(config_dir, 'config.json')
        
        # 检查配置文件是否存在
        if not os.path.exists(config_path):
            print(f"⚠️ 公告配置: 配置文件不存在 - {config_path}")
            return False
            
        # 读取配置文件
        try:
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 检查必要的配置项
            server_url = config.get('auth_server_url', '')
            if not server_url:
                print("⚠️ 公告配置: 服务器URL未设置")
                return False
                
            print(f"✅ 公告配置: 使用服务器URL - {server_url}")
            
            # 创建单独的公告配置文件，确保公告页面可以找到服务器地址
            announcements_config = {
                'server_url': server_url,
                'api_endpoint': f"{server_url}/api.php",
                'announcements_enabled': True
            }
            
            # 保存到单独的配置文件
            announcements_config_path = os.path.join(config_dir, 'announcements_config.json')
            with open(announcements_config_path, 'w', encoding='utf-8') as f:
                json.dump(announcements_config, f, indent=4, ensure_ascii=False)
                
            print(f"✅ 公告配置: 已创建公告专用配置文件")
            return True
            
        except Exception as e:
            print(f"❌ 公告配置: 读取或创建配置失败 - {e}")
            return False
            
    except Exception as e:
        print(f"❌ 公告配置处理失败: {e}")
        return False


def main():
    """主函数 - 认证后启动PyQt"""
    try:
        print("🚀 启动Augment续杯工具")
        
        # 强制设置配置路径
        config_dir = force_config_path()
        print(f"📂 使用配置目录: {config_dir}")
        
        # 检查并修复配置路径问题
        check_config_path_problems()
        
        # 复制Release目录中的配置文件
        copy_release_config()
        
        # 确保服务器配置正确
        ensure_server_config()
        
        # 确保公告页面配置正确
        ensure_announcements_config()

        # 启用快速导入和模块优化
        print("⚡ 启用快速导入优化...")
        try:
            from src.utils.module_optimizer import enable_fast_imports
            enable_fast_imports()
            print("✅ 快速导入已启用")
        except Exception as e:
            print(f"⚠️ 快速导入启用失败: {e}")

        # 快速启动基础性能优化
        print("⚡ 启动基础性能优化...")
        try:
            from src.utils.performance_optimizer import optimize_startup
            optimize_startup()
            print("✅ 基础性能优化完成")
        except Exception as e:
            print(f"⚠️ 基础性能优化失败: {e}")

        # 启动多线程I/O和网络优化
        print("🚀 启动多线程I/O和网络优化...")
        try:
            # 使用简化版异步网络（避免asyncio兼容性问题）
            from src.utils.simple_async_network import get_simple_network_manager
            network_manager = get_simple_network_manager()
            print("✅ 简化版异步网络管理器已启动")

            # 初始化多线程I/O管理器
            from src.utils.threaded_io import get_io_manager
            io_manager = get_io_manager()
            print("✅ 多线程I/O管理器已启动")

        except Exception as e:
            print(f"⚠️ 多线程优化启动失败: {e}")
            print("🔄 继续使用标准网络请求")

        # 创建默认配置文件
        print("📋 检查并创建默认配置...")
        try:
            # 获取配置目录
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR', '')
            if not config_dir:
                print("❌ 错误: AUGMENT_CONFIG_DIR环境变量未设置")
                if hasattr(sys, '_MEIPASS'):
                    # 打包后的临时目录
                    config_dir = os.path.dirname(sys.executable)
                else:
                    # 开发环境
                    config_dir = os.path.dirname(os.path.abspath(__file__))
                print(f"⚠️ 使用备用配置目录: {config_dir}")

            config_path = os.path.join(config_dir, 'config.json')
            print(f"📄 配置文件路径: {config_path}")
            
            if not os.path.exists(config_path):
                print(f"📝 创建默认配置文件: {config_path}")
                default_config = {
                    "temp_mail_address": "",
                    "temp_mail_pin": "",
                    "auto_refresh_enabled": True,
                    "refresh_interval": 10,
                    "server_address": "",
                    "auth_server_url": "http://127.0.0.1:777",  # 默认服务器地址
                    "auth_enabled": True,
                    "require_auth": False,  # 设置为False允许无需认证启动
                    "theme": "dark",
                    "language": "zh_CN",
                    "auth_config": {
                        "enabled": True,
                        "require_auth_for_start": False,
                        "server": "http://127.0.0.1:777"
                    }
                }

                # 确保目录存在
                os.makedirs(os.path.dirname(config_path), exist_ok=True)
                
                import json
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                print("✅ 默认配置文件已创建")
            else:
                print("✅ 配置文件已存在")
        except Exception as e:
            print(f"⚠️ 配置文件创建失败: {e}")
            import traceback
            traceback.print_exc()

        # 初始化后台认证系统
        print("🔐 初始化后台认证系统...")
        security_manager = None

        # 跳过认证系统，直接进入主界面
        print("⚠️ 已跳过认证系统，直接进入主界面（测试/开发模式）")
        security_manager = None

        # 添加src目录到Python路径
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        # 快速启动PyQt UI - 延迟加载优化
        print("🎨 快速启动界面...")

        # 延迟导入PyQt，避免阻塞启动
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer

        # 创建应用实例
        if not QApplication.instance():
            qt_app = QApplication(sys.argv)
        else:
            qt_app = QApplication.instance()

        # 延迟加载主窗口
        def load_main_window():
            try:
                from src.gui.pyqt_ui.pyqt_main_window import PyQtMainWindow
                print("✅ 主窗口模块导入完成")

                app = PyQtMainWindow()

                # 传递安全管理器给GUI
                if security_manager:
                    app.set_security_manager(security_manager)

                app.show()
                print("✅ 主窗口显示完成")

                # 延迟显示帮助信息，避免阻塞界面
                QTimer.singleShot(1000, show_help_info)

            except Exception as e:
                print(f"❌ 主窗口加载失败: {e}")
                import traceback
                traceback.print_exc()

        # 使用定时器延迟加载，让界面先显示
        QTimer.singleShot(100, load_main_window)

        print("🎉 界面启动中...")
        return qt_app.exec_()

    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
