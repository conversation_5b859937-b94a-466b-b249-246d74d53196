"""
重构的论坛UI界面 - 基于PyQt5标准规范和最佳实践
采用MVC架构模式，组件化设计，现代化Material Design风格
"""

import sys
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.gui.pyqt_ui.pyqt_design_system import DS
from src.utils.logger import get_logger


class PostType(Enum):
    """帖子类型枚举"""
    DISCUSSION = "discussion"
    QUESTION = "question"
    ANNOUNCEMENT = "announcement"
    FEEDBACK = "feedback"


@dataclass
class PostData:
    """帖子数据模型"""
    id: int
    title: str
    content: str
    author: str
    post_type: PostType
    status: int  # 1=正常, 2=置顶
    replies_count: int = 0
    likes_count: int = 0
    views_count: int = 0
    created_at: str = ""
    
    @classmethod
    def from_dict(cls, data: dict) -> 'PostData':
        """从字典创建PostData实例"""
        return cls(
            id=int(data.get('id', 0)),
            title=str(data.get('title', '')),
            content=str(data.get('content', '')),
            author=str(data.get('author', '')),
            post_type=PostType(data.get('post_type', 'discussion')),
            status=int(data.get('status', 1)),
            replies_count=int(data.get('replies_count', 0)),
            likes_count=int(data.get('likes_count', 0)),
            views_count=int(data.get('views_count', 0)),
            created_at=str(data.get('created_at', ''))
        )


class ForumTheme:
    """论坛主题配置"""
    
    # 颜色配置
    COLORS = {
        'primary': '#1e1e1e',
        'secondary': '#2d2d2d',
        'tertiary': '#3d3d3d',
        'accent': '#00bcd4',
        'success': '#4caf50',
        'warning': '#ff9800',
        'error': '#f44336',
        'text_primary': '#ffffff',
        'text_secondary': '#cccccc',
        'text_tertiary': '#999999',
        'border': '#444444',
        'hover': '#404040'
    }
    
    # 尺寸配置
    SIZES = {
        'border_radius': 12,
        'card_height': 120,
        'button_height': 36,
        'spacing_xs': 4,
        'spacing_sm': 8,
        'spacing_md': 12,
        'spacing_lg': 16,
        'spacing_xl': 20
    }
    
    # 字体配置
    FONTS = {
        'title': ('Microsoft YaHei UI', 18, QFont.Bold),
        'heading': ('Microsoft YaHei UI', 16, QFont.Bold),
        'body': ('Microsoft YaHei UI', 14, QFont.Normal),
        'caption': ('Microsoft YaHei UI', 12, QFont.Normal),
        'small': ('Microsoft YaHei UI', 10, QFont.Normal)
    }


class MaterialButton(QPushButton):
    """Material Design风格按钮组件"""
    
    def __init__(self, text: str, button_type: str = 'primary', parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self._setup_ui()
        
    def _setup_ui(self):
        """设置按钮UI"""
        self.setMinimumHeight(ForumTheme.SIZES['button_height'])
        self.setCursor(Qt.PointingHandCursor)
        
        # 根据按钮类型设置样式
        styles = {
            'primary': f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {ForumTheme.COLORS['accent']},
                        stop:1 {self._darken_color(ForumTheme.COLORS['accent'], 0.2)});
                    color: white;
                    border: none;
                    border-radius: {ForumTheme.SIZES['border_radius']//2}px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {self._lighten_color(ForumTheme.COLORS['accent'], 0.1)},
                        stop:1 {ForumTheme.COLORS['accent']});
                }}
                QPushButton:pressed {{
                    background: {self._darken_color(ForumTheme.COLORS['accent'], 0.3)};
                }}
            """,
            'secondary': f"""
                QPushButton {{
                    background: {ForumTheme.COLORS['secondary']};
                    color: {ForumTheme.COLORS['text_primary']};
                    border: 1px solid {ForumTheme.COLORS['border']};
                    border-radius: {ForumTheme.SIZES['border_radius']//2}px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background: {ForumTheme.COLORS['tertiary']};
                    border-color: {ForumTheme.COLORS['accent']};
                }}
                QPushButton:pressed {{
                    background: {ForumTheme.COLORS['hover']};
                }}
            """
        }
        
        self.setStyleSheet(styles.get(self.button_type, styles['primary']))
        
    def _darken_color(self, color: str, factor: float) -> str:
        """颜色变暗"""
        color_obj = QColor(color)
        return color_obj.darker(int(100 + factor * 100)).name()
        
    def _lighten_color(self, color: str, factor: float) -> str:
        """颜色变亮"""
        color_obj = QColor(color)
        return color_obj.lighter(int(100 + factor * 100)).name()


class MaterialCard(QFrame):
    """Material Design风格卡片组件"""
    
    clicked = pyqtSignal()
    
    def __init__(self, elevation: int = 2, parent=None):
        super().__init__(parent)
        self.elevation = elevation
        self._setup_ui()
        
    def _setup_ui(self):
        """设置卡片UI"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setCursor(Qt.PointingHandCursor)
        
        # 根据elevation设置阴影效果
        shadow_blur = self.elevation * 2
        shadow_offset = self.elevation
        
        self.setStyleSheet(f"""
            MaterialCard {{
                background: {ForumTheme.COLORS['secondary']};
                border: 1px solid {ForumTheme.COLORS['border']};
                border-radius: {ForumTheme.SIZES['border_radius']}px;
                margin: {ForumTheme.SIZES['spacing_sm']}px;
            }}
            MaterialCard:hover {{
                background: {ForumTheme.COLORS['tertiary']};
                border-color: {ForumTheme.COLORS['accent']};
            }}
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(shadow_blur)
        shadow.setOffset(0, shadow_offset)
        shadow.setColor(QColor(0, 0, 0, 50))
        self.setGraphicsEffect(shadow)
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class PostCard(MaterialCard):
    """帖子卡片组件"""
    
    post_clicked = pyqtSignal(PostData)
    
    def __init__(self, post_data: PostData, parent=None):
        super().__init__(elevation=2, parent=parent)
        self.post_data = post_data
        self._setup_content()
        
    def _setup_content(self):
        """设置卡片内容"""
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(ForumTheme.SIZES['spacing_lg'], 
                                 ForumTheme.SIZES['spacing_md'],
                                 ForumTheme.SIZES['spacing_lg'], 
                                 ForumTheme.SIZES['spacing_md'])
        layout.setSpacing(ForumTheme.SIZES['spacing_md'])
        
        # 左侧内容区域
        content_layout = QVBoxLayout()
        content_layout.setSpacing(ForumTheme.SIZES['spacing_sm'])
        
        # 头部信息
        header_layout = QHBoxLayout()
        header_layout.setSpacing(ForumTheme.SIZES['spacing_sm'])
        
        # 帖子类型图标
        type_icons = {
            PostType.DISCUSSION: '💬',
            PostType.QUESTION: '❓',
            PostType.ANNOUNCEMENT: '📢',
            PostType.FEEDBACK: '💡'
        }
        
        type_icon = QLabel(type_icons.get(self.post_data.post_type, '💬'))
        type_icon.setFont(QFont('Segoe UI Emoji', 16))
        header_layout.addWidget(type_icon)
        
        # 置顶标识
        if self.post_data.status == 2:
            pin_label = QLabel('📌 置顶')
            pin_label.setStyleSheet(f"color: {ForumTheme.COLORS['error']}; font-weight: bold;")
            header_layout.addWidget(pin_label)
        
        # 标题
        title_label = QLabel(self.post_data.title)
        title_font = QFont(*ForumTheme.FONTS['heading'])
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_primary']}; font-weight: bold;")
        title_label.setWordWrap(True)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        content_layout.addLayout(header_layout)
        
        # 内容预览
        content_preview = self.post_data.content[:80] + '...' if len(self.post_data.content) > 80 else self.post_data.content
        content_label = QLabel(content_preview)
        content_font = QFont(*ForumTheme.FONTS['body'])
        content_label.setFont(content_font)
        content_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_secondary']};")
        content_label.setWordWrap(True)
        content_layout.addWidget(content_label)
        
        # 底部信息
        footer_layout = QHBoxLayout()
        footer_layout.setSpacing(ForumTheme.SIZES['spacing_lg'])
        
        # 作者信息
        author_label = QLabel(f"👤 {self.post_data.author}")
        author_font = QFont(*ForumTheme.FONTS['caption'])
        author_label.setFont(author_font)
        author_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_tertiary']};")
        footer_layout.addWidget(author_label)
        
        # 时间信息
        if self.post_data.created_at:
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(self.post_data.created_at.replace('Z', '+00:00'))
                time_str = dt.strftime('%m-%d %H:%M')
                time_label = QLabel(f"🕒 {time_str}")
                time_font = QFont(*ForumTheme.FONTS['caption'])
                time_label.setFont(time_font)
                time_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_tertiary']};")
                footer_layout.addWidget(time_label)
            except:
                pass
        
        footer_layout.addStretch()
        content_layout.addLayout(footer_layout)
        
        layout.addLayout(content_layout, 3)
        
        # 右侧统计信息
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(ForumTheme.SIZES['spacing_xs'])
        stats_layout.setAlignment(Qt.AlignCenter)
        
        # 回复数
        replies_label = QLabel(f"{self.post_data.replies_count}")
        replies_font = QFont(*ForumTheme.FONTS['heading'])
        replies_label.setFont(replies_font)
        replies_label.setStyleSheet(f"color: {ForumTheme.COLORS['accent']}; font-weight: bold;")
        replies_label.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(replies_label)
        
        replies_text = QLabel("回复")
        replies_text_font = QFont(*ForumTheme.FONTS['caption'])
        replies_text.setFont(replies_text_font)
        replies_text.setStyleSheet(f"color: {ForumTheme.COLORS['text_tertiary']};")
        replies_text.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(replies_text)
        
        # 点赞数
        if self.post_data.likes_count > 0:
            likes_label = QLabel(f"👍 {self.post_data.likes_count}")
            likes_font = QFont(*ForumTheme.FONTS['caption'])
            likes_label.setFont(likes_font)
            likes_label.setStyleSheet(f"color: {ForumTheme.COLORS['success']};")
            likes_label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(likes_label)
        
        # 浏览数
        if self.post_data.views_count > 0:
            views_label = QLabel(f"👁️ {self.post_data.views_count}")
            views_font = QFont(*ForumTheme.FONTS['caption'])
            views_label.setFont(views_font)
            views_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_tertiary']};")
            views_label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(views_label)
        
        layout.addLayout(stats_layout, 1)
        
        # 连接点击信号
        self.clicked.connect(lambda: self.post_clicked.emit(self.post_data))


class CategoryFilter(QWidget):
    """分类筛选组件"""
    
    category_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_category = ''
        self.filter_buttons = {}
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        # 容器
        container = QFrame()
        container.setStyleSheet(f"""
            QFrame {{
                background: {ForumTheme.COLORS['secondary']};
                border: 1px solid {ForumTheme.COLORS['border']};
                border-radius: {ForumTheme.SIZES['border_radius']}px;
                padding: {ForumTheme.SIZES['spacing_sm']}px;
            }}
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(container)
        
        container_layout = QHBoxLayout(container)
        container_layout.setContentsMargins(ForumTheme.SIZES['spacing_md'], 
                                          ForumTheme.SIZES['spacing_sm'],
                                          ForumTheme.SIZES['spacing_md'], 
                                          ForumTheme.SIZES['spacing_sm'])
        container_layout.setSpacing(ForumTheme.SIZES['spacing_sm'])
        
        # 标签
        label = QLabel("分类:")
        label_font = QFont(*ForumTheme.FONTS['body'])
        label.setFont(label_font)
        label.setStyleSheet(f"color: {ForumTheme.COLORS['text_secondary']}; font-weight: bold;")
        container_layout.addWidget(label)
        
        # 分类按钮
        categories = [
            ('', '全部', '📋'),
            ('discussion', '讨论', '💬'),
            ('question', '问答', '❓'),
            ('announcement', '公告', '📢'),
            ('feedback', '反馈', '💡')
        ]
        
        for cat_type, cat_name, cat_icon in categories:
            btn = QPushButton(f"{cat_icon} {cat_name}")
            btn.setCheckable(True)
            btn.setMinimumSize(80, 32)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: transparent;
                    border: 1px solid transparent;
                    border-radius: {ForumTheme.SIZES['border_radius']//2}px;
                    padding: 6px 12px;
                    color: {ForumTheme.COLORS['text_secondary']};
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background: {ForumTheme.COLORS['tertiary']};
                    color: {ForumTheme.COLORS['text_primary']};
                }}
                QPushButton:checked {{
                    background: {ForumTheme.COLORS['accent']};
                    color: white;
                    border-color: {ForumTheme.COLORS['accent']};
                }}
            """)
            btn.clicked.connect(lambda checked, t=cat_type: self._on_category_clicked(t))
            self.filter_buttons[cat_type] = btn
            container_layout.addWidget(btn)
        
        # 默认选中"全部"
        self.filter_buttons[''].setChecked(True)
        
        container_layout.addStretch()
        
    def _on_category_clicked(self, category: str):
        """分类点击事件"""
        # 更新按钮状态
        for cat, btn in self.filter_buttons.items():
            btn.setChecked(cat == category)
        
        self.current_category = category
        self.category_changed.emit(category)


class ForumHeader(QWidget):
    """论坛头部组件"""
    
    new_post_clicked = pyqtSignal()
    refresh_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(ForumTheme.SIZES['spacing_sm'], 
                                 ForumTheme.SIZES['spacing_sm'],
                                 ForumTheme.SIZES['spacing_sm'], 
                                 ForumTheme.SIZES['spacing_sm'])
        layout.setSpacing(ForumTheme.SIZES['spacing_lg'])
        
        # 标题
        title_label = QLabel("🏛️ 社区论坛")
        title_font = QFont(*ForumTheme.FONTS['title'])
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_primary']}; font-weight: bold;")
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # 按钮组
        button_layout = QHBoxLayout()
        button_layout.setSpacing(ForumTheme.SIZES['spacing_sm'])
        
        # 发帖按钮
        new_post_btn = MaterialButton("✏️ 发布新帖", 'primary')
        new_post_btn.clicked.connect(self.new_post_clicked.emit)
        button_layout.addWidget(new_post_btn)
        
        # 刷新按钮
        refresh_btn = MaterialButton("🔄 刷新", 'secondary')
        refresh_btn.clicked.connect(self.refresh_clicked.emit)
        button_layout.addWidget(refresh_btn)
        
        layout.addLayout(button_layout)


class PostListView(QWidget):
    """帖子列表视图组件"""

    post_clicked = pyqtSignal(PostData)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.posts = []
        self._setup_ui()

    def _setup_ui(self):
        """设置UI"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 滚动区域容器
        scroll_container = MaterialCard(elevation=1)
        layout.addWidget(scroll_container)

        container_layout = QVBoxLayout(scroll_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background: transparent;
                border-radius: {ForumTheme.SIZES['border_radius']}px;
            }}
            QScrollBar:vertical {{
                background: {ForumTheme.COLORS['primary']};
                width: 8px;
                border-radius: 4px;
                margin: 0;
            }}
            QScrollBar::handle:vertical {{
                background: {ForumTheme.COLORS['accent']};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {self._lighten_color(ForumTheme.COLORS['accent'], 0.2)};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}
        """)

        # 滚动内容
        self.scroll_content = QWidget()
        self.scroll_content.setStyleSheet("background: transparent;")
        self.posts_layout = QVBoxLayout(self.scroll_content)
        self.posts_layout.setContentsMargins(ForumTheme.SIZES['spacing_md'],
                                           ForumTheme.SIZES['spacing_md'],
                                           ForumTheme.SIZES['spacing_md'],
                                           ForumTheme.SIZES['spacing_md'])
        self.posts_layout.setSpacing(ForumTheme.SIZES['spacing_sm'])
        self.posts_layout.setAlignment(Qt.AlignTop)

        self.scroll_area.setWidget(self.scroll_content)
        container_layout.addWidget(self.scroll_area)

        # 状态标签
        self.status_label = QLabel("正在加载论坛数据...")
        status_font = QFont(*ForumTheme.FONTS['body'])
        self.status_label.setFont(status_font)
        self.status_label.setStyleSheet(f"""
            color: {ForumTheme.COLORS['text_secondary']};
            padding: {ForumTheme.SIZES['spacing_xl']}px;
            background: {ForumTheme.COLORS['secondary']};
            border: 1px solid {ForumTheme.COLORS['border']};
            border-radius: {ForumTheme.SIZES['border_radius']}px;
            margin: {ForumTheme.SIZES['spacing_sm']}px;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)

    def _lighten_color(self, color: str, factor: float) -> str:
        """颜色变亮"""
        color_obj = QColor(color)
        return color_obj.lighter(int(100 + factor * 100)).name()

    def set_posts(self, posts: List[PostData]):
        """设置帖子列表"""
        self.posts = posts
        self._display_posts()

    def _display_posts(self):
        """显示帖子列表"""
        # 隐藏状态标签
        self.status_label.hide()

        # 清空现有帖子
        self._clear_posts()

        if not self.posts:
            # 显示空状态
            empty_widget = self._create_empty_state()
            self.posts_layout.addWidget(empty_widget)
        else:
            # 显示帖子卡片
            for post in self.posts:
                card = PostCard(post)
                card.post_clicked.connect(self.post_clicked.emit)
                self.posts_layout.addWidget(card)

        # 添加弹性空间
        self.posts_layout.addStretch()

    def _clear_posts(self):
        """清空帖子列表"""
        while self.posts_layout.count():
            child = self.posts_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def _create_empty_state(self) -> QWidget:
        """创建空状态组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(ForumTheme.SIZES['spacing_md'])

        # 空状态图标
        icon_label = QLabel("📭")
        icon_label.setFont(QFont('Segoe UI Emoji', 48))
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # 空状态文字
        text_label = QLabel("暂无帖子")
        text_font = QFont(*ForumTheme.FONTS['heading'])
        text_label.setFont(text_font)
        text_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_secondary']};")
        text_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(text_label)

        # 提示文字
        hint_label = QLabel("快来发布第一个帖子吧！")
        hint_font = QFont(*ForumTheme.FONTS['body'])
        hint_label.setFont(hint_font)
        hint_label.setStyleSheet(f"color: {ForumTheme.COLORS['text_tertiary']};")
        hint_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(hint_label)

        return widget

    def show_loading(self, message: str = "正在加载..."):
        """显示加载状态"""
        self.status_label.setText(message)
        self.status_label.show()
        self._clear_posts()

    def show_error(self, message: str):
        """显示错误状态"""
        self.status_label.setText(f"❌ {message}")
        self.status_label.show()
        self._clear_posts()


class RefactoredForumPage(QWidget):
    """重构的论坛页面 - 主控制器"""

    def __init__(self, main_window=None):
        super().__init__(parent=main_window)
        self.main_window = main_window
        self.logger = get_logger()
        self.current_category = ''
        self._setup_ui()
        self._connect_signals()
        self._load_initial_data()

    def _setup_ui(self):
        """设置UI"""
        # 设置主题样式
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {ForumTheme.COLORS['primary']};
                color: {ForumTheme.COLORS['text_primary']};
            }}
        """)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(ForumTheme.SIZES['spacing_lg'],
                                     ForumTheme.SIZES['spacing_lg'],
                                     ForumTheme.SIZES['spacing_lg'],
                                     ForumTheme.SIZES['spacing_lg'])
        main_layout.setSpacing(ForumTheme.SIZES['spacing_lg'])

        # 头部组件
        self.header = ForumHeader()
        main_layout.addWidget(self.header)

        # 分类筛选 (保留)
        self.category_filter = CategoryFilter()
        main_layout.addWidget(self.category_filter)

        # 帖子列表
        self.post_list = PostListView()
        main_layout.addWidget(self.post_list)

    def _connect_signals(self):
        """连接信号槽"""
        # 头部信号
        self.header.new_post_clicked.connect(self._on_new_post_clicked)
        self.header.refresh_clicked.connect(self._on_refresh_clicked)

        # 分类筛选信号
        self.category_filter.category_changed.connect(self._on_category_changed)

        # 帖子列表信号
        self.post_list.post_clicked.connect(self._on_post_clicked)

    def _load_initial_data(self):
        """加载初始数据"""
        self.post_list.show_loading("正在加载论坛数据...")

        # 模拟数据加载
        QTimer.singleShot(1000, self._load_mock_data)

    def _load_mock_data(self):
        """加载模拟数据"""
        try:
            # 模拟帖子数据
            mock_posts = [
                PostData(
                    id=1,
                    title="🎉 Augment续杯工具重大更新 - 全新论坛系统上线",
                    content="我们很高兴地宣布，Augment续杯工具迎来了一次重大更新！本次更新的核心亮点是全新的论坛系统...",
                    author="admin",
                    post_type=PostType.ANNOUNCEMENT,
                    status=2,  # 置顶
                    replies_count=15,
                    likes_count=8,
                    views_count=256,
                    created_at="2024-01-15T10:30:00"
                ),
                PostData(
                    id=2,
                    title="❓ 如何解决VSCode清理后的配置问题？",
                    content="清理后发现一些配置丢失了，有什么解决办法吗？特别是插件配置和主题设置...",
                    author="user123",
                    post_type=PostType.QUESTION,
                    status=1,
                    replies_count=3,
                    likes_count=2,
                    views_count=45,
                    created_at="2024-01-15T14:20:00"
                ),
                PostData(
                    id=3,
                    title="💡 建议增加邮箱自动清理功能",
                    content="希望能够定时清理临时邮箱，避免手动操作。可以设置清理间隔和保留时间...",
                    author="feedback_user",
                    post_type=PostType.FEEDBACK,
                    status=1,
                    replies_count=1,
                    likes_count=5,
                    views_count=23,
                    created_at="2024-01-15T16:45:00"
                )
            ]

            # 更新帖子列表
            self.post_list.set_posts(mock_posts)

            if self.logger:
                self.logger.info(f"论坛模拟数据加载完成，共 {len(mock_posts)} 条帖子")

        except Exception as e:
            self.post_list.show_error(f"数据加载失败: {str(e)}")
            if self.logger:
                self.logger.error(f"论坛数据加载失败: {e}")

    def _on_new_post_clicked(self):
        """发帖按钮点击事件"""
        try:
            # TODO: 实现发帖对话框
            QMessageBox.information(self, "提示", "发帖功能开发中...")
            if self.logger:
                self.logger.info("用户点击发帖按钮")
        except Exception as e:
            if self.logger:
                self.logger.error(f"发帖按钮点击处理失败: {e}")

    def _on_refresh_clicked(self):
        """刷新按钮点击事件"""
        try:
            self.post_list.show_loading("正在刷新数据...")
            QTimer.singleShot(500, self._load_mock_data)
            if self.logger:
                self.logger.info("用户点击刷新按钮")
        except Exception as e:
            if self.logger:
                self.logger.error(f"刷新按钮点击处理失败: {e}")

    def _on_category_changed(self, category: str):
        """分类变更事件"""
        try:
            self.current_category = category
            self.post_list.show_loading(f"正在加载{category or '全部'}分类...")
            QTimer.singleShot(300, self._load_mock_data)
            if self.logger:
                self.logger.info(f"用户切换分类: {category or '全部'}")
        except Exception as e:
            if self.logger:
                self.logger.error(f"分类切换处理失败: {e}")

    def _on_post_clicked(self, post_data: PostData):
        """帖子点击事件"""
        try:
            # TODO: 实现帖子详情对话框
            QMessageBox.information(self, "帖子详情", f"点击了帖子: {post_data.title}")
            if self.logger:
                self.logger.info(f"用户点击帖子: {post_data.title}")
        except Exception as e:
            if self.logger:
                self.logger.error(f"帖子点击处理失败: {e}")


# 导出主要类
__all__ = [
    'RefactoredForumPage',
    'PostData',
    'PostType',
    'ForumTheme',
    'MaterialButton',
    'MaterialCard',
    'PostCard'
]
