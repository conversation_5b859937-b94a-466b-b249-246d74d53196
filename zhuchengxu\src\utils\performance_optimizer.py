"""
性能优化器 - 使用第三方库优化程序性能
"""

import gc
import threading
import time
from typing import Optional
import sys
import os
import io

# 添加lib目录到路径
lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

try:
    import psutil
    PSUTIL_AVAILABLE = True
    print("✅ psutil已加载，完整性能监控可用")
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil未安装，使用内置性能监控")

try:
    from cachetools import TTLCache, LRUCache
    CACHE_AVAILABLE = True
    print("✅ cachetools已加载，智能缓存可用")
except ImportError:
    CACHE_AVAILABLE = False
    print("⚠️ cachetools未安装，使用内置缓存")

# 导入高性能工具
try:
    from src.utils.high_performance_utils import (
        HighPerformanceCache, fast_json_dumps, fast_json_loads,
        fast_compress, fast_decompress, PerformanceTimer
    )
    HIGH_PERF_AVAILABLE = True
    print("✅ 高性能工具已加载")
except ImportError:
    HIGH_PERF_AVAILABLE = False
    print("⚠️ 高性能工具加载失败")

    # 内置简单缓存实现
    class SimpleTTLCache:
        def __init__(self, maxsize=1000, ttl=300):
            self.maxsize = maxsize
            self.ttl = ttl
            self.data = {}
            self.timestamps = {}

        def get(self, key, default=None):
            if key in self.data:
                if time.time() - self.timestamps[key] < self.ttl:
                    return self.data[key]
                else:
                    del self.data[key]
                    del self.timestamps[key]
            return default

        def __setitem__(self, key, value):
            if len(self.data) >= self.maxsize:
                # 删除最旧的项目
                oldest_key = min(self.timestamps.keys(), key=lambda k: self.timestamps[k])
                del self.data[oldest_key]
                del self.timestamps[oldest_key]
            self.data[key] = value
            self.timestamps[key] = time.time()

        def __getitem__(self, key):
            return self.get(key)

        def __len__(self):
            return len(self.data)

        def clear(self):
            self.data.clear()
            self.timestamps.clear()

    TTLCache = SimpleTTLCache


class PerformanceOptimizer:
    """性能优化器"""

    def __init__(self):
        self.process = None
        self.cache = None
        self.gc_thread = None
        self.gc_running = False

        # 初始化日志记录器
        try:
            from ..utils.logger import Logger
            self.logger = Logger().get_logger()
        except:
            import logging
            self.logger = logging.getLogger(__name__)

        # 初始化
        self._init_process_monitor()
        self._init_cache()
        self._start_gc_optimizer()
        
    def _init_process_monitor(self):
        """初始化进程监控"""
        if PSUTIL_AVAILABLE:
            try:
                self.process = psutil.Process()
                print("✅ 完整进程监控已启用")
            except Exception as e:
                print(f"⚠️ 进程监控初始化失败: {e}")
        else:
            # 使用内置监控
            import os
            self.process = None
            self.pid = os.getpid()
            print("✅ 内置进程监控已启用")
        
    def _init_cache(self):
        """初始化缓存系统"""
        if CACHE_AVAILABLE:
            # 创建TTL缓存，5分钟过期，最大1000项
            self.cache = TTLCache(maxsize=1000, ttl=300)
            print("✅ 智能缓存已启用")
        
    def _start_gc_optimizer(self):
        """启动垃圾回收优化器"""
        self.gc_running = True
        self.gc_thread = threading.Thread(target=self._gc_loop, daemon=True)
        self.gc_thread.start()
        print("✅ 内存优化器已启动")
        
    def _gc_loop(self):
        """垃圾回收循环"""
        while self.gc_running:
            try:
                # 每30秒执行一次垃圾回收
                time.sleep(30)
                
                # 强制垃圾回收
                collected = gc.collect()
                if collected > 0:
                    print(f"🗑️ 清理了 {collected} 个对象")
                    
                # 内存压缩
                if PSUTIL_AVAILABLE and self.process:
                    memory_info = self.process.memory_info()
                    if memory_info.rss > 200 * 1024 * 1024:  # 超过200MB
                        gc.collect()
                        print("💾 执行内存压缩")
                        
            except Exception as e:
                print(f"⚠️ 垃圾回收异常: {e}")
                time.sleep(60)  # 出错时等待更长时间
                
    def get_memory_usage(self) -> dict:
        """获取内存使用情况"""
        if not PSUTIL_AVAILABLE or not self.process:
            return {"error": "psutil不可用"}
            
        try:
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            return {
                "rss": memory_info.rss / 1024 / 1024,  # MB
                "vms": memory_info.vms / 1024 / 1024,  # MB
                "percent": memory_percent,
                "available": psutil.virtual_memory().available / 1024 / 1024  # MB
            }
        except Exception as e:
            return {"error": str(e)}
            
    def get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        if not PSUTIL_AVAILABLE or not self.process:
            return 0.0
            
        try:
            return self.process.cpu_percent()
        except Exception:
            return 0.0
            
    def optimize_memory(self):
        """立即优化内存 - VSCode启发的全面优化"""
        try:
            self.logger.info("🧹 开始全面内存优化...")
            results = {
                "gc_collected": 0,
                "cache_cleared": False,
                "file_watchers_reduced": False,
                "memory_before": 0,
                "memory_after": 0
            }
            
            # 1. 记录优化前内存使用
            if PSUTIL_AVAILABLE and self.process:
                try:
                    memory_info = self.process.memory_info()
                    results["memory_before"] = memory_info.rss / 1024 / 1024  # MB
                    self.logger.info(f"💾 优化前内存: {results['memory_before']:.1f} MB")
                except Exception as e:
                    self.logger.warning(f"⚠️ 内存测量失败: {e}")
            
            # 2. 强制垃圾回收
            collected = gc.collect(2)  # 完整垃圾回收
            results["gc_collected"] = collected
            self.logger.info(f"🗑️ 垃圾回收: 清理了 {collected} 个对象")
            
            # 3. 清理所有缓存
            if self.cache:
                cache_size = len(self.cache)
                self.cache.clear()
                results["cache_cleared"] = True
                self.logger.info(f"🧼 已清理 {cache_size} 个缓存项")
                
            # 4. 移除多余的文件监视器 - VSCode方式
            try:
                # 尝试获取和减少系统文件监视器
                import os
                if os.name == 'posix':  # Linux/Mac
                    # 使用lsof检查文件描述符
                    try:
                        import subprocess
                        pid = os.getpid()
                        result = subprocess.run(['lsof', '-p', str(pid)], 
                                               capture_output=True, text=True, check=False)
                        if result.returncode == 0:
                            file_count = len(result.stdout.strip().split('\n')) - 1
                            self.logger.info(f"📁 当前打开的文件描述符: {file_count}")
                    except Exception:
                        pass
                
                # 清除Python内部的文件缓存
                if hasattr(gc, 'get_objects'):
                    file_objects_before = 0
                    file_objects_after = 0
                    
                    # 计算文件对象数量
                    for obj in gc.get_objects():
                        if isinstance(obj, io.IOBase) and hasattr(obj, 'name'):
                            file_objects_before += 1
                    
                    # 尝试关闭未使用的文件
                    for obj in gc.get_objects():
                        if isinstance(obj, io.IOBase) and not obj.closed:
                            try:
                                if not obj.isatty() and hasattr(obj, 'name'):
                                    # 仅关闭常规文件，不关闭标准输入/输出
                                    standard_streams = [sys.stdin, sys.stdout, sys.stderr]
                                    if obj not in standard_streams:
                                        obj.close()
                            except Exception:
                                pass
                    
                    # 重新计算文件对象
                    for obj in gc.get_objects():
                        if isinstance(obj, io.IOBase) and hasattr(obj, 'name'):
                            file_objects_after += 1
                    
                    if file_objects_before > file_objects_after:
                        results["file_watchers_reduced"] = True
                        self.logger.info(f"📁 已关闭 {file_objects_before - file_objects_after} 个文件句柄")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 文件监视器优化失败: {e}")
                
            # 5. 触发内存压缩 - 类似VSCode的compact
            try:
                # 再次运行垃圾回收
                gc.collect()
                
                # 在一些系统上，可以请求操作系统回收内存页
                if PSUTIL_AVAILABLE and hasattr(psutil, 'Process'):
                    # Windows特定API
                    if os.name == 'nt' and hasattr(self.process, 'memory_maps'):
                        try:
                            import ctypes
                            ctypes.windll.psapi.EmptyWorkingSet(ctypes.windll.kernel32.GetCurrentProcess())
                            self.logger.info("🧽 已请求操作系统清理工作集")
                        except Exception:
                            pass
            except Exception as e:
                self.logger.warning(f"⚠️ 内存压缩失败: {e}")
                
            # 6. 最后记录内存使用
            if PSUTIL_AVAILABLE and self.process:
                try:
                    memory_info = self.process.memory_info()
                    results["memory_after"] = memory_info.rss / 1024 / 1024  # MB
                    memory_saved = results["memory_before"] - results["memory_after"]
                    self.logger.info(f"💾 优化后内存: {results['memory_after']:.1f} MB")
                    self.logger.info(f"✅ 节省内存: {memory_saved:.1f} MB")
                except Exception as e:
                    self.logger.warning(f"⚠️ 内存测量失败: {e}")
            
            self.logger.info("🚀 内存优化完成")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 内存优化失败: {e}")
            import traceback
            self.logger.debug(traceback.format_exc())
            return False
            
    def cache_get(self, key: str):
        """从缓存获取数据"""
        if not self.cache:
            return None
        return self.cache.get(key)
        
    def cache_set(self, key: str, value, ttl: Optional[int] = None):
        """设置缓存数据"""
        if not self.cache:
            return False
            
        try:
            self.cache[key] = value
            return True
        except Exception:
            return False
            
    def get_performance_stats(self) -> dict:
        """获取性能统计"""
        stats = {
            "timestamp": time.time(),
            "gc_count": gc.get_count(),
            "cache_size": len(self.cache) if self.cache else 0
        }
        
        # 添加内存信息
        memory_info = self.get_memory_usage()
        if "error" not in memory_info:
            stats.update(memory_info)
            
        # 添加CPU信息
        stats["cpu_percent"] = self.get_cpu_usage()
        
        return stats
        
    def set_process_priority(self, priority: str = "normal"):
        """设置进程优先级"""
        if not PSUTIL_AVAILABLE or not self.process:
            return False
            
        try:
            priority_map = {
                "low": psutil.BELOW_NORMAL_PRIORITY_CLASS,
                "normal": psutil.NORMAL_PRIORITY_CLASS,
                "high": psutil.ABOVE_NORMAL_PRIORITY_CLASS,
                "realtime": psutil.REALTIME_PRIORITY_CLASS
            }
            
            if priority in priority_map:
                self.process.nice(priority_map[priority])
                print(f"⚡ 进程优先级设置为: {priority}")
                return True
        except Exception as e:
            print(f"⚠️ 设置进程优先级失败: {e}")
            
        return False
        
    def stop(self):
        """停止优化器"""
        self.gc_running = False
        if self.gc_thread:
            self.gc_thread.join(timeout=5)
        print("🛑 性能优化器已停止")


# 全局优化器实例
_optimizer = None

def get_optimizer() -> PerformanceOptimizer:
    """获取全局优化器实例"""
    global _optimizer
    if _optimizer is None:
        _optimizer = PerformanceOptimizer()
    return _optimizer

def optimize_startup():
    """启动时优化"""
    optimizer = get_optimizer()
    
    # 设置进程优先级为高
    optimizer.set_process_priority("high")
    
    # 立即优化内存
    optimizer.optimize_memory()
    
    print("🚀 启动优化完成")

def optimize_runtime():
    """运行时优化"""
    optimizer = get_optimizer()
    
    # 获取性能统计
    stats = optimizer.get_performance_stats()
    
    # 如果内存使用过高，执行优化
    if "rss" in stats and stats["rss"] > 150:  # 超过150MB
        optimizer.optimize_memory()
        
    return stats
