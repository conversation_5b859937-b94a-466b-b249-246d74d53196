"""
多进程管理器 - 为每个页面创建独立进程
主窗口负责显示，子进程负责数据处理，彻底解决线程阻塞问题
"""

import multiprocessing
import queue
import time
import json
import sys
import os
from typing import Dict, Any, Optional, Callable
from pathlib import Path


class ProcessManager:
    """多进程管理器"""
    
    def __init__(self):
        self.processes = {}  # 存储各个页面的进程
        self.queues = {}     # 存储进程间通信队列
        self.callbacks = {}  # 存储回调函数
        self.running = True
        
        # 设置多进程启动方法
        if sys.platform.startswith('win'):
            multiprocessing.set_start_method('spawn', force=True)
        
        print("✅ 多进程管理器初始化完成")
        
    def create_page_process(self, page_name: str, process_func: Callable, *args, **kwargs):
        """为页面创建独立进程"""
        try:
            # 🔧 确保子进程能继承正确的环境变量
            import sys
            current_env = os.environ.copy()

            # 🔧 使用与main.py一致的路径检测逻辑
            # 直接使用main.py中设置的环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                current_env['AUGMENT_CONFIG_DIR'] = config_dir
                print(f"🔧 为子进程继承配置目录: {config_dir}")
            else:
                # 备选方案：使用sys.argv[0]
                if sys.argv and len(sys.argv) > 0:
                    argv_path = os.path.abspath(sys.argv[0])
                    if argv_path.endswith('.exe'):
                        config_dir = os.path.dirname(argv_path)
                    else:
                        config_dir = os.path.dirname(argv_path)
                else:
                    config_dir = os.getcwd()
                current_env['AUGMENT_CONFIG_DIR'] = config_dir
                print(f"🔧 为子进程设置配置目录: {config_dir}")

            # 创建进程间通信队列
            request_queue = multiprocessing.Queue()  # 主进程 -> 子进程
            response_queue = multiprocessing.Queue() # 子进程 -> 主进程

            # 创建进程，传递环境变量
            process = multiprocessing.Process(
                target=self._process_wrapper,
                args=(process_func, request_queue, response_queue, current_env, *args),
                kwargs=kwargs,
                daemon=True
            )

            # 启动进程
            process.start()

            # 存储进程信息
            self.processes[page_name] = {
                'process': process,
                'request_queue': request_queue,
                'response_queue': response_queue,
                'status': 'running',
                'created_at': time.time()
            }

            self.queues[page_name] = {
                'request': request_queue,
                'response': response_queue
            }

            print(f"✅ 已为 {page_name} 页面创建独立进程 (PID: {process.pid})")
            return True

        except Exception as e:
            print(f"❌ 创建 {page_name} 页面进程失败: {e}")
            return False

    def _process_wrapper(self, process_func: Callable, request_queue, response_queue, env_vars: dict, *args, **kwargs):
        """进程包装器，确保子进程有正确的环境变量"""
        try:
            # 设置环境变量
            os.environ.update(env_vars)

            # 验证环境变量设置
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            print(f"🔍 子进程环境变量: AUGMENT_CONFIG_DIR = {config_dir}")

            # 调用实际的进程函数
            process_func(request_queue, response_queue, *args, **kwargs)

        except Exception as e:
            print(f"❌ 子进程包装器异常: {e}")
            import traceback
            traceback.print_exc()
            
    def send_request(self, page_name: str, request_data: Dict[str, Any]) -> bool:
        """向页面进程发送请求"""
        try:
            if page_name not in self.processes:
                print(f"⚠️ 页面进程 {page_name} 不存在")
                return False
                
            process_info = self.processes[page_name]
            if process_info['status'] != 'running':
                print(f"⚠️ 页面进程 {page_name} 未运行")
                return False
                
            # 发送请求
            request_queue = process_info['request_queue']
            request_queue.put(request_data, timeout=1.0)
            
            return True
            
        except Exception as e:
            print(f"❌ 向 {page_name} 发送请求失败: {e}")
            return False
            
    def get_response(self, page_name: str, timeout: float = 0.1) -> Optional[Dict[str, Any]]:
        """从页面进程获取响应"""
        try:
            if page_name not in self.processes:
                return None
                
            process_info = self.processes[page_name]
            response_queue = process_info['response_queue']
            
            # 非阻塞获取响应
            try:
                response = response_queue.get(timeout=timeout)
                return response
            except queue.Empty:
                return None
                
        except Exception as e:
            print(f"❌ 从 {page_name} 获取响应失败: {e}")
            return None
            
    def register_callback(self, page_name: str, callback: Callable):
        """注册页面回调函数"""
        self.callbacks[page_name] = callback
        
    def process_responses(self):
        """处理所有页面的响应（在主线程中调用）"""
        for page_name in list(self.processes.keys()):
            try:
                response = self.get_response(page_name)
                if response and page_name in self.callbacks:
                    # 调用回调函数处理响应
                    self.callbacks[page_name](response)
                    
            except Exception as e:
                print(f"❌ 处理 {page_name} 响应失败: {e}")
                
    def stop_page_process(self, page_name: str):
        """停止页面进程"""
        try:
            if page_name not in self.processes:
                return
                
            process_info = self.processes[page_name]
            process = process_info['process']
            
            # 发送停止信号
            self.send_request(page_name, {'action': 'stop'})
            
            # 等待进程结束
            process.join(timeout=2.0)
            
            if process.is_alive():
                # 强制终止
                process.terminate()
                process.join(timeout=1.0)
                
            # 清理资源
            process_info['status'] = 'stopped'
            print(f"✅ 已停止 {page_name} 页面进程")
            
        except Exception as e:
            print(f"❌ 停止 {page_name} 页面进程失败: {e}")
            
    def stop_all_processes(self):
        """停止所有页面进程"""
        print("🛑 正在停止所有页面进程...")
        
        for page_name in list(self.processes.keys()):
            self.stop_page_process(page_name)
            
        self.running = False
        print("✅ 所有页面进程已停止")
        
    def get_process_stats(self) -> Dict[str, Any]:
        """获取进程统计信息"""
        stats = {
            'total_processes': len(self.processes),
            'running_processes': 0,
            'stopped_processes': 0,
            'processes': {}
        }
        
        for page_name, process_info in self.processes.items():
            process = process_info['process']
            is_alive = process.is_alive()
            
            if is_alive:
                stats['running_processes'] += 1
            else:
                stats['stopped_processes'] += 1
                
            stats['processes'][page_name] = {
                'pid': process.pid,
                'alive': is_alive,
                'status': process_info['status'],
                'created_at': process_info['created_at'],
                'uptime': time.time() - process_info['created_at']
            }
            
        return stats


# 邮箱页面进程函数
def mailbox_process_worker(request_queue, response_queue):
    """邮箱页面进程工作函数"""
    try:
        # 导入邮箱相关模块（在子进程中）
        sys.path.insert(0, str(Path(__file__).parent.parent.parent))
        
        print(f"📧 邮箱进程启动 (PID: {os.getpid()})")
        
        # 初始化邮箱API
        mailbox_api = None
        
        while True:
            try:
                # 获取请求
                request = request_queue.get(timeout=1.0)
                
                if request.get('action') == 'stop':
                    break
                    
                elif request.get('action') == 'get_emails':
                    # 处理获取邮件请求
                    try:
                        if not mailbox_api:
                            # 延迟导入，避免主进程阻塞
                            from src.utils.email_api import EmailAPI
                            config = request.get('config', {})
                            mailbox_api = EmailAPI(config)
                        
                        emails = mailbox_api.get_emails()
                        
                        # 发送响应
                        response_queue.put({
                            'action': 'emails_result',
                            'success': True,
                            'data': emails,
                            'count': len(emails) if emails else 0
                        })
                        
                    except Exception as e:
                        response_queue.put({
                            'action': 'emails_result',
                            'success': False,
                            'error': str(e)
                        })
                        
                elif request.get('action') == 'test_connection':
                    # 处理连接测试请求
                    try:
                        from src.utils.email_api import EmailAPI
                        config = request.get('config', {})
                        api = EmailAPI(config)
                        emails = api.get_emails()
                        
                        response_queue.put({
                            'action': 'test_result',
                            'success': True,
                            'count': len(emails) if emails else 0
                        })
                        
                    except Exception as e:
                        response_queue.put({
                            'action': 'test_result',
                            'success': False,
                            'error': str(e)
                        })
                        
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ 邮箱进程处理请求失败: {e}")
                
    except Exception as e:
        print(f"❌ 邮箱进程启动失败: {e}")
    finally:
        print(f"📧 邮箱进程结束 (PID: {os.getpid()})")


# VSCode页面进程函数
def vscode_process_worker(request_queue, response_queue):
    """VSCode页面进程工作函数"""
    try:
        print(f"🔧 VSCode进程启动 (PID: {os.getpid()})")
        
        while True:
            try:
                request = request_queue.get(timeout=1.0)
                
                if request.get('action') == 'stop':
                    break
                    
                elif request.get('action') == 'scan_vscode':
                    # 处理VSCode扫描请求
                    try:
                        # 模拟VSCode扫描（实际实现时导入真实模块）
                        import time
                        time.sleep(0.1)  # 模拟扫描时间
                        
                        response_queue.put({
                            'action': 'scan_result',
                            'success': True,
                            'data': {
                                'cache_size': '125.6 MB',
                                'log_files': 45,
                                'temp_files': 23
                            }
                        })
                        
                    except Exception as e:
                        response_queue.put({
                            'action': 'scan_result',
                            'success': False,
                            'error': str(e)
                        })
                        
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ VSCode进程处理请求失败: {e}")
                
    except Exception as e:
        print(f"❌ VSCode进程启动失败: {e}")
    finally:
        print(f"🔧 VSCode进程结束 (PID: {os.getpid()})")


# 全局进程管理器实例
_process_manager = None

def get_process_manager() -> ProcessManager:
    """获取全局进程管理器"""
    global _process_manager
    if _process_manager is None:
        _process_manager = ProcessManager()
    return _process_manager

def init_page_processes():
    """初始化所有页面进程"""
    manager = get_process_manager()
    
    # 创建邮箱页面进程
    manager.create_page_process('mailbox', mailbox_process_worker)
    
    # 创建VSCode页面进程
    manager.create_page_process('vscode', vscode_process_worker)
    
    print("🚀 所有页面进程初始化完成")

def cleanup_processes():
    """清理所有进程"""
    manager = get_process_manager()
    manager.stop_all_processes()
