#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
论坛API客户端
统一的网络请求管理器，用于处理论坛API调用
"""

import json
import requests
from typing import Dict, Any, Optional, Tuple
try:
    from utils.logger import get_logger
except ImportError:
    from src.utils.logger import get_logger


class ForumAPIClient:
    """论坛API客户端"""

    def __init__(self, base_url: str = None, timeout: int = 30):
        """
        初始化API客户端

        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
        """
        self.logger = get_logger()
        self.timeout = timeout
        self.session = requests.Session()

        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Augment-Forum-Client/1.0'
        })

        # 设置基础URL
        if base_url:
            self.base_url = base_url.rstrip('/')
        else:
            self.base_url = self._get_default_base_url()

        self.logger.info(f"论坛API客户端初始化完成: {self.base_url}")
    
    def _get_default_base_url(self) -> str:
        """获取默认的基础URL"""
        try:
            # 从配置文件获取用户设置的服务器地址
            try:
                from utils.config_manager import ConfigManager
            except ImportError:
                from src.utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            auth_server_url = config_manager.get('auth_server_url')

            if auth_server_url:
                # 转换 api.php 为基础URL
                if auth_server_url.endswith('/api.php'):
                    base_url = auth_server_url.replace('/api.php', '')
                else:
                    base_url = auth_server_url.rstrip('/')

                self.logger.info(f"从配置获取服务器地址: {base_url}")
                return base_url

            # 默认地址
            default_url = 'http://127.0.0.1:81'
            self.logger.warning(f"配置中无服务器地址，使用默认: {default_url}")
            return default_url

        except Exception as e:
            self.logger.error(f"获取服务器地址失败: {e}")
            return 'http://127.0.0.1:81'
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Tuple[bool, Dict[str, Any]]:
        """
        发起HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            (success, response_data)
        """
        try:
            url = f"{self.base_url}/{endpoint.lstrip('/')}"
            
            # 设置超时
            kwargs.setdefault('timeout', self.timeout)
            
            self.logger.debug(f"发起{method}请求: {url}")
            
            response = self.session.request(method, url, **kwargs)

            # 解析JSON响应（不管状态码如何）
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = {'success': False, 'message': f'响应格式错误 (状态码: {response.status_code})'}

            # 检查HTTP状态码
            if response.status_code >= 400:
                error_msg = f"HTTP错误: {response.status_code}"
                if isinstance(data, dict):
                    error_msg += f" - {data.get('message', '未知错误')}"
                    self.logger.error(f"HTTP错误详情: {data}")
                else:
                    error_msg += f" - {response.text}"
                    self.logger.error(f"HTTP错误响应: {response.text}")

                return False, data if isinstance(data, dict) else {'success': False, 'message': error_msg}

            # 检查业务逻辑成功状态
            success = data.get('success', False)
            if success:
                self.logger.debug(f"请求成功: {url}")
            else:
                self.logger.warning(f"请求失败: {url} - {data.get('message', '未知错误')}")

            return success, data
            
        except requests.exceptions.Timeout:
            error_msg = f"请求超时: {url}"
            self.logger.error(error_msg)
            return False, {'success': False, 'message': error_msg}
            
        except requests.exceptions.ConnectionError:
            error_msg = f"连接失败: {url}"
            self.logger.error(error_msg)
            return False, {'success': False, 'message': error_msg}
            
        except requests.exceptions.HTTPError as e:
            try:
                # 尝试获取响应内容
                response_text = e.response.text
                try:
                    response_json = e.response.json()
                    error_msg = f"HTTP错误: {e.response.status_code} - {response_json.get('message', response_text)}"
                    self.logger.error(f"HTTP错误详情: {response_json}")
                    return False, response_json
                except:
                    error_msg = f"HTTP错误: {e.response.status_code} - {response_text}"
                    self.logger.error(error_msg)
                    return False, {'success': False, 'message': error_msg}
            except:
                error_msg = f"HTTP错误: {e.response.status_code}"
                self.logger.error(error_msg)
                return False, {'success': False, 'message': error_msg}
            
        except Exception as e:
            error_msg = f"请求异常: {str(e)}"
            self.logger.error(error_msg)
            return False, {'success': False, 'message': error_msg}
    
    def get_posts(self, page: int = 1, limit: int = 20, post_type: str = '') -> Tuple[bool, Dict[str, Any]]:
        """
        获取帖子列表
        
        Args:
            page: 页码
            limit: 每页数量
            post_type: 帖子类型
            
        Returns:
            (success, response_data)
        """
        params = {
            'action': 'get_posts',
            'page': page,
            'limit': limit
        }
        
        if post_type:
            params['type'] = post_type
            
        return self._make_request('GET', 'forum_api.php', params=params)
    
    def get_post(self, post_id: int) -> Tuple[bool, Dict[str, Any]]:
        """
        获取单个帖子详情
        
        Args:
            post_id: 帖子ID
            
        Returns:
            (success, response_data)
        """
        params = {
            'action': 'get_post',
            'id': post_id
        }
        
        return self._make_request('GET', 'forum_api.php', params=params)
    
    def create_post(self, username: str, title: str, content: str, 
                   post_type: str = 'discussion', attachments: list = None) -> Tuple[bool, Dict[str, Any]]:
        """
        创建新帖子
        
        Args:
            username: 用户名
            title: 帖子标题
            content: 帖子内容
            post_type: 帖子类型
            attachments: 附件列表
            
        Returns:
            (success, response_data)
        """
        data = {
            'username': username,
            'title': title,
            'content': content,
            'post_type': post_type,
            'attachments': attachments or []
        }
        
        return self._make_request('POST', 'forum_api.php?action=create_post', json=data)
    
    def create_reply(self, username: str, post_id: int, content: str, 
                    parent_id: int = None) -> Tuple[bool, Dict[str, Any]]:
        """
        创建回复
        
        Args:
            username: 用户名
            post_id: 帖子ID
            content: 回复内容
            parent_id: 父回复ID（可选）
            
        Returns:
            (success, response_data)
        """
        data = {
            'username': username,
            'post_id': post_id,
            'content': content
        }
        
        if parent_id:
            data['parent_id'] = parent_id
            
        return self._make_request('POST', 'forum_api.php?action=create_reply', json=data)

    def get_replies(self, post_id: int) -> Tuple[bool, Dict[str, Any]]:
        """
        获取帖子回复

        Args:
            post_id: 帖子ID

        Returns:
            (success, response_data)
        """
        params = {
            'action': 'get_replies',
            'post_id': post_id
        }

        return self._make_request('GET', 'forum_api.php', params=params)

    def like_post(self, username: str, post_id: int) -> Tuple[bool, Dict[str, Any]]:
        """
        点赞帖子
        
        Args:
            username: 用户名
            post_id: 帖子ID
            
        Returns:
            (success, response_data)
        """
        data = {
            'username': username,
            'target_type': 'post',
            'target_id': post_id
        }
        
        return self._make_request('POST', 'forum_api.php?action=toggle_like', json=data)

    def toggle_like(self, username: str, target_type: str, target_id: int) -> Tuple[bool, Dict[str, Any]]:
        """
        切换点赞状态（通用方法）

        Args:
            username: 用户名
            target_type: 目标类型 ('post' 或 'reply')
            target_id: 目标ID

        Returns:
            (success, response_data)
        """
        data = {
            'username': username,
            'target_type': target_type,
            'target_id': target_id
        }

        return self._make_request('POST', 'forum_api.php?action=toggle_like', json=data)

    def get_user_likes(self, username: str) -> Tuple[bool, Dict[str, Any]]:
        """
        获取用户点赞状态
        
        Args:
            username: 用户名
            
        Returns:
            (success, response_data)
        """
        params = {
            'action': 'get_user_likes',
            'username': username
        }
        
        return self._make_request('GET', 'forum_api.php', params=params)

    def check_user_like_status(self, username: str, target_type: str, target_id: int) -> Tuple[bool, Dict[str, Any]]:
        """
        检查用户是否已点赞

        Args:
            username: 用户名
            target_type: 目标类型 ('post' 或 'reply')
            target_id: 目标ID

        Returns:
            (success, response_data)
        """
        params = {
            'action': 'get_user_likes',
            'username': username
        }

        success, response = self._make_request('GET', 'forum_api.php', params=params)

        if success:
            # 后端返回的是简单数组，格式如: ["post_1", "reply_2", ...]
            user_likes = response.get('data', [])

            # 检查是否是列表
            if isinstance(user_likes, list):
                # 构造目标标识符
                target_identifier = f"{target_type}_{target_id}"
                is_liked = target_identifier in user_likes
            else:
                # 如果不是列表，默认未点赞
                is_liked = False

            return True, {
                'data': {
                    'is_liked': is_liked,
                    'target_type': target_type,
                    'target_id': target_id
                }
            }

        return success, response

    def increment_view(self, post_id: int) -> Tuple[bool, Dict[str, Any]]:
        """
        增加帖子浏览量

        Args:
            post_id: 帖子ID

        Returns:
            (success, response_data)
        """
        data = {
            'post_id': post_id
        }

        return self._make_request('POST', 'forum_api.php?action=increment_view', json=data)


# 全局API客户端实例
_api_client = None


def get_forum_api_client(base_url: str = None) -> ForumAPIClient:
    """
    获取论坛API客户端实例（单例模式）

    Args:
        base_url: API基础URL

    Returns:
        ForumAPIClient实例
    """
    global _api_client

    if _api_client is None:
        _api_client = ForumAPIClient(base_url)

    return _api_client


def reset_forum_api_client():
    """重置API客户端实例"""
    global _api_client
    _api_client = None
