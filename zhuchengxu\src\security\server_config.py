#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器配置模块
加密存储可信服务器列表，防止篡改
"""

import base64
import hashlib
import hmac
import json
from typing import List, Dict, Any, Optional

class ServerConfig:
    """服务器配置管理"""
    
    # 加密密钥（在实际部署时应该使用更复杂的密钥管理）
    _ENCRYPTION_KEY = "augment_server_config_key_2025"
    
    # 加密后的可信服务器列表
    _ENCRYPTED_SERVERS = [
        # 这些是加密后的服务器配置，防止被轻易修改
        "eyJuYW1lIjoi5a6Y5pa55pyN5Yqh5ZmoIiwidXJsIjoiaHR0cHM6Ly9hdXRoLmF1Z21lbnRjb2RlLmNvbSIsInB1YmxpY19rZXkiOiJNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQS4uLiIsInByaW9yaXR5IjoxfQ==",
        "eyJuYW1lIjoi5aSH55So5pyN5Yqh5ZmoIiwidXJsIjoiaHR0cHM6Ly9hdXRoMi5hdWdtZW50Y29kZS5jb20iLCJwdWJsaWNfa2V5IjoiTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF0Li4uIiwicHJpb3JpdHkiOjJ9",
        "eyJuYW1lIjoi5pys5Zyw5rWL6K+V5pyN5Yqh5ZmoIiwidXJsIjoiaHR0cDovLzEyNy4wLjAuMTo3NzciLCJwdWJsaWNfa2V5IjoiTE9DQUxfVEVTVF9LRVkiLCJwcmlvcml0eSI6M30=",
        "eyJuYW1lIjoi5pys5Zyw5pyN5Yqh5ZmoIiwidXJsIjoiaHR0cDovL2xvY2FsaG9zdDo3NzciLCJwdWJsaWNfa2V5IjoiTE9DQUxfVEVTVF9LRVkiLCJwcmlvcml0eSI6NH0="
    ]
    
    @classmethod
    def _decrypt_server_config(cls, encrypted_data: str) -> Optional[Dict[str, Any]]:
        """解密服务器配置"""
        try:
            # 简单的Base64解码（实际应该使用AES等加密算法）
            decoded_data = base64.b64decode(encrypted_data).decode('utf-8')
            server_config = json.loads(decoded_data)
            
            # 验证配置完整性
            if cls._verify_config_integrity(server_config):
                return server_config
            else:
                return None
                
        except Exception:
            return None
    
    @classmethod
    def _verify_config_integrity(cls, config: Dict[str, Any]) -> bool:
        """验证配置完整性"""
        required_fields = ['name', 'url', 'public_key', 'priority']
        return all(field in config for field in required_fields)
    
    @classmethod
    def get_trusted_servers(cls) -> List[Dict[str, Any]]:
        """获取可信服务器列表"""
        servers = []
        
        for encrypted_config in cls._ENCRYPTED_SERVERS:
            server_config = cls._decrypt_server_config(encrypted_config)
            if server_config:
                servers.append(server_config)
        
        # 按优先级排序
        servers.sort(key=lambda x: x.get('priority', 999))
        
        return servers
    
    @classmethod
    def get_server_by_url(cls, url: str) -> Optional[Dict[str, Any]]:
        """根据URL获取服务器配置"""
        for server in cls.get_trusted_servers():
            if server['url'] == url:
                return server
        return None
    
    @classmethod
    def is_trusted_server(cls, url: str) -> bool:
        """检查是否为可信服务器"""
        return cls.get_server_by_url(url) is not None
    
    @classmethod
    def get_server_public_key(cls, url: str) -> Optional[str]:
        """获取服务器公钥"""
        server = cls.get_server_by_url(url)
        return server['public_key'] if server else None
    
    @classmethod
    def generate_encrypted_config(cls, server_config: Dict[str, Any]) -> str:
        """生成加密的服务器配置（开发工具）"""
        config_json = json.dumps(server_config, ensure_ascii=False)
        encrypted_data = base64.b64encode(config_json.encode('utf-8')).decode('utf-8')
        return encrypted_data


# 预定义的服务器配置（明文，仅用于生成加密配置）
_SERVER_CONFIGS = [
    {
        "name": "官方服务器",
        "url": "https://auth.augmentcode.com",
        "public_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
        "priority": 1
    },
    {
        "name": "备用服务器", 
        "url": "https://auth2.augmentcode.com",
        "public_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt...",
        "priority": 2
    },
    {
        "name": "本地测试服务器",
        "url": "http://127.0.0.1:777",
        "public_key": "LOCAL_TEST_KEY",
        "priority": 3
    },
    {
        "name": "本地服务器",
        "url": "http://localhost:777", 
        "public_key": "LOCAL_TEST_KEY",
        "priority": 4
    }
]


def generate_encrypted_servers():
    """生成加密的服务器配置（开发工具）"""
    print("生成加密的服务器配置:")
    for config in _SERVER_CONFIGS:
        encrypted = ServerConfig.generate_encrypted_config(config)
        print(f'"{encrypted}",')


if __name__ == "__main__":
    # 测试功能
    print("可信服务器列表:")
    servers = ServerConfig.get_trusted_servers()
    for server in servers:
        print(f"- {server['name']}: {server['url']}")
    
    print("\n测试服务器验证:")
    test_urls = [
        "https://auth.augmentcode.com",
        "http://127.0.0.1:777",
        "http://malicious-server.com"
    ]
    
    for url in test_urls:
        is_trusted = ServerConfig.is_trusted_server(url)
        print(f"- {url}: {'✅ 可信' if is_trusted else '❌ 不可信'}")
    
    # 生成加密配置（开发用）
    print("\n" + "="*50)
    generate_encrypted_servers()
