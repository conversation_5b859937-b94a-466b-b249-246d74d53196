"""
自动注册功能模块 - 真正能工作的版本
"""

import time
import threading
import webbrowser
import subprocess
import ctypes
from ctypes import wintypes

from src.utils.logger import get_logger


class AutoRegister:
    """真正能工作的自动注册助手"""

    def __init__(self, save_to_database=True):
        self.logger = get_logger()
        self.is_running = False
        self.monitor_thread = None
        self.current_email = None  # 保存当前使用的邮箱地址
        self.save_to_database = save_to_database  # 控制是否保存账号到数据库

    def start_auto_register(self):
        """启动自动注册流程 - 简化版：打开浏览器 → Tab 6次 → Enter"""
        try:
            self.logger.info("🚀 启动自动注册流程...")

            # 打开浏览器并全屏
            target_url = "https://augmentcode.com"
            self.logger.info(f"🌐 打开浏览器并全屏: {target_url}")
            webbrowser.open(target_url)

            # 等待浏览器启动后设置全屏
            time.sleep(2)
            self._set_browser_fullscreen()

            # 硬编码启动后6秒执行Tab 6次+Enter
            self.logger.info("⏳ 启动后6秒执行Tab 6次+Enter...")
            time.sleep(6)

            # 执行完整注册流程
            success = self._execute_full_register_flow()

            if success:
                self.logger.info("✅ 成功完成完整注册流程！")
                return True
            else:
                self.logger.warning("⚠️ 注册流程执行失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 自动注册失败: {e}")
            return False

    def _wait_for_page_load(self, target_url, max_wait=10):
        """立即检测网页是否打开并加载完成"""
        try:
            import psutil

            self.logger.info(f"🔍 立即开始检测网页加载，最大等待{max_wait}秒...")

            start_time = time.time()
            check_count = 0

            while time.time() - start_time < max_wait:
                check_count += 1
                elapsed = time.time() - start_time

                # 每0.2秒检测一次
                if check_count % 5 == 1:  # 每1秒显示一次日志
                    self.logger.info(f"🔍 检测中... 已等待 {elapsed:.1f}秒")

                # 检测浏览器进程是否存在
                browser_found = self._check_browser_process()

                if browser_found:
                    # 检测窗口标题是否包含目标网站
                    window_title_ok = self._check_window_title(target_url)

                    if window_title_ok:
                        # 简化检测，只检测CPU使用率
                        cpu_usage = self._get_browser_cpu_usage()

                        if cpu_usage < 10:  # CPU使用率低于10%认为加载完成
                            self.logger.info(f"✅ 网页加载完成！CPU: {cpu_usage:.1f}%")
                            return True
                        else:
                            if check_count % 5 == 1:
                                self.logger.info(f"⏳ 页面加载中，CPU: {cpu_usage:.1f}%")

                time.sleep(0.2)  # 每0.2秒检测一次

            self.logger.warning(f"⚠️ 等待{max_wait}秒后仍未检测到网页完全加载")
            return False

        except Exception as e:
            self.logger.error(f"❌ 检测网页加载失败: {e}")
            return False

    def _check_browser_process(self):
        """检测浏览器进程是否存在"""
        try:
            import psutil

            browser_names = ['chrome.exe', 'firefox.exe', 'msedge.exe', 'opera.exe', 'brave.exe']

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'].lower() in [name.lower() for name in browser_names]:
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return False

        except Exception as e:
            self.logger.error(f"检测浏览器进程失败: {e}")
            return False

    def _check_window_title(self, target_url):
        """检测窗口标题是否包含目标网站"""
        try:
            import ctypes
            from ctypes import wintypes

            user32 = ctypes.windll.user32

            # 获取前台窗口
            hwnd = user32.GetForegroundWindow()
            if not hwnd:
                return False

            # 获取窗口标题
            length = user32.GetWindowTextLengthW(hwnd)
            if length == 0:
                return False

            buffer = ctypes.create_unicode_buffer(length + 1)
            user32.GetWindowTextW(hwnd, buffer, length + 1)
            window_title = buffer.value.lower()

            # 检测标题是否包含目标网站关键词
            keywords = ['augmentcode', 'augment', 'code']
            for keyword in keywords:
                if keyword in window_title:
                    self.logger.info(f"✅ 检测到窗口标题包含关键词: {keyword}")
                    return True

            self.logger.info(f"🔍 当前窗口标题: {window_title}")
            return False

        except Exception as e:
            self.logger.error(f"检测窗口标题失败: {e}")
            return False

    def _check_page_content_loaded(self):
        """检测页面内容是否加载完成 - 通过CPU使用率和交互性检测"""
        try:
            # 检测浏览器CPU使用率
            cpu_usage = self._get_browser_cpu_usage()

            if cpu_usage < 5:  # CPU使用率低于5%认为加载完成
                self.logger.info(f"✅ 页面加载完成，CPU使用率: {cpu_usage:.1f}%")
                return True
            else:
                self.logger.info(f"⏳ 页面仍在加载，CPU使用率: {cpu_usage:.1f}%")
                return False

        except Exception as e:
            self.logger.error(f"检测页面内容失败: {e}")
            # 如果检测失败，额外等待2秒
            time.sleep(2)
            return True

    def _get_browser_cpu_usage(self):
        """获取浏览器进程的CPU使用率"""
        try:
            import psutil

            browser_names = ['chrome.exe', 'firefox.exe', 'msedge.exe', 'opera.exe', 'brave.exe']
            total_cpu = 0
            process_count = 0

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'].lower() in [name.lower() for name in browser_names]:
                        # 获取CPU使用率，间隔0.2秒
                        cpu = proc.cpu_percent(interval=0.2)
                        total_cpu += cpu
                        process_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if process_count > 0:
                avg_cpu = total_cpu / process_count
                return avg_cpu
            else:
                return 0

        except Exception as e:
            self.logger.error(f"获取CPU使用率失败: {e}")
            return 0

    def _set_browser_fullscreen(self):
        """设置浏览器全屏"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            self.logger.info("🖥️ 设置浏览器全屏...")

            # 获取前台窗口（应该是浏览器）
            hwnd = user32.GetForegroundWindow()
            if hwnd:
                # 按F11键进入全屏模式
                user32.keybd_event(0x7A, 0, 0, 0)  # F11按下
                user32.keybd_event(0x7A, 0, 2, 0)  # F11释放
                time.sleep(1)

                self.logger.info("✅ 浏览器已设置为全屏模式")
                return True
            else:
                self.logger.warning("⚠️ 未找到浏览器窗口")
                return False

        except Exception as e:
            self.logger.error(f"设置浏览器全屏失败: {e}")
            return False

    def _execute_full_register_flow(self):
        """执行完整注册流程：Tab 6次 + Enter + 生成邮箱 + 检测跳转 + 粘贴 + Tab + Enter"""
        try:
            # 第一步：Tab 6次 + Enter（点击登录按钮）
            self.logger.info("🎯 第一步：执行Tab 6次 + Enter点击登录按钮...")
            success = self._execute_tab_enter()
            if not success:
                return False

            # 第二步：等待5秒让页面跳转
            self.logger.info("⏳ 第二步：等待5秒让页面跳转...")
            time.sleep(5)

            # 第三步：生成随机邮箱用于输入
            self.logger.info("📧 第三步：生成随机邮箱用于输入...")
            self.current_email = self._generate_random_email_from_config()
            self.logger.info(f"📧 生成的随机邮箱：{self.current_email}")

            # 第四步：直接输入邮箱（避免乱码）
            self.logger.info("⌨️ 第四步：直接输入邮箱...")
            success = self._type_email_directly(self.current_email)
            if not success:
                return False
                
            # 重要：输入邮箱后等待4秒再进行下一步操作
            self.logger.info("⏳ 等待4秒后继续...")
            time.sleep(4)
            self.logger.info("✅ 等待完成，继续执行下一步")

            # 第六步：Tab 1次
            self.logger.info("⌨️ 第六步：按Tab键1次...")
            success = self._execute_single_tab()
            if not success:
                return False

            # 第七步：Enter 1次
            self.logger.info("⏎ 第七步：按Enter键1次...")
            success = self._execute_single_enter()
            if not success:
                return False

            # 第八步：显示人机认证提示窗口并等待
            self.logger.info("🤖 第八步：显示人机认证提示窗口...")
            success = self._show_captcha_prompt_and_wait()
            if not success:
                self.logger.warning("⚠️ 人机认证流程异常")
                return False

            self.logger.info("✅ 完整注册流程执行成功！")
            return True

        except Exception as e:
            self.logger.error(f"❌ 完整注册流程失败: {e}")
            return False

    def _get_configured_email(self):
        """获取配置文件中的邮箱地址"""
        try:
            try:
                from src.config.config_manager import ConfigManager
            except ImportError:
                from config.config_manager import ConfigManager
            config = ConfigManager()

            # 获取配置的邮箱地址
            temp_mail_address = config.get('temp_mail_address', '')

            if temp_mail_address:
                self.logger.info(f"📧 使用配置文件中的邮箱: {temp_mail_address}")
                return temp_mail_address
            else:
                # 如果没有配置，则生成随机邮箱
                self.logger.warning("⚠️ 配置文件中没有邮箱地址，生成随机邮箱")
                return self._generate_random_email_from_config()

        except Exception as e:
            self.logger.error(f"❌ 获取配置邮箱失败: {e}")
            # 降级到生成随机邮箱
            return self._generate_random_email_from_config()

    def _generate_random_email_from_config(self):
        """从配置文件读取域名生成随机邮箱"""
        try:
            # 导入配置管理器
            try:
                from src.config.config_manager import ConfigManager
            except ImportError:
                from config.config_manager import ConfigManager
            config = ConfigManager()

            # 读取邮箱域名
            domain = config.get('email_domain', 'hwsyyds.xyz')

            # 生成随机邮箱
            import random
            import string

            # 生成8-12位随机字符（字母+数字）
            length = random.randint(8, 12)
            random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

            email = f"{random_part}@{domain}"
            return email

        except Exception as e:
            self.logger.error(f"生成随机邮箱失败: {e}")
            # 降级方案
            import random
            import string
            random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            return f"{random_part}@hwsyyds.xyz"

    def _generate_random_email(self, domain):
        """生成随机邮箱：随机字符 + 域名（保留兼容性）"""
        try:
            import random
            import string

            # 生成8-12位随机字符（字母+数字）
            length = random.randint(8, 12)
            random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

            email = random_part + domain
            return email

        except Exception as e:
            self.logger.error(f"生成随机邮箱失败: {e}")
            return f"user{int(time.time())}{domain}"  # 降级方案

    def _wait_for_page_change(self, max_wait=15):
        """检测页面跳转 - 严格检测确保跳转完成"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 获取当前窗口标题作为基准
            hwnd = user32.GetForegroundWindow()
            length = user32.GetWindowTextLengthW(hwnd)
            if length > 0:
                buffer = ctypes.create_unicode_buffer(length + 1)
                user32.GetWindowTextW(hwnd, buffer, length + 1)
                original_title = buffer.value.lower()
            else:
                original_title = ""

            self.logger.info(f"🔍 原始页面标题: {original_title}")

            start_time = time.time()
            page_changed = False
            stable_count = 0

            while time.time() - start_time < max_wait:
                # 检测当前标题
                hwnd = user32.GetForegroundWindow()
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    current_title = buffer.value.lower()

                    # 如果标题发生变化，说明页面开始跳转
                    if current_title != original_title and not page_changed:
                        self.logger.info(f"🔍 检测到页面开始跳转: {current_title}")
                        page_changed = True
                        stable_count = 0

                    # 如果页面已经跳转，检测是否稳定
                    if page_changed:
                        stable_count += 1
                        if stable_count >= 6:  # 连续3秒稳定（6次 * 0.5秒）
                            # 额外检测CPU使用率确保页面加载完成
                            cpu_usage = self._get_browser_cpu_usage()
                            if cpu_usage < 8:  # 跳转后CPU使用率应该较低
                                self.logger.info(f"✅ 页面跳转完成且稳定，CPU: {cpu_usage:.1f}%")
                                return True
                            else:
                                self.logger.info(f"⏳ 页面仍在加载，CPU: {cpu_usage:.1f}%")
                                stable_count = 0  # 重置稳定计数

                time.sleep(0.5)

            if page_changed:
                self.logger.warning("⚠️ 检测到页面跳转但未完全稳定")
            else:
                self.logger.warning("⚠️ 页面跳转检测超时")
            return False

        except Exception as e:
            self.logger.error(f"检测页面跳转失败: {e}")
            return False

    def _quick_wait_for_page_change(self, max_wait=3):
        """快速检测页面跳转 - 3秒内完成"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 获取当前窗口标题
            hwnd = user32.GetForegroundWindow()
            length = user32.GetWindowTextLengthW(hwnd)
            if length > 0:
                buffer = ctypes.create_unicode_buffer(length + 1)
                user32.GetWindowTextW(hwnd, buffer, length + 1)
                original_title = buffer.value.lower()
            else:
                original_title = ""

            self.logger.info(f"🔍 检测页面跳转，原始标题: {original_title[:30]}...")

            start_time = time.time()
            while time.time() - start_time < max_wait:
                # 检测标题变化
                hwnd = user32.GetForegroundWindow()
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    current_title = buffer.value.lower()

                    if current_title != original_title:
                        self.logger.info(f"✅ 页面跳转成功: {current_title[:30]}...")
                        time.sleep(0.5)  # 等待0.5秒确保页面稳定
                        return True

                time.sleep(0.1)  # 每0.1秒检测一次，更快响应

            self.logger.info("⚠️ 3秒内未检测到页面跳转")
            return False

        except Exception as e:
            self.logger.error(f"快速检测失败: {e}")
            return False

    def _type_email_directly(self, email):
        """直接输入邮箱（使用剪贴板方式避免乱码）"""
        try:
            self.logger.info(f"⌨️ 输入邮箱: {email}")

            # 使用剪贴板方式输入，避免特殊字符问题
            self._copy_to_clipboard(email)
            success = self._paste_email()

            if success:
                self.logger.info("✅ 邮箱输入完成")
                return True
            else:
                self.logger.error("❌ 邮箱输入失败")
                return False

        except Exception as e:
            self.logger.error(f"输入邮箱失败: {e}")
            return False

    def _copy_to_clipboard(self, text):
        """复制文本到剪贴板 - 简单粗暴的方法"""
        try:
            # 方法1：使用subprocess调用echo和clip
            import subprocess
            process = subprocess.Popen(['clip'], stdin=subprocess.PIPE, text=True, shell=True)
            process.communicate(input=text)

            if process.returncode == 0:
                self.logger.info(f"📋 已复制到剪贴板: {text}")
                return True
            else:
                raise Exception("clip命令执行失败")

        except Exception as e1:
            try:
                # 方法2：使用pyperclip
                import pyperclip
                pyperclip.copy(text)
                self.logger.info(f"📋 已复制到剪贴板: {text}")
                return True
            except Exception as e2:
                try:
                    # 方法3：写入临时文件然后用type命令
                    import tempfile
                    import subprocess

                    # 🔧 使用与main.py一致的路径检测逻辑
                    import sys
                    # 直接使用main.py中设置的环境变量
                    config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
                    if config_dir:
                        temp_dir = config_dir
                    else:
                        # 备选方案：使用sys.argv[0]
                        if sys.argv and len(sys.argv) > 0:
                            argv_path = os.path.abspath(sys.argv[0])
                            if argv_path.endswith('.exe'):
                                temp_dir = os.path.dirname(argv_path)
                            else:
                                temp_dir = os.path.dirname(argv_path)
                        else:
                            temp_dir = os.getcwd()

                    temp_file = os.path.join(temp_dir, f'temp_clipboard_{int(time.time())}.txt')
                    with open(temp_file, 'w', encoding='utf-8') as f:
                        f.write(text)

                    # 使用type命令读取文件内容并传给clip
                    cmd = f'type "{temp_file}" | clip'
                    result = subprocess.run(cmd, shell=True, capture_output=True)

                    # 删除临时文件
                    import os
                    os.unlink(temp_file)

                    if result.returncode == 0:
                        self.logger.info(f"📋 已复制到剪贴板: {text}")
                        return True
                    else:
                        raise Exception("type|clip命令失败")

                except Exception as e3:
                    self.logger.error(f"所有复制方法都失败了: {e1}, {e2}, {e3}")
                    return False

    def _paste_email(self):
        """粘贴邮箱（Ctrl+V）"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            self.logger.info("📋 执行Ctrl+V粘贴邮箱...")

            # 按Ctrl+V
            user32.keybd_event(0x11, 0, 0, 0)  # Ctrl按下
            user32.keybd_event(0x56, 0, 0, 0)  # V按下
            user32.keybd_event(0x56, 0, 2, 0)  # V释放
            user32.keybd_event(0x11, 0, 2, 0)  # Ctrl释放

            time.sleep(0.5)
            self.logger.info("✅ 邮箱粘贴完成")
            return True

        except Exception as e:
            self.logger.error(f"粘贴邮箱失败: {e}")
            return False

    def _execute_single_tab(self):
        """执行单次Tab操作"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
            user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
            time.sleep(0.3)

            self.logger.info("✅ Tab操作完成")
            return True

        except Exception as e:
            self.logger.error(f"Tab操作失败: {e}")
            return False

    def _execute_single_enter(self):
        """执行单次Enter操作"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            user32.keybd_event(0x0D, 0, 0, 0)  # Enter按下
            user32.keybd_event(0x0D, 0, 2, 0)  # Enter释放
            time.sleep(0.3)

            self.logger.info("✅ Enter操作完成")
            return True

        except Exception as e:
            self.logger.error(f"Enter操作失败: {e}")
            return False

    def _wait_for_email_input_page(self, max_wait=10):
        """等待页面跳转到邮箱输入页面"""
        try:
            self.logger.info(f"🔍 等待页面跳转到邮箱输入页面，最大等待{max_wait}秒...")

            start_time = time.time()
            check_count = 0

            while time.time() - start_time < max_wait:
                check_count += 1
                elapsed = time.time() - start_time

                # 每2秒显示一次进度
                if check_count % 10 == 1:  # 10 * 0.2秒 = 2秒
                    self.logger.info(f"🔍 等待页面跳转... 已等待 {elapsed:.1f}秒")

                # 检测页面标题变化
                current_title = self._get_current_page_title()
                if current_title:
                    title_lower = current_title.lower()

                    # 检测邮箱输入页面的关键词
                    email_keywords = ['email', 'register', 'sign up', 'create', 'account']
                    if any(keyword in title_lower for keyword in email_keywords):
                        # 进一步检测是否有输入框可用
                        if self._test_email_input_available():
                            self.logger.info(f"✅ 检测到邮箱输入页面: {current_title[:30]}...")
                            return True

                time.sleep(0.2)

            self.logger.warning(f"⚠️ 等待{max_wait}秒后未检测到邮箱输入页面")
            return False

        except Exception as e:
            self.logger.error(f"❌ 等待邮箱输入页面失败: {e}")
            return False

    def _test_email_input_available(self):
        """测试邮箱输入框是否可用"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 尝试Tab键定位到输入框
            for i in range(5):
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.1)

                # 尝试输入测试字符
                user32.keybd_event(0x41, 0, 0, 0)  # A按下
                user32.keybd_event(0x41, 0, 2, 0)  # A释放
                time.sleep(0.05)

                # 立即删除测试字符
                user32.keybd_event(0x08, 0, 0, 0)  # Backspace按下
                user32.keybd_event(0x08, 0, 2, 0)  # Backspace释放
                time.sleep(0.05)

                # 如果能成功输入和删除，说明找到了输入框
                return True

            return False

        except Exception as e:
            return False

    def _show_captcha_prompt_and_wait(self):
        """显示人机认证提示窗口 - 简化版本确保按钮显示"""
        try:
            import tkinter as tk

            self.logger.info("🖼️ 创建人机认证提示窗口...")

            # 创建窗口
            prompt_window = tk.Tk()
            prompt_window.title("人机认证 - 请完成后点击确认")

            # 获取屏幕尺寸
            screen_width = prompt_window.winfo_screenwidth()
            screen_height = prompt_window.winfo_screenheight()

            # 计算窗口尺寸和位置（增大窗口确保按钮显示）
            window_width = int(screen_width * 0.4)
            window_height = 400
            x = 0
            y = (screen_height - window_height) // 2

            prompt_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            prompt_window.attributes('-topmost', True)
            prompt_window.resizable(False, False)

            # 标题
            title_label = tk.Label(prompt_window, text="🤖 人机认证",
                                 font=('Microsoft YaHei', 18, 'bold'))
            title_label.pack(pady=20)

            # 提示文本
            prompt_text = "请在浏览器中完成人机认证\n并点击网页上的继续按钮\n\n完成后点击下方确认按钮"
            prompt_label = tk.Label(prompt_window, text=prompt_text,
                                  font=('Microsoft YaHei', 12),
                                  justify=tk.CENTER)
            prompt_label.pack(pady=20)

            # 状态标签
            status_label = tk.Label(prompt_window, text="等待用户完成认证...",
                                  font=('Microsoft YaHei', 10),
                                  fg='blue')
            status_label.pack(pady=10)

            # 定义按钮功能
            def close_window():
                prompt_window.destroy()

            def confirm_completed():
                self.logger.info("✅ 用户确认已完成人机认证")
                status_label.config(text="正在读取验证码...", fg='orange')
                prompt_window.update()

                # 使用配置文件中的邮箱地址读取验证码（不是输入的随机邮箱）
                try:
                    from src.config.config_manager import ConfigManager
                except ImportError:
                    from config.config_manager import ConfigManager
                config = ConfigManager()
                config_email = config.get('temp_mail_address', '')

                self.logger.info(f"📧 输入的随机邮箱: {self.current_email}")
                self.logger.info(f"📧 读取验证码的邮箱: {config_email}")

                # 从配置邮箱读取最新验证码
                verification_code = self._read_latest_verification_code(config_email)

                if verification_code:
                    self.logger.info(f"📧 读取到验证码: {verification_code}")
                    status_label.config(text="验证码读取成功！正在提交...", fg='green')
                    prompt_window.update()

                    # 复制验证码到剪贴板
                    self._copy_to_clipboard(verification_code)

                    # 粘贴验证码
                    self._paste_email()

                    # Tab + Enter
                    self._execute_single_tab()
                    self._execute_single_enter()

                    status_label.config(text="验证码提交完成！正在检测注册结果...", fg='green')
                    prompt_window.update()

                    # 检测注册成功
                    success = self._check_registration_success()
                    if success:
                        if self.save_to_database:
                            status_label.config(text="🎉 注册成功！正在保存账号信息...", fg='green')
                            prompt_window.update()

                            # 保存账号到账号库
                            self._save_account_to_database(self.current_email)

                            status_label.config(text="✅ 账号已保存到账号库！", fg='green')
                            prompt_window.update()
                        else:
                            status_label.config(text="🎉 注册成功！", fg='green')
                            prompt_window.update()
                            self.logger.info("✅ 注册成功，跳过保存到账号库（账号已存在）")
                    else:
                        status_label.config(text="⚠️ 注册状态未确认", fg='orange')
                        prompt_window.update()

                    prompt_window.after(3000, close_window)
                else:
                    self.logger.error("❌ 未能读取到验证码")
                    status_label.config(text="验证码读取失败！", fg='red')
                    prompt_window.update()
                    prompt_window.after(3000, close_window)

            # 大确认按钮
            confirm_btn = tk.Button(prompt_window,
                                  text="✅ 我已完成人机认证",
                                  font=('Microsoft YaHei', 14, 'bold'),
                                  bg='green', fg='white',
                                  width=20, height=2,
                                  command=confirm_completed)
            confirm_btn.pack(pady=20)

            # 取消按钮
            close_btn = tk.Button(prompt_window,
                                text="取消",
                                font=('Microsoft YaHei', 10),
                                bg='gray', fg='white',
                                width=10, height=1,
                                command=close_window)
            close_btn.pack(pady=10)

            # 显示窗口
            self.logger.info("🖼️ 人机认证提示窗口已显示，等待用户手动确认")
            prompt_window.mainloop()

            self.logger.info("🖼️ 人机认证提示窗口已关闭")
            return True

        except Exception as e:
            self.logger.error(f"❌ 显示人机认证提示窗口失败: {e}")
            return False

    def _check_code_input_exists(self):
        """检测code输入框是否存在 - 通过Tab键和焦点检测"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 保存当前状态
            original_hwnd = user32.GetForegroundWindow()

            # 尝试Tab键定位到输入框
            for i in range(10):  # 最多尝试10次Tab
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.1)

                # 尝试输入测试字符
                user32.keybd_event(0x41, 0, 0, 0)  # A按下
                user32.keybd_event(0x41, 0, 2, 0)  # A释放
                time.sleep(0.1)

                # 立即删除测试字符
                user32.keybd_event(0x08, 0, 0, 0)  # Backspace按下
                user32.keybd_event(0x08, 0, 2, 0)  # Backspace释放
                time.sleep(0.1)

                # 如果能成功输入和删除，说明找到了输入框
                # 这里简化检测，如果Tab能定位到可输入的元素就认为找到了
                current_hwnd = user32.GetForegroundWindow()
                if current_hwnd == original_hwnd:  # 窗口没变，可能在输入框内
                    # 检测页面标题是否包含code相关关键词
                    title = self._get_current_page_title()
                    if title and ('code' in title.lower() or 'verification' in title.lower() or 'confirm' in title.lower()):
                        return True

            return False

        except Exception as e:
            return False

    def _check_code_input_by_title(self):
        """通过页面标题检测是否到达code输入页面"""
        try:
            title = self._get_current_page_title()
            if not title:
                return False

            title_lower = title.lower()

            # 检测标题中的关键词
            code_keywords = [
                'code', 'verification', 'verify', 'confirm', 'email',
                'check', 'inbox', 'message', 'digit', 'number'
            ]

            # 如果标题包含这些关键词，可能已经跳转到code输入页面
            if any(keyword in title_lower for keyword in code_keywords):
                # 同时确保不再包含认证相关关键词
                captcha_keywords = ['captcha', 'human', 'robot', 'challenge']
                if not any(keyword in title_lower for keyword in captcha_keywords):
                    return True

            return False

        except Exception as e:
            return False

    def _check_enter_code_label(self):
        """检测'Enter the code'标签是否存在 - 通过Tab键和文本检测"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 保存当前窗口状态
            original_hwnd = user32.GetForegroundWindow()

            # 尝试Tab键遍历页面元素，寻找包含"Enter the code"的标签
            for i in range(15):  # 最多尝试15次Tab
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.05)  # 短暂等待

                # 检测页面标题是否包含相关关键词
                title = self._get_current_page_title()
                if title:
                    title_lower = title.lower()
                    # 检测标题中是否包含验证码相关词汇
                    code_indicators = ['enter', 'code', 'verification', 'verify', 'confirm', 'email']
                    if any(indicator in title_lower for indicator in code_indicators):
                        # 如果标题包含相关词汇，很可能已经跳转到验证页面
                        return True

            # 备用检测：检测页面是否有输入框焦点
            # 如果能成功定位到输入框，说明页面已加载
            try:
                # 尝试输入测试字符并立即删除
                user32.keybd_event(0x41, 0, 0, 0)  # A按下
                user32.keybd_event(0x41, 0, 2, 0)  # A释放
                time.sleep(0.05)

                user32.keybd_event(0x08, 0, 0, 0)  # Backspace按下
                user32.keybd_event(0x08, 0, 2, 0)  # Backspace释放

                # 如果能成功输入和删除，说明找到了输入框
                return True

            except:
                pass

            return False

        except Exception as e:
            return False

    def _check_enter_code_by_page_content(self):
        """通过页面内容检测是否存在Enter the code标签"""
        try:
            # 检测页面标题
            title = self._get_current_page_title()
            if not title:
                return False

            title_lower = title.lower()

            # 检测标题中的关键词组合
            enter_code_indicators = [
                'enter the code',
                'enter code',
                'verification code',
                'confirm email',
                'email verification',
                'check email',
                'code sent'
            ]

            # 如果标题包含这些关键词组合，很可能是验证页面
            for indicator in enter_code_indicators:
                if indicator in title_lower:
                    return True

            # 检测单个关键词的组合
            has_enter = 'enter' in title_lower
            has_code = 'code' in title_lower
            has_verify = any(word in title_lower for word in ['verify', 'verification', 'confirm'])

            # 如果同时包含enter和code，或者包含verify相关词汇
            if (has_enter and has_code) or has_verify:
                return True

            return False

        except Exception as e:
            return False

    def _wait_for_captcha_completion(self, max_wait=60):
        """检测hCaptcha认证完成 - 多种检测方法"""
        try:
            self.logger.info("🤖 开始检测hCaptcha认证状态...")
            self.logger.info("💡 请用户完成hCaptcha人机认证")

            start_time = time.time()
            check_count = 0
            last_title = ""

            while time.time() - start_time < max_wait:
                check_count += 1
                elapsed = time.time() - start_time

                # 每3秒显示一次进度
                if check_count % 15 == 1:  # 15 * 0.2秒 = 3秒
                    self.logger.info(f"🤖 等待hCaptcha完成... 已等待 {elapsed:.0f}秒")

                # 检测方法1：页面标题变化
                current_title = self._get_current_page_title()
                if current_title != last_title and current_title:
                    self.logger.info(f"🔍 页面标题: {current_title[:50]}...")
                    last_title = current_title

                    # 如果标题不再包含captcha相关词，可能已完成
                    if not any(word in current_title.lower() for word in ['captcha', 'verify', 'challenge']):
                        self.logger.info("✅ 页面标题显示认证可能已完成")
                        return True

                # 检测方法2：Continue按钮可用性
                continue_available = self._check_continue_button_available()
                if continue_available:
                    self.logger.info("✅ 检测到Continue按钮可用，认证已完成！")
                    return True

                # 检测方法3：每10秒尝试一次Tab+Enter
                if check_count % 50 == 0:  # 50 * 0.2秒 = 10秒
                    self.logger.info("🔘 尝试Tab+Enter检测...")
                    if self._try_continue_click():
                        self.logger.info("✅ Tab+Enter成功，认证已完成！")
                        return True

                time.sleep(0.2)

            self.logger.warning(f"⚠️ hCaptcha认证检测超时（{max_wait}秒）")
            self.logger.info("💡 如果您已完成认证，程序将尝试继续...")
            return True  # 超时也返回True，让用户手动处理

        except Exception as e:
            self.logger.error(f"❌ hCaptcha认证检测失败: {e}")
            return True  # 出错也返回True，让流程继续

    def _get_current_page_title(self):
        """获取当前页面标题"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            hwnd = user32.GetForegroundWindow()
            length = user32.GetWindowTextLengthW(hwnd)
            if length > 0:
                buffer = ctypes.create_unicode_buffer(length + 1)
                user32.GetWindowTextW(hwnd, buffer, length + 1)
                return buffer.value
            return ""
        except:
            return ""



    def _try_continue_click(self):
        """尝试点击Continue按钮"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 保存原始状态
            original_title = self._get_current_page_title()

            # 尝试Tab+Enter
            for i in range(3):
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.2)

            user32.keybd_event(0x0D, 0, 0, 0)  # Enter按下
            user32.keybd_event(0x0D, 0, 2, 0)  # Enter释放
            time.sleep(1)

            # 检测页面是否变化
            new_title = self._get_current_page_title()
            if new_title != original_title:
                return True

            return False

        except Exception as e:
            return False

    def _check_continue_button_available(self):
        """检测Continue按钮是否可用 - 专门针对hCaptcha优化"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 方法1：直接尝试Tab键定位Continue按钮
            self.logger.info("🔍 方法1：尝试Tab键定位Continue按钮...")
            if self._try_tab_to_continue():
                return True

            # 方法2：检测页面CPU使用率（hCaptcha完成后CPU会降低）
            self.logger.info("🔍 方法2：检测页面CPU使用率...")
            cpu_usage = self._get_browser_cpu_usage()
            if cpu_usage < 5:  # CPU使用率很低，可能认证已完成
                self.logger.info(f"💻 CPU使用率较低: {cpu_usage:.1f}%，可能认证已完成")
                return True

            # 方法3：检测页面标题变化
            self.logger.info("🔍 方法3：检测页面状态...")
            hwnd = user32.GetForegroundWindow()
            length = user32.GetWindowTextLengthW(hwnd)
            if length > 0:
                buffer = ctypes.create_unicode_buffer(length + 1)
                user32.GetWindowTextW(hwnd, buffer, length + 1)
                title = buffer.value.lower()

                # hCaptcha相关关键词
                hcaptcha_keywords = ['hcaptcha', 'continue', 'verify', 'complete', 'success']
                if any(keyword in title for keyword in hcaptcha_keywords):
                    self.logger.info(f"🔍 检测到hCaptcha相关页面: {title[:50]}...")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"检测Continue按钮失败: {e}")
            return False

    def _try_tab_to_continue(self):
        """尝试Tab键定位到Continue按钮"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 保存当前窗口状态
            original_hwnd = user32.GetForegroundWindow()

            # 尝试5次Tab键
            for i in range(5):
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.2)

                # 检测是否定位到了按钮（尝试按空格键测试）
                user32.keybd_event(0x20, 0, 0, 0)  # Space按下
                user32.keybd_event(0x20, 0, 2, 0)  # Space释放
                time.sleep(0.3)

                # 检测页面是否有变化
                current_hwnd = user32.GetForegroundWindow()
                if current_hwnd != original_hwnd:
                    self.logger.info("✅ 检测到Continue按钮可用并已点击")
                    return True

                # 检测页面标题是否变化
                length = user32.GetWindowTextLengthW(original_hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(original_hwnd, buffer, length + 1)
                    current_title = buffer.value.lower()

                    # 如果标题不再包含captcha关键词，说明已跳转
                    if 'captcha' not in current_title and 'verify' not in current_title:
                        self.logger.info("✅ 页面已跳转，Continue按钮点击成功")
                        return True

            return False

        except Exception as e:
            return False

    def _test_continue_button_interaction(self):
        """测试Continue按钮交互性"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            # 保存当前状态
            original_hwnd = user32.GetForegroundWindow()

            # 尝试多次Tab键定位到Continue按钮
            for i in range(10):  # 最多尝试10次Tab
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.1)

                # 检测是否定位到了可用的按钮（通过按空格键测试）
                # 如果按钮可用，按空格键会有响应
                user32.keybd_event(0x20, 0, 0, 0)  # Space按下
                user32.keybd_event(0x20, 0, 2, 0)  # Space释放
                time.sleep(0.1)

                # 检测页面是否有变化（说明按钮被点击了）
                current_hwnd = user32.GetForegroundWindow()
                if current_hwnd != original_hwnd:
                    # 页面发生了变化，说明按钮可用且被点击了
                    return True

            return False

        except Exception as e:
            return False

    def _click_continue_button(self):
        """点击Continue按钮"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            self.logger.info("🔘 尝试点击Continue按钮...")

            # 方法1：尝试Tab键定位并按Enter
            for i in range(15):  # 最多尝试15次Tab
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.1)

                # 尝试按Enter
                user32.keybd_event(0x0D, 0, 0, 0)  # Enter按下
                user32.keybd_event(0x0D, 0, 2, 0)  # Enter释放
                time.sleep(0.5)

                # 检测页面是否跳转（标题变化）
                hwnd = user32.GetForegroundWindow()
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    title = buffer.value.lower()

                    # 如果标题不再包含continue等关键词，说明成功跳转
                    captcha_keywords = ['continue', 'verify', 'captcha']
                    if not any(keyword in title for keyword in captcha_keywords):
                        self.logger.info("✅ Continue按钮点击成功，页面已跳转")
                        return True

            # 方法2：如果Tab+Enter失败，尝试直接按空格键
            self.logger.info("🔘 尝试空格键点击...")
            user32.keybd_event(0x20, 0, 0, 0)  # Space按下
            user32.keybd_event(0x20, 0, 2, 0)  # Space释放
            time.sleep(1)

            self.logger.warning("⚠️ Continue按钮点击可能失败，请手动检查")
            return True  # 返回True继续流程，让用户手动处理

        except Exception as e:
            self.logger.error(f"❌ 点击Continue按钮失败: {e}")
            return False

    def _read_latest_verification_code(self, email_address):
        """从指定邮箱读取最新验证码"""
        try:
            self.logger.info(f"📧 从邮箱读取验证码: {email_address}")

            # 方法1：尝试从邮箱界面的全局实例获取
            try:
                # 导入邮箱界面
                try:
                    from src.gui.modern_2025_ui.modern_2025_window import Modern2025Window
                except ImportError:
                    from gui.modern_2025_ui.modern_2025_window import Modern2025Window

                # 尝试获取已存在的邮箱界面实例
                if hasattr(Modern2025Window, '_instance') and Modern2025Window._instance:
                    window_instance = Modern2025Window._instance
                    if hasattr(window_instance, '_get_emails'):
                        self.logger.info("🔍 通过邮箱界面后门获取邮件...")
                        emails = window_instance._get_emails()
                        if emails:
                            latest_email = emails[0]
                            verification_code = self._extract_code_from_email(latest_email)
                            if verification_code:
                                self.logger.info(f"✅ 后门接口获取验证码成功: {verification_code}")
                                return verification_code
            except Exception as e:
                self.logger.info(f"方法1失败: {e}")

            # 方法2：直接使用油猴脚本的逻辑
            try:
                self.logger.info("🔍 使用油猴脚本逻辑获取验证码...")
                verification_code = self._get_code_like_tampermonkey(email_address)
                if verification_code:
                    self.logger.info(f"✅ 油猴脚本逻辑获取验证码成功: {verification_code}")
                    return verification_code
            except Exception as e:
                self.logger.info(f"方法2失败: {e}")

            # 方法3：等待重试机制
            self.logger.info("🔍 启动等待重试机制...")
            for attempt in range(5):
                self.logger.info(f"第{attempt+1}/5次尝试获取验证码...")
                time.sleep(3)  # 等待3秒

                try:
                    try:
                        from src.email_service.email_api import EmailAPI
                        from src.config.config_manager import ConfigManager
                    except ImportError:
                        from email_service.email_api import EmailAPI
                        from config.config_manager import ConfigManager

                    config = ConfigManager()
                    email_config = {
                        'temp_mail_address': email_address,
                        'temp_mail_pin': config.get('temp_mail_pin', ''),
                        'auto_refresh_enabled': True,
                        'refresh_interval': 10
                    }

                    api = EmailAPI(email_config)
                    emails = api.get_emails()

                    if emails:
                        latest_email = emails[0]
                        verification_code = self._extract_code_from_email(latest_email)
                        if verification_code:
                            self.logger.info(f"✅ 重试获取验证码成功: {verification_code}")
                            return verification_code
                except Exception as e:
                    self.logger.info(f"重试{attempt+1}失败: {e}")

            self.logger.error("❌ 所有方法都无法获取验证码")
            return None

        except Exception as e:
            self.logger.error(f"❌ 邮箱后门接口失败: {e}")
            return None

    def _extract_code_from_email(self, email_data):
        """从邮件数据中提取验证码"""
        try:
            # 检查是否已经包含验证码
            if 'verification_code' in email_data and email_data['verification_code']:
                return email_data['verification_code']

            # 从内容中提取
            content = email_data.get('content', '')
            if content:
                try:
                    from src.utils.email_utils import extract_verification_code
                except ImportError:
                    from utils.email_utils import extract_verification_code
                return extract_verification_code(content)

            return None
        except Exception as e:
            self.logger.error(f"提取验证码失败: {e}")
            return None

    def _get_code_like_tampermonkey(self, email_address):
        """模拟油猴脚本的验证码获取逻辑"""
        try:
            import requests
            try:
                from src.config.config_manager import ConfigManager
            except ImportError:
                from config.config_manager import ConfigManager

            config = ConfigManager()
            pin_code = config.get('temp_mail_pin', '')

            # 构建请求URL（按照油猴脚本）
            mail_list_url = f"https://tempmail.plus/api/mails?email={email_address}&limit=20&epin={pin_code}"

            self.logger.info(f"🌐 油猴脚本逻辑请求: {mail_list_url}")

            response = requests.get(mail_list_url, timeout=10, verify=False)

            if response.ok:
                data = response.json()
                if data.get('result') and data.get('first_id'):
                    first_id = data['first_id']

                    # 获取邮件详情
                    detail_url = f"https://tempmail.plus/api/mails/{first_id}?email={email_address}&epin={pin_code}"
                    detail_response = requests.get(detail_url, timeout=10, verify=False)

                    if detail_response.ok:
                        detail_data = detail_response.json()
                        if detail_data.get('result'):
                            mail_text = detail_data.get('text', '')

                            # 使用油猴脚本的正则表达式
                            import re
                            code_match = re.search(r'(?<![a-zA-Z@.])\b(\d{6})\b', mail_text)
                            if code_match:
                                return code_match.group(1)

            return None

        except Exception as e:
            self.logger.error(f"油猴脚本逻辑失败: {e}")
            return None

    def _execute_tab_enter(self):
        """执行Tab 6次 + Enter操作 - 简化版，无鼠标点击"""
        try:
            user32 = ctypes.windll.user32

            # Tab 6次定位到登录按钮
            self.logger.info("⌨️ 按Tab键6次定位到登录按钮...")
            for i in range(6):
                user32.keybd_event(0x09, 0, 0, 0)  # Tab按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab释放
                time.sleep(0.1)
                self.logger.info(f"Tab {i+1}/6")

            # 等待焦点稳定
            time.sleep(0.5)

            # 按Enter执行
            self.logger.info("⏎ 按Enter执行...")
            user32.keybd_event(0x0D, 0, 0, 0)  # Enter按下
            user32.keybd_event(0x0D, 0, 2, 0)  # Enter释放

            self.logger.info("✅ 已执行Tab 6次 + Enter操作")
            return True

        except Exception as e:
            self.logger.error(f"Tab+Enter操作失败: {e}")
            return False




    
    def stop_auto_register(self):
        """停止自动注册"""
        self.is_running = False

    def _check_registration_success(self, max_wait=30):
        """检测注册成功的标志元素"""
        try:
            self.logger.info("🔍 开始检测注册成功标志...")

            start_time = time.time()
            check_count = 0

            while time.time() - start_time < max_wait:
                check_count += 1
                elapsed = time.time() - start_time

                # 每5秒显示一次进度
                if check_count % 25 == 1:  # 25 * 0.2秒 = 5秒
                    self.logger.info(f"🔍 检测注册成功... 已等待 {elapsed:.1f}秒")

                # 检测注册成功的标志元素
                success_detected = self._detect_success_element()
                if success_detected:
                    self.logger.info("✅ 检测到注册成功标志！")
                    return True

                time.sleep(0.2)

            self.logger.warning(f"⚠️ 等待{max_wait}秒后未检测到注册成功标志")
            return False

        except Exception as e:
            self.logger.error(f"❌ 检测注册成功失败: {e}")
            return False

    def _detect_success_element(self):
        """检测页面中的注册成功元素"""
        try:
            # 方法1：检测页面标题变化
            current_title = self._get_current_page_title()
            if current_title:
                success_keywords = ['dashboard', 'welcome', 'success', 'complete']
                if any(keyword in current_title.lower() for keyword in success_keywords):
                    self.logger.info(f"✅ 页面标题显示成功: {current_title}")
                    return True

            # 方法2：检测特定元素（模拟）
            # 实际项目中这里应该使用selenium或其他工具检测DOM元素
            # <span class="gsi-material-button-contents">Sign up and start coding</span>

            # 暂时使用简单的时间延迟模拟检测
            time.sleep(1)

            # 这里应该实现真正的元素检测逻辑
            return True  # 暂时返回True用于测试

        except Exception as e:
            self.logger.error(f"检测成功元素失败: {e}")
            return False

    def _save_account_to_database(self, email_address):
        """保存账号到账号库"""
        try:
            from datetime import datetime, timedelta
            import json
            import os

            self.logger.info(f"💾 保存账号到账号库: {email_address}")

            # 🔧 账号库文件路径 - 使用与main.py一致的路径检测逻辑
            import sys
            # 直接使用main.py中设置的环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                accounts_file = os.path.join(config_dir, 'accounts.json')
            else:
                # 备选方案：使用sys.argv[0]
                if sys.argv and len(sys.argv) > 0:
                    argv_path = os.path.abspath(sys.argv[0])
                    if argv_path.endswith('.exe'):
                        accounts_file = os.path.join(os.path.dirname(argv_path), 'accounts.json')
                    else:
                        accounts_file = os.path.join(os.path.dirname(argv_path), 'accounts.json')
                else:
                    accounts_file = os.path.join(os.getcwd(), 'accounts.json')

            # 确保目录存在
            os.makedirs(os.path.dirname(accounts_file), exist_ok=True)

            # 读取现有账号库
            accounts = []
            if os.path.exists(accounts_file):
                try:
                    with open(accounts_file, 'r', encoding='utf-8') as f:
                        accounts = json.load(f)
                except Exception as e:
                    self.logger.warning(f"读取账号库失败，创建新的: {e}")
                    accounts = []

            # 创建账号记录
            now = datetime.now()
            expire_date = now + timedelta(days=14)  # 14天使用期限

            account_record = {
                'id': len(accounts) + 1,
                'email': email_address,
                'created_time': now.strftime('%Y-%m-%d %H:%M:%S'),
                'expire_time': expire_date.strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'active',
                'days_remaining': 14,
                'usage_count': 0,
                'last_used': None,
                'notes': '自动注册创建'
            }

            # 添加到账号库
            accounts.append(account_record)

            # 保存账号库
            with open(accounts_file, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ 账号已保存到账号库，ID: {account_record['id']}")
            self.logger.info(f"📅 创建时间: {account_record['created_time']}")
            self.logger.info(f"⏰ 过期时间: {account_record['expire_time']}")

            return True

        except Exception as e:
            self.logger.error(f"❌ 保存账号到账号库失败: {e}")
            return False


class SimpleAutoRegister:
    """简化版自动注册 - 仅打开浏览器"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def start_simple_register(self):
        """启动简化版注册流程 - 仅打开浏览器"""
        try:
            self.logger.info("🚀 启动简化版注册流程...")
            
            # 直接使用系统默认浏览器打开
            target_url = "https://augmentcode.com"
            self.logger.info(f"🌐 正在打开浏览器访问: {target_url}")
            
            webbrowser.open(target_url)
            
            self.logger.info("✅ 浏览器已打开，请手动点击登录按钮")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 打开浏览器失败: {e}")
            return False


# 全局实例
auto_register = AutoRegister()
simple_auto_register = SimpleAutoRegister()
