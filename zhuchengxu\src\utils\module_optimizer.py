"""
模块优化器 - 基于CSDN文章的Python性能优化方案
参考: https://blog.csdn.net/weixin_45939263/article/details/135270998
"""

import os
import sys
import py_compile
import compileall
import time
import shutil
from pathlib import Path
import importlib.util
from typing import List, Dict, Any


class ModuleOptimizer:
    """模块优化器 - 预编译和缓存Python模块"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.cache_dir = self.project_root / "__pycache_optimized__"
        self.compiled_modules = {}
        self.optimization_stats = {
            'compiled_files': 0,
            'cache_hits': 0,
            'total_time_saved': 0
        }
        
        # 确保缓存目录存在
        self.cache_dir.mkdir(exist_ok=True)
        print(f"✅ 模块优化器初始化完成，缓存目录: {self.cache_dir}")
        
    def precompile_all_modules(self):
        """预编译所有Python模块"""
        print("🔄 开始预编译所有Python模块...")
        start_time = time.time()
        
        # 获取所有Python文件
        python_files = list(self.project_root.rglob("*.py"))
        
        # 过滤掉不需要编译的文件
        excluded_patterns = ['__pycache__', '.git', 'build', 'dist', 'test']
        python_files = [
            f for f in python_files 
            if not any(pattern in str(f) for pattern in excluded_patterns)
        ]
        
        compiled_count = 0
        for py_file in python_files:
            try:
                self._compile_single_file(py_file)
                compiled_count += 1
            except Exception as e:
                print(f"⚠️ 编译失败 {py_file.name}: {e}")
                
        elapsed = time.time() - start_time
        self.optimization_stats['compiled_files'] = compiled_count
        
        print(f"""
🎉 模块预编译完成！
📊 统计信息:
   - 编译文件数: {compiled_count}/{len(python_files)}
   - 编译耗时: {elapsed:.2f} 秒
   - 缓存目录: {self.cache_dir}
        """)
        
    def _compile_single_file(self, py_file: Path):
        """编译单个Python文件"""
        try:
            # 计算相对路径
            rel_path = py_file.relative_to(self.project_root)
            
            # 创建缓存文件路径
            cache_file = self.cache_dir / f"{rel_path.with_suffix('.pyc')}"
            cache_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 检查是否需要重新编译
            if (cache_file.exists() and 
                cache_file.stat().st_mtime > py_file.stat().st_mtime):
                return  # 缓存是最新的
                
            # 编译文件
            py_compile.compile(str(py_file), str(cache_file), doraise=True)
            
        except Exception as e:
            raise Exception(f"编译 {py_file.name} 失败: {e}")
            
    def optimize_imports(self):
        """优化导入性能"""
        print("⚡ 开始优化导入性能...")
        
        # 预加载常用模块到sys.modules
        common_modules = [
            'json', 'os', 'sys', 'time', 'threading', 'queue',
            'pathlib', 'typing', 'functools', 'itertools'
        ]
        
        loaded_count = 0
        for module_name in common_modules:
            try:
                if module_name not in sys.modules:
                    __import__(module_name)
                    loaded_count += 1
            except ImportError:
                pass
                
        print(f"✅ 预加载了 {loaded_count} 个常用模块")
        
    def create_import_cache(self):
        """创建导入缓存"""
        print("📦 创建导入缓存...")
        
        # 缓存项目模块的导入路径
        import_cache = {}
        
        for py_file in self.project_root.rglob("*.py"):
            if '__pycache__' in str(py_file):
                continue
                
            try:
                # 计算模块名
                rel_path = py_file.relative_to(self.project_root)
                module_parts = list(rel_path.with_suffix('').parts)
                
                # 跳过__init__.py
                if module_parts[-1] == '__init__':
                    module_parts = module_parts[:-1]
                    
                module_name = '.'.join(module_parts)
                import_cache[module_name] = str(py_file)
                
            except Exception:
                continue
                
        # 保存缓存
        cache_file = self.cache_dir / "import_cache.json"
        import json
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(import_cache, f, indent=2)
            
        print(f"✅ 导入缓存已创建，包含 {len(import_cache)} 个模块")
        
    def enable_fast_import(self):
        """启用快速导入模式"""
        try:
            # 设置Python优化标志
            sys.dont_write_bytecode = False  # 允许写入字节码
            
            # 添加优化的缓存目录到路径
            if str(self.cache_dir) not in sys.path:
                sys.path.insert(0, str(self.cache_dir))
                
            # 启用导入优化
            import importlib
            importlib.invalidate_caches()
            
            print("✅ 快速导入模式已启用")
            
        except Exception as e:
            print(f"⚠️ 启用快速导入失败: {e}")
            
    def cleanup_old_cache(self):
        """清理旧的缓存文件"""
        try:
            if self.cache_dir.exists():
                # 删除超过7天的缓存文件
                current_time = time.time()
                cleaned_count = 0
                
                for cache_file in self.cache_dir.rglob("*"):
                    if cache_file.is_file():
                        file_age = current_time - cache_file.stat().st_mtime
                        if file_age > 7 * 24 * 3600:  # 7天
                            cache_file.unlink()
                            cleaned_count += 1
                            
                if cleaned_count > 0:
                    print(f"🗑️ 清理了 {cleaned_count} 个过期缓存文件")
                    
        except Exception as e:
            print(f"⚠️ 清理缓存失败: {e}")
            
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        cache_size = 0
        cache_files = 0
        
        if self.cache_dir.exists():
            for cache_file in self.cache_dir.rglob("*"):
                if cache_file.is_file():
                    cache_size += cache_file.stat().st_size
                    cache_files += 1
                    
        return {
            **self.optimization_stats,
            'cache_size_mb': cache_size / 1024 / 1024,
            'cache_files': cache_files,
            'cache_dir': str(self.cache_dir)
        }
        
    def optimize_all(self):
        """执行所有优化"""
        print("🚀 开始全面模块优化...")
        start_time = time.time()
        
        # 清理旧缓存
        self.cleanup_old_cache()
        
        # 预编译模块
        self.precompile_all_modules()
        
        # 优化导入
        self.optimize_imports()
        
        # 创建导入缓存
        self.create_import_cache()
        
        # 启用快速导入
        self.enable_fast_import()
        
        # 统计信息
        elapsed = time.time() - start_time
        stats = self.get_optimization_stats()
        
        print(f"""
🎉 模块优化完成！
📊 优化统计:
   - 总耗时: {elapsed:.2f} 秒
   - 编译文件: {stats['compiled_files']} 个
   - 缓存大小: {stats['cache_size_mb']:.1f} MB
   - 缓存文件: {stats['cache_files']} 个
   
🚀 程序启动和运行速度将显著提升！
        """)


class FastImporter:
    """快速导入器 - 运行时导入优化"""
    
    def __init__(self):
        self.import_cache = {}
        self.load_import_cache()
        
    def load_import_cache(self):
        """加载导入缓存"""
        try:
            cache_file = Path(__file__).parent.parent.parent / "__pycache_optimized__" / "import_cache.json"
            if cache_file.exists():
                import json
                with open(cache_file, 'r', encoding='utf-8') as f:
                    self.import_cache = json.load(f)
                print(f"✅ 导入缓存已加载，包含 {len(self.import_cache)} 个模块")
        except Exception as e:
            print(f"⚠️ 加载导入缓存失败: {e}")
            
    def fast_import(self, module_name: str):
        """快速导入模块"""
        try:
            # 检查是否已在sys.modules中
            if module_name in sys.modules:
                return sys.modules[module_name]
                
            # 检查导入缓存
            if module_name in self.import_cache:
                module_path = self.import_cache[module_name]
                spec = importlib.util.spec_from_file_location(module_name, module_path)
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    sys.modules[module_name] = module
                    spec.loader.exec_module(module)
                    return module
                    
            # 降级到标准导入
            return __import__(module_name)
            
        except Exception:
            # 最后的降级方案
            return __import__(module_name)


# 全局优化器实例
_module_optimizer = None
_fast_importer = None

def get_module_optimizer() -> ModuleOptimizer:
    """获取模块优化器"""
    global _module_optimizer
    if _module_optimizer is None:
        _module_optimizer = ModuleOptimizer()
    return _module_optimizer

def get_fast_importer() -> FastImporter:
    """获取快速导入器"""
    global _fast_importer
    if _fast_importer is None:
        _fast_importer = FastImporter()
    return _fast_importer

def optimize_modules():
    """执行模块优化"""
    optimizer = get_module_optimizer()
    optimizer.optimize_all()

def enable_fast_imports():
    """启用快速导入"""
    importer = get_fast_importer()
    print("✅ 快速导入器已启用")
