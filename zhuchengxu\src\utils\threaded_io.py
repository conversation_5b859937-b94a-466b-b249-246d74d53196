"""
多线程I/O优化模块 - 高性能文件和数据处理
"""

import threading
import queue
import time
import json
import os
from typing import Any, Callable, Optional, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path


class ThreadedIOManager:
    """多线程I/O管理器"""
    
    def __init__(self, max_workers: int = 8):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 任务队列
        self.io_queue = queue.Queue()
        self.running = True
        
        # 工作线程
        self.worker_threads = []
        for i in range(min(4, max_workers)):  # 最多4个I/O工作线程
            thread = threading.Thread(target=self._io_worker, daemon=True)
            thread.start()
            self.worker_threads.append(thread)
            
        print(f"✅ 多线程I/O管理器已启动 ({max_workers}个工作线程)")
        
    def _io_worker(self):
        """I/O工作线程"""
        while self.running:
            try:
                # 获取任务
                task = self.io_queue.get(timeout=1)
                if task is None:
                    break
                    
                task_type = task['type']
                callback = task.get('callback')
                
                try:
                    if task_type == 'read_file':
                        result = self._read_file_sync(task['path'], task.get('encoding', 'utf-8'))
                    elif task_type == 'write_file':
                        result = self._write_file_sync(task['path'], task['content'], task.get('encoding', 'utf-8'))
                    elif task_type == 'read_json':
                        result = self._read_json_sync(task['path'])
                    elif task_type == 'write_json':
                        result = self._write_json_sync(task['path'], task['data'])
                    elif task_type == 'list_dir':
                        result = self._list_dir_sync(task['path'], task.get('pattern'))
                    elif task_type == 'delete_files':
                        result = self._delete_files_sync(task['paths'])
                    else:
                        result = {'success': False, 'error': f'未知任务类型: {task_type}'}
                        
                    # 调用回调
                    if callback:
                        callback(result)
                        
                except Exception as e:
                    if callback:
                        callback({'success': False, 'error': str(e)})
                        
                finally:
                    self.io_queue.task_done()
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"⚠️ I/O工作线程异常: {e}")
                time.sleep(1)
                
    def _read_file_sync(self, path: str, encoding: str = 'utf-8') -> Dict[str, Any]:
        """同步读取文件"""
        try:
            with open(path, 'r', encoding=encoding) as f:
                content = f.read()
            return {'success': True, 'content': content, 'path': path}
        except Exception as e:
            return {'success': False, 'error': str(e), 'path': path}
            
    def _write_file_sync(self, path: str, content: str, encoding: str = 'utf-8') -> Dict[str, Any]:
        """同步写入文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(path), exist_ok=True)
            
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
            return {'success': True, 'path': path, 'size': len(content)}
        except Exception as e:
            return {'success': False, 'error': str(e), 'path': path}
            
    def _read_json_sync(self, path: str) -> Dict[str, Any]:
        """同步读取JSON文件"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return {'success': True, 'data': data, 'path': path}
        except Exception as e:
            return {'success': False, 'error': str(e), 'path': path}
            
    def _write_json_sync(self, path: str, data: Any) -> Dict[str, Any]:
        """同步写入JSON文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(path), exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return {'success': True, 'path': path}
        except Exception as e:
            return {'success': False, 'error': str(e), 'path': path}
            
    def _list_dir_sync(self, path: str, pattern: Optional[str] = None) -> Dict[str, Any]:
        """同步列出目录"""
        try:
            path_obj = Path(path)
            if pattern:
                files = list(path_obj.glob(pattern))
            else:
                files = list(path_obj.iterdir())
                
            file_list = [str(f) for f in files]
            return {'success': True, 'files': file_list, 'count': len(file_list)}
        except Exception as e:
            return {'success': False, 'error': str(e), 'path': path}
            
    def _delete_files_sync(self, paths: list) -> Dict[str, Any]:
        """同步删除文件"""
        deleted = []
        errors = []
        
        for path in paths:
            try:
                if os.path.isfile(path):
                    os.remove(path)
                    deleted.append(path)
                elif os.path.isdir(path):
                    import shutil
                    shutil.rmtree(path)
                    deleted.append(path)
            except Exception as e:
                errors.append({'path': path, 'error': str(e)})
                
        return {
            'success': len(errors) == 0,
            'deleted': deleted,
            'errors': errors,
            'deleted_count': len(deleted)
        }
        
    def read_file_async(self, path: str, callback: Callable, encoding: str = 'utf-8'):
        """异步读取文件"""
        task = {
            'type': 'read_file',
            'path': path,
            'encoding': encoding,
            'callback': callback
        }
        self.io_queue.put(task)
        
    def write_file_async(self, path: str, content: str, callback: Callable, encoding: str = 'utf-8'):
        """异步写入文件"""
        task = {
            'type': 'write_file',
            'path': path,
            'content': content,
            'encoding': encoding,
            'callback': callback
        }
        self.io_queue.put(task)
        
    def read_json_async(self, path: str, callback: Callable):
        """异步读取JSON文件"""
        task = {
            'type': 'read_json',
            'path': path,
            'callback': callback
        }
        self.io_queue.put(task)
        
    def write_json_async(self, path: str, data: Any, callback: Callable):
        """异步写入JSON文件"""
        task = {
            'type': 'write_json',
            'path': path,
            'data': data,
            'callback': callback
        }
        self.io_queue.put(task)
        
    def list_dir_async(self, path: str, callback: Callable, pattern: Optional[str] = None):
        """异步列出目录"""
        task = {
            'type': 'list_dir',
            'path': path,
            'pattern': pattern,
            'callback': callback
        }
        self.io_queue.put(task)
        
    def delete_files_async(self, paths: list, callback: Callable):
        """异步删除文件"""
        task = {
            'type': 'delete_files',
            'paths': paths,
            'callback': callback
        }
        self.io_queue.put(task)
        
    def batch_process(self, tasks: list, callback: Callable):
        """批量处理任务"""
        def _batch_worker():
            results = []
            futures = []
            
            # 提交所有任务
            for task in tasks:
                future = self.executor.submit(self._process_single_task, task)
                futures.append(future)
                
            # 收集结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    results.append({'success': False, 'error': str(e)})
                    
            # 调用回调
            callback({
                'success': True,
                'results': results,
                'total': len(tasks),
                'completed': len(results)
            })
            
        # 在单独线程中执行批量任务
        threading.Thread(target=_batch_worker, daemon=True).start()
        
    def _process_single_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个任务"""
        task_type = task['type']
        
        if task_type == 'read_file':
            return self._read_file_sync(task['path'], task.get('encoding', 'utf-8'))
        elif task_type == 'write_file':
            return self._write_file_sync(task['path'], task['content'], task.get('encoding', 'utf-8'))
        elif task_type == 'read_json':
            return self._read_json_sync(task['path'])
        elif task_type == 'write_json':
            return self._write_json_sync(task['path'], task['data'])
        elif task_type == 'list_dir':
            return self._list_dir_sync(task['path'], task.get('pattern'))
        elif task_type == 'delete_files':
            return self._delete_files_sync(task['paths'])
        else:
            return {'success': False, 'error': f'未知任务类型: {task_type}'}
            
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.io_queue.qsize()
        
    def stop(self):
        """停止I/O管理器"""
        self.running = False
        
        # 停止工作线程
        for _ in self.worker_threads:
            self.io_queue.put(None)
            
        # 等待线程结束
        for thread in self.worker_threads:
            thread.join(timeout=2)
            
        # 关闭线程池
        self.executor.shutdown(wait=False)
        print("🛑 多线程I/O管理器已停止")


# 全局I/O管理器
_io_manager = None

def get_io_manager() -> ThreadedIOManager:
    """获取全局I/O管理器"""
    global _io_manager
    if _io_manager is None:
        _io_manager = ThreadedIOManager()
    return _io_manager
