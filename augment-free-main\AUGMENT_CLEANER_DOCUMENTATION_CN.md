# Augment Free - 重置与切换工具使用文档

## 1. 概述

Augment Free 是一个专为 AugmentCode（VS Code 的 AI 代码助手插件）用户设计的数据清理工具，通过重置设备标识和清理跟踪数据，使同一台设备能够无限次切换不同账号，彻底解决账号被锁定、设备绑定等问题。

## 2. 主要功能

### 2.1 核心功能概述

- **设备 ID 重置**：生成全新的设备标识符，解除设备绑定限制
- **机器 ID 重置**：更新机器唯一标识符，使系统视为全新设备
- **遥测数据清理**：清除所有用于跟踪的遥测数据
- **数据库清理**：清理 SQLite 数据库中与 Augment 相关的记录
- **工作区存储清理**：清除工作区存储文件中的账号关联信息
- **缓存清理**：删除所有 Augment 相关的缓存数据
- **用户配置清理**：移除设置文件中与 Augment 相关的配置项

### 2.2 安全特性

- **自动备份**：操作前自动备份所有原始文件，防止意外损失
- **一键恢复**：支持从备份快速恢复到原始状态
- **详细日志**：记录所有操作过程，便于追踪和排错
- **异常处理**：完善的错误处理机制，保证操作安全性

## 3. 技术实现

### 3.1 数据重置原理

本工具通过分析 AugmentCode 插件的数据存储机制，发现其主要通过以下几种方式识别和绑定设备：

1. **机器 ID**：存储在 VS Code 的 `storage.json` 文件中的 `telemetry.machineId` 字段
2. **设备 ID**：存储在 `machineid` 文件和 `storage.json` 的 `telemetry.devDeviceId` 字段
3. **SQLite 数据库记录**：存储在 `state.vscdb` 数据库的 `ItemTable` 表中
4. **工作区存储**：每个工作区中的 Augment 插件数据

通过生成新的随机 ID 并替换这些标识符，同时清理相关数据记录，可以使 VS Code 和 Augment 插件将设备识别为一个全新的未绑定设备。

### 3.2 模块结构

工具由以下主要模块组成：

#### 3.2.1 核心模块

- **augment_cleaner.py**：主程序逻辑，整合各个功能模块
- **index.py**：程序入口，调用各功能模块执行清理操作

#### 3.2.2 功能模块

- **augutils/json_modifier.py**：修改 JSON 配置文件，重置设备 ID
- **augutils/sqlite_modifier.py**：清理 SQLite 数据库中的 Augment 相关数据
- **augutils/workspace_cleaner.py**：清理工作区存储中的 Augment 数据

#### 3.2.3 工具模块

- **utils/paths.py**：处理不同操作系统的路径定位
- **utils/device_codes.py**：生成安全的随机设备 ID 和机器 ID

### 3.3 跨平台实现

工具采用 Python 标准库实现核心功能，保证了跨平台兼容性：

- **Windows**：通过 `os.getenv("APPDATA")` 定位 VS Code 数据目录
- **macOS**：使用 `~/Library/Application Support` 路径
- **Linux**：使用 `~/.config` 和 `~/.local/share` 路径

通过 `sys.platform` 检测操作系统类型，为不同平台提供对应的文件路径处理逻辑。

### 3.4 数据安全与备份

每次操作前，工具会自动创建带时间戳的备份：

1. JSON 文件备份：`<filename>.bak.<timestamp>`
2. SQLite 数据库备份：`<filename>.bak.<timestamp>`
3. 工作区文件备份：创建 ZIP 压缩包 `workspaceStorage_backup_<timestamp>.zip`

备份文件保存在原始文件目录或工具运行目录中，便于在需要时快速恢复。

## 4. 使用说明

### 4.1 前置条件

在使用本工具前，确保：

- VS Code 已完全关闭（包括所有窗口和后台进程）
- 已禁用或卸载 AugmentCode 插件
- 系统中的 VS Code 版本已运行过至少一次

### 4.2 运行方式

#### Windows 用户

```
# 方式一：双击运行 (推荐)
双击 augment-free.exe

# 方式二：命令行运行
.\augment-free.exe

# 方式三：使用 PowerShell 脚本
.\augment_cleaner.ps1
```

#### macOS/Linux 用户

```bash
# 直接运行 Python 脚本
python index.py

# 或使用 augment_cleaner.py
python augment_cleaner.py --clean
```

### 4.3 命令行参数

`augment_cleaner.py` 支持以下命令行参数：

```
--scan             扫描 Augment 相关数据，不进行清理
--clean            执行完整清理（默认会创建备份）
--no-backup        清理时不创建备份
--restore <path>   从指定备份路径恢复
--clean-global     只清理全局存储
--clean-workspace  只清理工作区存储
--clean-settings   只清理用户配置
--clean-logs       只清理扩展日志
--clean-cache      只清理扩展缓存
--clean-sqlite     只清理 SQLite 存储
--clean-registry   只清理注册表项（仅限 Windows）
```

### 4.4 操作流程

1. **关闭 VS Code**：确保所有 VS Code 窗口和进程已完全关闭
2. **运行工具**：执行清理操作，等待完成
3. **重新启动 VS Code**：启动 VS Code
4. **安装/启用插件**：重新启用 AugmentCode 插件
5. **登录新账号**：使用新的账号登录 AugmentCode

## 5. 高级功能

### 5.1 仅清理特定数据

如果只需要清理特定类型的数据，可以使用相应的命令行参数：

```bash
# 只清理全局存储
python augment_cleaner.py --clean-global

# 只清理工作区存储
python augment_cleaner.py --clean-workspace

# 只清理用户配置
python augment_cleaner.py --clean-settings
```

### 5.2 数据扫描

在清理前，可以先扫描系统中的 Augment 相关数据：

```bash
python augment_cleaner.py --scan
```

这将显示所有找到的 Augment 相关文件和数据，但不会进行任何修改。

### 5.3 从备份恢复

如果清理后需要恢复到之前的状态：

```bash
python augment_cleaner.py --restore <备份路径>
```

指定的备份路径是之前清理操作时自动创建的备份文件或目录。

## 6. 常见问题解答

### 6.1 工具无法找到或修改文件？

确保：
- VS Code 已至少运行过一次，生成了必要的配置文件
- 您有足够的文件系统权限（Windows 可能需要管理员权限）
- 使用的是标准安装路径的 VS Code（非便携版或自定义路径）

### 6.2 清理后 VS Code 出现问题？

可能的解决方法：
- 从备份恢复：`python augment_cleaner.py --restore <备份路径>`
- 重新安装 VS Code（如果没有备份或备份无效）
- 检查 VS Code 日志（帮助 -> 切换开发人员工具 -> 控制台）

### 6.3 特殊版本的 VS Code 支持？

本工具支持：
- VS Code 标准版
- VS Code Insiders
- VSCodium

对于这些特殊版本，可能需要手动修改路径定义（在 `utils/paths.py` 中）。

## 7. 技术规格

- **语言**：Python 3.6+
- **依赖**：仅使用 Python 标准库，无外部依赖
- **平台**：Windows 10/11、macOS 10.14+、主流 Linux 发行版
- **EXE 大小**：约 9.3 MB（Windows 可执行文件）

## 8. 安全说明

本工具：
- 仅修改 VS Code 和 Augment 插件的配置文件和数据
- 不收集任何用户数据
- 不发送任何网络请求
- 不修改系统级别的设置（除 Windows 注册表中与 VS Code 相关的项目外）
- 在操作前创建所有修改文件的备份

## 9. 总结

Augment Free 工具通过精确定位和清理 AugmentCode 插件的设备识别数据，实现了在同一设备上无限次切换账号的功能，有效解决了账号被锁定、设备绑定等限制问题。工具采用安全可靠的方式操作，自动备份所有数据，适用于 Windows、macOS 和 Linux 等主流操作系统。 