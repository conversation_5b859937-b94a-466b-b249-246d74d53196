"""
简化的多线程网络模块 - 避免asyncio兼容性问题
"""

import threading
import queue
import time
import json
from typing import Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor
import requests


class SimpleAsyncNetworkManager:
    """简化的异步网络管理器 - 基于多线程"""
    
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.running = True
        
        # 请求队列
        self.request_queue = queue.Queue()
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 会话配置
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'AugmentTool/2.0'})
        
        # 启动工作线程
        self.worker_threads = []
        for i in range(min(4, max_workers)):  # 最多4个网络工作线程
            thread = threading.Thread(target=self._network_worker, daemon=True)
            thread.start()
            self.worker_threads.append(thread)
            
        print(f"✅ 简化异步网络管理器已启动 ({max_workers}个工作线程)")
        
    def _network_worker(self):
        """网络工作线程"""
        while self.running:
            try:
                # 获取请求任务
                task = self.request_queue.get(timeout=1)
                if task is None:
                    break
                    
                self._process_request_sync(task)
                self.request_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"⚠️ 网络工作线程异常: {e}")
                time.sleep(1)
                
    def _process_request_sync(self, request_data: Dict[str, Any]):
        """同步处理网络请求"""
        try:
            request_id = request_data['id']
            method = request_data['method']
            url = request_data['url']
            data = request_data.get('data')
            headers = request_data.get('headers', {})
            timeout = request_data.get('timeout', 10)
            callback = request_data.get('callback')
            
            # 发送请求
            if method.upper() == 'GET':
                response = self.session.get(
                    url, params=data, headers=headers, timeout=timeout
                )
            elif method.upper() == 'POST':
                response = self.session.post(
                    url, json=data, headers=headers, timeout=timeout
                )
            elif method.upper() == 'PUT':
                response = self.session.put(
                    url, json=data, headers=headers, timeout=timeout
                )
            elif method.upper() == 'DELETE':
                response = self.session.delete(
                    url, headers=headers, timeout=timeout
                )
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            # 解析响应
            try:
                if response.headers.get('content-type', '').startswith('application/json'):
                    result_data = response.json()
                else:
                    result_data = response.text
            except:
                result_data = response.text
            
            # 构建响应数据
            response_data = {
                'status_code': response.status_code,
                'data': result_data,
                'headers': dict(response.headers),
                'success': 200 <= response.status_code < 300,
                'request_id': request_id
            }
            
            # 调用回调函数
            if callback:
                try:
                    callback(response_data)
                except Exception as e:
                    print(f"⚠️ 回调函数执行异常: {e}")
                    
        except requests.exceptions.Timeout:
            if callback:
                callback({
                    'success': False, 
                    'error': 'timeout', 
                    'status_code': 0,
                    'request_id': request_data.get('id', 'unknown')
                })
        except Exception as e:
            if callback:
                callback({
                    'success': False, 
                    'error': str(e), 
                    'status_code': 0,
                    'request_id': request_data.get('id', 'unknown')
                })
                
    def async_request(self, method: str, url: str, data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None, timeout: int = 10,
                     callback: Optional[Callable] = None) -> str:
        """发送异步请求"""
        request_id = f"req_{int(time.time() * 1000)}_{id(callback)}"
        
        request_data = {
            'id': request_id,
            'method': method,
            'url': url,
            'data': data,
            'headers': headers or {},
            'timeout': timeout,
            'callback': callback
        }
        
        self.request_queue.put(request_data)
        return request_id
        
    def get(self, url: str, params: Optional[Dict] = None, **kwargs) -> str:
        """GET请求"""
        return self.async_request('GET', url, data=params, **kwargs)
        
    def post(self, url: str, data: Optional[Dict] = None, **kwargs) -> str:
        """POST请求"""
        return self.async_request('POST', url, data=data, **kwargs)
        
    def put(self, url: str, data: Optional[Dict] = None, **kwargs) -> str:
        """PUT请求"""
        return self.async_request('PUT', url, data=data, **kwargs)
        
    def delete(self, url: str, **kwargs) -> str:
        """DELETE请求"""
        return self.async_request('DELETE', url, **kwargs)
        
    def sync_request(self, method: str, url: str, data: Optional[Dict] = None,
                    headers: Optional[Dict] = None, timeout: int = 10) -> Dict[str, Any]:
        """同步请求（阻塞）"""
        try:
            if method.upper() == 'GET':
                response = self.session.get(
                    url, params=data, headers=headers or {}, timeout=timeout
                )
            elif method.upper() == 'POST':
                response = self.session.post(
                    url, json=data, headers=headers or {}, timeout=timeout
                )
            else:
                response = self.session.request(
                    method, url, json=data, headers=headers or {}, timeout=timeout
                )
            
            try:
                result_data = response.json()
            except:
                result_data = response.text
                
            return {
                'status_code': response.status_code,
                'data': result_data,
                'headers': dict(response.headers),
                'success': 200 <= response.status_code < 300
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'status_code': 0
            }
            
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.request_queue.qsize()
        
    def stop(self):
        """停止网络管理器"""
        self.running = False
        
        # 停止工作线程
        for _ in self.worker_threads:
            self.request_queue.put(None)
            
        # 等待线程结束
        for thread in self.worker_threads:
            thread.join(timeout=2)
            
        # 关闭线程池
        self.executor.shutdown(wait=False)
        
        # 关闭会话
        self.session.close()
        print("🛑 简化异步网络管理器已停止")


# 全局网络管理器
_simple_network_manager = None

def get_simple_network_manager() -> SimpleAsyncNetworkManager:
    """获取简化网络管理器"""
    global _simple_network_manager
    if _simple_network_manager is None:
        _simple_network_manager = SimpleAsyncNetworkManager()
    return _simple_network_manager

def simple_async_get(url: str, params: Optional[Dict] = None, 
                    callback: Optional[Callable] = None, **kwargs) -> str:
    """简化异步GET请求"""
    return get_simple_network_manager().get(url, params, callback=callback, **kwargs)

def simple_async_post(url: str, data: Optional[Dict] = None,
                     callback: Optional[Callable] = None, **kwargs) -> str:
    """简化异步POST请求"""
    return get_simple_network_manager().post(url, data, callback=callback, **kwargs)
