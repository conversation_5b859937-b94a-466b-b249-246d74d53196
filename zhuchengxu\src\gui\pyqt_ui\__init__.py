#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt版本的现代化UI模块
Modern PyQt UI Module - 高性能GUI实现
"""

try:
    # 尝试相对导入（开发环境）
    from .pyqt_main_window import PyQtMainWindow
    from .pyqt_design_system import PyQtDesignSystem
    from .pyqt_components import GlassWidget, NeonButton, ModernSidebar
except ImportError:
    try:
        # 尝试绝对导入（打包环境）
        from src.gui.pyqt_ui.pyqt_main_window import PyQtMainWindow
        from src.gui.pyqt_ui.pyqt_design_system import PyQtDesignSystem
        from src.gui.pyqt_ui.pyqt_components import GlassWidget, NeonButton, ModernSidebar
    except ImportError:
        # 最后尝试直接导入
        from gui.pyqt_ui.pyqt_main_window import PyQtMainWindow
        from gui.pyqt_ui.pyqt_design_system import PyQtDesignSystem
        from gui.pyqt_ui.pyqt_components import GlassWidget, NeonButton, ModernSidebar

__all__ = [
    'PyQtMainWindow',
    'PyQtDesignSystem',
    'GlassWidget',
    'NeonButton',
    'ModernSidebar'
]
