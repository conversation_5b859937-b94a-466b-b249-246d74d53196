<?php
// 测试公告页面的问题
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>公告页面诊断测试</h1>";

try {
    // 检查必要的文件
    $requiredFiles = [
        'includes/Database.php',
        'includes/Logger.php',
        'config/database.php'
    ];

    echo "<h2>1. 检查必要文件</h2>";
    foreach ($requiredFiles as $file) {
        if (file_exists(__DIR__ . '/' . $file)) {
            echo "✓ {$file} 存在<br>";
        } else {
            echo "✗ {$file} 不存在<br>";
        }
    }

    // 检查数据库连接
    echo "<h2>2. 检查数据库连接</h2>";
    require_once 'includes/Database.php';
    require_once 'config/database.php';

    $database = new Database($config['host'], $config['database'], $config['username'], $config['password']);
    echo "✓ 数据库连接成功<br>";

    // 检查公告表是否存在
    echo "<h2>3. 检查公告表</h2>";
    try {
        $result = $database->query("SHOW TABLES LIKE 'announcements'");
        if ($result->rowCount() > 0) {
            echo "✓ announcements 表存在<br>";

            // 检查表结构
            $columns = $database->fetchAll("SHOW COLUMNS FROM announcements");
            echo "表字段：<br>";
            foreach ($columns as $column) {
                echo "- {$column['Field']} ({$column['Type']})<br>";
            }

            // 检查现有数据
            $count = $database->fetchValue("SELECT COUNT(*) FROM announcements");
            echo "<br>现有公告数量: {$count}<br>";

        } else {
            echo "✗ announcements 表不存在<br>";
        }
    } catch (Exception $e) {
        echo "✗ 检查公告表失败: " . $e->getMessage() . "<br>";
    }

    // 检查上传目录
    echo "<h2>4. 检查上传目录</h2>";
    $uploadDir = __DIR__ . '/uploads/announcements/';
    if (is_dir($uploadDir)) {
        echo "✓ 上传目录存在: {$uploadDir}<br>";
        if (is_writable($uploadDir)) {
            echo "✓ 上传目录可写<br>";
        } else {
            echo "✗ 上传目录不可写<br>";
        }
    } else {
        echo "✗ 上传目录不存在: {$uploadDir}<br>";
        // 尝试创建目录
        if (mkdir($uploadDir, 0755, true)) {
            echo "✓ 成功创建上传目录<br>";
        } else {
            echo "✗ 创建上传目录失败<br>";
        }
    }

    // 检查PHP配置
    echo "<h2>5. 检查PHP配置</h2>";
    echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
    echo "post_max_size: " . ini_get('post_max_size') . "<br>";
    echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
    echo "memory_limit: " . ini_get('memory_limit') . "<br>";

    // 检查session
    echo "<h2>6. 检查Session</h2>";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (isset($_SESSION['admin_id'])) {
        echo "✓ 管理员已登录 (ID: {$_SESSION['admin_id']})<br>";
    } else {
        echo "✗ 管理员未登录<br>";
        echo "请先登录管理员账户<br>";
    }

    // 测试handleImageUpload函数
    echo "<h2>7. 测试图片上传函数</h2>";

    // 模拟handleImageUpload函数
    function testHandleImageUpload() {
        $uploadDir = __DIR__ . '/uploads/announcements/';
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                throw new Exception("无法创建上传目录: " . $uploadDir);
            }
        }

        if (!is_writable($uploadDir)) {
            throw new Exception("上传目录不可写: " . $uploadDir);
        }

        return "图片上传函数测试通过";
    }

    try {
        $result = testHandleImageUpload();
        echo "✓ {$result}<br>";
    } catch (Exception $e) {
        echo "✗ 图片上传函数测试失败: " . $e->getMessage() . "<br>";
    }

    // 测试访问admin.php页面
    echo "<h2>8. 测试admin.php页面访问</h2>";
    $adminUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/admin.php?page=announcements";
    echo "管理页面URL: <a href='{$adminUrl}' target='_blank'>{$adminUrl}</a><br>";

    echo "<h2>9. 测试完成</h2>";
    echo "如果以上所有项目都显示 ✓，那么公告页面应该可以正常工作。<br>";
    echo "如果有 ✗ 项目，请根据提示修复相应问题。<br>";

} catch (Exception $e) {
    echo "<h2>错误</h2>";
    echo "测试过程中发生错误: " . $e->getMessage() . "<br>";
    echo "错误堆栈: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>
