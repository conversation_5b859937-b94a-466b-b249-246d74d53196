"""
简化性能优化器 - Python 3.13兼容版本
"""

import gc
import threading
import time
import os
from typing import Dict, Any


class SimplePerformanceOptimizer:
    """简化性能优化器"""
    
    def __init__(self):
        self.running = True
        self.cache = {}
        self.cache_timestamps = {}
        self.max_cache_size = 1000
        self.cache_ttl = 300  # 5分钟
        
        # 启动优化线程
        self._start_optimizer()
        
    def _start_optimizer(self):
        """启动优化线程"""
        self.optimizer_thread = threading.Thread(target=self._optimizer_loop, daemon=True)
        self.optimizer_thread.start()
        print("✅ 简化性能优化器已启动")
        
    def _optimizer_loop(self):
        """优化循环"""
        while self.running:
            try:
                # 每30秒执行一次优化
                time.sleep(30)
                
                # 垃圾回收
                collected = gc.collect()
                if collected > 0:
                    print(f"🗑️ 清理了 {collected} 个对象")
                
                # 清理过期缓存
                self._clean_expired_cache()
                
            except Exception as e:
                print(f"⚠️ 优化循环异常: {e}")
                time.sleep(60)
                
    def _clean_expired_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        for key, timestamp in self.cache_timestamps.items():
            if current_time - timestamp > self.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.cache_timestamps.pop(key, None)
            
        if expired_keys:
            print(f"🧹 清理了 {len(expired_keys)} 个过期缓存项")
            
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况（简化版）"""
        try:
            import resource
            memory_usage = resource.getrusage(resource.RUSAGE_SELF).ru_maxrss
            # Windows上是字节，Unix上是KB
            if os.name == 'nt':
                memory_mb = memory_usage / 1024 / 1024
            else:
                memory_mb = memory_usage / 1024
                
            return {
                "rss": memory_mb,
                "cache_size": len(self.cache),
                "gc_count": sum(gc.get_count())
            }
        except Exception:
            return {
                "rss": 0,
                "cache_size": len(self.cache),
                "gc_count": sum(gc.get_count())
            }
            
    def optimize_memory(self):
        """立即优化内存"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            
            # 清理缓存
            self.cache.clear()
            self.cache_timestamps.clear()
            
            print(f"🚀 内存优化完成，清理了 {collected} 个对象")
            return True
        except Exception as e:
            print(f"❌ 内存优化失败: {e}")
            return False
            
    def cache_get(self, key: str):
        """从缓存获取数据"""
        if key in self.cache:
            # 检查是否过期
            if time.time() - self.cache_timestamps[key] < self.cache_ttl:
                return self.cache[key]
            else:
                # 删除过期项
                del self.cache[key]
                del self.cache_timestamps[key]
        return None
        
    def cache_set(self, key: str, value):
        """设置缓存数据"""
        try:
            # 如果缓存满了，删除最旧的项
            if len(self.cache) >= self.max_cache_size:
                oldest_key = min(self.cache_timestamps.keys(), 
                               key=lambda k: self.cache_timestamps[k])
                del self.cache[oldest_key]
                del self.cache_timestamps[oldest_key]
            
            self.cache[key] = value
            self.cache_timestamps[key] = time.time()
            return True
        except Exception:
            return False
            
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            "timestamp": time.time(),
            "gc_count": sum(gc.get_count()),
            "cache_size": len(self.cache),
            "pid": os.getpid()
        }
        
        # 添加内存信息
        memory_info = self.get_memory_usage()
        stats.update(memory_info)
        
        return stats
        
    def set_high_priority(self):
        """设置高优先级（简化版）"""
        try:
            if os.name == 'nt':
                # Windows
                import subprocess
                subprocess.run(['wmic', 'process', 'where', f'processid={os.getpid()}', 
                              'CALL', 'setpriority', '128'], 
                              capture_output=True, check=False)
                print("⚡ 进程优先级已设置为高")
                return True
            else:
                # Unix/Linux
                os.nice(-10)
                print("⚡ 进程优先级已提升")
                return True
        except Exception as e:
            print(f"⚠️ 设置进程优先级失败: {e}")
            return False
            
    def stop(self):
        """停止优化器"""
        self.running = False
        print("🛑 简化性能优化器已停止")


# 全局优化器实例
_simple_optimizer = None

def get_simple_optimizer() -> SimplePerformanceOptimizer:
    """获取简化优化器实例"""
    global _simple_optimizer
    if _simple_optimizer is None:
        _simple_optimizer = SimplePerformanceOptimizer()
    return _simple_optimizer

def simple_optimize_startup():
    """简化启动优化"""
    optimizer = get_simple_optimizer()
    
    # 设置高优先级
    optimizer.set_high_priority()
    
    # 立即优化内存
    optimizer.optimize_memory()
    
    print("🚀 简化启动优化完成")

def simple_optimize_runtime():
    """简化运行时优化"""
    optimizer = get_simple_optimizer()
    
    # 获取性能统计
    stats = optimizer.get_performance_stats()
    
    # 如果内存使用过高，执行优化
    if "rss" in stats and stats["rss"] > 200:  # 超过200MB
        optimizer.optimize_memory()
        
    return stats
