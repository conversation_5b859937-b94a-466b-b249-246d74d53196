#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Free 主清理工具（重构版）
根据 AUGMENT_CLEANER_DOCUMENTATION_CN.md 设计
"""
import os
import sys
import argparse
import logging
from pathlib import Path
from datetime import datetime

# 导入功能模块
from augutils.json_modifier import modify_telemetry_ids
from augutils.sqlite_modifier import clean_augment_data
from augutils.workspace_cleaner import clean_workspace_storage
from utils.paths import get_storage_path, get_db_path, get_machine_id_path, get_workspace_storage_path

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('augment_cleaner.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# ========== 清理功能实现 ==========

def backup_file(file_path: str) -> str:
    """为指定文件创建带时间戳的备份"""
    if not os.path.exists(file_path):
        return ''
    timestamp = int(datetime.now().timestamp())
    backup_path = f"{file_path}.bak.{timestamp}"
    try:
        import shutil
        shutil.copy2(file_path, backup_path)
        logger.info(f"已备份 {file_path} -> {backup_path}")
        return backup_path
    except Exception as e:
        logger.warning(f"备份 {file_path} 失败: {e}")
        return ''

def scan_all():
    """扫描所有 Augment 相关数据"""
    logger.info("\n==== Augment 相关数据扫描 ====")
    found = {}
    # storage.json
    storage_path = get_storage_path()
    if os.path.exists(storage_path):
        found['storage.json'] = storage_path
    # machineid
    machineid_path = get_machine_id_path()
    if os.path.exists(machineid_path):
        found['machineid'] = machineid_path
    # state.vscdb
    db_path = get_db_path()
    if os.path.exists(db_path):
        found['state.vscdb'] = db_path
    # workspaceStorage
    ws_path = get_workspace_storage_path()
    if os.path.exists(ws_path):
        found['workspaceStorage'] = ws_path
    for k, v in found.items():
        logger.info(f"发现: {k} -> {v}")
    if not found:
        logger.info("未发现任何 Augment 相关数据")
    return found

def clean_all():
    """完整清理流程：ID重置、数据库、工作区、配置等"""
    logger.info("\n==== 开始完整清理 ====")
    # 1. 重置 Telemetry/Machine/Device ID
    try:
        id_result = modify_telemetry_ids()
        logger.info(f"storage.json 备份: {id_result['storage_backup_path']}")
        if id_result['machine_id_backup_path']:
            logger.info(f"machineid 备份: {id_result['machine_id_backup_path']}")
        logger.info(f"旧 Machine ID: {id_result['old_machine_id']}")
        logger.info(f"新 Machine ID: {id_result['new_machine_id']}")
        logger.info(f"旧 Device ID: {id_result['old_device_id']}")
        logger.info(f"新 Device ID: {id_result['new_device_id']}")
    except Exception as e:
        logger.error(f"ID 重置失败: {e}")
    # 2. 清理数据库
    try:
        db_result = clean_augment_data()
        logger.info(f"数据库备份: {db_result['db_backup_path']}")
        logger.info(f"已删除 {db_result['deleted_rows']} 条含 'augment' 的记录")
    except Exception as e:
        logger.error(f"数据库清理失败: {e}")
    # 3. 清理工作区存储
    try:
        ws_result = clean_workspace_storage()
        logger.info(f"工作区备份: {ws_result['backup_path']}")
        logger.info(f"已删除 {ws_result['deleted_files_count']} 个工作区文件")
    except Exception as e:
        logger.error(f"工作区清理失败: {e}")
    logger.info("==== 完整清理完成 ====")

def restore_from_backup(backup_path: str):
    """从备份恢复（仅演示，需根据实际备份结构实现）"""
    logger.info(f"恢复功能请手动将 .bak 文件还原为原文件名")
    logger.info(f"备份路径: {backup_path}")
    # 可扩展为自动还原

def clean_id():
    try:
        id_result = modify_telemetry_ids()
        logger.info(f"storage.json 备份: {id_result['storage_backup_path']}")
        if id_result['machine_id_backup_path']:
            logger.info(f"machineid 备份: {id_result['machine_id_backup_path']}")
        logger.info(f"旧 Machine ID: {id_result['old_machine_id']}")
        logger.info(f"新 Machine ID: {id_result['new_machine_id']}")
        logger.info(f"旧 Device ID: {id_result['old_device_id']}")
        logger.info(f"新 Device ID: {id_result['new_device_id']}")
    except Exception as e:
        logger.error(f"ID 重置失败: {e}")

def clean_db():
    try:
        db_result = clean_augment_data()
        logger.info(f"数据库备份: {db_result['db_backup_path']}")
        logger.info(f"已删除 {db_result['deleted_rows']} 条含 'augment' 的记录")
    except Exception as e:
        logger.error(f"数据库清理失败: {e}")

def clean_workspace():
    try:
        ws_result = clean_workspace_storage()
        logger.info(f"工作区备份: {ws_result['backup_path']}")
        logger.info(f"已删除 {ws_result['deleted_files_count']} 个工作区文件")
    except Exception as e:
        logger.error(f"工作区清理失败: {e}")

# ========== 主入口 ==========

def main():
    parser = argparse.ArgumentParser(
        description="Augment Free 一键清理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python augment_cleaner.py --scan           # 扫描相关数据
  python augment_cleaner.py --clean          # 完整清理（推荐）
  python augment_cleaner.py --clean-id       # 只重置ID
  python augment_cleaner.py --clean-db       # 只清理数据库
  python augment_cleaner.py --clean-ws       # 只清理工作区
  python augment_cleaner.py --restore <bak>  # 恢复（手动）
        """
    )
    parser.add_argument('--scan', action='store_true', help='扫描 Augment 相关数据')
    parser.add_argument('--clean', action='store_true', help='执行完整清理')
    parser.add_argument('--clean-id', action='store_true', help='只重置 Telemetry/Machine/Device ID')
    parser.add_argument('--clean-db', action='store_true', help='只清理数据库')
    parser.add_argument('--clean-ws', action='store_true', help='只清理工作区存储')
    parser.add_argument('--restore', type=str, help='从指定备份路径恢复（需手动）')
    args = parser.parse_args()

    if args.scan:
        scan_all()
    elif args.clean:
        clean_all()
    elif args.clean_id:
        clean_id()
    elif args.clean_db:
        clean_db()
    elif args.clean_ws:
        clean_workspace()
    elif args.restore:
        restore_from_backup(args.restore)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
