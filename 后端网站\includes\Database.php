<?php
/**
 * 数据库管理类
 * 支持MySQL和SQLite数据库存储用户和认证信息
 */

declare(strict_types=1);

// 引入Logger类
require_once __DIR__ . '/Logger.php';

class Database
{
    private PDO $pdo;
    private array $config;
    private string $dbType;
    private ?Logger $logger = null;

    public function __construct(?array $config = null)
    {
        // 加载配置
        if ($config === null) {
            $configFile = __DIR__ . '/../config/database.php';
            if (file_exists($configFile)) {
                $this->config = require $configFile;
            } else {
                // 默认使用SQLite配置
                $this->config = [
                    'type' => 'sqlite',
                    'sqlite' => ['path' => __DIR__ . '/../data/auth_system.db']
                ];
            }
        } else {
            $this->config = $config;
        }

        $this->dbType = $this->config['type'] ?? 'sqlite';
        $this->logger = new Logger();
        $this->initDatabase();
    }
    
    /**
     * 初始化数据库连接和表结构
     */
    private function initDatabase(): void
    {
        try {
            if ($this->dbType === 'mysql') {
                $this->initMySQLDatabase();
            } else {
                $this->initSQLiteDatabase();
            }

            // 创建表结构
            $this->createTables();

        } catch (PDOException $e) {
            throw new Exception("数据库初始化失败: " . $e->getMessage());
        }
    }

    /**
     * 初始化MySQL数据库连接
     */
    private function initMySQLDatabase(): void
    {
        $mysql = $this->config['mysql'];
        $dsn = sprintf(
            "mysql:host=%s;port=%d;dbname=%s;charset=%s",
            $mysql['host'],
            $mysql['port'],
            $mysql['database'],
            $mysql['charset']
        );

        $this->pdo = new PDO($dsn, $mysql['username'], $mysql['password'], $mysql['options']);
    }

    /**
     * 初始化SQLite数据库连接
     */
    private function initSQLiteDatabase(): void
    {
        $dbPath = $this->config['sqlite']['path'];

        // 确保数据目录存在
        $dataDir = dirname($dbPath);
        if (!is_dir($dataDir)) {
            mkdir($dataDir, 0755, true);
        }

        // 创建PDO连接
        $this->pdo = new PDO("sqlite:{$dbPath}");
        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

        // 启用外键约束
        $this->pdo->exec('PRAGMA foreign_keys = ON');
    }
    
    /**
     * 创建数据表
     */
    private function createTables(): void
    {
        if ($this->dbType === 'mysql') {
            $this->createMySQLTables();
        } else {
            $this->createSQLiteTables();
        }

        // 创建索引
        $this->createIndexes();

        // 插入默认管理员账户
        $this->createDefaultAdmin();
    }

    /**
     * 创建MySQL数据表（兼容MySQL 5.7.38）
     */
    private function createMySQLTables(): void
    {
        // 软件授权表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(255) NOT NULL COMMENT '授权码/License Key',
                password VARCHAR(255) NOT NULL COMMENT '密码',
                email VARCHAR(255) COMMENT '联系邮箱',
                qq_number VARCHAR(20) COMMENT 'QQ号码',
                user_type VARCHAR(50) DEFAULT 'basic' COMMENT '授权类型: basic, pro, enterprise',
                max_devices INT DEFAULT 1 COMMENT '最大设备数',
                status TINYINT DEFAULT 1 COMMENT '状态: 1=正常, 0=禁用',
                expire_time DATETIME COMMENT '过期时间',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                notes TEXT COMMENT '备注信息',
                UNIQUE KEY unique_username (username)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // 为现有表添加QQ号字段（如果不存在）
        try {
            $this->pdo->exec("ALTER TABLE users ADD COLUMN qq_number VARCHAR(20) COMMENT 'QQ号码'");
        } catch (PDOException $e) {
            // 字段已存在，忽略错误
        }

        $this->createMySQLAdminTables();
        $this->createMySQLDeviceTables();
        $this->createMySQLTokenTables();
        $this->createMySQLLogTables();
    }

    /**
     * 创建SQLite数据表
     */
    private function createSQLiteTables(): void
    {
        // 软件授权表（重命名为users保持兼容性）
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,  -- 授权码/License Key
                password TEXT NOT NULL,         -- 预留字段
                email TEXT,                     -- 联系邮箱
                qq_number TEXT,                 -- QQ号码
                user_type TEXT DEFAULT 'basic', -- 授权类型: basic, pro, enterprise
                max_devices INTEGER DEFAULT 1, -- 最大设备数
                status INTEGER DEFAULT 1,      -- 状态: 1=正常, 0=禁用
                expire_time DATETIME,          -- 过期时间
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT                     -- 备注信息
            )
        ");

        // 为现有表添加QQ号字段（如果不存在）
        try {
            $this->pdo->exec("ALTER TABLE users ADD COLUMN qq_number TEXT");
        } catch (PDOException $e) {
            // 字段已存在，忽略错误
        }

        $this->createSQLiteDeviceTables();
        $this->createSQLiteTokenTables();
        $this->createSQLiteLogTables();
    }

    /**
     * 创建MySQL管理员相关表
     */
    private function createMySQLAdminTables(): void
    {
        // 管理员表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
                password VARCHAR(255) NOT NULL COMMENT '密码',
                email VARCHAR(100) COMMENT '邮箱',
                role ENUM('super_admin', 'admin', 'operator') DEFAULT 'admin' COMMENT '角色',
                status TINYINT(1) DEFAULT 1 COMMENT '状态: 1=正常, 0=禁用',
                last_login TIMESTAMP NULL COMMENT '最后登录时间',
                last_ip VARCHAR(45) COMMENT '最后登录IP',
                login_attempts INT DEFAULT 0 COMMENT '登录尝试次数',
                locked_until TIMESTAMP NULL COMMENT '锁定到期时间',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_username (username),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // 管理员日志表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS admin_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL COMMENT '管理员ID',
                action VARCHAR(100) NOT NULL COMMENT '操作类型',
                details TEXT COMMENT '操作详情',
                ip_address VARCHAR(45) COMMENT 'IP地址',
                user_agent TEXT COMMENT '用户代理',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                INDEX idx_admin_id (admin_id),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // 检查是否存在默认管理员，如果不存在则创建
        $adminCount = $this->fetchValue("SELECT COUNT(*) FROM admins");
        if ($adminCount == 0) {
            // 创建默认超级管理员
            $defaultPassword = 'admin123'; // 默认密码，首次登录后应该修改
            $this->pdo->prepare("
                INSERT INTO admins (username, password, email, role, status)
                VALUES (?, ?, ?, ?, ?)
            ")->execute([
                'admin',
                password_hash($defaultPassword, PASSWORD_DEFAULT),
                '<EMAIL>',
                'super_admin',
                1
            ]);
        }
    }

    /**
     * 创建MySQL设备相关表
     */
    private function createMySQLDeviceTables(): void
    {
        // 授权设备表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS user_devices (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL COMMENT '授权ID',
                device_id VARCHAR(255) NOT NULL COMMENT '设备唯一标识',
                device_name VARCHAR(255) COMMENT '设备名称',
                device_info TEXT COMMENT '设备信息(JSON)',
                last_login DATETIME COMMENT '最后登录时间',
                status TINYINT DEFAULT 1 COMMENT '状态: 1=正常, 0=禁用',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_device (user_id, device_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    /**
     * 创建MySQL令牌相关表
     */
    private function createMySQLTokenTables(): void
    {
        // 访问令牌表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS access_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL COMMENT '用户ID',
                token VARCHAR(255) NOT NULL COMMENT '访问令牌',
                device_id VARCHAR(255) COMMENT '设备ID',
                expires_at DATETIME NOT NULL COMMENT '过期时间',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                UNIQUE KEY unique_token (token),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    /**
     * 创建MySQL日志相关表
     */
    private function createMySQLLogTables(): void
    {
        // 授权日志表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS login_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT COMMENT '授权ID',
                username VARCHAR(255) COMMENT '授权码(部分)',
                login_type VARCHAR(50) DEFAULT 'success' COMMENT '类型: success, failed',
                ip_address VARCHAR(45) COMMENT 'IP地址',
                device_id VARCHAR(255) COMMENT '设备ID',
                user_agent TEXT COMMENT '用户代理',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // 心跳记录表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS heartbeat_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL COMMENT '用户ID',
                device_id VARCHAR(255) COMMENT '设备ID',
                ip_address VARCHAR(45) COMMENT 'IP地址',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    /**
     * 创建SQLite设备相关表
     */
    private function createSQLiteDeviceTables(): void
    {
        // 授权设备表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS user_devices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,       -- 授权ID
                device_id TEXT NOT NULL,        -- 设备唯一标识
                device_name TEXT,               -- 设备名称
                device_info TEXT,               -- 设备信息(JSON)
                last_login DATETIME,            -- 最后登录时间
                status INTEGER DEFAULT 1,      -- 状态: 1=正常, 0=禁用
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE(user_id, device_id)
            )
        ");
    }

    /**
     * 创建SQLite令牌相关表
     */
    private function createSQLiteTokenTables(): void
    {
        // 访问令牌表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS access_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token TEXT UNIQUE NOT NULL,
                device_id TEXT,
                expires_at DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
    }

    /**
     * 创建SQLite日志相关表
     */
    private function createSQLiteLogTables(): void
    {
        // 授权日志表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS login_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,                -- 授权ID
                username TEXT,                  -- 授权码(部分)
                login_type TEXT DEFAULT 'success', -- 类型: success, failed
                ip_address TEXT,                -- IP地址
                device_id TEXT,                 -- 设备ID
                user_agent TEXT,                -- 用户代理
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            )
        ");

        // 心跳记录表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS heartbeat_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                device_id TEXT,
                ip_address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
    }
    
    /**
     * 创建数据库索引（兼容MySQL 5.7.38）
     */
    private function createIndexes(): void
    {
        $indexes = [
            "idx_users_username" => "CREATE INDEX idx_users_username ON users(username)",
            "idx_users_status" => "CREATE INDEX idx_users_status ON users(status)",
            "idx_devices_user_id" => "CREATE INDEX idx_devices_user_id ON user_devices(user_id)",
            "idx_devices_device_id" => "CREATE INDEX idx_devices_device_id ON user_devices(device_id)",
            "idx_tokens_token" => "CREATE INDEX idx_tokens_token ON access_tokens(token)",
            "idx_tokens_user_id" => "CREATE INDEX idx_tokens_user_id ON access_tokens(user_id)",
            "idx_tokens_expires" => "CREATE INDEX idx_tokens_expires ON access_tokens(expires_at)",
            "idx_logs_user_id" => "CREATE INDEX idx_logs_user_id ON login_logs(user_id)",
            "idx_logs_created" => "CREATE INDEX idx_logs_created ON login_logs(created_at)",
            "idx_heartbeat_user_id" => "CREATE INDEX idx_heartbeat_user_id ON heartbeat_logs(user_id)",
            "idx_heartbeat_created" => "CREATE INDEX idx_heartbeat_created ON heartbeat_logs(created_at)"
        ];

        foreach ($indexes as $indexName => $sql) {
            try {
                // 直接尝试创建索引，忽略已存在错误
                $this->pdo->exec($sql);
            } catch (PDOException $e) {
                // 索引可能已存在，忽略错误
                if (strpos($e->getMessage(), 'Duplicate key name') === false &&
                    strpos($e->getMessage(), 'already exists') === false) {
                    // 如果不是重复索引错误，则记录日志
                    error_log("创建索引失败: {$indexName} - " . $e->getMessage());
                }
            }
        }
    }
    
    /**
     * 创建默认授权和测试数据
     */
    private function createDefaultAdmin(): void
    {
        // 创建管理员授权
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute(['ADMIN-LICENSE-001']);

        if ($stmt->fetchColumn() == 0) {
            $this->pdo->prepare("
                INSERT INTO users (username, password, email, user_type, max_devices, status, expire_time, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ")->execute([
                'ADMIN-LICENSE-001',
                password_hash('admin123', PASSWORD_DEFAULT),
                '<EMAIL>',
                'admin',
                999,
                1,
                date('Y-m-d H:i:s', strtotime('+10 years')),
                '管理员授权码'
            ]);
        }

        // 创建测试授权
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute(['TEST-LICENSE-001']);

        if ($stmt->fetchColumn() == 0) {
            $this->pdo->prepare("
                INSERT INTO users (username, password, email, user_type, max_devices, status, expire_time, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ")->execute([
                'TEST-LICENSE-001',
                password_hash('test123', PASSWORD_DEFAULT),
                '<EMAIL>',
                'basic',
                1,
                1,
                date('Y-m-d H:i:s', strtotime('+1 year')),
                '测试授权码'
            ]);
        }
    }
    
    /**
     * 执行查询
     */
    public function query(string $sql, array $params = []): PDOStatement
    {
        $startTime = microtime(true);

        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            $executionTime = microtime(true) - $startTime;

            // 记录慢查询（超过1秒）
            if ($executionTime > 1.0) {
                $this->logger->performance("慢查询检测", $executionTime, [
                    'sql' => $sql,
                    'params' => $params
                ]);
            }

            // 记录调试信息
            $this->logger->debug("数据库查询", [
                'sql' => $sql,
                'params' => $params,
                'execution_time' => $executionTime
            ]);

            return $stmt;
        } catch (PDOException $e) {
            $this->logger->error("数据库查询失败", [
                'sql' => $sql,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取单行数据
     */
    public function fetchOne(string $sql, array $params = []): ?array
    {
        $stmt = $this->query($sql, $params);
        $result = $stmt->fetch();
        return $result ?: null;
    }
    
    /**
     * 获取多行数据
     */
    public function fetchAll(string $sql, array $params = []): array
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 获取单个值
     */
    public function fetchValue(string $sql, array $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn();
    }
    
    /**
     * 插入数据并返回ID
     */
    public function insert(string $table, array $data): int
    {
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');

        $sql = sprintf(
            "INSERT INTO %s (%s) VALUES (%s)",
            $table,
            implode(', ', $columns),
            implode(', ', $placeholders)
        );

        $this->query($sql, array_values($data));
        $insertId = (int)$this->pdo->lastInsertId();

        // 记录数据库操作
        $this->logger->database("INSERT", $table, [
            'data' => $data,
            'insert_id' => $insertId
        ]);

        return $insertId;
    }
    
    /**
     * 更新数据
     */
    public function update(string $table, array $data, string $where, array $whereParams = []): int
    {
        $setParts = [];
        $values = [];

        foreach ($data as $column => $value) {
            $setParts[] = "{$column} = ?";
            $values[] = $value;
        }

        $sql = sprintf(
            "UPDATE %s SET %s WHERE %s",
            $table,
            implode(', ', $setParts),
            $where
        );

        $stmt = $this->query($sql, array_merge($values, $whereParams));
        $affectedRows = $stmt->rowCount();

        // 记录数据库操作
        $this->logger->database("UPDATE", $table, [
            'data' => $data,
            'where' => $where,
            'where_params' => $whereParams,
            'affected_rows' => $affectedRows
        ]);

        return $affectedRows;
    }
    
    /**
     * 删除数据
     */
    public function delete(string $table, string $where, array $params = []): int
    {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        $affectedRows = $stmt->rowCount();

        // 记录数据库操作
        $this->logger->database("DELETE", $table, [
            'where' => $where,
            'params' => $params,
            'affected_rows' => $affectedRows
        ]);

        return $affectedRows;
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction(): bool
    {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit(): bool
    {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback(): bool
    {
        return $this->pdo->rollback();
    }
    
    /**
     * 获取PDO实例
     */
    public function getPdo(): PDO
    {
        return $this->pdo;
    }
    
    /**
     * 清理过期数据
     */
    public function cleanup(): void
    {
        // 清理过期的访问令牌
        $this->delete('access_tokens', 'expires_at < ?', [date('Y-m-d H:i:s')]);

        // 清理30天前的心跳记录
        $this->delete('heartbeat_logs', 'created_at < ?', [date('Y-m-d H:i:s', strtotime('-30 days'))]);

        // 清理90天前的登录日志
        $this->delete('login_logs', 'created_at < ?', [date('Y-m-d H:i:s', strtotime('-90 days'))]);
    }

    /**
     * 获取统计数据（兼容MySQL 5.7.38）
     */
    public function getStats(): array
    {
        try {
            if ($this->dbType === 'mysql') {
                return [
                    'total_users' => (int)$this->fetchValue("SELECT COUNT(*) FROM users") ?: 0,
                    'active_users' => (int)$this->fetchValue("SELECT COUNT(*) FROM users WHERE status = 1") ?: 0,
                    'total_devices' => (int)$this->fetchValue("SELECT COUNT(*) FROM user_devices WHERE status = 1") ?: 0,
                    'online_users' => (int)$this->fetchValue("
                        SELECT COUNT(DISTINCT user_id) FROM access_tokens
                        WHERE expires_at > CURRENT_TIMESTAMP
                    ") ?: 0,
                    'today_logins' => (int)$this->fetchValue("
                        SELECT COUNT(*) FROM login_logs
                        WHERE DATE(created_at) = CURDATE() AND login_type = 'success'
                    ") ?: 0
                ];
            } else {
                return [
                    'total_users' => (int)$this->fetchValue("SELECT COUNT(*) FROM users") ?: 0,
                    'active_users' => (int)$this->fetchValue("SELECT COUNT(*) FROM users WHERE status = 1") ?: 0,
                    'total_devices' => (int)$this->fetchValue("SELECT COUNT(*) FROM user_devices WHERE status = 1") ?: 0,
                    'online_users' => (int)$this->fetchValue("
                        SELECT COUNT(DISTINCT user_id) FROM access_tokens
                        WHERE expires_at > datetime('now')
                    ") ?: 0,
                    'today_logins' => (int)$this->fetchValue("
                        SELECT COUNT(*) FROM login_logs
                        WHERE DATE(created_at) = DATE('now') AND login_type = 'success'
                    ") ?: 0
                ];
            }
        } catch (Exception $e) {
            // 如果查询失败，返回默认值
            return [
                'total_users' => 0,
                'active_users' => 0,
                'total_devices' => 0,
                'online_users' => 0,
                'today_logins' => 0
            ];
        }
    }
}
