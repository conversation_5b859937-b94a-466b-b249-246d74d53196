#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt版本的现代化UI组件库
包含玻璃拟态效果、霓虹按钮等现代化组件
"""

from PyQt5.QtWidgets import (QWidget, QPushButton, QFrame, QVBoxLayout,
                             QHBoxLayout, QLabel, QScrollArea, QGraphicsDropShadowEffect, QSizePolicy)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect, QTimer
from PyQt5.QtGui import QPainter, QBrush, QPen, QLinearGradient, QColor, QFont

from .pyqt_design_system import DS


class GlassWidget(QWidget):
    """玻璃拟态效果组件"""
    
    def __init__(self, parent=None, glass_opacity=0.1, blur_radius=10):
        super().__init__(parent)
        self.glass_opacity = glass_opacity
        self.blur_radius = blur_radius
        
        # 设置透明背景
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAutoFillBackground(False)
        
        # 添加阴影效果
        self._setup_shadow_effect()
    
    def _setup_shadow_effect(self):
        """设置阴影效果"""
        shadow = DS.create_shadow_effect('md')
        self.setGraphicsEffect(shadow)
    
    def paintEvent(self, event):
        """绘制玻璃效果"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建玻璃背景
        rect = self.rect()
        
        # 背景色
        bg_color = DS.COLORS['glass_bg']
        bg_color.setAlphaF(self.glass_opacity)
        
        # 边框色
        border_color = DS.COLORS['glass_border']
        
        # 绘制圆角矩形背景
        painter.setBrush(QBrush(bg_color))
        painter.setPen(QPen(border_color, 1))
        painter.drawRoundedRect(rect, DS.BORDER_RADIUS['md'], DS.BORDER_RADIUS['md'])


class NeonButton(QPushButton):
    """霓虹效果按钮"""
    
    def __init__(self, text="", neon_color='cyan', style='solid', parent=None):
        super().__init__(text, parent)
        self.neon_color_name = neon_color
        self.button_style = style
        self.is_hovered = False

        # 设置基本样式
        self._setup_style()

        # 设置动画
        self._setup_button_animations()
    
    def _setup_style(self):
        """设置按钮样式"""
        neon_color = DS.get_color(f'neon_{self.neon_color_name}', DS.COLORS['neon_cyan'])

        # 设置基本属性
        self.setMinimumHeight(45)  # 增加最小高度
        self.setMinimumWidth(120)  # 设置最小宽度
        self.setCursor(Qt.PointingHandCursor)
        self.setFont(DS.get_font('body_lg'))  # 使用更大的字体

        if self.button_style == 'solid':
            # 实心按钮
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {neon_color.name()};
                    color: {DS.COLORS['bg_primary'].name()};
                    border: none;
                    border-radius: {DS.BORDER_RADIUS['md']}px;
                    padding: {DS.SPACING['md']}px {DS.SPACING['lg']}px;
                    font-weight: bold;
                    font-size: 13px;
                    min-height: 40px;
                }}
                QPushButton:hover {{
                    background-color: {neon_color.lighter(110).name()};
                }}
                QPushButton:pressed {{
                    background-color: {neon_color.darker(110).name()};
                }}
            """)
        else:
            # 边框按钮
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {neon_color.name()};
                    border: 2px solid {neon_color.name()};
                    border-radius: {DS.BORDER_RADIUS['md']}px;
                    padding: {DS.SPACING['md']}px {DS.SPACING['lg']}px;
                    font-weight: bold;
                    font-size: 13px;
                    min-height: 40px;
                }}
                QPushButton:hover {{
                    background-color: {neon_color.name()};
                    color: {DS.COLORS['bg_primary'].name()};
                    border-color: {neon_color.lighter(110).name()};
                }}
                QPushButton:pressed {{
                    background-color: {neon_color.darker(110).name()};
                    color: {DS.COLORS['bg_primary'].name()};
                }}
            """)
        
        # 设置字体
        self.setFont(DS.get_font('body_md'))
    
    def _setup_button_animations(self):
        """设置按钮动画效果"""
        # 悬停缩放动画
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 点击动画
        self.press_animation = QPropertyAnimation(self, b"geometry")
        self.press_animation.setDuration(80)
        self.press_animation.setEasingCurve(QEasingCurve.OutQuad)

    def _play_hover_scale(self, scale_up=True):
        """播放悬停缩放动画"""
        current_rect = self.geometry()
        if scale_up:
            # 悬停时轻微放大
            new_rect = QRect(current_rect.x() - 2, current_rect.y() - 1,
                           current_rect.width() + 4, current_rect.height() + 2)
        else:
            # 离开时恢复
            new_rect = QRect(current_rect.x() + 2, current_rect.y() + 1,
                           current_rect.width() - 4, current_rect.height() - 2)

        self.scale_animation.setStartValue(current_rect)
        self.scale_animation.setEndValue(new_rect)
        self.scale_animation.start()

    def _play_press_animation(self):
        """播放按压动画"""
        current_rect = self.geometry()
        # 按下时轻微缩小
        press_rect = QRect(current_rect.x() + 1, current_rect.y() + 1,
                         current_rect.width() - 2, current_rect.height() - 2)

        self.press_animation.setStartValue(current_rect)
        self.press_animation.setEndValue(press_rect)
        self.press_animation.finished.connect(lambda: self._restore_button_size(current_rect))
        self.press_animation.start()

    def _restore_button_size(self, original_rect):
        """恢复按钮原始大小"""
        restore_animation = QPropertyAnimation(self, b"geometry")
        restore_animation.setDuration(100)
        restore_animation.setEasingCurve(QEasingCurve.OutCubic)
        restore_animation.setStartValue(self.geometry())
        restore_animation.setEndValue(original_rect)
        restore_animation.start()

    def enterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self._play_hover_scale(True)
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        self._play_hover_scale(False)
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._play_press_animation()
        super().mousePressEvent(event)


class ModernSidebar(QFrame):
    """现代化侧边栏"""
    
    # 信号
    item_clicked = pyqtSignal(str)  # 发送被点击的项目ID
    
    def __init__(self, parent=None, width=250):
        super().__init__(parent)
        self.sidebar_width = width
        self.menu_items = []
        self.current_item = None

        # 设置基本属性
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)
        self.setMinimumWidth(width)
        self.setMaximumWidth(width + 50)
        self.setFrameStyle(QFrame.NoFrame)

        # 设置样式
        self._setup_style()

        # 设置侧边栏动画
        self._setup_sidebar_animations()

        # 创建布局
        self._setup_layout()

        # 启动入场动画
        QTimer.singleShot(100, self._play_entrance_animation)

    def _setup_sidebar_animations(self):
        """设置侧边栏动画"""
        # 入场动画
        self.entrance_animation = QPropertyAnimation(self, b"geometry")
        self.entrance_animation.setDuration(500)
        self.entrance_animation.setEasingCurve(QEasingCurve.OutCubic)

    def _play_entrance_animation(self):
        """播放入场动画"""
        if self.parent():
            current_rect = self.geometry()
            # 从左侧滑入
            start_rect = QRect(-self.sidebar_width, current_rect.y(),
                             current_rect.width(), current_rect.height())

            self.entrance_animation.setStartValue(start_rect)
            self.entrance_animation.setEndValue(current_rect)
            self.entrance_animation.start()
    
    def _setup_style(self):
        """设置侧边栏样式"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                border: none;
                border-radius: {DS.BORDER_RADIUS['lg']}px;
            }}
        """)
    
    def _setup_layout(self):
        """设置布局"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(DS.SPACING['sm'], DS.SPACING['md'], 
                                     DS.SPACING['sm'], DS.SPACING['md'])
        self.layout.setSpacing(DS.SPACING['xs'])
        
        # 菜单区域
        self.menu_container = QWidget()
        self.menu_layout = QVBoxLayout(self.menu_container)
        self.menu_layout.setContentsMargins(0, 0, 0, 0)
        self.menu_layout.setSpacing(DS.SPACING['xs'])
        
        self.layout.addWidget(self.menu_container)
        self.layout.addStretch()  # 底部弹性空间
    

    
    def add_menu_item(self, item_id: str, icon: str, text: str, active: bool = False):
        """添加菜单项"""
        item_widget = SidebarMenuItem(item_id, icon, text, active)
        item_widget.clicked.connect(self._on_item_clicked)

        # 初始隐藏，准备动画
        item_widget.setVisible(False)

        self.menu_items.append(item_widget)
        self.menu_layout.addWidget(item_widget)

        if active:
            self.current_item = item_widget

        # 延迟显示动画
        delay = len(self.menu_items) * 50  # 每个项目延迟50ms
        QTimer.singleShot(delay + 200, lambda: self._show_menu_item(item_widget))
    
    def _on_item_clicked(self, item_id: str):
        """菜单项点击处理"""
        # 更新选中状态
        for item in self.menu_items:
            item.set_active(item.item_id == item_id)
            if item.item_id == item_id:
                self.current_item = item
        
        # 发送信号
        self.item_clicked.emit(item_id)
    
    def _show_menu_item(self, item_widget):
        """显示菜单项动画"""
        item_widget.setVisible(True)

        # 淡入动画
        fade_animation = QPropertyAnimation(item_widget, b"geometry")
        fade_animation.setDuration(300)
        fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 从右侧滑入
        current_rect = item_widget.geometry()
        start_rect = QRect(current_rect.x() + 50, current_rect.y(),
                         current_rect.width(), current_rect.height())

        fade_animation.setStartValue(start_rect)
        fade_animation.setEndValue(current_rect)
        fade_animation.start()

    def set_active_item(self, item_id: str):
        """设置活动项"""
        for item in self.menu_items:
            item.set_active(item.item_id == item_id)
            if item.item_id == item_id:
                self.current_item = item


class SidebarMenuItem(QWidget):
    """侧边栏菜单项"""
    
    clicked = pyqtSignal(str)  # 发送项目ID
    
    def __init__(self, item_id: str, icon: str, text: str, active: bool = False):
        super().__init__()
        self.item_id = item_id
        self.icon = icon
        self.text = text
        self.is_active = active
        self.is_hovered = False

        # 设置基本属性
        self.setFixedHeight(48)
        self.setCursor(Qt.PointingHandCursor)
        self.setMinimumWidth(180)

        # 动画相关
        self._setup_animations()

        # 创建布局
        self._setup_layout()

        # 更新样式
        self._update_style()

    def _setup_animations(self):
        """设置优雅的动画效果"""
        # 光感效果动画 - 背景色渐变
        self.bg_animation = QPropertyAnimation(self, b"styleSheet")
        self.bg_animation.setDuration(250)
        self.bg_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 文字颜色动画
        self.text_animation = QPropertyAnimation(self, b"styleSheet")
        self.text_animation.setDuration(200)
        self.text_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def _setup_layout(self):
        """设置布局"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)

        # 图标
        self.icon_label = QLabel(self.icon)
        self.icon_label.setFont(QFont("Segoe UI Emoji", 16))
        self.icon_label.setAlignment(Qt.AlignCenter)
        self.icon_label.setFixedSize(24, 24)
        self.icon_label.setStyleSheet("background: transparent;")

        # 主文本
        self.text_label = QLabel(self.text)
        self.text_label.setFont(DS.get_font('body_md'))
        self.text_label.setStyleSheet("background: transparent;")

        layout.addWidget(self.icon_label)
        layout.addWidget(self.text_label, 1)
        layout.addStretch()
    
    def _update_style(self):
        """更新样式 - 优雅的光感效果"""
        if self.is_active:
            # 活动状态 - 柔和的光感
            bg_color = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {DS.COLORS['neon_cyan'].darker(400).name()}, stop:1 {DS.COLORS['neon_cyan'].darker(350).name()})"
            text_color = DS.COLORS['text_primary'].name()
            icon_color = DS.COLORS['neon_cyan'].name()
            border = f"1px solid {DS.COLORS['neon_cyan'].name()}"
        elif self.is_hovered:
            # 悬停状态 - 柔和的光感渐变
            bg_color = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {DS.COLORS['bg_secondary'].name()}, stop:0.3 {DS.COLORS['bg_tertiary'].lighter(115).name()}, stop:0.7 {DS.COLORS['bg_tertiary'].lighter(110).name()}, stop:1 {DS.COLORS['bg_secondary'].name()})"
            text_color = DS.COLORS['text_primary'].name()
            icon_color = DS.COLORS['neon_cyan'].lighter(110).name()
            border = f"1px solid {DS.COLORS['neon_cyan'].darker(300).name()}"
        else:
            # 默认状态
            bg_color = 'transparent'
            text_color = DS.COLORS['text_secondary'].name()
            icon_color = DS.COLORS['text_secondary'].name()
            border = "1px solid transparent"

        # 设置整体样式 - 优雅的光感效果和平滑过渡
        self.setStyleSheet(f"""
            SidebarMenuItem {{
                background: {bg_color};
                border: {border};
                border-radius: 8px;
                margin: 2px 4px;
                padding: 2px;
            }}
        """)

        # 设置文字样式 - PyQt兼容的光感效果
        self.text_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                background: transparent;
                font-weight: {'bold' if self.is_active else 'normal'};
                font-size: 14px;
            }}
        """)

        # 设置图标样式 - PyQt兼容的光感效果
        self.icon_label.setStyleSheet(f"""
            QLabel {{
                color: {icon_color};
                background: transparent;
                font-size: 16px;
            }}
        """)


    
    def set_active(self, active: bool):
        """设置活动状态 - 平滑过渡"""
        self.is_active = active
        self._update_style()

    def _play_smooth_transition(self):
        """播放平滑的样式过渡"""
        # 使用CSS过渡效果，而不是几何动画
        # 这样更优雅，不会产生抽搐感
        pass

    def enterEvent(self, event):
        """鼠标进入事件 - 优雅的过渡"""
        self.is_hovered = True
        self._update_style()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件 - 优雅的过渡"""
        self.is_hovered = False
        self._update_style()
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """鼠标点击事件 - 简洁的反馈"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.item_id)
        super().mousePressEvent(event)
