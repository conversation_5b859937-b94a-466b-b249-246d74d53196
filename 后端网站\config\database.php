<?php
/**
 * 数据库配置文件
 * 支持MySQL和SQLite数据库
 */

return [
    // 数据库类型: 'mysql' 或 'sqlite'
    'type' => 'mysql',

    // MySQL配置
    'mysql' => [
        'host' => 'localhost',        // 数据库主机地址
        'port' => 3306,              // 数据库端口
        'database' => 'wxhlyn', // 数据库名称
        'username' => 'wxhlyn',        // 数据库用户名
        'password' => 'dBhG8ndGT4d8sxDy',            // 数据库密码（请根据实际情况修改）
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    ],

    // SQLite配置（备用）
    'sqlite' => [
        'path' => __DIR__ . '/../data/auth_system.db'
    ]
];

// 返回配置数组（用于require包含）
return [
    // 数据库类型: 'mysql' 或 'sqlite'
    'type' => 'mysql',

    // MySQL配置
    'mysql' => [
        'host' => 'localhost',        // 数据库主机地址
        'port' => 3306,              // 数据库端口
        'database' => 'wxhlyn', // 数据库名称
        'username' => 'wxhlyn',        // 数据库用户名
        'password' => 'dBhG8ndGT4d8sxDy',            // 数据库密码（请根据实际情况修改）
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    ],

    // SQLite配置（备用）
    'sqlite' => [
        'path' => __DIR__ . '/../data/auth_system.db'
    ]
];
