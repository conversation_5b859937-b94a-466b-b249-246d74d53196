"""
高性能工具模块 - 使用最快的第三方库
"""

import sys
import os
import time
from typing import Any, Dict, Optional

# 添加lib目录到路径
lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# 高性能JSON库
try:
    import orjson
    JSON_LIB = 'orjson'
    # print("✅ 使用orjson - 最快的JSON库")  # 减少输出
except ImportError:
    try:
        import ujson
        JSON_LIB = 'ujson'
        # print("✅ 使用ujson - 高性能JSON库")
    except ImportError:
        import json
        JSON_LIB = 'json'
        # print("⚠️ 使用标准json库")

# 高性能压缩库
try:
    import lz4.frame
    COMPRESSION_LIB = 'lz4'
    # print("✅ 使用LZ4 - 最快的压缩库")
except ImportError:
    try:
        import zstandard
        COMPRESSION_LIB = 'zstd'
        # print("✅ 使用Zstandard - 高性能压缩库")
    except ImportError:
        import gzip
        COMPRESSION_LIB = 'gzip'
        # print("⚠️ 使用标准gzip压缩")


class HighPerformanceJSON:
    """高性能JSON处理"""
    
    @staticmethod
    def dumps(obj: Any) -> str:
        """高性能JSON序列化"""
        if JSON_LIB == 'orjson':
            return orjson.dumps(obj).decode('utf-8')
        elif JSON_LIB == 'ujson':
            return ujson.dumps(obj, ensure_ascii=False)
        else:
            return json.dumps(obj, ensure_ascii=False, separators=(',', ':'))
    
    @staticmethod
    def loads(s: str) -> Any:
        """高性能JSON反序列化"""
        if JSON_LIB == 'orjson':
            return orjson.loads(s)
        elif JSON_LIB == 'ujson':
            return ujson.loads(s)
        else:
            return json.loads(s)
    
    @staticmethod
    def dump(obj: Any, fp) -> None:
        """高性能JSON写入文件"""
        if JSON_LIB == 'orjson':
            fp.write(orjson.dumps(obj, option=orjson.OPT_INDENT_2).decode('utf-8'))
        elif JSON_LIB == 'ujson':
            ujson.dump(obj, fp, ensure_ascii=False, indent=2)
        else:
            json.dump(obj, fp, ensure_ascii=False, indent=2)
    
    @staticmethod
    def load(fp) -> Any:
        """高性能JSON从文件读取"""
        content = fp.read()
        if isinstance(content, bytes):
            content = content.decode('utf-8')
        return HighPerformanceJSON.loads(content)


class HighPerformanceCompression:
    """高性能压缩处理"""
    
    @staticmethod
    def compress(data: bytes) -> bytes:
        """高性能压缩"""
        if COMPRESSION_LIB == 'lz4':
            return lz4.frame.compress(data)
        elif COMPRESSION_LIB == 'zstd':
            cctx = zstandard.ZstdCompressor(level=3)
            return cctx.compress(data)
        else:
            return gzip.compress(data)
    
    @staticmethod
    def decompress(data: bytes) -> bytes:
        """高性能解压缩"""
        if COMPRESSION_LIB == 'lz4':
            return lz4.frame.decompress(data)
        elif COMPRESSION_LIB == 'zstd':
            dctx = zstandard.ZstdDecompressor()
            return dctx.decompress(data)
        else:
            return gzip.decompress(data)
    
    @staticmethod
    def compress_string(text: str) -> bytes:
        """压缩字符串"""
        return HighPerformanceCompression.compress(text.encode('utf-8'))
    
    @staticmethod
    def decompress_string(data: bytes) -> str:
        """解压缩字符串"""
        return HighPerformanceCompression.decompress(data).decode('utf-8')


class HighPerformanceCache:
    """高性能缓存系统"""
    
    def __init__(self, max_size: int = 10000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.timestamps = {}
        self.access_times = {}
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        if key not in self.cache:
            return None
            
        # 检查TTL
        if time.time() - self.timestamps[key] > self.ttl:
            self._remove(key)
            return None
            
        # 更新访问时间
        self.access_times[key] = time.time()
        return self.cache[key]
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存"""
        current_time = time.time()
        
        # 如果缓存满了，删除最久未访问的项
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict_lru()
        
        self.cache[key] = value
        self.timestamps[key] = current_time
        self.access_times[key] = current_time
    
    def _remove(self, key: str) -> None:
        """删除缓存项"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
        self.access_times.pop(key, None)
    
    def _evict_lru(self) -> None:
        """删除最久未访问的项"""
        if not self.access_times:
            return
            
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self._remove(lru_key)
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.timestamps.clear()
        self.access_times.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def cleanup_expired(self) -> int:
        """清理过期项"""
        current_time = time.time()
        expired_keys = []
        
        for key, timestamp in self.timestamps.items():
            if current_time - timestamp > self.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove(key)
            
        return len(expired_keys)


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self, name: str = "操作"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.perf_counter()
        duration = (self.end_time - self.start_time) * 1000  # 转换为毫秒
        print(f"⏱️ {self.name}耗时: {duration:.2f}ms")
    
    def elapsed_ms(self) -> float:
        """获取已经过的时间（毫秒）"""
        if self.start_time is None:
            return 0.0
        current_time = time.perf_counter()
        return (current_time - self.start_time) * 1000


# 全局高性能缓存实例
_global_cache = HighPerformanceCache(max_size=5000, ttl=1800)  # 30分钟TTL

def get_global_cache() -> HighPerformanceCache:
    """获取全局缓存"""
    return _global_cache

# 便捷函数
def fast_json_dumps(obj: Any) -> str:
    """快速JSON序列化"""
    return HighPerformanceJSON.dumps(obj)

def fast_json_loads(s: str) -> Any:
    """快速JSON反序列化"""
    return HighPerformanceJSON.loads(s)

def fast_compress(data: bytes) -> bytes:
    """快速压缩"""
    return HighPerformanceCompression.compress(data)

def fast_decompress(data: bytes) -> bytes:
    """快速解压缩"""
    return HighPerformanceCompression.decompress(data)

def cache_get(key: str) -> Optional[Any]:
    """从全局缓存获取"""
    return _global_cache.get(key)

def cache_set(key: str, value: Any) -> None:
    """设置全局缓存"""
    _global_cache.set(key, value)

def performance_test():
    """性能测试"""
    print("🚀 开始性能测试...")
    
    # JSON性能测试
    test_data = {"test": "data", "numbers": list(range(1000)), "nested": {"key": "value"}}
    
    with PerformanceTimer("JSON序列化"):
        json_str = fast_json_dumps(test_data)
    
    with PerformanceTimer("JSON反序列化"):
        parsed_data = fast_json_loads(json_str)
    
    # 压缩性能测试
    test_bytes = json_str.encode('utf-8')
    
    with PerformanceTimer("数据压缩"):
        compressed = fast_compress(test_bytes)
    
    with PerformanceTimer("数据解压缩"):
        decompressed = fast_decompress(compressed)
    
    compression_ratio = len(compressed) / len(test_bytes) * 100
    print(f"📊 压缩率: {compression_ratio:.1f}% (原始: {len(test_bytes)}字节 -> 压缩: {len(compressed)}字节)")
    
    # 缓存性能测试
    cache = HighPerformanceCache()
    
    with PerformanceTimer("缓存写入1000项"):
        for i in range(1000):
            cache.set(f"key_{i}", f"value_{i}")
    
    with PerformanceTimer("缓存读取1000项"):
        for i in range(1000):
            cache.get(f"key_{i}")
    
    print("✅ 性能测试完成")

if __name__ == "__main__":
    performance_test()
