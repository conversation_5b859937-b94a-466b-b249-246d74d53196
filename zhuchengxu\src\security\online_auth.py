#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线认证系统
基于云端账号的安全防护
"""

import json
import time
import hashlib
import requests
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
import os
import uuid
import base64

try:
    from utils.logger import get_logger
except ImportError:
    import logging
    def get_logger():
        return logging.getLogger(__name__)


class OnlineAuthClient:
    """在线认证客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化在线认证客户端
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger()
        
        # 服务器配置
        self.auth_server = config.get('auth_server', 'https://auth.augmentcode.com')
        self.app_id = config.get('app_id', 'augment_tool_2025')
        self.app_secret = config.get('app_secret', 'your_app_secret_key')
        
        # 本地认证状态
        self.is_authenticated = False
        self.current_user = None
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = 0
        
        # 会话管理
        self.session_id = None
        self.last_heartbeat = 0
        self.heartbeat_interval = 60  # 60秒心跳
        
        # 本地存储
        self.auth_file = os.path.join(os.path.expanduser('~'), '.augment_auth')
        
        # 后台线程
        self.heartbeat_thread = None
        self.running = False
        
        # 回调函数
        self.on_kicked_out: Optional[Callable] = None
        self.on_auth_expired: Optional[Callable] = None
        
        # HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AugmentTool/1.0.0',
            'Content-Type': 'application/json',
            'X-App-ID': self.app_id
        })
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        import platform
        import socket
        
        try:
            # 获取本机IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            # 获取公网IP
            try:
                response = requests.get('https://api.ipify.org?format=json', timeout=5)
                public_ip = response.json().get('ip', 'unknown')
            except:
                public_ip = 'unknown'
            
            device_info = {
                'device_id': self._get_device_id(),
                'hostname': hostname,
                'local_ip': local_ip,
                'public_ip': public_ip,
                'platform': platform.platform(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'python_version': platform.python_version()
            }
            
            return device_info
            
        except Exception as e:
            self.logger.error(f"获取设备信息失败: {e}")
            return {
                'device_id': self._get_device_id(),
                'hostname': 'unknown',
                'local_ip': 'unknown',
                'public_ip': 'unknown',
                'platform': 'unknown'
            }
    
    def _get_device_id(self) -> str:
        """生成设备唯一ID"""
        import platform
        
        # 使用多个硬件特征生成设备ID
        device_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        device_id = hashlib.sha256(device_info.encode()).hexdigest()[:32]
        return device_id
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            登录结果
        """
        try:
            self.logger.info(f"🔐 用户登录: {username}")
            
            # 准备登录数据
            device_info = self.get_device_info()
            
            login_data = {
                'username': username,
                'password': hashlib.sha256(password.encode()).hexdigest(),  # 密码哈希
                'app_id': self.app_id,
                'device_info': device_info,
                'timestamp': int(time.time())
            }
            
            # 发送登录请求
            response = self.session.post(
                f"{self.auth_server}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    # 登录成功
                    self.access_token = result['data']['access_token']
                    self.refresh_token = result['data']['refresh_token']
                    self.session_id = result['data']['session_id']
                    self.token_expires_at = time.time() + result['data']['expires_in']
                    self.current_user = result['data']['user_info']
                    self.is_authenticated = True
                    
                    # 更新请求头
                    self.session.headers['Authorization'] = f"Bearer {self.access_token}"
                    
                    # 保存认证信息到本地
                    self._save_auth_data()
                    
                    # 启动心跳检测
                    self._start_heartbeat()
                    
                    self.logger.info("✅ 登录成功")
                    return {
                        'success': True,
                        'message': '登录成功',
                        'user_info': self.current_user
                    }
                else:
                    # 登录失败
                    error_msg = result.get('message', '登录失败')
                    self.logger.warning(f"❌ 登录失败: {error_msg}")
                    return {
                        'success': False,
                        'message': error_msg
                    }
            else:
                self.logger.error(f"登录请求失败: {response.status_code}")
                return {
                    'success': False,
                    'message': f'服务器错误: {response.status_code}'
                }
                
        except requests.exceptions.Timeout:
            self.logger.error("登录请求超时")
            return {
                'success': False,
                'message': '连接超时，请检查网络'
            }
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到认证服务器")
            return {
                'success': False,
                'message': '无法连接到服务器'
            }
        except Exception as e:
            self.logger.error(f"登录异常: {e}")
            return {
                'success': False,
                'message': f'登录异常: {e}'
            }
    
    def logout(self) -> bool:
        """用户登出"""
        try:
            if not self.is_authenticated:
                return True
            
            self.logger.info("🚪 用户登出")
            
            # 发送登出请求
            logout_data = {
                'session_id': self.session_id,
                'timestamp': int(time.time())
            }
            
            response = self.session.post(
                f"{self.auth_server}/api/auth/logout",
                json=logout_data,
                timeout=5
            )
            
            # 无论服务器响应如何，都清理本地状态
            self._clear_auth_data()
            
            self.logger.info("✅ 登出成功")
            return True
            
        except Exception as e:
            self.logger.error(f"登出异常: {e}")
            # 即使出错也清理本地状态
            self._clear_auth_data()
            return True
    
    def _save_auth_data(self):
        """保存认证数据到本地"""
        try:
            auth_data = {
                'access_token': self.access_token,
                'refresh_token': self.refresh_token,
                'session_id': self.session_id,
                'token_expires_at': self.token_expires_at,
                'current_user': self.current_user,
                'device_id': self._get_device_id(),
                'save_time': time.time()
            }
            
            # 简单加密（实际项目中应该使用更强的加密）
            auth_json = json.dumps(auth_data)
            encoded_data = base64.b64encode(auth_json.encode()).decode()
            
            with open(self.auth_file, 'w') as f:
                f.write(encoded_data)
            
            # 设置文件权限
            os.chmod(self.auth_file, 0o600)
            
            self.logger.debug("认证数据已保存到本地")
            
        except Exception as e:
            self.logger.error(f"保存认证数据失败: {e}")
    
    def _load_auth_data(self) -> bool:
        """从本地加载认证数据"""
        try:
            if not os.path.exists(self.auth_file):
                return False
            
            with open(self.auth_file, 'r') as f:
                encoded_data = f.read()
            
            # 解密
            auth_json = base64.b64decode(encoded_data.encode()).decode()
            auth_data = json.loads(auth_json)
            
            # 检查设备ID是否匹配
            if auth_data.get('device_id') != self._get_device_id():
                self.logger.warning("设备ID不匹配，清理认证数据")
                self._clear_auth_data()
                return False
            
            # 检查是否过期
            if time.time() > auth_data.get('token_expires_at', 0):
                self.logger.info("本地认证已过期")
                return False
            
            # 恢复认证状态
            self.access_token = auth_data['access_token']
            self.refresh_token = auth_data['refresh_token']
            self.session_id = auth_data['session_id']
            self.token_expires_at = auth_data['token_expires_at']
            self.current_user = auth_data['current_user']
            self.is_authenticated = True
            
            # 更新请求头
            self.session.headers['Authorization'] = f"Bearer {self.access_token}"
            
            self.logger.info("✅ 本地认证数据加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"加载认证数据失败: {e}")
            self._clear_auth_data()
            return False
    
    def _clear_auth_data(self):
        """清理认证数据"""
        # 停止心跳
        self._stop_heartbeat()
        
        # 清理状态
        self.is_authenticated = False
        self.current_user = None
        self.access_token = None
        self.refresh_token = None
        self.session_id = None
        self.token_expires_at = 0
        
        # 清理请求头
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']
        
        # 删除本地文件
        try:
            if os.path.exists(self.auth_file):
                os.remove(self.auth_file)
        except Exception as e:
            self.logger.error(f"删除认证文件失败: {e}")
    
    def auto_login(self) -> bool:
        """自动登录（使用本地保存的认证信息）"""
        try:
            self.logger.info("🔄 尝试自动登录...")
            
            # 加载本地认证数据
            if not self._load_auth_data():
                return False
            
            # 验证会话是否仍然有效
            if self._verify_session():
                # 启动心跳检测
                self._start_heartbeat()
                self.logger.info("✅ 自动登录成功")
                return True
            else:
                self.logger.info("❌ 会话已失效")
                self._clear_auth_data()
                return False
                
        except Exception as e:
            self.logger.error(f"自动登录失败: {e}")
            return False

    def _verify_session(self) -> bool:
        """验证会话是否有效"""
        try:
            if not self.session_id or not self.access_token:
                return False

            verify_data = {
                'session_id': self.session_id,
                'device_info': self.get_device_info(),
                'timestamp': int(time.time())
            }

            response = self.session.post(
                f"{self.auth_server}/api/auth/verify",
                json=verify_data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()

                if result.get('success'):
                    # 会话有效，更新用户信息
                    if 'user_info' in result.get('data', {}):
                        self.current_user = result['data']['user_info']
                    return True
                else:
                    # 会话无效
                    error_code = result.get('error_code')
                    if error_code == 'SESSION_KICKED':
                        self.logger.warning("⚠️ 检测到异地登录，当前会话被踢出")
                        if self.on_kicked_out:
                            self.on_kicked_out(result.get('message', '检测到异地登录'))
                    elif error_code == 'SESSION_EXPIRED':
                        self.logger.warning("⚠️ 会话已过期")
                        if self.on_auth_expired:
                            self.on_auth_expired(result.get('message', '会话已过期'))

                    return False
            else:
                self.logger.error(f"会话验证请求失败: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"会话验证异常: {e}")
            return False

    def _start_heartbeat(self):
        """启动心跳检测"""
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            return

        self.running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        self.logger.info("💓 心跳检测已启动")

    def _stop_heartbeat(self):
        """停止心跳检测"""
        self.running = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
        self.logger.info("💓 心跳检测已停止")

    def _heartbeat_loop(self):
        """心跳检测循环"""
        while self.running and self.is_authenticated:
            try:
                current_time = time.time()

                # 检查心跳间隔
                if current_time - self.last_heartbeat >= self.heartbeat_interval:
                    self.last_heartbeat = current_time

                    # 发送心跳
                    if not self._send_heartbeat():
                        self.logger.error("💔 心跳检测失败，会话可能已失效")
                        self.is_authenticated = False

                        if self.on_auth_expired:
                            self.on_auth_expired("心跳检测失败")

                        break

                # 休眠
                time.sleep(10)  # 每10秒检查一次

            except Exception as e:
                self.logger.error(f"心跳检测异常: {e}")
                time.sleep(30)  # 出错时等待更长时间

    def _send_heartbeat(self) -> bool:
        """发送心跳包"""
        try:
            heartbeat_data = {
                'session_id': self.session_id,
                'device_info': self.get_device_info(),
                'timestamp': int(time.time())
            }

            response = self.session.post(
                f"{self.auth_server}/api/auth/heartbeat",
                json=heartbeat_data,
                timeout=5
            )

            if response.status_code == 200:
                result = response.json()

                if result.get('success'):
                    self.logger.debug("💓 心跳正常")
                    return True
                else:
                    # 心跳失败
                    error_code = result.get('error_code')
                    if error_code == 'SESSION_KICKED':
                        self.logger.warning("⚠️ 检测到异地登录，当前会话被踢出")
                        if self.on_kicked_out:
                            self.on_kicked_out(result.get('message', '检测到异地登录'))

                    return False
            else:
                self.logger.warning(f"心跳请求失败: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"发送心跳异常: {e}")
            return False

    def refresh_token_if_needed(self) -> bool:
        """如果需要则刷新令牌"""
        try:
            # 检查令牌是否即将过期（提前5分钟刷新）
            if time.time() + 300 >= self.token_expires_at:
                return self._refresh_access_token()

            return True

        except Exception as e:
            self.logger.error(f"检查令牌过期异常: {e}")
            return False

    def _refresh_access_token(self) -> bool:
        """刷新访问令牌"""
        try:
            if not self.refresh_token:
                return False

            refresh_data = {
                'refresh_token': self.refresh_token,
                'app_id': self.app_id,
                'timestamp': int(time.time())
            }

            response = self.session.post(
                f"{self.auth_server}/api/auth/refresh",
                json=refresh_data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()

                if result.get('success'):
                    # 更新令牌
                    self.access_token = result['data']['access_token']
                    self.token_expires_at = time.time() + result['data']['expires_in']

                    # 更新请求头
                    self.session.headers['Authorization'] = f"Bearer {self.access_token}"

                    # 保存到本地
                    self._save_auth_data()

                    self.logger.info("🔄 访问令牌已刷新")
                    return True
                else:
                    self.logger.error("刷新令牌失败")
                    return False
            else:
                self.logger.error(f"刷新令牌请求失败: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"刷新令牌异常: {e}")
            return False

    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if self.is_authenticated and self.current_user:
            return self.current_user.copy()
        return None

    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self.is_authenticated and self.access_token is not None

    def get_auth_status(self) -> Dict[str, Any]:
        """获取认证状态"""
        return {
            'authenticated': self.is_authenticated,
            'username': self.current_user.get('username', '') if self.current_user else '',
            'user_id': self.current_user.get('user_id', '') if self.current_user else '',
            'session_id': self.session_id,
            'token_expires_at': self.token_expires_at,
            'expires_in_seconds': max(0, int(self.token_expires_at - time.time())),
            'device_id': self._get_device_id()
        }

    def __del__(self):
        """析构函数"""
        self._stop_heartbeat()
