#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt VSCode清理页面 - 基于PyQt5最佳实践
VSCode Cleanup Page - 响应式设计与标准布局管理
"""

import os
import sys
import shutil
import platform
import threading
import time
import json
import uuid
import sqlite3
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QSplitter,
    QLabel, QTreeWidget, QTreeWidgetItem, QTextEdit, QPushButton,
    QFrame, QSizePolicy, QProgressBar, QCheckBox, QGroupBox,
    QScrollArea, QMessageBox, QFileDialog, QApplication, QComboBox,
    QButtonGroup, QRadioButton, QMenu, QDialog, QHeaderView
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPalette, QColor

from src.utils.logger import get_logger

# 尝试导入psutil用于进程检查
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class AugmentCleaner:
    """Augment 扩展存储清理器 - 基于 augment-free-main 的增强版本"""

    def __init__(self, logger=None):
        self.logger = logger or get_logger(__name__)
        self.vscode_data_dir = self._get_vscode_data_dir()
        self.cursor_data_dir = self._get_cursor_data_dir()
        self.backup_dir = Path("augment_backup") / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.extension_id = "Augment.vscode-augment"

        # 存储路径定义
        self.storage_paths = {
            'vscode': {
                'global_storage': self.vscode_data_dir / "User" / "globalStorage" / self.extension_id,
                'workspace_storage_root': self.vscode_data_dir / "User" / "workspaceStorage",
                'user_settings': self.vscode_data_dir / "User" / "settings.json",
                'user_keybindings': self.vscode_data_dir / "User" / "keybindings.json",
                'extensions_dir': self.vscode_data_dir / "extensions",
                'logs_dir': self.vscode_data_dir / "logs",
                'cache_dir': self.vscode_data_dir / "CachedExtensions"
            },
            'cursor': {
                'global_storage': self.cursor_data_dir / "User" / "globalStorage" / self.extension_id,
                'workspace_storage_root': self.cursor_data_dir / "User" / "workspaceStorage",
                'user_settings': self.cursor_data_dir / "User" / "settings.json",
                'user_keybindings': self.cursor_data_dir / "User" / "keybindings.json",
                'extensions_dir': self.cursor_data_dir / "extensions",
                'logs_dir': self.cursor_data_dir / "logs",
                'cache_dir': self.cursor_data_dir / "CachedExtensions"
            }
        }

        # Augment 相关的存储键值
        self.augment_keys = [
            "sidecar.Augment.vscode-augment",
            "hasEverUsedAgent",
            "userTier",
            "apiToken",
            "sessionId",
            "trialStartDate",
            "lastUsedDate",
            "featureFlags",
            "authToken",
            "refreshToken"
        ]

        # Augment 相关的配置键 - 基于 augment-free-main 扩展
        self.augment_config_keys = [
            "augment.advanced.apiToken",
            "augment.enableEmptyFileHint",
            "augment.conflictingCodingAssistantCheck",
            "augment.advanced.completionURL",
            "augment.advanced.integrations",
            "augment.enableTelemetry",
            "augment.deviceId",
            "augment.machineId",
            "augment.sessionId",
            "augment.userId",
            "augment.installationId",
            "augment.firstInstallDate",
            "augment.lastUsedDate",
            "augment.trialStartDate",
            "augment.trialEndDate",
            "augment.licenseKey",
            "augment.activationStatus"
        ]

        # 设备标识相关的键
        self.device_id_keys = [
            "telemetry.machineId",
            "telemetry.sessionId",
            "telemetry.instanceId",
            "telemetry.sqmUserId",
            "machineId",
            "sessionId",
            "instanceId",
            "deviceId"
        ]

    def _get_vscode_data_dir(self) -> Path:
        """获取 VSCode 数据目录"""
        if sys.platform == "win32":
            return Path(os.environ.get("APPDATA", "")) / "Code"
        elif sys.platform == "darwin":
            return Path.home() / "Library" / "Application Support" / "Code"
        else:  # Linux
            return Path.home() / ".config" / "Code"

    def _get_cursor_data_dir(self) -> Path:
        """获取 Cursor 数据目录"""
        if sys.platform == "win32":
            return Path(os.environ.get("APPDATA", "")) / "Cursor"
        elif sys.platform == "darwin":
            return Path.home() / "Library" / "Application Support" / "Cursor"
        else:  # Linux
            return Path.home() / ".config" / "Cursor"

    def create_backup(self, editor_type: str = 'vscode') -> bool:
        """创建备份"""
        try:
            self.logger.info(f"创建 {editor_type} 备份到: {self.backup_dir}")
            self.backup_dir.mkdir(parents=True, exist_ok=True)

            paths = self.storage_paths.get(editor_type, {})

            # 备份全局存储
            global_storage = paths.get('global_storage')
            if global_storage and global_storage.exists():
                backup_global = self.backup_dir / f"{editor_type}_globalStorage"
                shutil.copytree(global_storage, backup_global)
                self.logger.info(f"已备份全局存储: {backup_global}")

            # 备份工作区存储中的 Augment 数据
            workspace_root = paths.get('workspace_storage_root')
            if workspace_root and workspace_root.exists():
                backup_workspace = self.backup_dir / f"{editor_type}_workspaceStorage"
                backup_workspace.mkdir(exist_ok=True)

                for workspace_dir in workspace_root.iterdir():
                    if workspace_dir.is_dir():
                        augment_workspace = workspace_dir / self.extension_id
                        if augment_workspace.exists():
                            backup_path = backup_workspace / workspace_dir.name / self.extension_id
                            backup_path.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copytree(augment_workspace, backup_path)
                            self.logger.info(f"已备份工作区存储: {backup_path}")

            # 备份用户配置
            settings_file = paths.get('user_settings')
            if settings_file and settings_file.exists():
                backup_settings = self.backup_dir / f"{editor_type}_settings.json"
                shutil.copy2(settings_file, backup_settings)
                self.logger.info(f"已备份用户配置: {backup_settings}")

            return True

        except Exception as e:
            self.logger.error(f"备份失败: {e}")
            return False

    def clean_global_storage(self, editor_type: str = 'vscode') -> bool:
        """清理全局存储"""
        try:
            paths = self.storage_paths.get(editor_type, {})
            global_storage = paths.get('global_storage')

            if global_storage and global_storage.exists():
                self.logger.info(f"清理 {editor_type} 全局存储: {global_storage}")
                shutil.rmtree(global_storage)
                self.logger.info(f"{editor_type} 全局存储清理完成")
                return True
            else:
                self.logger.info(f"{editor_type} 全局存储目录不存在，跳过清理")
                return True

        except Exception as e:
            self.logger.error(f"清理 {editor_type} 全局存储失败: {e}")
            return False

    def clean_workspace_storage(self, editor_type: str = 'vscode') -> bool:
        """清理工作区存储"""
        try:
            paths = self.storage_paths.get(editor_type, {})
            workspace_root = paths.get('workspace_storage_root')

            if not workspace_root or not workspace_root.exists():
                self.logger.info(f"{editor_type} 工作区存储目录不存在，跳过清理")
                return True

            cleaned_count = 0
            for workspace_dir in workspace_root.iterdir():
                if workspace_dir.is_dir():
                    augment_workspace = workspace_dir / self.extension_id
                    if augment_workspace.exists():
                        self.logger.info(f"清理 {editor_type} 工作区存储: {augment_workspace}")
                        shutil.rmtree(augment_workspace)
                        cleaned_count += 1

            self.logger.info(f"{editor_type} 工作区存储清理完成，共清理 {cleaned_count} 个工作区")
            return True

        except Exception as e:
            self.logger.error(f"清理 {editor_type} 工作区存储失败: {e}")
            return False

    def clean_user_settings(self, editor_type: str = 'vscode') -> bool:
        """清理用户配置中的 Augment 相关设置"""
        try:
            paths = self.storage_paths.get(editor_type, {})
            settings_file = paths.get('user_settings')

            if not settings_file or not settings_file.exists():
                self.logger.info(f"{editor_type} 用户配置文件不存在，跳过清理")
                return True

            # 读取配置文件
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # 移除 Augment 相关配置
            removed_keys = []
            for key in list(settings.keys()):
                if any(augment_key in key for augment_key in self.augment_config_keys):
                    del settings[key]
                    removed_keys.append(key)

            if removed_keys:
                # 写回配置文件
                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2, ensure_ascii=False)

                self.logger.info(f"{editor_type} 用户配置清理完成，移除了 {len(removed_keys)} 个配置项: {removed_keys}")
            else:
                self.logger.info(f"{editor_type} 用户配置中未找到 Augment 相关配置")

            return True

        except Exception as e:
            self.logger.error(f"清理 {editor_type} 用户配置失败: {e}")
            return False

    def clean_extension_logs(self, editor_type: str = 'vscode') -> bool:
        """清理扩展日志"""
        try:
            paths = self.storage_paths.get(editor_type, {})
            logs_dir = paths.get('logs_dir')

            if not logs_dir or not logs_dir.exists():
                self.logger.info(f"{editor_type} 日志目录不存在，跳过清理")
                return True

            cleaned_count = 0
            for log_file in logs_dir.rglob("*augment*"):
                if log_file.is_file():
                    self.logger.info(f"清理 {editor_type} 日志文件: {log_file}")
                    log_file.unlink()
                    cleaned_count += 1

            self.logger.info(f"{editor_type} 扩展日志清理完成，共清理 {cleaned_count} 个日志文件")
            return True

        except Exception as e:
            self.logger.error(f"清理 {editor_type} 扩展日志失败: {e}")
            return False

    def clean_extension_cache(self, editor_type: str = 'vscode') -> bool:
        """清理扩展缓存"""
        try:
            paths = self.storage_paths.get(editor_type, {})
            cache_dir = paths.get('cache_dir')

            if not cache_dir or not cache_dir.exists():
                self.logger.info(f"{editor_type} 缓存目录不存在，跳过清理")
                return True

            cleaned_count = 0
            for cache_file in cache_dir.rglob("*augment*"):
                if cache_file.is_file():
                    self.logger.info(f"清理 {editor_type} 缓存文件: {cache_file}")
                    cache_file.unlink()
                    cleaned_count += 1
                elif cache_file.is_dir():
                    self.logger.info(f"清理 {editor_type} 缓存目录: {cache_file}")
                    shutil.rmtree(cache_file)
                    cleaned_count += 1

            self.logger.info(f"{editor_type} 扩展缓存清理完成，共清理 {cleaned_count} 个缓存项")
            return True

        except Exception as e:
            self.logger.error(f"清理 {editor_type} 扩展缓存失败: {e}")
            return False

    def clean_sqlite_storage(self, editor_type: str = 'vscode') -> bool:
        """清理 SQLite 存储数据 - 基于 augment-free-main"""
        try:
            paths = self.storage_paths.get(editor_type, {})
            data_dir = self.vscode_data_dir if editor_type == 'vscode' else self.cursor_data_dir

            # SQLite 数据库文件路径
            sqlite_files = [
                data_dir / "User" / "globalStorage" / "state.vscdb",
                data_dir / "User" / "globalStorage" / "state.vscdb.backup",
                data_dir / "User" / "workspaceStorage" / "state.vscdb"
            ]

            cleaned_count = 0
            for sqlite_file in sqlite_files:
                if sqlite_file.exists():
                    try:
                        # 尝试清理 SQLite 数据库中的 Augment 相关记录
                        import sqlite3
                        conn = sqlite3.connect(str(sqlite_file))
                        cursor = conn.cursor()

                        # 查找包含 Augment 相关键的记录
                        for key in self.augment_keys + self.device_id_keys:
                            cursor.execute("DELETE FROM ItemTable WHERE key LIKE ?", (f"%{key}%",))

                        conn.commit()
                        conn.close()
                        cleaned_count += 1
                        self.logger.info(f"清理 SQLite 文件: {sqlite_file}")

                    except Exception as e:
                        self.logger.warning(f"清理 SQLite 文件失败 {sqlite_file}: {e}")

            self.logger.info(f"{editor_type} SQLite 存储清理完成，共清理 {cleaned_count} 个数据库文件")
            return True

        except Exception as e:
            self.logger.error(f"清理 {editor_type} SQLite 存储失败: {e}")
            return False

    def clean_registry_entries(self) -> bool:
        """清理 Windows 注册表项 - 基于 augment-free-main"""
        if sys.platform != "win32":
            self.logger.info("非 Windows 系统，跳过注册表清理")
            return True

        try:
            import winreg

            # 注册表路径
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\VSCode"),
                (winreg.HKEY_CURRENT_USER, r"Software\Classes\Applications\Code.exe"),
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\ApplicationAssociationToasts"),
                (winreg.HKEY_CURRENT_USER, r"Software\RegisteredApplications")
            ]

            cleaned_count = 0
            for hkey, subkey_path in registry_paths:
                try:
                    with winreg.OpenKey(hkey, subkey_path, 0, winreg.KEY_ALL_ACCESS) as key:
                        # 枚举所有值
                        i = 0
                        while True:
                            try:
                                value_name, value_data, value_type = winreg.EnumValue(key, i)

                                # 检查是否包含 Augment 相关内容
                                if any(aug_key in str(value_name).lower() or aug_key in str(value_data).lower()
                                      for aug_key in ['augment', 'vscode-augment']):
                                    winreg.DeleteValue(key, value_name)
                                    cleaned_count += 1
                                    self.logger.info(f"删除注册表项: {subkey_path}\\{value_name}")
                                else:
                                    i += 1

                            except OSError:
                                break

                except FileNotFoundError:
                    continue
                except Exception as e:
                    self.logger.warning(f"清理注册表路径失败 {subkey_path}: {e}")

            self.logger.info(f"注册表清理完成，共清理 {cleaned_count} 个注册表项")
            return True

        except Exception as e:
            self.logger.error(f"清理注册表失败: {e}")
            return False

    def reset_device_id(self, editor_type: str = 'vscode') -> bool:
        """重置设备 ID - 基于 augment-free-main"""
        try:
            import uuid
            import secrets

            paths = self.storage_paths.get(editor_type, {})
            settings_file = paths.get('user_settings')

            if not settings_file or not settings_file.exists():
                self.logger.warning(f"{editor_type} 用户配置文件不存在")
                return True

            # 读取配置文件
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # 生成新的设备标识
            new_machine_id = str(uuid.uuid4())
            new_session_id = secrets.token_hex(16)
            new_device_id = secrets.token_hex(32)

            # 更新设备相关配置
            device_updates = {
                "telemetry.machineId": new_machine_id,
                "telemetry.sessionId": new_session_id,
                "augment.deviceId": new_device_id,
                "augment.machineId": new_machine_id,
                "augment.sessionId": new_session_id
            }

            updated_count = 0
            for key, value in device_updates.items():
                if key in settings:
                    settings[key] = value
                    updated_count += 1

            # 写回配置文件
            if updated_count > 0:
                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2, ensure_ascii=False)

                self.logger.info(f"{editor_type} 设备 ID 重置完成，更新了 {updated_count} 个标识")
            else:
                self.logger.info(f"{editor_type} 未找到需要重置的设备标识")

            return True

        except Exception as e:
            self.logger.error(f"重置 {editor_type} 设备 ID 失败: {e}")
            return False

    def clean_telemetry_data(self, editor_type: str = 'vscode') -> bool:
        """清理遥测数据 - 基于 augment-free-main"""
        try:
            paths = self.storage_paths.get(editor_type, {})
            data_dir = self.vscode_data_dir if editor_type == 'vscode' else self.cursor_data_dir

            # 遥测数据目录
            telemetry_dirs = [
                data_dir / "logs",
                data_dir / "User" / "globalStorage" / "vscode.telemetry",
                data_dir / "User" / "globalStorage" / "ms-vscode.vscode-telemetry"
            ]

            cleaned_count = 0
            for telemetry_dir in telemetry_dirs:
                if telemetry_dir.exists():
                    for file_path in telemetry_dir.rglob("*"):
                        if file_path.is_file() and any(keyword in file_path.name.lower()
                                                     for keyword in ['telemetry', 'usage', 'crash', 'error']):
                            try:
                                file_path.unlink()
                                cleaned_count += 1
                                self.logger.info(f"删除遥测文件: {file_path}")
                            except Exception as e:
                                self.logger.warning(f"删除遥测文件失败 {file_path}: {e}")

            self.logger.info(f"{editor_type} 遥测数据清理完成，共清理 {cleaned_count} 个文件")
            return True

        except Exception as e:
            self.logger.error(f"清理 {editor_type} 遥测数据失败: {e}")
            return False

    def scan_augment_data(self, editor_type: str = 'vscode') -> Dict[str, List[str]]:
        """扫描所有 Augment 相关数据"""
        self.logger.info(f"开始扫描 {editor_type} Augment 相关数据...")

        found_data = {
            'global_storage': [],
            'workspace_storage': [],
            'user_settings': [],
            'logs': [],
            'cache': []
        }

        paths = self.storage_paths.get(editor_type, {})

        # 扫描全局存储
        global_storage = paths.get('global_storage')
        if global_storage and global_storage.exists():
            for item in global_storage.rglob("*"):
                found_data['global_storage'].append(str(item))

        # 扫描工作区存储
        workspace_root = paths.get('workspace_storage_root')
        if workspace_root and workspace_root.exists():
            for workspace_dir in workspace_root.iterdir():
                if workspace_dir.is_dir():
                    augment_workspace = workspace_dir / self.extension_id
                    if augment_workspace.exists():
                        for item in augment_workspace.rglob("*"):
                            found_data['workspace_storage'].append(str(item))

        # 扫描用户配置
        settings_file = paths.get('user_settings')
        if settings_file and settings_file.exists():
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                for key in settings.keys():
                    if any(augment_key in key for augment_key in self.augment_config_keys):
                        found_data['user_settings'].append(f"{key}: {settings[key]}")
            except Exception as e:
                self.logger.warning(f"扫描 {editor_type} 用户配置时出错: {e}")

        # 扫描日志
        logs_dir = paths.get('logs_dir')
        if logs_dir and logs_dir.exists():
            for log_file in logs_dir.rglob("*augment*"):
                found_data['logs'].append(str(log_file))

        # 扫描缓存
        cache_dir = paths.get('cache_dir')
        if cache_dir and cache_dir.exists():
            for cache_item in cache_dir.rglob("*augment*"):
                found_data['cache'].append(str(cache_item))

        return found_data

    def full_clean(self, editor_type: str = 'vscode', create_backup: bool = True) -> bool:
        """执行完整清理"""
        self.logger.info(f"开始执行 {editor_type} Augment 扩展完整清理...")

        # 创建备份
        if create_backup:
            if not self.create_backup(editor_type):
                self.logger.error("备份失败，终止清理操作")
                return False

        # 执行各项清理 - 基于 augment-free-main 的完整清理
        success = True

        success &= self.clean_global_storage(editor_type)
        success &= self.clean_workspace_storage(editor_type)
        success &= self.clean_user_settings(editor_type)
        success &= self.clean_extension_logs(editor_type)
        success &= self.clean_extension_cache(editor_type)
        success &= self.clean_sqlite_storage(editor_type)
        success &= self.clean_telemetry_data(editor_type)
        success &= self.reset_device_id(editor_type)

        # Windows 系统清理注册表
        if sys.platform == "win32":
            success &= self.clean_registry_entries()

        if success:
            self.logger.info(f"✅ {editor_type} Augment 扩展完整清理成功完成！")
        else:
            self.logger.error(f"❌ {editor_type} 清理过程中出现错误，请检查日志")

        return success

    def clean_all_editors(self, create_backup: bool = True) -> Dict[str, bool]:
        """清理所有编辑器的 Augment 数据"""
        results = {}

        for editor_type in ['vscode', 'cursor']:
            self.logger.info(f"开始清理 {editor_type}...")
            results[editor_type] = self.full_clean(editor_type, create_backup)

        return results


class VSCodeDesignSystem:
    """VSCode页面设计系统"""

    # 现代化配色方案
    COLORS = {
        'bg_primary': '#0a0a0f',      # 深空黑
        'bg_secondary': '#1a1a2e',    # 深紫蓝
        'bg_tertiary': '#16213e',     # 深蓝灰
        'glass_bg': '#1d1d31',        # 玻璃效果背景
        'glass_border': '#2a2a3e',    # 玻璃效果边框

        # 霓虹色彩
        'neon_cyan': '#00f5ff',       # 霓虹青
        'neon_green': '#00ff88',      # 霓虹绿
        'neon_red': '#ff0080',        # 霓虹红
        'neon_orange': '#ff8800',     # 霓虹橙
        'neon_pink': '#ec4899',       # 霓虹粉
        'neon_purple': '#a855f7',     # 霓虹紫
        'neon_blue': '#3b82f6',       # 霓虹蓝
        'neon_yellow': '#eab308',     # 霓虹黄

        # 文字颜色
        'text_primary': '#ffffff',    # 主要文字
        'text_secondary': '#b8bcc8',  # 次要文字
        'text_muted': '#6c7293',      # 弱化文字
    }

    # 字体系统
    FONTS = {
        'primary': 'Microsoft YaHei UI',
        'secondary': 'Consolas',
    }

    # 尺寸系统
    SIZES = {
        'title': 18,
        'heading': 16,
        'body': 14,
        'small': 12,
        'tiny': 10,
    }

    # 间距系统
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 16,
        'lg': 24,
        'xl': 32,
    }


class PyQtVSCodePage(QWidget):
    """
    PyQt VSCode清理页面 - 基于PyQt5最佳实践

    设计原理：
    1. 标准布局管理 - 使用QVBoxLayout、QHBoxLayout、QGroupBox
    2. 响应式设计 - 自适应窗口大小变化
    3. 现代化样式 - CSS样式表美化界面
    4. 高效交互 - 信号槽机制处理用户操作
    """

    # 定义信号
    cleanup_started = pyqtSignal()     # 清理开始信号
    cleanup_finished = pyqtSignal()   # 清理完成信号
    progress_updated = pyqtSignal(int) # 进度更新信号

    def __init__(self, parent=None):
        """
        初始化VSCode清理页面

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 核心组件初始化
        # 设置日志文件路径
        import os
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, 'cleaning_tools.log')

        self.logger = get_logger("CleaningTools", log_file)
        self.ds = VSCodeDesignSystem()  # 设计系统实例
        self.augment_cleaner = AugmentCleaner(self.logger)  # Augment 清理器实例

        # 状态管理
        self.is_cleaning = False
        self.cleanup_items = []
        self.cleanup_options = {}  # 清理选项变量
        self.selected_software = "all"  # 当前选择的软件：all, vscode, cursor

        # 高级选项
        self.backup_enabled = True
        self.force_cleanup = False
        self.verbose_logging = True

        # UI组件引用
        self.status_label = None
        self.progress_bar = None

        self.result_text = None
        self.start_button = None
        self.stop_button = None
        self.software_selector = None

        # 初始化界面
        self._init_ui()
        self._apply_styles()

        # 初始化清理项目
        self._initialize_cleanup_items()

        # 加载保存的软件选择
        self._load_software_selection()

        # 延迟扫描，避免阻塞界面加载
        QTimer.singleShot(500, self._scan_vscode_data)  # 增加延迟时间，确保界面完全加载

        # 设置响应式布局定时器
        self.layout_timer = QTimer()
        self.layout_timer.setSingleShot(True)
        self.layout_timer.timeout.connect(self._update_responsive_layout)

    # ==================== UI初始化方法 ====================

    def _init_ui(self):
        """
        初始化用户界面 - 基于PyQt5最佳实践
        """
        # 设置页面策略：完全填充父容器
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(self.ds.SPACING['sm'], self.ds.SPACING['sm'],
                                     self.ds.SPACING['sm'], self.ds.SPACING['sm'])
        main_layout.setSpacing(self.ds.SPACING['sm'])

        # 构建界面组件
        self._create_main_content(main_layout)
        self._create_status_bar(main_layout)

    def _create_page_header(self, parent_layout):
        """
        创建页面标题区域 - 包含软件选择器
        """
        # 标题容器
        self.header_frame = QFrame()
        self.header_frame.setFrameStyle(QFrame.StyledPanel)
        self.header_frame.setMinimumHeight(100)  # 设置最小高度，确保内容完整显示
        self.header_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)

        header_layout = QVBoxLayout(self.header_frame)
        header_layout.setContentsMargins(self.ds.SPACING['lg'], self.ds.SPACING['md'],
                                       self.ds.SPACING['lg'], self.ds.SPACING['md'])
        header_layout.setSpacing(self.ds.SPACING['sm'])

        # 标题行
        title_row = QHBoxLayout()

        # 主标题
        self.title_label = QLabel("🛠️ VSCode/Cursor清理工具")
        self.title_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['title'], QFont.Bold))
        self.title_label.setObjectName("titleLabel")
        self.title_label.setMinimumHeight(30)  # 确保标题有足够高度
        self.title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        title_row.addWidget(self.title_label)
        title_row.addStretch()

        # 软件选择器
        self._create_software_selector(title_row)

        header_layout.addLayout(title_row)

        # 描述信息
        self.desc_label = QLabel("清理VSCode/Cursor缓存、扩展和临时文件，释放磁盘空间")
        self.desc_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        self.desc_label.setObjectName("descLabel")
        self.desc_label.setMinimumHeight(25)  # 确保描述有足够高度
        self.desc_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.desc_label.setWordWrap(True)  # 允许文字换行

        header_layout.addWidget(self.desc_label)

        parent_layout.addWidget(self.header_frame)

    def _create_software_selector(self, parent_layout):
        """
        创建软件选择器
        """
        # 软件选择标签
        selector_label = QLabel("选择软件:")
        selector_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        selector_label.setObjectName("selectorLabel")

        # 软件选择下拉框
        self.software_selector = QComboBox()
        self.software_selector.addItems([
            "🔄 全部软件",
            "📘 VSCode",
            "🔷 Cursor",
            "🎯 Visual Studio",
            "🚀 JetBrains IDEs",
            "🎨 Sublime Text",
            "⚡ Atom",
            "📝 Notepad++",
            "⌨️ Vim/Neovim",
            "🔧 Emacs",
            "🤖 Android Studio",
            "🌙 Eclipse",
            "⚙️ Code::Blocks",
            "💻 Dev-C++",
            "🛠️ Qt Creator",
            "🗂️ 系统缓存"
        ])
        self.software_selector.setCurrentIndex(0)  # 默认选择全部
        self.software_selector.setObjectName("softwareSelector")
        self.software_selector.setMinimumWidth(180)
        self.software_selector.setMinimumHeight(30)  # 确保下拉框有足够高度
        self.software_selector.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        # 绑定选择变化事件
        self.software_selector.currentIndexChanged.connect(self._on_software_changed)

        parent_layout.addWidget(selector_label)
        parent_layout.addWidget(self.software_selector)

    def _on_software_changed(self, index):
        """
        软件选择变化处理 - 增强版本，支持更多编辑器并自动扫描
        """
        software_map = {
            0: "all",               # 全部软件
            1: "vscode",            # VSCode
            2: "cursor",            # Cursor
            3: "visual_studio",     # Visual Studio
            4: "jetbrains",         # JetBrains IDEs
            5: "sublime",           # Sublime Text
            6: "atom",              # Atom
            7: "notepad_plus",      # Notepad++
            8: "vim",               # Vim/Neovim
            9: "emacs",             # Emacs
            10: "android_studio",   # Android Studio
            11: "eclipse",          # Eclipse
            12: "codeblocks",       # Code::Blocks
            13: "devcpp",           # Dev-C++
            14: "qtcreator",        # Qt Creator
            15: "system_cache"      # 系统缓存
        }

        self.selected_software = software_map.get(index, "all")

        # 保存软件选择
        self._save_software_selection()

        # 更新状态显示
        software_names = {
            "all": "全部软件",
            "vscode": "VSCode",
            "cursor": "Cursor",
            "visual_studio": "Visual Studio",
            "jetbrains": "JetBrains IDEs",
            "sublime": "Sublime Text",
            "atom": "Atom",
            "notepad_plus": "Notepad++",
            "vim": "Vim/Neovim",
            "emacs": "Emacs",
            "android_studio": "Android Studio",
            "eclipse": "Eclipse",
            "codeblocks": "Code::Blocks",
            "devcpp": "Dev-C++",
            "qtcreator": "Qt Creator",
            "system_cache": "系统缓存"
        }

        selected_name = software_names.get(self.selected_software, "未知软件")
        self.status_label.setText(f"📊 已选择: {selected_name} - 正在自动扫描...")

        self.logger.info(f"软件选择已更改为: {selected_name} (索引: {index})")

        # 自动扫描选择的软件
        self._auto_scan_selected_software()

        # 直接更新清理树，不需要额外过滤
        self._update_cleanup_tree()

    def _filter_cleanup_items(self):
        """
        根据选择的软件过滤清理项目
        """
        try:
            # 过滤项目
            filtered_items = []

            self.logger.info(f"开始过滤清理项目，当前选择: {self.selected_software}")

            if not self.cleanup_items:
                self.logger.warning("清理项目列表为空，无法过滤。")
                return []

            if self.selected_software == 'all':
                self.logger.info(f"总共有 {len(self.cleanup_items)} 个清理项目")
                return self.cleanup_items

            filtered_groups = []
            for group in self.cleanup_items:
                # 确保group是字典并且有children
                if not isinstance(group, dict) or 'children' not in group:
                    continue

                # 筛选子项目
                matching_children = []
                for child in group.get('children', []):
                    if not isinstance(child, dict):
                        continue

                    child_software = child.get('software', '')
                    # 处理 software 字段可能是字符串或列表的情况
                    if isinstance(child_software, str):
                        child_software_list = [child_software]
                    elif isinstance(child_software, list):
                        child_software_list = child_software
                    else:
                        child_software_list = []

                    # 检查是否匹配当前选择的软件
                    if self.selected_software in child_software_list or child_software == 'global':
                        matching_children.append(child)

                # 如果这个分组有匹配的子项目，就把它和匹配的子项目一起加进去
                if matching_children:
                    new_group = group.copy()
                    new_group['children'] = matching_children
                    filtered_groups.append(new_group)

            self.logger.info(f"过滤后剩下 {len(filtered_groups)} 个分组。")
            return filtered_groups

        except Exception as e:
            self.logger.error(f"过滤清理项目失败: {e}")
            self.status_label.setText(f"❌ 过滤失败: {e}")
            return []  # 确保在异常情况下也返回空列表而不是None

    def _create_main_content(self, parent_layout):
        """
        创建主要内容区域 - 重构为固定比例布局，更稳定可靠
        """
        # 创建主容器
        main_container = QWidget()
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(self.ds.SPACING['sm'])

        # 创建清理列表面板（占主要空间）
        cleanup_panel = self._create_cleanup_list_panel()
        cleanup_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        main_layout.addWidget(cleanup_panel, 1)  # 拉伸因子为1，占主要空间

        # 创建结果显示面板（固定高度）
        result_panel = self._create_result_panel()
        result_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        main_layout.addWidget(result_panel, 0)  # 拉伸因子为0，固定大小

        parent_layout.addWidget(main_container)



    def _adjust_layout_for_window_size(self, width, height):
        """
        根据窗口大小调整布局 - 增强版本，支持更精细的响应式调整

        Args:
            width (int): 窗口宽度
            height (int): 窗口高度
        """
        try:
            # 定义断点
            breakpoints = {
                'xs': 800,   # 超小屏幕
                'sm': 1024,  # 小屏幕
                'md': 1280,  # 中等屏幕
                'lg': 1600,  # 大屏幕
                'xl': 1920   # 超大屏幕
            }

            # 确定当前断点
            current_breakpoint = 'xs'
            if width >= breakpoints['xl']:
                current_breakpoint = 'xl'
            elif width >= breakpoints['lg']:
                current_breakpoint = 'lg'
            elif width >= breakpoints['md']:
                current_breakpoint = 'md'
            elif width >= breakpoints['sm']:
                current_breakpoint = 'sm'

            # 结果面板已经是固定高度，无需动态调整

            # 调整树形控件列宽
            if hasattr(self, 'cleanup_tree') and width > 0:
                # 根据断点调整列宽
                if current_breakpoint == 'xs':
                    # 超小屏幕：紧凑布局
                    name_width = max(200, int(width * 0.45))
                    size_width = max(60, int(width * 0.12))
                elif current_breakpoint == 'sm':
                    # 小屏幕：适中布局
                    name_width = max(250, int(width * 0.42))
                    size_width = max(70, int(width * 0.13))
                elif current_breakpoint == 'md':
                    # 中等屏幕：标准布局
                    name_width = max(300, int(width * 0.4))
                    size_width = max(80, int(width * 0.15))
                elif current_breakpoint == 'lg':
                    # 大屏幕：宽松布局
                    name_width = max(350, int(width * 0.38))
                    size_width = max(90, int(width * 0.15))
                else:  # xl
                    # 超大屏幕：最宽松布局
                    name_width = max(400, int(width * 0.35))
                    size_width = max(100, int(width * 0.15))

                self.cleanup_tree.setColumnWidth(0, name_width)
                self.cleanup_tree.setColumnWidth(1, size_width)
                # 路径列自动填充剩余空间

            # 结果面板已经是固定高度，无需动态调整

            # 根据断点调整按钮布局
            self._adjust_button_layout_for_breakpoint(current_breakpoint, width)

            # 根据窗口宽度调整按钮排列方式
            self._adjust_button_arrangement_for_width(width)

            # 根据断点调整头部面板
            self._adjust_header_layout_for_breakpoint(current_breakpoint, width)

            # 根据断点调整状态栏
            self._adjust_status_layout_for_breakpoint(current_breakpoint, width)

        except Exception as e:
            self.logger.warning(f"调整布局失败: {e}")

    def _adjust_button_layout_for_breakpoint(self, breakpoint, width):
        """根据断点调整按钮布局"""
        try:
            # 根据断点调整按钮最小宽度，确保按钮不被压缩
            if breakpoint == 'xs':
                # 超小屏幕：紧凑按钮，但保证最小可读性
                button_widths = {
                    'scan': 60, 'quick_clean': 80, 'reset_machine_id': 90, 'augment_clean': 100,
                    'select_all': 60, 'select_none': 70, 'select_recommended': 60,
                    'stats': 60, 'settings': 60
                }
            elif breakpoint == 'sm':
                # 小屏幕：适中按钮
                button_widths = {
                    'scan': 70, 'quick_clean': 90, 'reset_machine_id': 100, 'augment_clean': 110,
                    'select_all': 65, 'select_none': 75, 'select_recommended': 65,
                    'stats': 65, 'settings': 65
                }
            else:
                # 中等及以上屏幕：标准按钮
                button_widths = {
                    'scan': 80, 'quick_clean': 100, 'reset_machine_id': 110, 'augment_clean': 120,
                    'select_all': 70, 'select_none': 80, 'select_recommended': 70,
                    'stats': 70, 'settings': 70
                }

            # 应用按钮最小宽度，确保按钮不被压缩
            if hasattr(self, 'scan_btn'):
                self.scan_btn.setMinimumWidth(button_widths['scan'])
            if hasattr(self, 'quick_clean_btn'):
                self.quick_clean_btn.setMinimumWidth(button_widths['quick_clean'])
            if hasattr(self, 'reset_machine_id_btn'):
                self.reset_machine_id_btn.setMinimumWidth(button_widths['reset_machine_id'])

            if hasattr(self, 'select_all_btn'):
                self.select_all_btn.setMinimumWidth(button_widths['select_all'])
            if hasattr(self, 'select_none_btn'):
                self.select_none_btn.setMinimumWidth(button_widths['select_none'])
            if hasattr(self, 'select_recommended_btn'):
                self.select_recommended_btn.setMinimumWidth(button_widths['select_recommended'])
            if hasattr(self, 'stats_btn'):
                self.stats_btn.setMinimumWidth(button_widths['stats'])
            if hasattr(self, 'settings_btn'):
                self.settings_btn.setMinimumWidth(button_widths['settings'])

        except Exception as e:
            self.logger.warning(f"调整按钮布局失败: {e}")

    def _adjust_button_arrangement_for_width(self, width):
        """根据窗口宽度调整按钮排列方式"""
        try:
            # 当窗口宽度小于800px时，隐藏分隔符以节省空间
            if hasattr(self, 'separator1'):
                if width < 800:
                    self.separator1.setVisible(False)
                else:
                    self.separator1.setVisible(True)

            if hasattr(self, 'separator2'):
                if width < 800:
                    self.separator2.setVisible(False)
                else:
                    self.separator2.setVisible(True)

            # 当窗口宽度非常小时，调整按钮文字
            if width < 600:
                # 超小窗口：使用简化文字
                if hasattr(self, 'reset_machine_id_btn'):
                    self.reset_machine_id_btn.setText("🔑 重置")

                if hasattr(self, 'select_recommended_btn'):
                    self.select_recommended_btn.setText("⭐")
            else:
                # 恢复完整文字
                if hasattr(self, 'reset_machine_id_btn'):
                    self.reset_machine_id_btn.setText("🔑 重置机器码")

                if hasattr(self, 'select_recommended_btn'):
                    self.select_recommended_btn.setText("⭐ 推荐")

        except Exception as e:
            self.logger.warning(f"调整按钮排列失败: {e}")

    def _adjust_header_layout_for_breakpoint(self, breakpoint, width):
        """根据断点调整头部布局"""
        try:
            # 根据断点调整头部高度和字体大小
            if hasattr(self, 'header_frame'):
                if breakpoint == 'xs':
                    # 超小屏幕：紧凑头部
                    self.header_frame.setFixedHeight(60)
                elif breakpoint == 'sm':
                    # 小屏幕：适中头部
                    self.header_frame.setFixedHeight(70)
                else:
                    # 中等及以上屏幕：标准头部
                    self.header_frame.setFixedHeight(80)

            # 调整标题字体大小
            if hasattr(self, 'title_label'):
                if breakpoint == 'xs':
                    font_size = max(12, self.ds.SIZES['title'] - 2)
                elif breakpoint == 'sm':
                    font_size = max(14, self.ds.SIZES['title'] - 1)
                else:
                    font_size = self.ds.SIZES['title']

                font = QFont(self.ds.FONTS['primary'], font_size, QFont.Bold)
                self.title_label.setFont(font)

            # 调整描述文字
            if hasattr(self, 'desc_label'):
                if breakpoint == 'xs':
                    # 超小屏幕：隐藏描述或使用简短版本
                    self.desc_label.setText("清理VSCode缓存文件")
                    font_size = max(10, self.ds.SIZES['body'] - 2)
                elif breakpoint == 'sm':
                    # 小屏幕：简化描述
                    self.desc_label.setText("清理VSCode/Cursor缓存和临时文件")
                    font_size = max(11, self.ds.SIZES['body'] - 1)
                else:
                    # 标准描述
                    self.desc_label.setText("清理VSCode/Cursor缓存、扩展和临时文件，释放磁盘空间")
                    font_size = self.ds.SIZES['body']

                font = QFont(self.ds.FONTS['primary'], font_size)
                self.desc_label.setFont(font)

        except Exception as e:
            self.logger.warning(f"调整头部布局失败: {e}")

    def _adjust_status_layout_for_breakpoint(self, breakpoint, width):
        """根据断点调整状态栏布局"""
        try:
            # 调整状态标签文字
            if hasattr(self, 'status_label'):
                if breakpoint == 'xs':
                    # 超小屏幕：简化状态文字
                    current_text = self.status_label.text()
                    if "准备就绪" in current_text:
                        self.status_label.setText("📊 就绪")
                    elif "扫描中" in current_text:
                        self.status_label.setText("🔍 扫描中...")
                    elif "清理中" in current_text:
                        self.status_label.setText("🧹 清理中...")
                elif breakpoint == 'sm':
                    # 小屏幕：适中文字
                    current_text = self.status_label.text()
                    if "就绪" in current_text and "准备" not in current_text:
                        self.status_label.setText("📊 准备就绪")
                # 中等及以上屏幕保持完整文字

            # 调整进度条宽度
            if hasattr(self, 'progress_bar'):
                if breakpoint == 'xs':
                    self.progress_bar.setMaximumWidth(150)
                elif breakpoint == 'sm':
                    self.progress_bar.setMaximumWidth(200)
                else:
                    self.progress_bar.setMaximumWidth(16777215)  # 无限制

        except Exception as e:
            self.logger.warning(f"调整状态栏布局失败: {e}")

    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        # 延迟调整布局，避免频繁调整
        if hasattr(self, 'layout_timer'):
            self.layout_timer.start(100)  # 100ms后调整布局

    def _update_responsive_layout(self):
        """更新响应式布局"""
        try:
            size = self.size()
            self._adjust_layout_for_window_size(size.width(), size.height())
        except Exception as e:
            self.logger.warning(f"更新响应式布局失败: {e}")

    def _create_cleanup_list_panel(self):
        """创建清理列表面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(self.ds.SPACING['md'], self.ds.SPACING['md'],
                                      self.ds.SPACING['md'], self.ds.SPACING['md'])
        panel_layout.setSpacing(self.ds.SPACING['sm'])

        # 软件筛选器 - 第一行
        title_layout = QHBoxLayout()

        # 软件筛选下拉框
        title_layout.addStretch()
        filter_label = QLabel("筛选:")
        title_layout.addWidget(filter_label)

        self.software_filter = QComboBox()
        self.software_filter.addItems([
            "全部软件", "VSCode", "Cursor", "Sublime Text",
            "Atom", "WebStorm", "开发工具缓存"
        ])
        self.software_filter.currentTextChanged.connect(self._on_software_filter_changed)
        self.software_filter.setMinimumWidth(120)
        title_layout.addWidget(self.software_filter)

        panel_layout.addLayout(title_layout)

        # 操作按钮 - 第二行，分为三组，使用更好的布局策略
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)  # 增加按钮组之间的间距

        # 第一组：扫描和清理
        scan_group = QHBoxLayout()
        scan_group.setSpacing(8)  # 组内按钮间距

        self.scan_btn = QPushButton("🔄 扫描")
        self.scan_btn.setObjectName("scanButton")
        self.scan_btn.clicked.connect(self._scan_vscode_data)
        self.scan_btn.setToolTip("扫描系统中的VSCode相关文件")
        self.scan_btn.setMinimumWidth(80)
        self.scan_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        scan_group.addWidget(self.scan_btn)

        # 添加快速清理按钮
        self.quick_clean_btn = QPushButton("🧹 快速清理")
        self.quick_clean_btn.setObjectName("quickCleanButton")
        self.quick_clean_btn.clicked.connect(self._start_cleanup)
        self.quick_clean_btn.setToolTip("快速清理选中的项目")
        self.quick_clean_btn.setMinimumWidth(100)
        self.quick_clean_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        scan_group.addWidget(self.quick_clean_btn)

        self.reset_machine_id_btn = QPushButton("🔑 重置机器码")
        self.reset_machine_id_btn.setObjectName("resetButton")
        self.reset_machine_id_btn.clicked.connect(self._quick_reset_machine_id)
        self.reset_machine_id_btn.setToolTip("重置软件机器标识符")
        self.reset_machine_id_btn.setMinimumWidth(110)
        self.reset_machine_id_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        scan_group.addWidget(self.reset_machine_id_btn)

        button_layout.addLayout(scan_group)

        # 添加分隔符
        self.separator1 = QFrame()
        self.separator1.setFrameShape(QFrame.VLine)
        self.separator1.setFrameShadow(QFrame.Sunken)
        self.separator1.setMaximumWidth(2)
        button_layout.addWidget(self.separator1)

        # 第二组：选择操作
        select_group = QHBoxLayout()
        select_group.setSpacing(8)  # 组内按钮间距

        self.select_all_btn = QPushButton("☑️ 全选")
        self.select_all_btn.setObjectName("selectAllButton")
        self.select_all_btn.clicked.connect(self._select_all_items)
        self.select_all_btn.setToolTip("选中所有清理项目")
        self.select_all_btn.setMinimumWidth(70)
        self.select_all_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        select_group.addWidget(self.select_all_btn)

        self.select_none_btn = QPushButton("☐ 全不选")
        self.select_none_btn.setObjectName("selectNoneButton")
        self.select_none_btn.clicked.connect(self._select_none_items)
        self.select_none_btn.setToolTip("取消选中所有项目")
        self.select_none_btn.setMinimumWidth(80)
        self.select_none_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        select_group.addWidget(self.select_none_btn)

        self.select_recommended_btn = QPushButton("⭐ 推荐")
        self.select_recommended_btn.setObjectName("selectRecommendedButton")
        self.select_recommended_btn.clicked.connect(self._select_recommended_items)
        self.select_recommended_btn.setToolTip("只选中推荐的清理项目")
        self.select_recommended_btn.setMinimumWidth(70)
        self.select_recommended_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        select_group.addWidget(self.select_recommended_btn)

        button_layout.addLayout(select_group)

        # 添加分隔符
        self.separator2 = QFrame()
        self.separator2.setFrameShape(QFrame.VLine)
        self.separator2.setFrameShadow(QFrame.Sunken)
        self.separator2.setMaximumWidth(2)
        button_layout.addWidget(self.separator2)

        # 第三组：高级功能
        advanced_group = QHBoxLayout()
        advanced_group.setSpacing(8)  # 组内按钮间距

        self.stats_btn = QPushButton("📊 统计")
        self.stats_btn.setObjectName("statsButton")
        self.stats_btn.clicked.connect(self._show_cleanup_stats)
        self.stats_btn.setToolTip("查看历史清理统计信息")
        self.stats_btn.setMinimumWidth(70)
        self.stats_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        advanced_group.addWidget(self.stats_btn)

        self.settings_btn = QPushButton("⚙️ 设置")
        self.settings_btn.setObjectName("settingsButton")
        self.settings_btn.clicked.connect(self._show_settings)
        self.settings_btn.setToolTip("清理工具设置")
        self.settings_btn.setMinimumWidth(70)
        self.settings_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        advanced_group.addWidget(self.settings_btn)

        button_layout.addLayout(advanced_group)

        # 添加弹性空间，将按钮推向左侧
        button_layout.addStretch()

        panel_layout.addLayout(button_layout)

        # 清理项目树形列表
        self.cleanup_tree = QTreeWidget()
        self.cleanup_tree.setHeaderLabels(['项目名称', '大小', '路径'])
        self.cleanup_tree.setObjectName("cleanupTree")

        # 设置树形列表属性
        self.cleanup_tree.setAlternatingRowColors(True)
        self.cleanup_tree.setRootIsDecorated(True)  # 显示展开/折叠图标
        self.cleanup_tree.setSelectionMode(QTreeWidget.ExtendedSelection)
        self.cleanup_tree.setIndentation(20)  # 设置缩进

        # 设置列宽 - 响应式设计
        header = self.cleanup_tree.header()
        header.setStretchLastSection(True)  # 最后一列自动拉伸

        # 设置列宽比例：项目名称50%，大小15%，路径35%
        self.cleanup_tree.setColumnWidth(0, 350)  # 项目名称列
        self.cleanup_tree.setColumnWidth(1, 100)  # 大小列
        # 路径列自动拉伸填充剩余空间

        # 设置最小列宽
        header.setMinimumSectionSize(80)

        # 绑定事件
        self.cleanup_tree.itemChanged.connect(self._on_item_check_changed)

        # 启用右键菜单
        self.cleanup_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.cleanup_tree.customContextMenuRequested.connect(self._show_context_menu)

        panel_layout.addWidget(self.cleanup_tree)

        return panel

    def _create_result_panel(self):
        """创建结果显示面板 - 重构为固定高度的紧凑面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setFixedHeight(180)  # 固定高度，避免占用过多空间

        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(self.ds.SPACING['md'], self.ds.SPACING['sm'],
                                      self.ds.SPACING['md'], self.ds.SPACING['sm'])
        panel_layout.setSpacing(self.ds.SPACING['xs'])

        # 状态栏 - 紧凑设计
        status_layout = QHBoxLayout()
        status_layout.setSpacing(self.ds.SPACING['sm'])

        self.status_label = QLabel("📊 准备就绪，点击'重新扫描'开始")
        self.status_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        self.status_label.setObjectName("statusLabel")
        status_layout.addWidget(self.status_label)

        # 进度条 - 紧凑设计
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setMaximumHeight(20)  # 限制进度条高度
        status_layout.addWidget(self.progress_bar)

        panel_layout.addLayout(status_layout)

        # 结果文本区域 - 固定合理高度
        self.result_text = QTextEdit()
        self.result_text.setObjectName("resultText")
        self.result_text.setReadOnly(True)
        self.result_text.setFixedHeight(120)  # 固定高度，确保不会过大
        self.result_text.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.result_text.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        panel_layout.addWidget(self.result_text)

        # 显示默认信息
        self._show_default_result()

        return panel

    def _show_default_result(self):
        """显示默认结果信息"""
        default_text = """🎯 VSCode 清理工具

📋 功能说明：
• 🔄 重新扫描：扫描VSCode相关文件
• 🔑 重置机器码：重置VSCode机器标识

💡 使用提示：
1. 点击"重新扫描"开始扫描VSCode文件
2. 在上方列表中勾选要清理的项目
3. 使用筛选下拉框过滤特定软件
4. 右键点击项目执行清理操作

⚠️ 注意：清理操作不可撤销，请谨慎操作！"""
        self.result_text.setPlainText(default_text)

    def _on_software_filter_changed(self, software_name):
        """软件筛选改变事件"""
        # 映射显示名称到内部标识
        software_map = {
            "全部软件": "all",
            "VSCode": "vscode",
            "Cursor": "cursor",
            "Sublime Text": "sublime",
            "Atom": "atom",
            "WebStorm": "webstorm",
            "开发工具缓存": "dev_tools"
        }

        self.selected_software = software_map.get(software_name, "all")
        self._update_cleanup_tree()

    def _on_item_check_changed(self, item, column):
        """处理项目勾选状态变化 - 增强版本"""
        if column != 0:  # 只处理第一列的复选框
            return

        is_checked = item.checkState(0) == Qt.Checked

        # 获取项目数据
        item_data = item.data(0, Qt.UserRole)
        if item_data:
            # 更新清理项目的启用状态
            item_data['enabled'] = is_checked

            # 如果是机器码重置项目，显示警告
            if item_data.get('category') == 'machine_id' and is_checked:
                reply = QMessageBox.question(
                    self,
                    "⚠️ 机器码重置确认",
                    f"您即将启用机器码重置功能。\n\n"
                    f"⚠️ 警告：{item_data.get('warning', '')}\n\n"
                    f"这可能会导致软件需要重新激活。\n\n"
                    f"是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.No:
                    # 取消勾选
                    item.setCheckState(0, Qt.Unchecked)
                    item_data['enabled'] = False
                    return

            self.logger.info(f"项目状态更新: {item_data['name']} -> {'启用' if is_checked else '禁用'}")
        else:
            # 处理分组节点的勾选（全选/全不选子项目）
            if item.childCount() > 0:
                for i in range(item.childCount()):
                    child = item.child(i)
                    child.setCheckState(0, Qt.Checked if is_checked else Qt.Unchecked)
                    # 递归调用处理子项目
                    self._on_item_check_changed(child, 0)

    def _update_cleanup_tree(self):
        """更新清理项目树形列表 - 增强版本"""
        # 清空现有项目
        self.cleanup_tree.clear()
        self.cleanup_tree.setColumnCount(4)
        self.cleanup_tree.setHeaderLabels(['清理项目', '大小', '状态', '路径'])

        # 优化列宽
        self.cleanup_tree.header().setSectionResizeMode(0, QHeaderView.Stretch)
        self.cleanup_tree.header().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.cleanup_tree.header().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.cleanup_tree.header().setSectionResizeMode(3, QHeaderView.Interactive)
        self.cleanup_tree.setColumnWidth(3, 300)

        # 过滤需要显示的项目
        filtered_items = self._filter_cleanup_items()

        # 确保filtered_items不是None
        if filtered_items is None:
            self.logger.warning("过滤项目返回None，使用空列表代替")
            filtered_items = []

        # 递归函数，用于添加项目和子项目
        def add_items_recursively(parent_widget, items):
            # 增强鲁棒性：确保items是可迭代的
            if items is None:
                self.logger.warning("传入的items为None，跳过")
                return

            for item_data in items:
                # 跳过无效数据
                if not isinstance(item_data, dict):
                    self.logger.warning(f"跳过无效项目数据: {item_data}")
                    continue

                # 创建树项目
                tree_item = QTreeWidgetItem(parent_widget)

                # 设置复选框
                tree_item.setFlags(tree_item.flags() | Qt.ItemIsUserCheckable)
                tree_item.setCheckState(0, Qt.Unchecked)

                # 设置项目数据
                tree_item.setText(0, item_data.get('name', 'N/A'))
                tree_item.setData(0, Qt.UserRole, item_data)
                tree_item.setToolTip(0, item_data.get('description', item_data.get('desc', item_data.get('name', ''))))

                # 如果是分组，递归添加子项目
                if item_data.get('type') == 'group':
                    tree_item.setFont(0, QFont(self.ds.FONTS['primary'], self.ds.SIZES['body'], QFont.Bold))
                    children = item_data.get('children', [])
                    if children and isinstance(children, list):
                        # 设置分组信息
                        tree_item.setText(1, f"{len(children)} 项")
                        tree_item.setText(2, "分组")
                        tree_item.setText(3, "")
                        add_items_recursively(tree_item, children)
                else:
                    tree_item.setFont(0, QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
                    # 设置清理项目的详细信息
                    size = item_data.get('size', 0)
                    if size > 0:
                        tree_item.setText(1, self._format_size(size))
                    else:
                        tree_item.setText(1, item_data.get('size_estimate', '未知'))

                    # 设置状态
                    if item_data.get('recommended', True):
                        tree_item.setText(2, "推荐")
                        tree_item.setForeground(2, QColor(0, 150, 0))  # 绿色
                    else:
                        tree_item.setText(2, "可选")
                        tree_item.setForeground(2, QColor(200, 100, 0))  # 橙色

                    # 设置路径信息
                    base_path = item_data.get('base_path', '')
                    if base_path:
                        tree_item.setText(3, base_path)
                        tree_item.setToolTip(3, base_path)

                # 默认展开所有顶级分组
                if parent_widget == self.cleanup_tree:
                    tree_item.setExpanded(True)

        # 开始添加项目
        try:
            add_items_recursively(self.cleanup_tree, filtered_items)
            item_count = len(filtered_items) if filtered_items else 0
            self.logger.info(f"✅ 清理项目树已更新，显示 {item_count} 个顶级项目。")
        except Exception as e:
            self.logger.error(f"更新清理项目树失败: {e}")
            self.status_label.setText(f"❌ 更新清理项目失败: {e}")

    def _select_all_items(self):
        """全选所有清理项目"""
        try:
            root = self.cleanup_tree.invisibleRootItem()
            for i in range(root.childCount()):
                group_item = root.child(i)
                group_item.setCheckState(0, Qt.Checked)

                # 选中所有子项目
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    child_item.setCheckState(0, Qt.Checked)

                    # 更新数据
                    item_data = child_item.data(0, Qt.UserRole)
                    if item_data:
                        item_data['enabled'] = True

            self.status_label.setText("✅ 已全选所有清理项目")
            self.logger.info("用户执行全选操作")

        except Exception as e:
            self.logger.error(f"全选操作失败: {e}")
            QMessageBox.warning(self, "操作失败", f"全选操作失败: {e}")

    def _select_none_items(self):
        """取消选中所有清理项目"""
        try:
            root = self.cleanup_tree.invisibleRootItem()
            for i in range(root.childCount()):
                group_item = root.child(i)
                group_item.setCheckState(0, Qt.Unchecked)

                # 取消选中所有子项目
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    child_item.setCheckState(0, Qt.Unchecked)

                    # 更新数据
                    item_data = child_item.data(0, Qt.UserRole)
                    if item_data:
                        item_data['enabled'] = False

            self.status_label.setText("❌ 已取消选中所有项目")
            self.logger.info("用户执行全不选操作")

        except Exception as e:
            self.logger.error(f"全不选操作失败: {e}")
            QMessageBox.warning(self, "操作失败", f"全不选操作失败: {e}")

    def _select_recommended_items(self):
        """只选中推荐的清理项目"""
        try:
            selected_count = 0
            root = self.cleanup_tree.invisibleRootItem()

            for i in range(root.childCount()):
                group_item = root.child(i)
                group_has_selected = False

                # 处理子项目
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    item_data = child_item.data(0, Qt.UserRole)

                    if item_data:
                        is_recommended = item_data.get('recommended', False)
                        child_item.setCheckState(0, Qt.Checked if is_recommended else Qt.Unchecked)
                        item_data['enabled'] = is_recommended

                        if is_recommended:
                            selected_count += 1
                            group_has_selected = True

                # 更新分组状态
                group_item.setCheckState(0, Qt.Checked if group_has_selected else Qt.Unchecked)

            self.status_label.setText(f"⭐ 已选中 {selected_count} 个推荐项目")
            self.logger.info(f"用户选择推荐项目，共 {selected_count} 个")

        except Exception as e:
            self.logger.error(f"选择推荐项目失败: {e}")
            QMessageBox.warning(self, "操作失败", f"选择推荐项目失败: {e}")

    def _show_context_menu(self, position):
        """显示增强的右键菜单"""
        item = self.cleanup_tree.itemAt(position)
        if not item:
            return

        menu = QMenu(self)

        # 获取项目数据
        item_data = item.data(0, Qt.UserRole)

        if item_data:
            # 单个项目的菜单
            software = item_data.get('software', 'vscode')
            category = item_data.get('category', 'unknown')
            risk_level = item_data.get('risk_level', 'low')

            # 选中状态切换
            if item_data.get('enabled', False):
                action = menu.addAction("❌ 取消选中")
                action.triggered.connect(lambda: self._toggle_item_selection(item, False))
            else:
                action = menu.addAction("✅ 选中")
                action.triggered.connect(lambda: self._toggle_item_selection(item, True))

            menu.addSeparator()

            # 清理选项子菜单
            clean_menu = menu.addMenu("🧹 清理选项")

            # 单独清理
            clean_action = clean_menu.addAction("🗑️ 立即清理")
            clean_action.triggered.connect(lambda: self._clean_single_item(item_data))

            # 备份后清理
            if risk_level in ['medium', 'high']:
                backup_clean_action = clean_menu.addAction("💾 备份后清理")
                backup_clean_action.triggered.connect(lambda: self._backup_and_clean(item_data))

            # 预览清理
            preview_action = clean_menu.addAction("👁️ 预览清理内容")
            preview_action.triggered.connect(lambda: self._preview_cleanup(item_data))

            clean_menu.addSeparator()

            # 软件特定清理选项
            if software == 'vscode':
                self._add_vscode_specific_menu(clean_menu, item_data)
            elif software == 'cursor':
                self._add_cursor_specific_menu(clean_menu, item_data)
            elif software in ['sublime', 'notepad++', 'android_studio', 'atom']:
                self._add_generic_software_menu(clean_menu, item_data, software)

            menu.addSeparator()

            # 信息选项
            info_menu = menu.addMenu("ℹ️ 信息")

            # 查看详情
            detail_action = info_menu.addAction("📋 查看详情")
            detail_action.triggered.connect(lambda: self._show_item_details(item_data))

            # 风险评估
            risk_action = info_menu.addAction("⚠️ 风险评估")
            risk_action.triggered.connect(lambda: self._show_risk_assessment(item_data))

            # 大小估算
            size_action = info_menu.addAction("📏 大小估算")
            size_action.triggered.connect(lambda: self._show_size_estimation(item_data))

            # 打开文件夹
            if os.path.exists(item_data.get('base_path', '')):
                menu.addSeparator()
                folder_action = menu.addAction("📁 打开文件夹")
                folder_action.triggered.connect(lambda: self._open_folder(item_data.get('base_path', '')))

        else:
            # 分组节点的菜单
            group_menu = menu.addMenu("📂 分组操作")

            select_action = group_menu.addAction("☑️ 全选此组")
            select_action.triggered.connect(lambda: self._select_group(item, True))

            deselect_action = group_menu.addAction("☐ 全不选此组")
            deselect_action.triggered.connect(lambda: self._select_group(item, False))

            group_menu.addSeparator()

            expand_action = group_menu.addAction("🔄 展开/折叠")
            expand_action.triggered.connect(lambda: self._toggle_group_expansion(item))

            # 批量清理
            menu.addSeparator()
            batch_action = menu.addAction("🧹 批量清理此组")
            batch_action.triggered.connect(lambda: self._batch_clean_group(item))

        menu.exec_(self.cleanup_tree.mapToGlobal(position))

    def _add_vscode_specific_menu(self, menu, item_data):
        """添加VSCode特定的清理选项"""
        category = item_data.get('category', '')

        if category == 'extensions':
            menu.addAction("🔌 重置扩展设置", lambda: self._reset_extensions(item_data))
            menu.addAction("📦 清理扩展缓存", lambda: self._clean_extension_cache(item_data))
        elif category == 'workspace':
            menu.addAction("🗂️ 清理工作区历史", lambda: self._clean_workspace_history(item_data))
            menu.addAction("💾 重置工作区设置", lambda: self._reset_workspace_settings(item_data))
        elif category == 'settings':
            menu.addAction("⚙️ 重置用户设置", lambda: self._reset_user_settings(item_data))
            menu.addAction("⌨️ 重置快捷键", lambda: self._reset_keybindings(item_data))

    def _add_cursor_specific_menu(self, menu, item_data):
        """添加Cursor特定的清理选项"""
        category = item_data.get('category', '')

        if category == 'ai_cache':
            menu.addAction("🤖 清理AI模型缓存", lambda: self._clean_ai_cache(item_data))
            menu.addAction("🧠 重置AI设置", lambda: self._reset_ai_settings(item_data))
        elif category == 'chat_history':
            menu.addAction("💬 清理对话历史", lambda: self._clean_chat_history(item_data))
            menu.addAction("🗨️ 导出对话记录", lambda: self._export_chat_history(item_data))

    def _add_generic_software_menu(self, menu, item_data, software):
        """添加通用软件清理选项"""
        category = item_data.get('category', '')

        if category == 'cache':
            menu.addAction(f"🗑️ 清理{software.upper()}缓存", lambda: self._clean_software_cache(item_data, software))
        elif category == 'settings':
            menu.addAction(f"⚙️ 重置{software.upper()}设置", lambda: self._reset_software_settings(item_data, software))
        elif category == 'plugins':
            menu.addAction(f"🔌 管理{software.upper()}插件", lambda: self._manage_software_plugins(item_data, software))

    def _backup_and_clean(self, item_data):
        """备份后清理"""
        reply = QMessageBox.question(
            self,
            "备份确认",
            f"是否要在清理前创建备份？\n\n"
            f"项目: {item_data.get('name', 'Unknown')}\n"
            f"风险等级: {item_data.get('risk_level', 'low')}\n\n"
            f"备份将保存到桌面的DevTools_Backup文件夹中。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            if self._create_backup(item_data):
                QMessageBox.information(self, "备份成功", "备份创建成功，现在开始清理...")
                self._clean_single_item(item_data)
            else:
                QMessageBox.warning(self, "备份失败", "备份创建失败，清理已取消。")

    def _preview_cleanup(self, item_data):
        """预览清理内容"""
        base_path = item_data.get('base_path', '')
        paths = item_data.get('paths', [])
        name = item_data.get('name', 'Unknown')

        preview_text = f"📋 清理预览: {name}\n\n"
        preview_text += f"📁 基础路径: {base_path}\n\n"
        preview_text += "🗂️ 将要清理的路径:\n"

        total_size = 0
        file_count = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)
            if os.path.exists(full_path):
                if os.path.isfile(full_path):
                    size = os.path.getsize(full_path)
                    preview_text += f"  📄 {sub_path} ({self._format_size(size)})\n"
                    total_size += size
                    file_count += 1
                elif os.path.isdir(full_path):
                    size = self._get_folder_size_fast(full_path)
                    file_count_in_dir = sum([len(files) for r, d, files in os.walk(full_path)])
                    preview_text += f"  📂 {sub_path} ({self._format_size(size)}, {file_count_in_dir}个文件)\n"
                    total_size += size
                    file_count += file_count_in_dir
            else:
                preview_text += f"  ❌ {sub_path} (不存在)\n"

        preview_text += f"\n📊 总计: {file_count}个文件, {self._format_size(total_size)}"

        if item_data.get('warning'):
            preview_text += f"\n\n⚠️ 警告: {item_data['warning']}"

        QMessageBox.information(self, "清理预览", preview_text)

    def _show_risk_assessment(self, item_data):
        """显示风险评估"""
        risk_level = item_data.get('risk_level', 'low')
        category = item_data.get('category', 'unknown')
        name = item_data.get('name', 'Unknown')

        risk_info = {
            'low': {
                'icon': '🟢',
                'level': '低风险',
                'description': '安全清理，不会影响软件正常使用',
                'recommendation': '可以放心清理'
            },
            'medium': {
                'icon': '🟡',
                'level': '中等风险',
                'description': '可能影响部分功能或丢失非关键数据',
                'recommendation': '建议先备份，然后清理'
            },
            'high': {
                'icon': '🔴',
                'level': '高风险',
                'description': '可能严重影响软件使用或丢失重要数据',
                'recommendation': '强烈建议备份，谨慎清理'
            }
        }

        risk = risk_info.get(risk_level, risk_info['low'])

        assessment_text = f"⚠️ 风险评估: {name}\n\n"
        assessment_text += f"{risk['icon']} 风险等级: {risk['level']}\n"
        assessment_text += f"📝 说明: {risk['description']}\n"
        assessment_text += f"💡 建议: {risk['recommendation']}\n\n"
        assessment_text += f"📂 清理类别: {category}\n"

        if item_data.get('warning'):
            assessment_text += f"\n⚠️ 特别警告: {item_data['warning']}"

        QMessageBox.information(self, "风险评估", assessment_text)

    def _show_size_estimation(self, item_data):
        """显示大小估算"""
        base_path = item_data.get('base_path', '')
        paths = item_data.get('paths', [])
        name = item_data.get('name', 'Unknown')
        size_estimate = item_data.get('size_estimate', '未知')

        size_text = f"📏 大小估算: {name}\n\n"
        size_text += f"📊 预估大小: {size_estimate}\n\n"

        # 实际计算大小
        actual_size = 0
        file_count = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)
            if os.path.exists(full_path):
                if os.path.isfile(full_path):
                    actual_size += os.path.getsize(full_path)
                    file_count += 1
                elif os.path.isdir(full_path):
                    folder_size = self._get_folder_size_fast(full_path)
                    actual_size += folder_size
                    file_count += sum([len(files) for r, d, files in os.walk(full_path)])

        if actual_size > 0:
            size_text += f"📋 实际大小: {self._format_size(actual_size)}\n"
            size_text += f"📄 文件数量: {file_count}个\n"
        else:
            size_text += "📋 实际大小: 无文件或路径不存在\n"

        QMessageBox.information(self, "大小估算", size_text)

    def _batch_clean_group(self, group_item):
        """批量清理分组"""
        group_name = group_item.text(0)

        # 收集分组中的所有项目
        items_to_clean = []
        for i in range(group_item.childCount()):
            child = group_item.child(i)
            if child.checkState(0) == Qt.Checked:
                item_data = child.data(0, Qt.UserRole)
                if item_data:
                    items_to_clean.append(item_data)

        if not items_to_clean:
            QMessageBox.information(self, "无项目", f"分组 '{group_name}' 中没有选中的项目。")
            return

        # 确认批量清理
        reply = QMessageBox.question(
            self,
            "批量清理确认",
            f"确定要批量清理分组 '{group_name}' 中的 {len(items_to_clean)} 个项目吗？\n\n"
            f"此操作将依次清理所有选中的项目。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self._execute_batch_cleanup(items_to_clean, group_name)

    def _execute_batch_cleanup(self, items_to_clean, group_name):
        """执行批量清理"""
        success_count = 0
        failed_count = 0
        total_size = 0

        # 创建进度对话框
        progress = QProgressDialog(f"正在批量清理 '{group_name}'...", "取消", 0, len(items_to_clean), self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setAutoClose(True)
        progress.setAutoReset(True)

        for i, item_data in enumerate(items_to_clean):
            if progress.wasCanceled():
                break

            progress.setValue(i)
            progress.setLabelText(f"正在清理: {item_data.get('name', 'Unknown')}")
            QApplication.processEvents()

            try:
                if item_data.get('category') == 'machine_id':
                    success = self._reset_machine_id(item_data)
                else:
                    success = self._clean_item(item_data)

                if success:
                    success_count += 1
                    # 估算释放的空间
                    size_estimate = item_data.get('size', 0)
                    total_size += size_estimate
                else:
                    failed_count += 1

            except Exception as e:
                self.logger.error(f"批量清理项目失败 {item_data.get('name')}: {e}")
                failed_count += 1

        progress.setValue(len(items_to_clean))

        # 显示结果
        result_text = f"🧹 批量清理完成\n\n"
        result_text += f"📂 分组: {group_name}\n"
        result_text += f"✅ 成功: {success_count}个项目\n"
        result_text += f"❌ 失败: {failed_count}个项目\n"
        result_text += f"💾 释放空间: {self._format_size(total_size)}"

        QMessageBox.information(self, "批量清理结果", result_text)

        # 重新扫描更新状态
        self._scan_vscode_data()

    # ==================== 软件特定清理方法 ====================

    def _reset_extensions(self, item_data):
        """重置扩展设置"""
        try:
            base_path = item_data.get('base_path', '')
            extensions_path = os.path.join(base_path, 'extensions')

            if os.path.exists(extensions_path):
                # 只清理扩展数据，保留扩展本身
                for ext_dir in os.listdir(extensions_path):
                    ext_path = os.path.join(extensions_path, ext_dir)
                    if os.path.isdir(ext_path):
                        # 清理扩展的工作区存储
                        storage_path = os.path.join(ext_path, 'globalStorage')
                        if os.path.exists(storage_path):
                            import shutil
                            shutil.rmtree(storage_path)

                QMessageBox.information(self, "重置完成", "扩展设置已重置")
            else:
                QMessageBox.information(self, "无需重置", "未找到扩展目录")

        except Exception as e:
            QMessageBox.warning(self, "重置失败", f"重置扩展设置失败: {e}")

    def _clean_extension_cache(self, item_data):
        """清理扩展缓存"""
        try:
            base_path = item_data.get('base_path', '')
            cache_paths = ['CachedExtensions', 'CachedExtensionVSIXs', 'extensions/.obsolete']

            cleaned_count = 0
            for cache_path in cache_paths:
                full_path = os.path.join(base_path, cache_path)
                if os.path.exists(full_path):
                    if os.path.isdir(full_path):
                        import shutil
                        shutil.rmtree(full_path)
                        cleaned_count += 1

            if cleaned_count > 0:
                QMessageBox.information(self, "清理完成", f"已清理 {cleaned_count} 个扩展缓存目录")
            else:
                QMessageBox.information(self, "无需清理", "未找到扩展缓存")

        except Exception as e:
            QMessageBox.warning(self, "清理失败", f"清理扩展缓存失败: {e}")

    def _clean_workspace_history(self, item_data):
        """清理工作区历史"""
        try:
            base_path = item_data.get('base_path', '')
            history_paths = ['User/History', 'User/workspaceStorage', 'Workspaces']

            cleaned_count = 0
            for history_path in history_paths:
                full_path = os.path.join(base_path, history_path)
                if os.path.exists(full_path):
                    if os.path.isdir(full_path):
                        import shutil
                        shutil.rmtree(full_path)
                        cleaned_count += 1

            if cleaned_count > 0:
                QMessageBox.information(self, "清理完成", f"已清理 {cleaned_count} 个工作区历史目录")
            else:
                QMessageBox.information(self, "无需清理", "未找到工作区历史")

        except Exception as e:
            QMessageBox.warning(self, "清理失败", f"清理工作区历史失败: {e}")

    def _reset_user_settings(self, item_data):
        """重置用户设置"""
        try:
            base_path = item_data.get('base_path', '')
            settings_file = os.path.join(base_path, 'User', 'settings.json')

            if os.path.exists(settings_file):
                # 创建备份
                backup_file = settings_file + '.backup'
                import shutil
                shutil.copy2(settings_file, backup_file)

                # 重置为默认设置
                default_settings = '{\n    // 默认设置\n}'
                with open(settings_file, 'w', encoding='utf-8') as f:
                    f.write(default_settings)

                QMessageBox.information(self, "重置完成", f"用户设置已重置\n备份保存为: {backup_file}")
            else:
                QMessageBox.information(self, "无需重置", "未找到用户设置文件")

        except Exception as e:
            QMessageBox.warning(self, "重置失败", f"重置用户设置失败: {e}")

    def _clean_ai_cache(self, item_data):
        """清理AI缓存（Cursor特有）"""
        try:
            base_path = item_data.get('base_path', '')
            ai_cache_paths = ['ai-cache', 'model-cache', 'inference-cache', 'llm-cache']

            cleaned_count = 0
            total_size = 0

            for cache_path in ai_cache_paths:
                full_path = os.path.join(base_path, cache_path)
                if os.path.exists(full_path):
                    if os.path.isdir(full_path):
                        folder_size = self._get_folder_size_fast(full_path)
                        import shutil
                        shutil.rmtree(full_path)
                        total_size += folder_size
                        cleaned_count += 1

            if cleaned_count > 0:
                QMessageBox.information(self, "清理完成",
                    f"已清理 {cleaned_count} 个AI缓存目录\n释放空间: {self._format_size(total_size)}")
            else:
                QMessageBox.information(self, "无需清理", "未找到AI缓存")

        except Exception as e:
            QMessageBox.warning(self, "清理失败", f"清理AI缓存失败: {e}")

    def _clean_software_cache(self, item_data, software):
        """通用软件缓存清理"""
        try:
            base_path = item_data.get('base_path', '')
            cache_patterns = ['cache', 'Cache', 'temp', 'Temp', 'logs', 'Logs']

            cleaned_count = 0
            total_size = 0

            for pattern in cache_patterns:
                full_path = os.path.join(base_path, pattern)
                if os.path.exists(full_path):
                    if os.path.isdir(full_path):
                        folder_size = self._get_folder_size_fast(full_path)
                        import shutil
                        shutil.rmtree(full_path)
                        total_size += folder_size
                        cleaned_count += 1

            if cleaned_count > 0:
                QMessageBox.information(self, "清理完成",
                    f"{software.upper()} 缓存清理完成\n清理项目: {cleaned_count}个\n释放空间: {self._format_size(total_size)}")
            else:
                QMessageBox.information(self, "无需清理", f"未找到{software.upper()}缓存")

        except Exception as e:
            QMessageBox.warning(self, "清理失败", f"清理{software.upper()}缓存失败: {e}")

    # ==================== 占位符方法（避免错误） ====================

    def _reset_workspace_settings(self, item_data):
        """重置工作区设置"""
        QMessageBox.information(self, "功能开发中", "工作区设置重置功能正在开发中...")

    def _reset_keybindings(self, item_data):
        """重置快捷键"""
        QMessageBox.information(self, "功能开发中", "快捷键重置功能正在开发中...")

    def _reset_ai_settings(self, item_data):
        """重置AI设置"""
        QMessageBox.information(self, "功能开发中", "AI设置重置功能正在开发中...")

    def _clean_chat_history(self, item_data):
        """清理对话历史"""
        QMessageBox.information(self, "功能开发中", "对话历史清理功能正在开发中...")

    def _export_chat_history(self, item_data):
        """导出对话记录"""
        QMessageBox.information(self, "功能开发中", "对话记录导出功能正在开发中...")

    def _reset_software_settings(self, item_data, software):
        """重置软件设置"""
        QMessageBox.information(self, "功能开发中", f"{software.upper()}设置重置功能正在开发中...")

    def _manage_software_plugins(self, item_data, software):
        """管理软件插件"""
        QMessageBox.information(self, "功能开发中", f"{software.upper()}插件管理功能正在开发中...")

    def _toggle_item_selection(self, item, selected):
        """切换项目选中状态"""
        item.setCheckState(0, Qt.Checked if selected else Qt.Unchecked)
        self._on_item_check_changed(item, 0)

    def _select_group(self, group_item, selected):
        """选中/取消选中整个分组"""
        group_item.setCheckState(0, Qt.Checked if selected else Qt.Unchecked)
        for i in range(group_item.childCount()):
            child = group_item.child(i)
            child.setCheckState(0, Qt.Checked if selected else Qt.Unchecked)
            self._on_item_check_changed(child, 0)

    def _toggle_group_expansion(self, group_item):
        """切换分组展开/折叠状态"""
        group_item.setExpanded(not group_item.isExpanded())

    def _show_item_details(self, item_data):
        """显示项目详细信息"""
        details = f"""📋 清理项目详情

🏷️ 名称: {item_data.get('name', 'N/A')}
📂 软件: {item_data.get('software', 'N/A')}
📁 路径: {item_data.get('base_path', 'N/A')}
📏 大小: {self._format_size(item_data.get('size', 0))}
📝 描述: {item_data.get('description', 'N/A')}
⭐ 推荐: {'是' if item_data.get('recommended', False) else '否'}
✅ 已选中: {'是' if item_data.get('enabled', False) else '否'}

📋 包含路径:
"""

        for path in item_data.get('paths', []):
            details += f"  • {path}\n"

        if item_data.get('warning'):
            details += f"\n⚠️ 警告: {item_data['warning']}"

        QMessageBox.information(self, "项目详情", details)

    def _open_folder(self, folder_path):
        """打开文件夹"""
        try:
            if os.path.exists(folder_path):
                os.startfile(folder_path)
            else:
                QMessageBox.warning(self, "路径不存在", f"路径不存在: {folder_path}")
        except Exception as e:
            QMessageBox.warning(self, "打开失败", f"无法打开文件夹: {e}")

    def _clean_single_item(self, item_data):
        """单独清理一个项目"""
        reply = QMessageBox.question(
            self,
            "确认清理",
            f"确定要清理以下项目吗？\n\n"
            f"📋 名称: {item_data.get('name', 'N/A')}\n"
            f"📏 大小: {self._format_size(item_data.get('size', 0))}\n"
            f"📁 路径: {item_data.get('base_path', 'N/A')}\n\n"
            f"此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 执行清理
                if item_data.get('category') == 'machine_id':
                    success = self._reset_machine_id(item_data)
                else:
                    success = self._clean_item(item_data)

                if success:
                    QMessageBox.information(self, "清理成功", f"项目 '{item_data.get('name')}' 清理完成！")
                    # 重新扫描以更新状态
                    self._scan_vscode_data()
                else:
                    QMessageBox.warning(self, "清理失败", f"项目 '{item_data.get('name')}' 清理失败！")

            except Exception as e:
                QMessageBox.critical(self, "清理错误", f"清理过程中发生错误: {e}")

        # 更新状态
        software_names = {
            "all": "全部软件",
            "vscode": "VSCode",
            "cursor": "Cursor",
            "sublime": "Sublime Text",
            "atom": "Atom",
            "webstorm": "WebStorm",
            "dev_tools": "开发工具缓存"
        }

        if filtered_items:
            total_size_str = self._format_size(total_size)
            self.status_label.setText(f"📊 {software_names[self.selected_software]}: 找到 {len(filtered_items)} 个清理项目，总大小: {total_size_str}")
        else:
            self.status_label.setText(f"📊 {software_names[self.selected_software]}: 未找到清理项目")

    def _clean_item(self, item_data):
        """
        清理单个项目 - 增强版本，支持备份和安全检查，支持多软件清理

        Args:
            item_data (dict): 清理项目数据

        Returns:
            bool: 清理是否成功
        """
        try:
            category = item_data.get('category', '')
            software = item_data.get('software', 'vscode')
            risk_level = item_data.get('risk_level', 'low')

            # 检查是否是 Augment 相关的清理项目
            if category.startswith('augment_'):
                return self._clean_augment_item(item_data)

            # 检查是否是机器码重置
            if category == 'machine_id':
                return self._reset_machine_id(item_data)

            # 获取软件特定的清理逻辑
            return self._clean_software_item(item_data, software, category, risk_level)

        except Exception as e:
            self.logger.error(f"清理项目失败 {item_data.get('name')}: {e}")
            return False

    def _clean_software_item(self, item_data, software, category, risk_level):
        """
        清理特定软件的项目

        Args:
            item_data (dict): 清理项目数据
            software (str): 软件类型
            category (str): 清理类别
            risk_level (str): 风险等级

        Returns:
            bool: 清理是否成功
        """
        try:
            base_path = item_data.get('base_path', '')
            paths = item_data.get('paths', [])
            name = item_data.get('name', 'Unknown')

            # 验证基础路径
            if not self._validate_path(base_path, f"清理项目 {name}"):
                return False

            # 高风险项目需要额外确认和备份
            if risk_level == 'high':
                if not self._handle_high_risk_cleanup(item_data):
                    return False

            # 中等风险项目创建备份
            elif risk_level == 'medium' or category in ['settings', 'workspace', 'extension_data']:
                if not self._create_backup(item_data):
                    self.logger.warning(f"备份创建失败，跳过清理: {name}")
                    return False

            # 执行清理
            cleaned_files = 0
            total_size = 0

            # 根据软件类型使用不同的清理策略
            if software in ['vscode', 'cursor']:
                cleaned_files, total_size = self._clean_vscode_like_paths(base_path, paths, name)
            elif software == 'sublime':
                cleaned_files, total_size = self._clean_sublime_paths(base_path, paths, name)
            elif software == 'notepad++':
                cleaned_files, total_size = self._clean_notepadpp_paths(base_path, paths, name)
            elif software == 'android_studio':
                cleaned_files, total_size = self._clean_android_studio_paths(base_path, paths, name)
            elif software == 'atom':
                cleaned_files, total_size = self._clean_atom_paths(base_path, paths, name)
            else:
                # 通用清理逻辑
                cleaned_files, total_size = self._clean_generic_paths(base_path, paths, name)

            if cleaned_files > 0:
                self.logger.info(f"✅ {software.upper()} 清理完成: {name}, 删除 {cleaned_files} 个项目, 释放 {self._format_size(total_size)}")
                return True
            else:
                self.logger.warning(f"⚠️ {software.upper()} 未找到可清理文件: {name}")
                return False

        except Exception as e:
            self.logger.error(f"❌ {software.upper()} 清理失败 {item_data.get('name')}: {e}")
            return False

    def _handle_high_risk_cleanup(self, item_data):
        """
        处理高风险清理项目

        Args:
            item_data (dict): 清理项目数据

        Returns:
            bool: 是否继续清理
        """
        name = item_data.get('name', 'Unknown')
        description = item_data.get('description', '')

        # 高风险项目需要额外确认
        reply = QMessageBox.question(
            self,
            "⚠️ 高风险清理确认",
            f"您即将清理高风险项目：\n\n"
            f"📋 项目: {name}\n"
            f"📝 说明: {description}\n\n"
            f"⚠️ 警告:\n"
            f"• 此操作可能影响软件正常使用\n"
            f"• 可能丢失个人设置和配置\n"
            f"• 建议先手动备份重要数据\n\n"
            f"是否继续清理？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.No:
            return False

        # 强制创建备份
        if not self._create_backup(item_data):
            QMessageBox.warning(
                self,
                "备份失败",
                f"无法为高风险项目创建备份，清理已取消。\n\n"
                f"请检查磁盘空间和权限设置。"
            )
            return False

        return True

    def _clean_vscode_like_paths(self, base_path, paths, name):
        """
        清理 VSCode/Cursor 类型的路径

        Args:
            base_path (str): 基础路径
            paths (list): 要清理的子路径列表
            name (str): 项目名称

        Returns:
            tuple: (清理文件数, 总大小)
        """
        cleaned_files = 0
        total_size = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)

            # 支持通配符路径
            if '*' in sub_path:
                import glob
                matching_paths = glob.glob(full_path)
                for match_path in matching_paths:
                    if os.path.exists(match_path):
                        size, count = self._remove_path_safely(match_path, name)
                        total_size += size
                        cleaned_files += count
            else:
                if os.path.exists(full_path):
                    size, count = self._remove_path_safely(full_path, name)
                    total_size += size
                    cleaned_files += count

        return cleaned_files, total_size

    def _clean_sublime_paths(self, base_path, paths, name):
        """
        清理 Sublime Text 特定路径

        Args:
            base_path (str): 基础路径
            paths (list): 要清理的子路径列表
            name (str): 项目名称

        Returns:
            tuple: (清理文件数, 总大小)
        """
        cleaned_files = 0
        total_size = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)

            if os.path.exists(full_path):
                # Sublime Text 特殊处理：保留用户许可证文件
                if 'License.sublime_license' in full_path:
                    self.logger.info(f"跳过许可证文件: {full_path}")
                    continue

                size, count = self._remove_path_safely(full_path, name)
                total_size += size
                cleaned_files += count

        return cleaned_files, total_size

    def _clean_notepadpp_paths(self, base_path, paths, name):
        """
        清理 Notepad++ 特定路径

        Args:
            base_path (str): 基础路径
            paths (list): 要清理的子路径列表
            name (str): 项目名称

        Returns:
            tuple: (清理文件数, 总大小)
        """
        cleaned_files = 0
        total_size = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)

            if os.path.exists(full_path):
                # Notepad++ 特殊处理：保留重要配置文件
                if any(important in full_path.lower() for important in ['config.xml', 'stylers.xml']):
                    # 对于重要配置文件，只清理其中的临时数据
                    if full_path.endswith('.xml'):
                        size, count = self._clean_xml_temp_data(full_path, name)
                        total_size += size
                        cleaned_files += count
                    continue

                size, count = self._remove_path_safely(full_path, name)
                total_size += size
                cleaned_files += count

        return cleaned_files, total_size

    def _clean_android_studio_paths(self, base_path, paths, name):
        """
        清理 Android Studio 特定路径

        Args:
            base_path (str): 基础路径
            paths (list): 要清理的子路径列表
            name (str): 项目名称

        Returns:
            tuple: (清理文件数, 总大小)
        """
        cleaned_files = 0
        total_size = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)

            if os.path.exists(full_path):
                # Android Studio 特殊处理：保留SDK配置
                if 'sdk' in full_path.lower() and 'config' in full_path.lower():
                    self.logger.info(f"跳过SDK配置: {full_path}")
                    continue

                # 清理Gradle缓存时要小心
                if 'gradle' in full_path.lower():
                    size, count = self._clean_gradle_cache_safely(full_path, name)
                    total_size += size
                    cleaned_files += count
                else:
                    size, count = self._remove_path_safely(full_path, name)
                    total_size += size
                    cleaned_files += count

        return cleaned_files, total_size

    def _clean_atom_paths(self, base_path, paths, name):
        """
        清理 Atom 编辑器特定路径

        Args:
            base_path (str): 基础路径
            paths (list): 要清理的子路径列表
            name (str): 项目名称

        Returns:
            tuple: (清理文件数, 总大小)
        """
        cleaned_files = 0
        total_size = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)

            if os.path.exists(full_path):
                # Atom 特殊处理：保留用户包配置
                if 'packages' in full_path and 'config.cson' in full_path:
                    self.logger.info(f"跳过包配置: {full_path}")
                    continue

                size, count = self._remove_path_safely(full_path, name)
                total_size += size
                cleaned_files += count

        return cleaned_files, total_size

    def _clean_generic_paths(self, base_path, paths, name):
        """
        通用路径清理方法

        Args:
            base_path (str): 基础路径
            paths (list): 要清理的子路径列表
            name (str): 项目名称

        Returns:
            tuple: (清理文件数, 总大小)
        """
        cleaned_files = 0
        total_size = 0

        for sub_path in paths:
            full_path = os.path.join(base_path, sub_path)

            if os.path.exists(full_path):
                size, count = self._remove_path_safely(full_path, name)
                total_size += size
                cleaned_files += count

        return cleaned_files, total_size

    def _remove_path_safely(self, path, item_name):
        """
        安全地删除文件或文件夹

        Args:
            path (str): 要删除的路径
            item_name (str): 项目名称（用于日志）

        Returns:
            tuple: (删除的大小, 删除的文件数)
        """
        try:
            if os.path.isfile(path):
                # 删除文件
                file_size = os.path.getsize(path)
                os.remove(path)
                self.logger.info(f"🗑️ 删除文件: {path}")
                return file_size, 1

            elif os.path.isdir(path):
                # 删除文件夹
                folder_size = self._get_folder_size_fast(path)
                import shutil
                shutil.rmtree(path)
                self.logger.info(f"🗂️ 删除文件夹: {path}")
                return folder_size, 1

        except PermissionError:
            self.logger.warning(f"⚠️ 权限不足，无法删除: {path}")
        except FileNotFoundError:
            self.logger.info(f"ℹ️ 文件不存在，跳过: {path}")
        except Exception as e:
            self.logger.error(f"❌ 删除失败 {path}: {e}")

        return 0, 0

    def _clean_xml_temp_data(self, xml_path, item_name):
        """
        清理XML文件中的临时数据（保留配置结构）

        Args:
            xml_path (str): XML文件路径
            item_name (str): 项目名称

        Returns:
            tuple: (清理的大小, 文件数)
        """
        try:
            import xml.etree.ElementTree as ET

            # 读取XML文件
            tree = ET.parse(xml_path)
            root = tree.getroot()

            # 移除临时数据节点（如最近文件列表等）
            temp_tags = ['RecentFiles', 'RecentProjects', 'TempData', 'Cache']
            removed_count = 0

            for tag in temp_tags:
                elements = root.findall(f".//{tag}")
                for element in elements:
                    parent = root.find(f".//{element.tag}/..")
                    if parent is not None:
                        parent.remove(element)
                        removed_count += 1

            if removed_count > 0:
                # 保存清理后的XML
                tree.write(xml_path, encoding='utf-8', xml_declaration=True)
                self.logger.info(f"🧹 清理XML临时数据: {xml_path} ({removed_count}个节点)")
                return 1024, 1  # 估算清理大小

        except Exception as e:
            self.logger.error(f"❌ XML清理失败 {xml_path}: {e}")

        return 0, 0

    def _clean_gradle_cache_safely(self, gradle_path, item_name):
        """
        安全地清理Gradle缓存

        Args:
            gradle_path (str): Gradle缓存路径
            item_name (str): 项目名称

        Returns:
            tuple: (清理的大小, 文件数)
        """
        try:
            total_size = 0
            file_count = 0

            # 只清理缓存文件，保留配置
            cache_patterns = ['build-cache', 'caches', 'daemon', 'wrapper/dists']

            for pattern in cache_patterns:
                cache_path = os.path.join(gradle_path, pattern)
                if os.path.exists(cache_path):
                    if os.path.isdir(cache_path):
                        folder_size = self._get_folder_size_fast(cache_path)
                        import shutil
                        shutil.rmtree(cache_path)
                        total_size += folder_size
                        file_count += 1
                        self.logger.info(f"🧹 清理Gradle缓存: {cache_path}")

            return total_size, file_count

        except Exception as e:
            self.logger.error(f"❌ Gradle缓存清理失败 {gradle_path}: {e}")
            return 0, 0

    def _clean_augment_item(self, item_data):
        """
        清理 Augment 相关项目 - 使用 AugmentCleaner

        Args:
            item_data (dict): Augment 清理项目数据

        Returns:
            bool: 清理是否成功
        """
        try:
            category = item_data.get('category', '')
            editor_type = item_data.get('editor_type', 'vscode')

            self.logger.info(f"使用 AugmentCleaner 清理: {item_data.get('name')}")

            # 根据类别调用相应的清理方法
            if 'global_storage' in category:
                success = self.augment_cleaner.clean_global_storage(editor_type)
            elif 'workspace_storage' in category:
                success = self.augment_cleaner.clean_workspace_storage(editor_type)
            elif 'user_settings' in category:
                success = self.augment_cleaner.clean_user_settings(editor_type)
            elif 'logs' in category:
                success = self.augment_cleaner.clean_extension_logs(editor_type)
            elif 'cache' in category:
                success = self.augment_cleaner.clean_extension_cache(editor_type)
            else:
                # 对于其他类型，直接删除文件
                success = self._clean_augment_files_directly(item_data)

            if success:
                self.logger.info(f"✅ Augment 清理成功: {item_data.get('name')}")
            else:
                self.logger.warning(f"⚠️ Augment 清理失败: {item_data.get('name')}")

            return success

        except Exception as e:
            self.logger.error(f"Augment 清理异常 {item_data.get('name')}: {e}")
            return False

    def _clean_augment_files_directly(self, item_data):
        """直接清理 Augment 文件"""
        try:
            paths = item_data.get('paths', [])
            cleaned_count = 0

            for file_path in paths:
                if os.path.exists(file_path):
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            cleaned_count += 1
                            self.logger.info(f"删除 Augment 文件: {file_path}")
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            cleaned_count += 1
                            self.logger.info(f"删除 Augment 目录: {file_path}")
                    except (OSError, PermissionError) as e:
                        self.logger.warning(f"无法删除 {file_path}: {e}")
                        continue

            return cleaned_count > 0

        except Exception as e:
            self.logger.error(f"直接清理 Augment 文件失败: {e}")
            return False

    def _create_backup(self, item_data):
        """
        创建备份 - 增强版本，支持多软件类型

        Args:
            item_data (dict): 清理项目数据

        Returns:
            bool: 备份是否成功
        """
        try:
            base_path = item_data.get('base_path', '')
            name = item_data.get('name', 'unknown')
            software = item_data.get('software', 'vscode')
            category = item_data.get('category', 'unknown')
            risk_level = item_data.get('risk_level', 'low')

            # 根据软件类型创建不同的备份目录
            software_names = {
                'vscode': 'VSCode',
                'cursor': 'Cursor',
                'sublime': 'SublimeText',
                'notepad++': 'NotepadPP',
                'android_studio': 'AndroidStudio',
                'atom': 'Atom'
            }

            software_name = software_names.get(software, software.upper())
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 创建备份目录结构
            backup_root = os.path.join(os.path.expanduser("~"), "Desktop", "DevTools_Backup")
            backup_dir = os.path.join(backup_root, software_name, f"{category}_{timestamp}")
            os.makedirs(backup_dir, exist_ok=True)

            # 创建备份信息文件
            backup_info = {
                'software': software,
                'category': category,
                'risk_level': risk_level,
                'item_name': name,
                'backup_time': timestamp,
                'original_paths': item_data.get('paths', []),
                'base_path': base_path
            }

            info_file = os.path.join(backup_dir, 'backup_info.json')
            with open(info_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(backup_info, f, indent=2, ensure_ascii=False)

            # 备份每个路径
            backup_count = 0
            total_size = 0

            for sub_path in item_data.get('paths', []):
                full_path = os.path.join(base_path, sub_path)
                if os.path.exists(full_path):
                    # 创建相对路径结构
                    backup_path = os.path.join(backup_dir, 'data', sub_path)
                    backup_parent = os.path.dirname(backup_path)
                    os.makedirs(backup_parent, exist_ok=True)

                    try:
                        if os.path.isfile(full_path):
                            file_size = os.path.getsize(full_path)
                            shutil.copy2(full_path, backup_path)
                            total_size += file_size
                            backup_count += 1

                        elif os.path.isdir(full_path):
                            # 对于大文件夹，只备份重要文件
                            if risk_level == 'high' or category in ['settings', 'workspace']:
                                folder_size = self._backup_directory_selective(full_path, backup_path, software)
                                total_size += folder_size
                                backup_count += 1
                            else:
                                # 低风险项目，创建简单备份
                                shutil.copytree(full_path, backup_path)
                                folder_size = self._get_folder_size_fast(full_path)
                                total_size += folder_size
                                backup_count += 1

                    except Exception as e:
                        self.logger.warning(f"备份路径失败 {full_path}: {e}")
                        continue

            if backup_count > 0:
                # 更新备份信息
                backup_info['backup_count'] = backup_count
                backup_info['total_size'] = total_size
                backup_info['size_formatted'] = self._format_size(total_size)

                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_info, f, indent=2, ensure_ascii=False)

                self.logger.info(f"✅ {software_name} 备份创建成功: {backup_dir} ({backup_count}个项目, {self._format_size(total_size)})")
                return True
            else:
                # 删除空备份目录
                import shutil
                shutil.rmtree(backup_dir, ignore_errors=True)
                self.logger.warning(f"⚠️ 没有文件需要备份: {name}")
                return True  # 没有文件备份也算成功

        except Exception as e:
            self.logger.error(f"❌ 创建备份失败: {e}")
            return False

    def _backup_directory_selective(self, source_dir, backup_dir, software):
        """
        选择性备份目录（只备份重要文件）

        Args:
            source_dir (str): 源目录
            backup_dir (str): 备份目录
            software (str): 软件类型

        Returns:
            int: 备份的总大小
        """
        try:
            total_size = 0

            # 根据软件类型定义重要文件模式
            important_patterns = {
                'vscode': ['*.json', '*.xml', '*.ini', 'settings.json', 'keybindings.json'],
                'cursor': ['*.json', '*.xml', '*.ini', 'settings.json', 'keybindings.json'],
                'sublime': ['*.sublime-settings', '*.sublime-keymap', '*.sublime-project'],
                'notepad++': ['*.xml', '*.ini', 'config.xml', 'stylers.xml'],
                'android_studio': ['*.xml', '*.properties', '*.gradle'],
                'atom': ['*.cson', '*.json', 'config.cson', 'keymap.cson']
            }

            patterns = important_patterns.get(software, ['*.json', '*.xml', '*.ini'])

            import glob
            os.makedirs(backup_dir, exist_ok=True)

            # 备份重要文件
            for pattern in patterns:
                for file_path in glob.glob(os.path.join(source_dir, '**', pattern), recursive=True):
                    if os.path.isfile(file_path):
                        # 计算相对路径
                        rel_path = os.path.relpath(file_path, source_dir)
                        backup_file_path = os.path.join(backup_dir, rel_path)
                        backup_file_dir = os.path.dirname(backup_file_path)

                        os.makedirs(backup_file_dir, exist_ok=True)

                        file_size = os.path.getsize(file_path)
                        shutil.copy2(file_path, backup_file_path)
                        total_size += file_size

            return total_size

        except Exception as e:
            self.logger.error(f"选择性备份失败 {source_dir}: {e}")
            return 0

    def _check_running_processes(self):
        """
        检查VSCode相关进程是否正在运行

        Returns:
            list: 正在运行的进程名称列表
        """
        running_processes = []

        try:
            import psutil

            # 要检查的进程名称
            target_processes = [
                'code.exe', 'Code.exe', 'Code - Insiders.exe',
                'cursor.exe', 'Cursor.exe',
                'sublime_text.exe', 'subl.exe',
                'atom.exe', 'Atom.exe',
                'webstorm.exe', 'webstorm64.exe',
                'notepad++.exe', 'brackets.exe'
            ]

            # 检查所有运行的进程
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name']
                    if proc_name.lower() in [p.lower() for p in target_processes]:
                        running_processes.append(proc_name)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except ImportError:
            # 如果没有psutil，使用Windows的tasklist命令
            try:
                import subprocess
                result = subprocess.run(['tasklist'], capture_output=True, text=True, shell=True)
                output = result.stdout.lower()

                target_processes = ['code.exe', 'cursor.exe', 'sublime_text.exe', 'atom.exe', 'webstorm.exe']
                for proc in target_processes:
                    if proc in output:
                        running_processes.append(proc)

            except Exception as e:
                self.logger.warning(f"无法检查运行进程: {e}")

        return list(set(running_processes))  # 去重



    def _perform_enhanced_cleanup(self, items):
        """
        执行增强清理 - 支持进度显示、错误处理和统计

        Args:
            items (list): 要清理的项目列表
        """
        try:
            # 初始化进度
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setVisible(True)
                self.progress_bar.setMaximum(len(items))
                self.progress_bar.setValue(0)

            # 禁用按钮
            self.scan_btn.setEnabled(False)
            self.reset_machine_id_btn.setEnabled(False)


            # 清理统计
            results = {
                'success': [],
                'failed': [],
                'total_size': 0,
                'total_files': 0
            }

            # 执行清理
            for i, item in enumerate(items):
                name = item.get('name', 'Unknown')
                category = item.get('category', '')

                # 更新状态
                self.status_label.setText(f"🧹 正在清理: {name}")
                if hasattr(self, 'progress_bar') and self.progress_bar:
                    self.progress_bar.setValue(i)

                # 处理应用事件，保持界面响应
                QApplication.processEvents()

                try:
                    if category == 'machine_id':
                        # 机器码重置
                        success = self._reset_machine_id(item)
                    else:
                        # 普通清理
                        success = self._clean_item(item)

                    if success:
                        results['success'].append(name)
                        results['total_size'] += item.get('size', 0)
                        self.logger.info(f"清理成功: {name}")
                    else:
                        results['failed'].append(name)
                        self.logger.warning(f"清理失败: {name}")

                except Exception as e:
                    results['failed'].append(f"{name} (错误: {e})")
                    self.logger.error(f"清理异常 {name}: {e}")

            # 完成清理
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setValue(len(items))

            # 显示结果
            self._show_cleanup_results(results)

        except Exception as e:
            self.logger.error(f"清理过程异常: {e}")
            QMessageBox.critical(self, "清理错误", f"清理过程中发生严重错误: {e}")

        finally:
            # 恢复界面状态
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setVisible(False)

            self.scan_btn.setEnabled(True)
            self.reset_machine_id_btn.setEnabled(True)


            # 重新扫描以更新状态
            self._scan_vscode_data()

    def _show_cleanup_results(self, results):
        """
        显示清理结果

        Args:
            results (dict): 清理结果统计
        """
        success_count = len(results['success'])
        failed_count = len(results['failed'])
        total_size = results['total_size']

        # 构建结果消息
        message = f"🎉 清理完成！\n\n"
        message += f"✅ 成功清理: {success_count} 项\n"
        message += f"❌ 清理失败: {failed_count} 项\n"
        message += f"💾 释放空间: {self._format_size(total_size)}\n\n"

        if results['success']:
            message += "成功清理的项目：\n"
            for item in results['success'][:5]:  # 只显示前5个
                message += f"• {item}\n"
            if len(results['success']) > 5:
                message += f"• ... 还有 {len(results['success']) - 5} 个项目\n"

        if results['failed']:
            message += "\n清理失败的项目：\n"
            for item in results['failed'][:3]:  # 只显示前3个
                message += f"• {item}\n"
            if len(results['failed']) > 3:
                message += f"• ... 还有 {len(results['failed']) - 3} 个项目\n"

        # 选择合适的消息框类型
        if failed_count == 0:
            QMessageBox.information(self, "清理完成", message)
        else:
            QMessageBox.warning(self, "清理完成（有失败项目）", message)

        # 更新状态标签
        self.status_label.setText(f"✅ 清理完成: 成功 {success_count} 项，失败 {failed_count} 项")

        # 保存清理统计
        self._save_cleanup_stats(results)

    def _save_cleanup_stats(self, results):
        """
        保存清理统计信息

        Args:
            results (dict): 清理结果
        """
        try:
            stats_file = os.path.join(os.path.expanduser("~"), ".augment_cleanup_stats.json")

            # 读取现有统计
            stats = []
            if os.path.exists(stats_file):
                try:
                    with open(stats_file, 'r', encoding='utf-8') as f:
                        stats = json.load(f)
                except:
                    stats = []

            # 添加新的统计记录
            new_record = {
                'timestamp': datetime.now().isoformat(),
                'success_count': len(results['success']),
                'failed_count': len(results['failed']),
                'total_size': results['total_size'],
                'items_cleaned': results['success'][:10]  # 只保存前10个项目名称
            }

            stats.append(new_record)

            # 只保留最近50次记录
            if len(stats) > 50:
                stats = stats[-50:]

            # 保存统计
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)

            self.logger.info(f"清理统计已保存: {stats_file}")

        except Exception as e:
            self.logger.error(f"保存清理统计失败: {e}")

    def _show_cleanup_stats(self):
        """显示清理统计信息"""
        try:
            stats_file = os.path.join(os.path.expanduser("~"), ".augment_cleanup_stats.json")

            if not os.path.exists(stats_file):
                QMessageBox.information(self, "清理统计", "暂无清理统计数据")
                return

            with open(stats_file, 'r', encoding='utf-8') as f:
                stats = json.load(f)

            if not stats:
                QMessageBox.information(self, "清理统计", "暂无清理统计数据")
                return

            # 计算总体统计
            total_cleanups = len(stats)
            total_success = sum(record['success_count'] for record in stats)
            total_failed = sum(record['failed_count'] for record in stats)
            total_size_freed = sum(record['total_size'] for record in stats)

            # 最近清理记录
            recent_records = stats[-5:]  # 最近5次

            # 构建统计信息
            message = f"📊 清理统计报告\n\n"
            message += f"🔢 总清理次数: {total_cleanups} 次\n"
            message += f"✅ 总成功项目: {total_success} 个\n"
            message += f"❌ 总失败项目: {total_failed} 个\n"
            message += f"💾 总释放空间: {self._format_size(total_size_freed)}\n\n"

            message += "📅 最近清理记录:\n"
            for record in reversed(recent_records):
                timestamp = datetime.fromisoformat(record['timestamp']).strftime("%Y-%m-%d %H:%M")
                message += f"• {timestamp}: 成功 {record['success_count']} 项, "
                message += f"释放 {self._format_size(record['total_size'])}\n"

            QMessageBox.information(self, "清理统计", message)

        except Exception as e:
            QMessageBox.warning(self, "统计错误", f"读取清理统计失败: {e}")

    def _show_settings(self):
        """显示设置对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("⚙️ 清理工具设置")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # 自动备份设置
        backup_group = QGroupBox("备份设置")
        backup_layout = QVBoxLayout(backup_group)

        self.auto_backup_cb = QCheckBox("清理前自动创建备份")
        self.auto_backup_cb.setChecked(True)  # 默认启用
        backup_layout.addWidget(self.auto_backup_cb)

        layout.addWidget(backup_group)

        # 清理设置
        clean_group = QGroupBox("清理设置")
        clean_layout = QVBoxLayout(clean_group)

        self.confirm_each_cb = QCheckBox("每个项目单独确认")
        self.confirm_each_cb.setChecked(False)
        clean_layout.addWidget(self.confirm_each_cb)

        self.show_details_cb = QCheckBox("显示详细清理过程")
        self.show_details_cb.setChecked(True)
        clean_layout.addWidget(self.show_details_cb)

        layout.addWidget(clean_group)

        # 按钮
        button_layout = QHBoxLayout()

        clear_stats_btn = QPushButton("🗑️ 清空统计")
        clear_stats_btn.clicked.connect(self._clear_stats)
        button_layout.addWidget(clear_stats_btn)

        button_layout.addStretch()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

        dialog.exec_()

    def _clear_stats(self):
        """清空统计数据"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有清理统计数据吗？\n\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                stats_file = os.path.join(os.path.expanduser("~"), ".augment_cleanup_stats.json")
                if os.path.exists(stats_file):
                    os.remove(stats_file)
                QMessageBox.information(self, "清空完成", "统计数据已清空")
            except Exception as e:
                QMessageBox.warning(self, "清空失败", f"清空统计数据失败: {e}")

    def _handle_error(self, operation, error, show_dialog=True):
        """
        统一错误处理方法

        Args:
            operation (str): 操作名称
            error (Exception): 错误对象
            show_dialog (bool): 是否显示错误对话框
        """
        error_msg = f"{operation}失败: {str(error)}"
        self.logger.error(error_msg)

        if show_dialog:
            # 根据错误类型选择合适的提示
            if isinstance(error, PermissionError):
                QMessageBox.warning(
                    self,
                    "权限错误",
                    f"{operation}失败：权限不足\n\n"
                    f"请尝试以管理员身份运行程序，或检查文件/文件夹权限。\n\n"
                    f"详细错误：{str(error)}"
                )
            elif isinstance(error, FileNotFoundError):
                QMessageBox.warning(
                    self,
                    "文件未找到",
                    f"{operation}失败：文件或文件夹不存在\n\n"
                    f"可能文件已被移动或删除。\n\n"
                    f"详细错误：{str(error)}"
                )
            elif isinstance(error, OSError):
                QMessageBox.warning(
                    self,
                    "系统错误",
                    f"{operation}失败：系统操作错误\n\n"
                    f"可能是磁盘空间不足或文件被占用。\n\n"
                    f"详细错误：{str(error)}"
                )
            else:
                QMessageBox.critical(
                    self,
                    "操作错误",
                    f"{operation}失败\n\n"
                    f"详细错误：{str(error)}\n\n"
                    f"如果问题持续存在，请联系技术支持。"
                )

    def _safe_execute(self, func, operation_name, *args, **kwargs):
        """
        安全执行方法，自动处理异常

        Args:
            func: 要执行的函数
            operation_name (str): 操作名称
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            tuple: (success, result)
        """
        try:
            result = func(*args, **kwargs)
            return True, result
        except Exception as e:
            self._handle_error(operation_name, e)
            return False, None

    def _validate_path(self, path, operation_name="路径验证"):
        """
        验证路径有效性

        Args:
            path (str): 要验证的路径
            operation_name (str): 操作名称

        Returns:
            bool: 路径是否有效
        """
        if not path:
            self._handle_error(operation_name, ValueError("路径为空"))
            return False

        if not os.path.exists(path):
            self._handle_error(operation_name, FileNotFoundError(f"路径不存在: {path}"))
            return False

        return True

    def _check_disk_space(self, path, required_space=0):
        """
        检查磁盘空间

        Args:
            path (str): 检查路径
            required_space (int): 需要的空间（字节）

        Returns:
            bool: 空间是否足够
        """
        try:
            if platform.system() == "Windows":
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(path),
                    ctypes.pointer(free_bytes),
                    None,
                    None
                )
                available_space = free_bytes.value
            else:
                statvfs = os.statvfs(path)
                available_space = statvfs.f_frsize * statvfs.f_bavail

            if available_space < required_space:
                QMessageBox.warning(
                    self,
                    "磁盘空间不足",
                    f"可用空间: {self._format_size(available_space)}\n"
                    f"需要空间: {self._format_size(required_space)}\n\n"
                    f"请清理磁盘空间后重试。"
                )
                return False

            return True

        except Exception as e:
            self.logger.warning(f"检查磁盘空间失败: {e}")
            return True  # 检查失败时假设空间足够

    def _quick_reset_machine_id(self):
        """
        快速重置机器码
        """
        # 查找机器码重置项目
        machine_id_items = [item for item in self.cleanup_items if item.get('category') == 'machine_id']

        if not machine_id_items:
            QMessageBox.warning(self, "警告", "未找到机器码重置项目！")
            return

        # 特殊确认对话框 - 完善警告信息
        reply = QMessageBox.question(
            self,
            "🔑 重置机器码",
            "⚠️ 即将重置所有开发工具的机器码和设备指纹！\n\n"
            "🎯 影响的软件：\n"
            "• VSCode/Cursor - 机器ID、遥测ID、试用状态\n"
            "• JetBrains系列 - 设备ID、许可证、评估密钥\n"
            "• Sublime Text - 许可证文件、会话数据\n"
            "• Atom - 配置文件、状态数据\n"
            "• Vim/NeoVim - 配置和会话文件\n"
            "• Emacs - 配置和自动保存文件\n"
            "• Notepad++ - 配置和会话文件\n\n"
            "🔄 重置效果：\n"
            "• 清除试用限制和设备绑定\n"
            "• 重新生成唯一设备标识符\n"
            "• 删除激活状态和许可证缓存\n\n"
            "⚠️ 重要提示：\n"
            "• 重置后可能需要重新激活软件\n"
            "• 建议先关闭所有开发工具\n"
            "• 操作前会自动创建备份文件\n\n"
            "确定要继续吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self._perform_cleanup(machine_id_items)



    def _show_settings_dialog(self):
        """
        显示设置对话框
        """
        dialog = QMessageBox(self)
        dialog.setWindowTitle("⚙️ 清理设置")
        dialog.setIcon(QMessageBox.Information)

        settings_text = f"""
当前设置：

🔧 基本设置
• 备份功能：{'✅ 已启用' if self.backup_enabled else '❌ 已禁用'}
• 强制清理：{'✅ 已启用' if self.force_cleanup else '❌ 已禁用'}
• 详细日志：{'✅ 已启用' if self.verbose_logging else '❌ 已禁用'}

📊 软件选择
• 当前选择：{self.selected_software}

💡 提示
• 备份功能会在清理前自动创建备份文件
• 强制清理会忽略文件占用错误
• 详细日志会记录更多操作信息
        """

        dialog.setText(settings_text)
        dialog.addButton("关闭", QMessageBox.AcceptRole)
        dialog.exec_()




    def _create_status_bar(self, parent_layout):
        """
        创建状态栏
        """
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setFixedHeight(35)  # 优化状态栏高度

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(self.ds.SPACING['lg'], self.ds.SPACING['sm'],
                                       self.ds.SPACING['lg'], self.ds.SPACING['sm'])

        # 状态标签
        self.status_label = QLabel("📊 准备就绪，请选择要清理的项目")
        self.status_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        self.status_label.setObjectName("statusLabel")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        parent_layout.addWidget(status_frame)

    def _apply_styles(self):
        """
        应用样式表 - 基于PyQt5最佳实践（性能优化版本）
        """
        try:
            # 使用缓存避免重复计算样式表
            if hasattr(self, '_cached_styles'):
                self.setStyleSheet(self._cached_styles)
                return

            # 确保颜色值是字符串格式
            colors = self.ds.COLORS
            fonts = self.ds.FONTS

            # 验证必要的颜色值存在
            required_colors = ['bg_primary', 'text_primary', 'bg_secondary', 'glass_border']
            for color_key in required_colors:
                if color_key not in colors:
                    self.logger.warning(f"缺少颜色定义: {color_key}")
                    return

            style = f"""
        /* 主窗口样式 */
        QWidget {{
            background-color: {colors['bg_primary']};
            color: {colors['text_primary']};
            font-family: '{fonts['primary']}';
        }}

        /* 框架样式 */
        QFrame {{
            background-color: {colors['bg_secondary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 8px;
        }}

        /* 标题样式 */
        QLabel#titleLabel {{
            color: {colors['neon_orange']};
            font-weight: bold;
            background: transparent;
            border: none;
        }}

        QLabel#descLabel {{
            color: {colors['text_secondary']};
            background: transparent;
            border: none;
        }}

        QLabel#panelTitle {{
            color: {colors['text_primary']};
            font-weight: bold;
            background: transparent;
            border: none;
        }}

        QLabel#statusLabel {{
            color: {colors['text_secondary']};
            background: transparent;
            border: none;
        }}

        QLabel#selectorLabel {{
            color: {colors['text_primary']};
            background: transparent;
            border: none;
            font-weight: bold;
        }}

        /* 软件选择器样式 */
        QComboBox#softwareSelector {{
            background-color: {colors['bg_tertiary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            padding: 8px 12px;
            font-weight: bold;
        }}

        QComboBox#softwareSelector:hover {{
            border-color: {colors['neon_cyan']};
            background-color: {colors['glass_bg']};
        }}

        QComboBox#softwareSelector::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox#softwareSelector QAbstractItemView {{
            background-color: {colors['bg_secondary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            selection-background-color: {colors['neon_cyan']}33;
        }}

        QComboBox#softwareSelector QAbstractItemView::item {{
            padding: 8px 12px;
            border-bottom: 1px solid {colors['glass_border']};
        }}

        QComboBox#softwareSelector QAbstractItemView::item:selected {{
            background-color: {colors['neon_cyan']}33;
            color: {colors['neon_cyan']};
        }}

        /* 清理列表样式 */
        QTreeWidget#cleanupTree {{
            background-color: {colors['bg_tertiary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            selection-background-color: {colors['neon_orange']}33;
            alternate-background-color: {colors['bg_primary']};
        }}

        QTreeWidget#cleanupTree::item {{
            padding: 8px;
            border-bottom: 1px solid {colors['glass_border']};
        }}

        QTreeWidget#cleanupTree::item:selected {{
            background-color: {colors['neon_orange']}33;
            color: {colors['neon_orange']};
        }}

        /* 结果文本样式 */
        QTextEdit#resultText {{
            background-color: {colors['bg_tertiary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            padding: 12px;
        }}

        /* 按钮样式 */
        QPushButton#startButton {{
            background-color: {colors['neon_green']};
            color: {colors['bg_primary']};
            border: none;
            border-radius: 6px;
            padding: 12px 24px;
            font-weight: bold;
        }}

        QPushButton#startButton:hover {{
            background-color: {colors['neon_cyan']};
        }}

        QPushButton#refreshButton {{
            background-color: {colors['bg_tertiary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            padding: 8px 16px;
        }}

        QPushButton#refreshButton:hover {{
            background-color: {colors['glass_bg']};
        }}

        /* 进度条样式 */
        QProgressBar#progressBar {{
            background-color: {colors['bg_tertiary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            text-align: center;
        }}

        QProgressBar#progressBar::chunk {{
            background-color: {colors['neon_green']};
            border-radius: 6px;
        }}

        /* 简化的操作面板样式 */

        QPushButton#quickCleanButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['neon_green']}, stop:1 {colors['neon_cyan']});
            color: {colors['text_primary']};
            border: none;
            border-radius: 8px;
            font-weight: bold;
            padding: 8px 16px;
        }}

        QPushButton#quickCleanButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['neon_cyan']}, stop:1 {colors['neon_green']});
            border: 2px solid {colors['neon_cyan']};
        }}

        QPushButton#resetMachineIdButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['neon_pink']}, stop:1 {colors['neon_purple']});
            color: {colors['text_primary']};
            border: none;
            border-radius: 8px;
            font-weight: bold;
            padding: 8px 16px;
        }}

        QPushButton#resetMachineIdButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['neon_purple']}, stop:1 {colors['neon_pink']});
            border: 2px solid {colors['neon_purple']};
        }}

        QPushButton#scanButton, QPushButton#settingsButton {{
            background-color: {colors['bg_tertiary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 8px;
            font-weight: bold;
            padding: 8px 16px;
        }}

        QPushButton#scanButton:hover, QPushButton#settingsButton:hover {{
            background-color: {colors['glass_bg']};
            border-color: {colors['neon_cyan']};
            border: 2px solid {colors['neon_cyan']};
        }}

        /* 简化的按钮样式 */
            padding: 6px 12px;
        }}

        QPushButton#cleanButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['neon_red']}, stop:1 {colors['neon_orange']});
        }}

        /* 结果面板样式 */
        QFrame#resultFrame {{
            background-color: {colors['bg_secondary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 12px;
        }}

        QLabel#resultTitle {{
            color: {colors['neon_purple']};
            font-weight: bold;
            background: transparent;
            border: none;
        }}

        /* 滚动区域样式 */
        QScrollArea#mainScrollArea {{
            background-color: transparent;
            border: none;
        }}

        QScrollArea#mainScrollArea QScrollBar:vertical {{
            background-color: {colors['bg_tertiary']};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollArea#mainScrollArea QScrollBar::handle:vertical {{
            background-color: {colors['neon_cyan']};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollArea#mainScrollArea QScrollBar::handle:vertical:hover {{
            background-color: {colors['neon_blue']};
        }}
        """

            # 缓存样式表以提高性能
            self._cached_styles = style
            self.setStyleSheet(style)

        except Exception as e:
            self.logger.error(f"样式表应用失败: {e}")
            # 应用基础样式作为备选
            basic_style = """
            QWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Microsoft YaHei UI';
            }
            """
            self.setStyleSheet(basic_style)

    # ==================== 初始化方法 ====================

    def _initialize_cleanup_items(self):
        """初始化清理项目 - 学习enhanced_vscode_page.py的详细分类"""
        self.cleanup_items = []

        # 获取VSCode路径
        vscode_paths = self._get_vscode_paths()

        # 如果没有找到VSCode路径，添加一些基本的清理项目用于演示
        if not vscode_paths:
            self._add_default_cleanup_items()
            # 更新界面显示
            self._update_cleanup_tree()
            return

        # 定义清理项目类别 - 基于网络搜索结果完善，增加更多清理选项
        cleanup_categories = {
            'cache': {
                'name': '缓存文件',
                'description': '清理VSCode缓存文件，释放磁盘空间',
                'recommended': True,
                'paths': ['CachedExtensions', 'logs', 'CachedData', 'CachedExtensionVSIXs', 'GPUCache']
            },
            'extensions': {
                'name': '扩展数据',
                'description': '清理已安装的扩展和扩展数据',
                'recommended': False,
                'paths': ['extensions']
            },
            'extension_cache': {
                'name': '扩展缓存',
                'description': '清理扩展运行时产生的缓存和临时文件',
                'recommended': True,
                'paths': ['User/workspaceStorage', 'User/History', 'User/CachedExtensions', 'User/globalStorage']
            },
            'workspace_storage': {
                'name': '工作区存储',
                'description': '清理工作区配置、历史记录和状态文件',
                'recommended': True,
                'paths': ['User/workspaceStorage', 'Workspaces', 'Backups', 'User/History']
            },
            'user_settings': {
                'name': '用户设置',
                'description': '重置用户配置、快捷键和代码片段',
                'recommended': False,
                'paths': ['User/settings.json', 'User/keybindings.json', 'User/snippets']
            },
            'recent_files': {
                'name': '最近文件',
                'description': '清理最近打开的文件和文件夹历史',
                'recommended': True,
                'paths': ['User/globalStorage/storage.json', 'User/History', 'storage.json']
            },
            'crash_dumps': {
                'name': '崩溃转储',
                'description': '清理崩溃日志和错误报告文件',
                'recommended': True,
                'paths': ['crashDumps', 'logs/main.log', 'logs/renderer*.log', 'logs/window*.log']
            },
            'machine_id': {
                'name': '🔑 机器码重置',
                'description': '重置VSCode/Cursor机器标识符和遥测ID，可能需要重新激活',
                'recommended': True,
                'paths': ['machineid', 'User/globalStorage/storage.json', 'User/workspaceStorage', 'logs'],
                'warning': '⚠️ 重置后可能需要重新激活软件'
            },
            'temp_files': {
                'name': '临时文件',
                'description': '清理临时文件和下载缓存',
                'recommended': True,
                'paths': ['tmp', 'temp', 'User/CachedData', 'DawnCache']
            },
            'session_data': {
                'name': '会话数据',
                'description': '清理会话存储和状态文件',
                'recommended': True,
                'paths': ['User/globalStorage', 'Session Storage', 'Local Storage']
            },
            'network_cache': {
                'name': '网络缓存',
                'description': '清理网络请求缓存和离线数据',
                'recommended': True,
                'paths': ['Network Persistent State', 'TransportSecurity', 'blob_storage']
            },
            'search_history': {
                'name': '搜索历史',
                'description': '清理搜索历史和查找记录',
                'recommended': True,
                'paths': ['User/globalStorage/state.vscdb', 'User/globalStorage/state.vscdb.backup']
            }
        }

        # 为每个存在的软件路径创建清理项目
        machine_id_added = False  # 防止重复添加机器码重置

        for base_path, app_name in vscode_paths:
            if os.path.exists(base_path):
                # 为每个类别创建一个清理项目
                for category_id, category_info in cleanup_categories.items():
                    # 特殊处理机器码重置，避免重复
                    if category_id == 'machine_id':
                        if machine_id_added:
                            continue
                        machine_id_added = True
                        # 机器码重置是全局的，不针对特定软件
                        item = {
                            'id': 'machine_id_global',
                            'name': '机器码重置 (全局)',
                            'description': '重置所有VSCode/Cursor的机器标识符和遥测ID',
                            'category': category_id,
                            'app_name': 'Global',
                            'base_path': '',  # 全局操作
                            'paths': category_info['paths'],
                            'recommended': category_info['recommended'],
                            'size': 1024,  # 估算大小
                            'enabled': category_info['recommended']
                        }
                        self.cleanup_items.append(item)
                        continue

                    # 检查该路径下是否存在相关的子路径
                    has_valid_paths = False
                    for sub_path in category_info['paths']:
                        full_path = os.path.join(base_path, sub_path)
                        if os.path.exists(full_path):
                            has_valid_paths = True
                            break

                    if has_valid_paths:
                        item = {
                            'id': f"{category_id}_{app_name.lower().replace(' ', '_')}",
                            'name': f"{category_info['name']} ({app_name})",
                            'description': category_info['description'],
                            'category': category_id,
                            'app_name': app_name,
                            'base_path': base_path,
                            'paths': category_info['paths'],
                            'recommended': category_info['recommended'],
                            'size': 0,
                            'enabled': category_info['recommended']
                        }
                        self.cleanup_items.append(item)

    def _get_vscode_paths(self):
        """获取开发工具路径列表 - 支持多种开发工具"""
        system = platform.system()
        paths = []
        user_home = os.path.expanduser("~")

        if system == "Windows":
            # Windows路径
            paths.extend([
                # VSCode
                (os.path.join(user_home, "AppData", "Roaming", "Code"), "VSCode"),
                (os.path.join(user_home, ".vscode"), "VSCode用户配置"),

                # Cursor
                (os.path.join(user_home, "AppData", "Roaming", "Cursor"), "Cursor"),
                (os.path.join(user_home, ".cursor"), "Cursor用户配置"),

                # Sublime Text
                (os.path.join(user_home, "AppData", "Roaming", "Sublime Text 3"), "Sublime Text 3"),
                (os.path.join(user_home, "AppData", "Roaming", "Sublime Text"), "Sublime Text 4"),

                # Atom
                (os.path.join(user_home, "AppData", "Roaming", ".atom"), "Atom"),
                (os.path.join(user_home, ".atom"), "Atom用户配置"),

                # JetBrains系列IDE
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "WebStorm2023.3"), "WebStorm 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "WebStorm2024.1"), "WebStorm 2024.1"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "IntelliJIdea2023.3"), "IntelliJ IDEA 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "IntelliJIdea2024.1"), "IntelliJ IDEA 2024.1"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "PyCharm2023.3"), "PyCharm 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "PyCharm2024.1"), "PyCharm 2024.1"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "PhpStorm2023.3"), "PhpStorm 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "PhpStorm2024.1"), "PhpStorm 2024.1"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "CLion2023.3"), "CLion 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "CLion2024.1"), "CLion 2024.1"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "GoLand2023.3"), "GoLand 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "GoLand2024.1"), "GoLand 2024.1"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "RubyMine2023.3"), "RubyMine 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "RubyMine2024.1"), "RubyMine 2024.1"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "DataGrip2023.3"), "DataGrip 2023.3"),
                (os.path.join(user_home, "AppData", "Roaming", "JetBrains", "DataGrip2024.1"), "DataGrip 2024.1"),

                # 其他编辑器
                (os.path.join(user_home, "AppData", "Roaming", "Notepad++"), "Notepad++"),
                (os.path.join(user_home, "AppData", "Roaming", "Code - OSS"), "VSCode OSS"),
                (os.path.join(user_home, "AppData", "Roaming", "VSCodium"), "VSCodium"),
                (os.path.join(user_home, "AppData", "Roaming", "Vim"), "Vim"),
                (os.path.join(user_home, "AppData", "Roaming", "Emacs"), "Emacs"),

                # 通用开发工具缓存
                (os.path.join(user_home, "AppData", "Local", "Temp"), "系统临时文件"),
                (os.path.join(user_home, "AppData", "Local", "Microsoft", "Windows", "INetCache"), "IE缓存"),
            ])
        elif system == "Darwin":  # macOS
            paths.extend([
                # VSCode
                (os.path.join(user_home, "Library", "Application Support", "Code"), "VSCode"),
                (os.path.join(user_home, ".vscode"), "VSCode用户配置"),

                # Cursor
                (os.path.join(user_home, "Library", "Application Support", "Cursor"), "Cursor"),
                (os.path.join(user_home, ".cursor"), "Cursor用户配置"),

                # Sublime Text
                (os.path.join(user_home, "Library", "Application Support", "Sublime Text 3"), "Sublime Text 3"),
                (os.path.join(user_home, "Library", "Application Support", "Sublime Text"), "Sublime Text 4"),

                # Atom
                (os.path.join(user_home, ".atom"), "Atom"),

                # JetBrains系列IDE
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "WebStorm2023.3"), "WebStorm 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "WebStorm2024.1"), "WebStorm 2024.1"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "IntelliJIdea2023.3"), "IntelliJ IDEA 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "IntelliJIdea2024.1"), "IntelliJ IDEA 2024.1"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "PyCharm2023.3"), "PyCharm 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "PyCharm2024.1"), "PyCharm 2024.1"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "PhpStorm2023.3"), "PhpStorm 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "PhpStorm2024.1"), "PhpStorm 2024.1"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "CLion2023.3"), "CLion 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "CLion2024.1"), "CLion 2024.1"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "GoLand2023.3"), "GoLand 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "GoLand2024.1"), "GoLand 2024.1"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "RubyMine2023.3"), "RubyMine 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "RubyMine2024.1"), "RubyMine 2024.1"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "DataGrip2023.3"), "DataGrip 2023.3"),
                (os.path.join(user_home, "Library", "Application Support", "JetBrains", "DataGrip2024.1"), "DataGrip 2024.1"),

                # 其他编辑器
                (os.path.join(user_home, "Library", "Application Support", "Code - OSS"), "VSCode OSS"),
                (os.path.join(user_home, "Library", "Application Support", "VSCodium"), "VSCodium"),
                (os.path.join(user_home, ".vim"), "Vim"),
                (os.path.join(user_home, ".emacs.d"), "Emacs"),

                # 通用缓存
                (os.path.join(user_home, "Library", "Caches"), "系统缓存"),
            ])
        else:  # Linux
            paths.extend([
                # VSCode
                (os.path.join(user_home, ".config", "Code"), "VSCode"),
                (os.path.join(user_home, ".vscode"), "VSCode用户配置"),

                # Cursor
                (os.path.join(user_home, ".config", "Cursor"), "Cursor"),
                (os.path.join(user_home, ".cursor"), "Cursor用户配置"),

                # Sublime Text
                (os.path.join(user_home, ".config", "sublime-text-3"), "Sublime Text 3"),
                (os.path.join(user_home, ".config", "sublime-text"), "Sublime Text 4"),

                # Atom
                (os.path.join(user_home, ".atom"), "Atom"),

                # JetBrains系列IDE
                (os.path.join(user_home, ".config", "JetBrains", "WebStorm2023.3"), "WebStorm 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "WebStorm2024.1"), "WebStorm 2024.1"),
                (os.path.join(user_home, ".config", "JetBrains", "IntelliJIdea2023.3"), "IntelliJ IDEA 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "IntelliJIdea2024.1"), "IntelliJ IDEA 2024.1"),
                (os.path.join(user_home, ".config", "JetBrains", "PyCharm2023.3"), "PyCharm 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "PyCharm2024.1"), "PyCharm 2024.1"),
                (os.path.join(user_home, ".config", "JetBrains", "PhpStorm2023.3"), "PhpStorm 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "PhpStorm2024.1"), "PhpStorm 2024.1"),
                (os.path.join(user_home, ".config", "JetBrains", "CLion2023.3"), "CLion 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "CLion2024.1"), "CLion 2024.1"),
                (os.path.join(user_home, ".config", "JetBrains", "GoLand2023.3"), "GoLand 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "GoLand2024.1"), "GoLand 2024.1"),
                (os.path.join(user_home, ".config", "JetBrains", "RubyMine2023.3"), "RubyMine 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "RubyMine2024.1"), "RubyMine 2024.1"),
                (os.path.join(user_home, ".config", "JetBrains", "DataGrip2023.3"), "DataGrip 2023.3"),
                (os.path.join(user_home, ".config", "JetBrains", "DataGrip2024.1"), "DataGrip 2024.1"),

                # 其他编辑器
                (os.path.join(user_home, ".config", "Code - OSS"), "VSCode OSS"),
                (os.path.join(user_home, ".config", "VSCodium"), "VSCodium"),
                (os.path.join(user_home, ".vim"), "Vim"),
                (os.path.join(user_home, ".config", "nvim"), "NeoVim"),
                (os.path.join(user_home, ".emacs.d"), "Emacs"),

                # 通用缓存
                (os.path.join(user_home, ".cache"), "用户缓存"),
            ])

        return paths

    # ==================== 功能方法 ====================

    def _scan_vscode_files(self):
        """
        扫描VSCode相关文件和文件夹 - 增强版本

        Returns:
            list: 清理项目列表
        """
        cleanup_items = []

        try:
            # 获取用户目录
            user_home = os.path.expanduser("~")

            # 定义VSCode相关路径 - 扩展支持更多软件和路径
            vscode_paths = {
                'vscode': {
                    'name': 'Visual Studio Code',
                    'paths': [
                        os.path.join(user_home, 'AppData', 'Roaming', 'Code'),
                        os.path.join(user_home, '.vscode'),
                        os.path.join(user_home, 'AppData', 'Local', 'Programs', 'Microsoft VS Code'),
                        os.path.join(user_home, 'AppData', 'Local', 'Microsoft', 'vscode-cpptools'),
                        os.path.join(user_home, 'AppData', 'Local', 'vscode-eslint'),
                        os.path.join(user_home, 'AppData', 'Local', 'vscode-typescript')
                    ]
                },
                'cursor': {
                    'name': 'Cursor',
                    'paths': [
                        os.path.join(user_home, 'AppData', 'Roaming', 'Cursor'),
                        os.path.join(user_home, '.cursor'),
                        os.path.join(user_home, 'AppData', 'Local', 'Programs', 'cursor'),
                        os.path.join(user_home, 'AppData', 'Local', 'cursor-updater')
                    ]
                },
                'sublime': {
                    'name': 'Sublime Text',
                    'paths': [
                        os.path.join(user_home, 'AppData', 'Roaming', 'Sublime Text'),
                        os.path.join(user_home, 'AppData', 'Roaming', 'Sublime Text 3'),
                        os.path.join(user_home, 'AppData', 'Roaming', 'Sublime Text 4'),
                        os.path.join(user_home, 'AppData', 'Local', 'Sublime Text 3'),
                        os.path.join(user_home, 'AppData', 'Local', 'Sublime Text 4')
                    ]
                },
                'atom': {
                    'name': 'Atom',
                    'paths': [
                        os.path.join(user_home, 'AppData', 'Roaming', '.atom'),
                        os.path.join(user_home, '.atom'),
                        os.path.join(user_home, 'AppData', 'Local', 'atom')
                    ]
                },
                'webstorm': {
                    'name': 'WebStorm/JetBrains',
                    'paths': [
                        os.path.join(user_home, 'AppData', 'Roaming', 'JetBrains'),
                        os.path.join(user_home, 'AppData', 'Local', 'JetBrains'),
                        os.path.join(user_home, '.WebStorm'),
                        os.path.join(user_home, '.IntelliJIdea')
                    ]
                },
                'notepadpp': {
                    'name': 'Notepad++',
                    'paths': [
                        os.path.join(user_home, 'AppData', 'Roaming', 'Notepad++'),
                        os.path.join(user_home, 'AppData', 'Local', 'Notepad++')
                    ]
                },
                'brackets': {
                    'name': 'Brackets',
                    'paths': [
                        os.path.join(user_home, 'AppData', 'Roaming', 'Brackets'),
                        os.path.join(user_home, '.brackets')
                    ]
                },
                'vim': {
                    'name': 'Vim/NeoVim',
                    'paths': [
                        os.path.join(user_home, '.vim'),
                        os.path.join(user_home, '.vimrc'),
                        os.path.join(user_home, 'AppData', 'Local', 'nvim'),
                        os.path.join(user_home, '.config', 'nvim')
                    ]
                }
            }

            # 增强的清理类别定义
            cleanup_categories = {
                'cache': {
                    'name': '缓存文件',
                    'description': '清理软件缓存文件，释放磁盘空间',
                    'recommended': True,
                    'paths': ['CachedExtensions', 'logs', 'CachedData', 'CachedExtensionVSIXs', 'GPUCache', 'ShaderCache', 'DawnCache']
                },
                'extensions': {
                    'name': '扩展数据',
                    'description': '清理已安装的扩展和扩展数据',
                    'recommended': False,
                    'paths': ['extensions', 'Extensions']
                },
                'extension_cache': {
                    'name': '扩展缓存',
                    'description': '清理扩展运行时产生的缓存和临时文件',
                    'recommended': True,
                    'paths': ['User/workspaceStorage', 'User/History', 'User/CachedExtensions', 'User/globalStorage']
                },
                'workspace_storage': {
                    'name': '工作区存储',
                    'description': '清理工作区配置、历史记录和状态文件',
                    'recommended': True,
                    'paths': ['User/workspaceStorage', 'Workspaces', 'Backups', 'User/History']
                },
                'user_settings': {
                    'name': '用户设置',
                    'description': '重置用户配置、快捷键和代码片段',
                    'recommended': False,
                    'paths': ['User/settings.json', 'User/keybindings.json', 'User/snippets']
                },
                'recent_files': {
                    'name': '最近文件',
                    'description': '清理最近打开的文件和文件夹历史',
                    'recommended': True,
                    'paths': ['User/globalStorage/storage.json', 'User/History', 'storage.json']
                },
                'crash_dumps': {
                    'name': '崩溃转储',
                    'description': '清理崩溃日志和错误报告文件',
                    'recommended': True,
                    'paths': ['crashDumps', 'logs/main.log', 'logs/renderer*.log', 'logs/window*.log']
                },
                'machine_id': {
                    'name': '🔑 机器码重置',
                    'description': '重置软件机器标识符和遥测ID，可能需要重新激活',
                    'recommended': True,
                    'paths': ['machineid', 'User/globalStorage/storage.json', 'User/workspaceStorage', 'logs'],
                    'warning': '⚠️ 重置后可能需要重新激活软件'
                },
                'temp_files': {
                    'name': '临时文件',
                    'description': '清理临时文件和下载缓存',
                    'recommended': True,
                    'paths': ['tmp', 'temp', 'User/CachedData', 'DawnCache', 'Temp']
                },
                'session_data': {
                    'name': '会话数据',
                    'description': '清理会话存储和状态文件',
                    'recommended': True,
                    'paths': ['User/globalStorage', 'Session Storage', 'Local Storage']
                },
                'network_cache': {
                    'name': '网络缓存',
                    'description': '清理网络请求缓存和离线数据',
                    'recommended': True,
                    'paths': ['Network Persistent State', 'TransportSecurity', 'blob_storage']
                },
                'search_history': {
                    'name': '搜索历史',
                    'description': '清理搜索历史和查找记录',
                    'recommended': True,
                    'paths': ['User/globalStorage/state.vscdb', 'User/globalStorage/state.vscdb.backup']
                }
            }

            # 扫描每个软件的路径
            machine_id_added = False  # 防止重复添加机器码重置

            for software_key, software_info in vscode_paths.items():
                software_name = software_info['name']

                for base_path in software_info['paths']:
                    if os.path.exists(base_path):
                        self.logger.info(f"🔍 发现 {software_name} 路径: {base_path}")

                        # 为每个类别创建清理项目
                        for category_key, category_info in cleanup_categories.items():
                            category_paths = []
                            total_size = 0

                            # 检查该类别下的所有路径
                            for sub_path in category_info['paths']:
                                full_path = os.path.join(base_path, sub_path)
                                if os.path.exists(full_path):
                                    category_paths.append(sub_path)
                                    try:
                                        if os.path.isfile(full_path):
                                            total_size += os.path.getsize(full_path)
                                        else:
                                            total_size += self._get_folder_size_fast(full_path)
                                    except (OSError, PermissionError):
                                        pass  # 忽略权限错误

                            # 如果找到了文件，创建清理项目
                            if category_paths:
                                # 特殊处理机器码重置（只添加一次）
                                if category_key == 'machine_id':
                                    if not machine_id_added:
                                        cleanup_items.append({
                                            'name': f"{category_info['name']} (所有软件)",
                                            'category': category_key,
                                            'software': 'all',
                                            'base_path': base_path,
                                            'paths': category_paths,
                                            'size': total_size,
                                            'description': category_info['description'],
                                            'recommended': category_info['recommended'],
                                            'enabled': category_info['recommended'],
                                            'warning': category_info.get('warning', '')
                                        })
                                        machine_id_added = True
                                else:
                                    cleanup_items.append({
                                        'name': f"{software_name} - {category_info['name']}",
                                        'category': category_key,
                                        'software': software_key,
                                        'base_path': base_path,
                                        'paths': category_paths,
                                        'size': total_size,
                                        'description': category_info['description'],
                                        'recommended': category_info['recommended'],
                                        'enabled': category_info['recommended']
                                    })

            self.logger.info(f"✅ 扫描完成，发现 {len(cleanup_items)} 个清理项目")
            return cleanup_items

        except Exception as e:
            self.logger.error(f"扫描VSCode文件失败: {e}")
            return []

    def _get_folder_size_fast(self, folder_path):
        """
        快速计算文件夹大小

        Args:
            folder_path (str): 文件夹路径

        Returns:
            int: 文件夹大小（字节）
        """
        total_size = 0
        try:
            # 使用os.walk遍历文件夹
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    try:
                        file_path = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(file_path)
                    except (OSError, PermissionError):
                        # 忽略无法访问的文件
                        continue
        except (OSError, PermissionError):
            # 忽略无法访问的文件夹
            pass
        return total_size

    def _scan_vscode_data(self):
        """扫描VSCode数据 - 树形列表版本"""
        try:
            # 更新状态
            self.status_label.setText("🔍 正在扫描VSCode数据...")
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setVisible(True)

            # 禁用按钮
            self.quick_clean_btn.setEnabled(False)
            self.reset_machine_id_btn.setEnabled(False)
            self.scan_btn.setEnabled(False)


            # 扫描VSCode数据
            self.cleanup_items = self._scan_vscode_files()

            # 使用 AugmentCleaner 扫描 Augment 相关数据
            self._scan_augment_data_with_cleaner()

            # 如果没有找到任何项目，添加默认项目
            if not self.cleanup_items:
                self.logger.info("未找到VSCode文件，添加默认清理项目")
                self._add_default_cleanup_items()

            # 初始化软件筛选
            if not hasattr(self, 'selected_software'):
                self.selected_software = "all"

            # 更新树形列表
            self._update_cleanup_tree()

            # 计算统计信息
            total_items = len(self.cleanup_items)
            total_size = sum(item.get('size', 0) for item in self.cleanup_items)

            # 显示扫描结果
            result_text = f"""📊 扫描结果：

🔍 扫描完成！
• 发现项目：{total_items} 个
• 总大小：{self._format_size(total_size)}

📋 扫描到的项目类型："""

            # 按类别统计
            categories = {}
            for item in self.cleanup_items:
                category = item.get('category', 'other')
                if category not in categories:
                    categories[category] = 0
                categories[category] += 1

            for category, count in categories.items():
                result_text += f"\n• {category}: {count} 个项目"

            result_text += f"\n\n💡 在上方列表中勾选要清理的项目，然后使用操作按钮"

            self.result_text.setPlainText(result_text)

        except Exception as e:
            self.logger.error(f"扫描VSCode数据失败: {e}")
            self.status_label.setText(f"❌ 扫描失败: {e}")
            self.result_text.setPlainText(f"❌ 扫描失败：{e}")

        finally:
            # 恢复按钮状态
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setVisible(False)
            self.quick_clean_btn.setEnabled(True)
            self.reset_machine_id_btn.setEnabled(True)
            self.scan_btn.setEnabled(True)



    def _scan_augment_data_with_cleaner(self):
        """使用 AugmentCleaner 扫描 Augment 相关数据"""
        try:
            self.logger.info("🔍 使用 AugmentCleaner 扫描 Augment 数据...")

            # 扫描 VSCode 和 Cursor 的 Augment 数据
            for editor_type in ['vscode', 'cursor']:
                found_data = self.augment_cleaner.scan_augment_data(editor_type)

                # 将扫描结果转换为清理项目
                for category, items in found_data.items():
                    if items:
                        # 创建 Augment 专用清理项目
                        augment_item = {
                            'name': f'🎯 Augment {category.replace("_", " ").title()} ({editor_type.upper()})',
                            'description': f'清理 {editor_type.upper()} 中的 Augment {category.replace("_", " ")} 数据',
                            'category': f'augment_{category}',
                            'editor_type': editor_type,
                            'paths': items,
                            'size': self._calculate_augment_data_size(items),
                            'recommended': True,
                            'enabled': False,
                            'risk_level': 'low' if category in ['logs', 'cache'] else 'medium'
                        }

                        # 避免重复添加
                        existing_item = next((item for item in self.cleanup_items
                                            if item.get('name') == augment_item['name']), None)
                        if not existing_item:
                            self.cleanup_items.append(augment_item)
                            self.logger.info(f"添加 Augment 清理项目: {augment_item['name']} ({len(items)} 个文件)")

        except Exception as e:
            self.logger.error(f"AugmentCleaner 扫描失败: {e}")

    def _calculate_augment_data_size(self, file_paths):
        """计算 Augment 数据文件的总大小"""
        total_size = 0
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    if os.path.isfile(file_path):
                        total_size += os.path.getsize(file_path)
                    elif os.path.isdir(file_path):
                        total_size += self._get_folder_size_fast(file_path)
            except (OSError, PermissionError):
                continue
        return total_size




    def _calculate_item_size(self, item):
        """计算清理项目的大小"""
        try:
            total_size = 0
            base_path = item.get('base_path', '')

            if not base_path or not os.path.exists(base_path):
                return 0

            for sub_path in item.get('paths', []):
                full_path = os.path.join(base_path, sub_path)
                if os.path.exists(full_path):
                    if os.path.isfile(full_path):
                        total_size += os.path.getsize(full_path)
                    else:
                        total_size += self._get_folder_size_fast(full_path)

            return total_size
        except Exception as e:
            self.logger.error(f"计算项目大小失败 {item.get('name', '')}: {e}")
            return 0







    def _check_running_processes(self):
        """检查VSCode/Cursor是否正在运行 - 学习enhanced_vscode_page.py"""
        running_processes = []

        if not PSUTIL_AVAILABLE:
            return running_processes

        try:
            # 要检查的进程名 - 扩展支持更多编辑器
            target_processes = [
                # VSCode系列
                'Code.exe', 'code', 'Code - OSS', 'VSCodium.exe', 'vscodium',
                # Cursor
                'Cursor.exe', 'cursor',
                # JetBrains系列
                'webstorm64.exe', 'webstorm', 'idea64.exe', 'idea',
                'pycharm64.exe', 'pycharm', 'phpstorm64.exe', 'phpstorm',
                'clion64.exe', 'clion', 'goland64.exe', 'goland',
                'rubymine64.exe', 'rubymine', 'datagrip64.exe', 'datagrip',
                # Sublime Text
                'sublime_text.exe', 'sublime_text', 'subl',
                # Atom
                'atom.exe', 'atom',
                # Notepad++
                'notepad++.exe', 'notepad++',
                # Vim/NeoVim
                'vim.exe', 'vim', 'nvim.exe', 'nvim', 'gvim.exe', 'gvim',
                # Emacs
                'emacs.exe', 'emacs', 'runemacs.exe'
            ]

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name']
                    if proc_name in target_processes:
                        running_processes.append(proc_name)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except Exception as e:
            self.logger.error(f"检查进程失败: {e}")

        return running_processes

    def _reset_machine_id(self, item):
        """重置机器码 - 使用增强的 AugmentCleaner 功能"""
        try:
            self.logger.info("🔄 开始重置机器码和 Augment 数据...")

            # 使用 AugmentCleaner 进行全面清理
            results = self.augment_cleaner.clean_all_editors(create_backup=self.backup_enabled)

            success_count = 0
            total_count = len(results)

            for editor_type, success in results.items():
                if success:
                    success_count += 1
                    self.logger.info(f"✅ {editor_type.upper()} Augment 数据清理成功")
                else:
                    self.logger.error(f"❌ {editor_type.upper()} Augment 数据清理失败")

            # 继续执行传统的机器码重置
            updated_tools = []
            vscode_paths = self._get_vscode_paths()

            for base_path, app_name in vscode_paths:
                if not os.path.exists(base_path):
                    continue

                # 根据不同工具定义不同的机器码文件
                machine_id_files = self._get_machine_id_files_for_app(app_name)
                updated_files = []

                for file_name in machine_id_files:
                    file_path = os.path.join(base_path, file_name)

                    if os.path.exists(file_path):
                        try:
                            if file_name.endswith('.json'):
                                # JSON文件处理
                                if self._reset_json_machine_id(file_path, app_name):
                                    updated_files.append(file_name)
                            else:
                                # 普通文件处理
                                if self._reset_text_machine_id(file_path, app_name):
                                    updated_files.append(file_name)
                        except Exception as e:
                            self.logger.error(f"重置文件失败 {file_path}: {e}")

                if updated_files:
                    updated_tools.append(f"{app_name}({len(updated_files)}个文件)")
                    self.logger.info(f"✅ {app_name} 机器码已重置: {', '.join(updated_files)}")

            # 综合结果
            if success_count > 0 or updated_tools:
                self.logger.info(f"🎉 机器码重置完成: 专业清理({success_count}/{total_count}), 传统重置({len(updated_tools)}个工具)")
                return True
            else:
                self.logger.warning("⚠️ 未找到任何可重置的数据")
                return False

        except Exception as e:
            self.logger.error(f"重置机器码失败: {e}")
            return False

    def _get_machine_id_files_for_app(self, app_name):
        """根据应用名称获取对应的机器码文件列表 - 完善支持更多编辑器"""
        if "VSCode" in app_name or "Cursor" in app_name:
            files = [
                # 核心机器ID文件
                'machineid',
                'machine-id',
                # 全局存储和遥测数据
                'User/globalStorage/storage.json',
                'User/globalStorage/telemetry.json',
                'User/globalStorage/@vscode/telemetry/common/telemetry.json',
                # 工作区存储
                'User/workspaceStorage',
                # 日志文件（包含设备信息）
                'logs/main.log',
                'logs/renderer1.log',
                'logs/extensionHost1.log',
                'logs/sharedprocess.log',
                # 扩展相关
                'User/extensions/.obsolete',
                'User/extensions/.init-default-profile-extensions',
                # 会话和状态
                'User/state/global.json',
                'User/state/storage.json'
            ]
            # Cursor特有文件
            if "Cursor" in app_name:
                files.extend([
                    'User/globalStorage/cursor.json',
                    'User/globalStorage/cursor-settings.json',
                    'User/globalStorage/cursor-trial.json'
                ])
            return [f for f in files if f is not None]

        elif any(ide in app_name for ide in ["JetBrains", "WebStorm", "IntelliJ", "PyCharm", "PhpStorm", "CLion", "GoLand", "RubyMine", "DataGrip"]):
            return [
                # 评估和许可证文件
                'eval/idea.key',
                'eval/idea.evaluation.key',
                'eval/idea.evaluation.key.lock',
                'config/eval/idea.evaluation.key',
                'config/eval/idea.key',
                # 配置文件
                'options/other.xml',
                'options/ide.general.xml',
                'options/statistics.application.usages.xml',
                'options/usage.statistics.xml',
                # 系统和设备信息
                'options/deviceId.xml',
                'options/machineId.xml',
                'system/.home',
                'system/.lock',
                # 统计和遥测
                'statistics/applicationUsages.xml',
                'statistics/deviceId',
                'statistics/machineId'
            ]
        elif "Sublime" in app_name:
            return [
                # 许可证文件
                'License.sublime_license',
                'License.sublime-license',
                # 会话文件
                'Settings/Session.sublime_session',
                'Settings/Auto Save Session.sublime_session',
                # 用户特定文件
                'Settings/Preferences.sublime-settings',
                'Packages/User/Package Control.sublime-settings'
            ]
        elif "Atom" in app_name:
            return [
                # 配置和状态
                '.atom/config.cson',
                '.atom/storage/application.json',
                '.atom/storage/state.json',
                # 包管理
                '.atom/packages.cson',
                '.atom/.apm/.apmrc'
            ]
        elif "Vim" in app_name or "NeoVim" in app_name:
            return [
                # Vim配置
                '.vimrc',
                '.vim/viminfo',
                '.vim/sessions',
                # NeoVim配置
                '.config/nvim/init.vim',
                '.local/share/nvim/shada'
            ]
        elif "Emacs" in app_name:
            return [
                # Emacs配置
                '.emacs',
                '.emacs.d/init.el',
                '.emacs.d/elpa',
                '.emacs.d/auto-save-list'
            ]
        elif "Notepad++" in app_name:
            return [
                # Notepad++配置
                'config.xml',
                'session.xml',
                'userDefineLang.xml'
            ]
        else:
            # 通用机器码文件
            return [
                'machineid',
                'machine-id',
                'deviceid',
                'device-id',
                'storage.json',
                'globalStorage/storage.json'
            ]

    def _reset_json_machine_id(self, file_path, app_name):
        """重置JSON格式的机器码文件 - 支持更多机器码字段"""
        try:
            # 创建备份
            if self.backup_enabled:
                backup_path = f"{file_path}.backup"
                shutil.copy2(file_path, backup_path)
                self.logger.info(f"📦 已创建备份: {backup_path}")

            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 更新机器码相关字段 - 完善支持更多编辑器的字段
            updated_fields = []
            machine_id_fields = [
                # VSCode/Cursor 遥测字段
                'telemetry.machineId',
                'telemetry.devDeviceId',
                'telemetry.sqmId',
                'telemetry.sessionId',
                'telemetry.macMachineId',
                'telemetry.firstSessionDate',
                'telemetry.lastSessionDate',
                # 通用机器码字段
                'machineId',
                'deviceId',
                'sessionId',
                'installationId',
                'userId',
                'instanceId',
                'clientId',
                'hardwareId',
                'fingerprint',
                # Cursor特有字段
                'cursor.machineId',
                'cursor.deviceId',
                'cursor.trialId',
                'cursor.userId',
                'cursor.sessionId',
                # JetBrains字段
                'jetbrains.deviceId',
                'jetbrains.machineId',
                'jetbrains.installationId',
                'idea.deviceId',
                'idea.machineId',
                # 其他编辑器字段
                'sublime.deviceId',
                'sublime.machineId',
                'atom.deviceId',
                'atom.machineId'
            ]

            for field in machine_id_fields:
                if field in data:
                    data[field] = str(uuid.uuid4())
                    updated_fields.append(field)

            # 递归查找嵌套的机器码字段
            def update_nested_ids(obj, path=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        if any(id_field in key.lower() for id_field in ['machineid', 'deviceid', 'sessionid', 'installationid']):
                            if isinstance(value, str) and len(value) > 10:  # 可能是ID
                                obj[key] = str(uuid.uuid4())
                                updated_fields.append(current_path)
                        elif isinstance(value, (dict, list)):
                            update_nested_ids(value, current_path)
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        update_nested_ids(item, f"{path}[{i}]")

            update_nested_ids(data)

            # 写入更新后的数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            if updated_fields:
                self.logger.info(f"✅ {app_name} JSON机器码已更新: {', '.join(updated_fields[:5])}{'...' if len(updated_fields) > 5 else ''}")
                return True
            else:
                self.logger.info(f"ℹ️ {app_name} JSON文件中未找到机器码字段")
                return False

        except Exception as e:
            self.logger.error(f"重置JSON机器码失败: {e}")
            return False

    def _reset_text_machine_id(self, file_path, app_name):
        """重置文本格式的机器码文件 - 支持删除或重新生成"""
        try:
            # 创建备份
            if self.backup_enabled:
                backup_path = f"{file_path}.backup"
                if os.path.isfile(file_path):
                    shutil.copy2(file_path, backup_path)
                else:
                    shutil.copytree(file_path, backup_path)
                self.logger.info(f"📦 已创建备份: {backup_path}")

            # 根据文件类型决定处理方式
            if os.path.isfile(file_path):
                # 对于单个文件，生成新的机器码或删除
                if any(keyword in os.path.basename(file_path).lower() for keyword in ['license', 'key', 'eval']):
                    # 许可证文件直接删除
                    os.remove(file_path)
                    self.logger.info(f"🗑️ {app_name} 许可证文件已删除")
                else:
                    # 机器码文件重新生成
                    new_machine_id = str(uuid.uuid4())
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_machine_id)
                    self.logger.info(f"🔄 {app_name} 机器码已重新生成")
            else:
                # 对于目录，删除整个目录
                shutil.rmtree(file_path)
                self.logger.info(f"🗑️ {app_name} 机器码目录已删除")

            return True

        except Exception as e:
            self.logger.error(f"重置文本机器码失败: {e}")
            return False

    def _start_cleanup(self):
        """开始清理 - 增强版本，支持进程检查和高级功能"""
        if self.is_cleaning:
            return

        # 检查VSCode/Cursor是否正在运行
        running_processes = self._check_running_processes()
        if running_processes:
            reply = QMessageBox.question(
                self,
                "⚠️ 进程检查",
                f"检测到以下VSCode相关进程正在运行：\n{', '.join(running_processes)}\n\n建议先关闭这些程序再进行清理。\n\n是否继续清理？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        # 获取选中的项目（从卡片界面）
        selected_items = []
        for item in self.cleanup_items:
            if item.get('enabled', False):
                selected_items.append(item)

        if not selected_items:
            self.status_label.setText("⚠️ 请先选择要清理的项目")
            return

        # 检查是否包含机器码重置
        has_machine_id_reset = any(item.get('category') == 'machine_id' for item in selected_items)

        # 确认清理
        total_size = sum(item.get('size', 0) for item in selected_items)
        size_str = self._format_size(total_size)

        # 构建确认消息
        confirm_message = f"即将清理 {len(selected_items)} 个项目，总大小约 {size_str}\n\n"

        if has_machine_id_reset:
            confirm_message += "⚠️ 注意：包含机器码重置操作！\n"
            confirm_message += "• 重置后可能需要重新激活相关软件\n"
            confirm_message += "• 建议先关闭所有开发工具\n"
            confirm_message += "• 已启用备份功能保护数据\n\n"

        confirm_message += "此操作不可撤销，是否继续？"

        reply = QMessageBox.question(
            self,
            "🗑️ 确认清理" + (" - 包含机器码重置" if has_machine_id_reset else ""),
            confirm_message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.No:
            return

        # 开始清理
        self.is_cleaning = True
        # 禁用所有快速操作按钮
        self.quick_clean_btn.setEnabled(False)
        self.reset_machine_id_btn.setEnabled(False)
        self.scan_btn.setEnabled(False)
        self.settings_btn.setEnabled(False)


        # 显示进度条
        if hasattr(self, 'progress_bar') and self.progress_bar:
            self.progress_bar.setVisible(True)
            self.progress_bar.setMaximum(len(selected_items))
            self.progress_bar.setValue(0)

        # 执行清理
        self._perform_cleanup(selected_items)

    def _perform_cleanup(self, items):
        """执行清理 - 增强版本，支持不同类型的清理操作"""
        try:
            results = []
            cleaned_size = 0

            for i, item in enumerate(items):
                name = item['name']
                category = item['category']
                base_path = item['base_path']
                size = item.get('size', 0)

                self.status_label.setText(f"🧹 正在清理: {name}")
                if hasattr(self, 'progress_bar') and self.progress_bar:
                    self.progress_bar.setValue(i)

                try:
                    if category == 'machine_id':
                        # 机器码重置
                        if self._reset_machine_id(item):
                            results.append(f"✅ {name}: 机器码已重置")
                            cleaned_size += size
                        else:
                            results.append(f"⚠️ {name}: 机器码重置失败")
                    else:
                        # 普通文件/文件夹清理
                        cleaned_paths = []
                        item_cleaned_size = 0

                        for sub_path in item['paths']:
                            full_path = os.path.join(base_path, sub_path)
                            if os.path.exists(full_path):
                                try:
                                    # 创建备份（如果启用）
                                    if self.backup_enabled:
                                        backup_path = f"{full_path}.backup"
                                        if os.path.isfile(full_path):
                                            shutil.copy2(full_path, backup_path)
                                        else:
                                            shutil.copytree(full_path, backup_path)
                                        self.logger.info(f"📦 已创建备份: {backup_path}")

                                    # 计算大小
                                    if os.path.isfile(full_path):
                                        item_cleaned_size += os.path.getsize(full_path)
                                        os.remove(full_path)
                                    else:
                                        item_cleaned_size += self._get_folder_size_fast(full_path)
                                        shutil.rmtree(full_path)

                                    cleaned_paths.append(sub_path)

                                except Exception as e:
                                    self.logger.error(f"清理路径失败 {full_path}: {e}")

                        if cleaned_paths:
                            results.append(f"✅ {name}: 已清理 {len(cleaned_paths)} 个路径")
                            cleaned_size += item_cleaned_size
                        else:
                            results.append(f"⚠️ {name}: 未找到可清理的文件")

                except Exception as e:
                    results.append(f"❌ {name}: 清理失败 - {e}")
                    self.logger.error(f"清理项目失败 {name}: {e}")

                # 让界面保持响应
                QApplication.processEvents()

            # 清理完成
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setValue(len(items))
                self.progress_bar.setVisible(False)
            self.is_cleaning = False

            # 显示结果
            result_text = f"""🎉 清理完成！

清理项目: {len(items)} 个
释放空间: {self._format_size(cleaned_size)}
备份状态: {'已启用' if self.backup_enabled else '已禁用'}

📋 详细结果:
{chr(10).join(results)}

💡 提示:
• 如果启用了备份，可在原路径找到 .backup 文件
• 重启VSCode/Cursor以应用更改
• 如有问题可从备份恢复"""

            self.result_text.setPlainText(result_text)
            self.status_label.setText(f"✅ 清理完成，释放空间: {self._format_size(cleaned_size)}")

            # 显示完成对话框
            QMessageBox.information(
                self,
                "🎉 清理完成",
                f"成功清理 {len(items)} 个项目\n释放空间: {self._format_size(cleaned_size)}\n\n建议重启VSCode/Cursor以应用更改。"
            )

            # 重新扫描
            QTimer.singleShot(2000, self._scan_vscode_data)

        except Exception as e:
            self.logger.error(f"清理失败: {e}")
            self.status_label.setText(f"❌ 清理失败: {e}")

        finally:
            # 恢复按钮状态
            self.is_cleaning = False
            self.quick_clean_btn.setEnabled(True)
            self.reset_machine_id_btn.setEnabled(True)
            self.scan_btn.setEnabled(True)
            if hasattr(self, 'settings_btn'):
                self.settings_btn.setEnabled(True)

            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setVisible(False)

    def _show_default_result(self):
        """显示默认结果"""
        default_text = """🛠️ VSCode清理工具

此工具可以帮助您清理VSCode相关的缓存和临时文件。

⚠️ 注意事项：
• 清理前请确保VSCode已关闭
• 清理会删除扩展和配置，请谨慎操作
• 建议先备份重要配置文件

🔍 使用方法：
1. 点击"重新扫描"查找VSCode文件
2. 选择要清理的项目
3. 点击"开始清理"执行清理

📊 清理完成后会显示详细结果。"""

        self.result_text.setPlainText(default_text)

    # ==================== 工具方法 ====================

    def _get_folder_size(self, path):
        """获取文件夹大小 - 完整版本"""
        total_size = 0
        try:
            for dirpath, _, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, IOError):
                        pass
        except Exception:
            pass
        return total_size

    def _get_folder_size_fast(self, path):
        """获取文件夹大小 - 快速版本，限制扫描深度"""
        total_size = 0
        max_files = 1000  # 最多扫描1000个文件
        file_count = 0

        try:
            for dirpath, dirnames, filenames in os.walk(path):
                # 限制扫描深度，避免过深的目录
                level = dirpath.replace(path, '').count(os.sep)
                if level >= 3:  # 最多扫描3层深度
                    dirnames[:] = []  # 清空子目录列表，停止深入
                    continue

                for filename in filenames:
                    if file_count >= max_files:
                        # 如果文件太多，估算剩余大小
                        estimated_remaining = len(filenames) - (file_count % len(filenames))
                        if estimated_remaining > 0:
                            avg_size = total_size / max(file_count, 1)
                            total_size += avg_size * estimated_remaining
                        return total_size

                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                        file_count += 1
                    except (OSError, IOError):
                        pass
        except Exception:
            pass
        return total_size

    def _format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    # ==================== 公共接口方法 ====================

    def refresh_scan(self):
        """刷新扫描"""
        self._scan_vscode_data()

    def get_cleanup_items(self):
        """获取清理项目"""
        return self.cleanup_items

    def _save_software_selection(self):
        """保存软件选择到配置文件"""
        try:
            # 🔧 使用与main.py一致的路径检测逻辑
            import sys
            # 直接使用main.py中设置的环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                config_file = os.path.join(config_dir, ".augment_vscode_config.json")
            else:
                # 备选方案：使用sys.argv[0]
                if sys.argv and len(sys.argv) > 0:
                    argv_path = os.path.abspath(sys.argv[0])
                    if argv_path.endswith('.exe'):
                        config_file = os.path.join(os.path.dirname(argv_path), ".augment_vscode_config.json")
                    else:
                        config_file = os.path.join(os.path.dirname(argv_path), ".augment_vscode_config.json")
                else:
                    config_file = os.path.join(os.getcwd(), ".augment_vscode_config.json")
            config_data = {
                "selected_software": self.selected_software,
                "last_updated": time.time()
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2)

            self.logger.debug(f"软件选择已保存: {self.selected_software}")

        except Exception as e:
            self.logger.error(f"保存软件选择失败: {e}")

    def _load_software_selection(self):
        """从配置文件加载软件选择"""
        try:
            # 🔧 使用与main.py一致的路径检测逻辑
            import sys
            # 直接使用main.py中设置的环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                config_file = os.path.join(config_dir, ".augment_vscode_config.json")
            else:
                # 备选方案：使用sys.argv[0]
                if sys.argv and len(sys.argv) > 0:
                    argv_path = os.path.abspath(sys.argv[0])
                    if argv_path.endswith('.exe'):
                        config_file = os.path.join(os.path.dirname(argv_path), ".augment_vscode_config.json")
                    else:
                        config_file = os.path.join(os.path.dirname(argv_path), ".augment_vscode_config.json")
                else:
                    config_file = os.path.join(os.getcwd(), ".augment_vscode_config.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                saved_selection = config_data.get("selected_software", "all")

                # 更新选择器
                software_index_map = {
                    "all": 0,
                    "vscode": 1,
                    "cursor": 2,
                    "sublime": 3,
                    "atom": 4,
                    "webstorm": 5,
                    "dev_tools": 6
                }

                index = software_index_map.get(saved_selection, 0)
                if self.software_selector is not None:
                    self.software_selector.setCurrentIndex(index)
                self.selected_software = saved_selection

                self.logger.info(f"已加载保存的软件选择: {saved_selection}")
            else:
                self.logger.debug("未找到配置文件，使用默认选择")

        except Exception as e:
            self.logger.error(f"加载软件选择失败: {e}")
            # 使用默认选择
            self.selected_software = "all"

    def _add_default_cleanup_items(self):
        """添加默认的清理项目，根据当前选择的软件筛选 - 返回分组结构"""
        try:
            self.logger.info("🔄 使用默认清理项目方法")

            # 获取当前选择的软件
            current_software = getattr(self, 'selected_software', 'all')
            self.logger.info(f"当前选择的软件: {current_software}")

            # 如果是特定软件，尝试使用扫描方法
            if current_software != 'all':
                try:
                    scanned_items = self._scan_specific_software(current_software)
                    if scanned_items and isinstance(scanned_items, list):
                        self.cleanup_items = scanned_items
                        self.logger.info(f"✅ 成功使用扫描方法获取 {current_software} 的清理项目: {len(scanned_items)} 个分组")
                        return
                except Exception as e:
                    self.logger.error(f"扫描方法失败，使用备用默认项目: {e}")

            # 创建默认分组结构
            default_groups = []

            # 设备信息分组
            device_group = {
                "name": "🔑 设备信息",
                "type": "group",
                "software": current_software,
                "children": [
                    {
                        'name': f'🔑 设备ID重置 - {current_software.upper()}',
                        'description': f'重置 {current_software} 的设备ID和机器指纹',
                        'base_path': 'device_id_reset',
                        'software': current_software,
                        'category': 'device_id',
                        'enabled': True,
                        'size': 0,
                        'size_estimate': '< 1MB',
                        'recommended': True,
                        'risk_level': 'low'
                    }
                ]
            }
            default_groups.append(device_group)

            # 系统清理分组
            system_group = {
                "name": "🗑️ 系统清理",
                "type": "group",
                "software": current_software,
                "children": [
                    {
                        'name': f'🗑️ 注册表清理 - {current_software.upper()}',
                        'description': f'清理Windows注册表中的 {current_software} 相关项',
                        'base_path': 'registry_cleanup',
                        'software': current_software,
                        'category': 'registry',
                        'enabled': True,
                        'size': 0,
                        'size_estimate': '< 1MB',
                        'recommended': True,
                        'risk_level': 'medium'
                    }
                ]
            }
            default_groups.append(system_group)

            # 设置清理项目为分组结构
            self.cleanup_items = default_groups
            self.logger.info(f"✅ 创建了 {len(default_groups)} 个默认分组，软件: {current_software}")

        except Exception as e:
            self.logger.error(f"添加默认清理项目失败: {e}")
            # 确保至少有一个基本项目
            self.cleanup_items = [{
                "name": "🔧 基本清理",
                "type": "group",
                "software": current_software,
                "children": [{
                    'name': f'基本清理 - {current_software}',
                    'description': f'基本的 {current_software} 清理功能',
                    'base_path': 'basic_cleanup',
                    'software': current_software,
                    'category': 'basic',
                    'enabled': True,
                    'size': 0,
                    'recommended': True
                }]
            }]

    def _auto_scan_selected_software(self):
        """根据选择的软件自动扫描相应的清理项目"""
        try:
            self.logger.info(f"🔍 开始自动扫描软件: {self.selected_software}")
            print(f"DEBUG: 开始自动扫描软件: {self.selected_software}")  # 添加控制台输出

            # 清空现有清理项目
            self.cleanup_items = []

            # 根据选择的软件扫描相应的路径
            if self.selected_software == "all":
                # 扫描所有支持的软件
                self.logger.info("🔍 扫描所有软件...")
                print("DEBUG: 扫描所有软件...")
                self.cleanup_items = self._scan_all_software()
            else:
                # 扫描特定软件
                self.logger.info(f"🔍 扫描特定软件: {self.selected_software}")
                print(f"DEBUG: 扫描特定软件: {self.selected_software}")
                self.cleanup_items = self._scan_specific_software(self.selected_software)

            # 如果没有找到任何项目，添加默认项目
            if not self.cleanup_items:
                self.logger.info(f"⚠️ 未找到 {self.selected_software} 的清理项目，添加默认项目")
                print(f"DEBUG: 未找到清理项目，添加默认项目")
                self._add_default_cleanup_items()

            self.logger.info(f"✅ 自动扫描完成，发现 {len(self.cleanup_items)} 个清理项目")
            print(f"DEBUG: 扫描完成，发现 {len(self.cleanup_items)} 个清理项目")

        except Exception as e:
            self.logger.error(f"❌ 自动扫描失败: {e}")
            print(f"DEBUG: 扫描失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            # 添加默认项目作为备选
            self._add_default_cleanup_items()

        # 确保清理项目列表始终为列表而不是None
        if self.cleanup_items is None:
            self.cleanup_items = []

    def _scan_all_software(self):
        """扫描所有支持的软件"""
        all_groups = []

        # 扫描主要编辑器
        main_editors = ["vscode", "cursor", "sublime", "atom", "notepad_plus"]
        for editor in main_editors:
            try:
                groups = self._scan_specific_software(editor)
                if groups and isinstance(groups, list):
                    all_groups.extend(groups)
                    self.logger.info(f"✅ 已扫描编辑器 {editor}: 找到 {len(groups)} 个分组")
                else:
                    self.logger.warning(f"⚠️ 扫描编辑器 {editor} 返回空结果或无效结果")
            except Exception as e:
                self.logger.error(f"❌ 扫描编辑器 {editor} 失败: {e}")
                # 继续扫描其他软件

        # 扫描IDE
        ides = ["visual_studio", "jetbrains", "android_studio", "eclipse"]
        for ide in ides:
            try:
                groups = self._scan_specific_software(ide)
                if groups and isinstance(groups, list):
                    all_groups.extend(groups)
                    self.logger.info(f"✅ 已扫描IDE {ide}: 找到 {len(groups)} 个分组")
                else:
                    self.logger.warning(f"⚠️ 扫描IDE {ide} 返回空结果或无效结果")
            except Exception as e:
                self.logger.error(f"❌ 扫描IDE {ide} 失败: {e}")
                # 继续扫描其他软件

        # 添加系统缓存
        try:
            system_groups = self._scan_specific_software("system_cache")
            if system_groups and isinstance(system_groups, list):
                all_groups.extend(system_groups)
                self.logger.info(f"✅ 已扫描系统缓存: 找到 {len(system_groups)} 个分组")
            else:
                self.logger.warning("⚠️ 扫描系统缓存返回空结果或无效结果")
        except Exception as e:
            self.logger.error(f"❌ 扫描系统缓存失败: {e}")

        # 计算总项目数
        total_items = sum(len(group.get('children', [])) for group in all_groups)
        self.logger.info(f"🎉 全部软件扫描完成，总共找到 {len(all_groups)} 个分组，{total_items} 个清理项目")
        return all_groups

    def _scan_specific_software(self, software_type):
        """扫描特定软件的清理项目"""
        items = []

        try:
            self.logger.info(f"🔍 开始扫描特定软件: {software_type}")
            print(f"DEBUG: 开始扫描特定软件: {software_type}")

            # 如果软件类型无效，返回空列表
            if not software_type or not isinstance(software_type, str):
                self.logger.error(f"❌ 无效的软件类型: {software_type}")
                print(f"DEBUG: 无效的软件类型: {software_type}")
                return []

            import os
            user_home = os.path.expanduser("~")

            # 定义各软件的详细路径和清理项目
            software_configs = {
                "vscode": {
                    "name": "Visual Studio Code",
                    "items": [
                        {
                            "name": "🗂️ 扩展缓存文件",
                            "description": "清理扩展下载缓存、VSIX安装包和过期扩展文件",
                            "paths": ["CachedExtensions", "CachedExtensionVSIXs", "extensions/.obsolete", "extensions/ms-vscode.vscode-typescript-next"],
                            "category": "cache",
                            "recommended": True,
                            "size_estimate": "50-200MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📊 GPU渲染缓存",
                            "description": "清理图形处理单元缓存、Dawn WebGPU缓存和着色器缓存",
                            "paths": ["GPUCache", "DawnCache", "ShaderCache", "Code Cache"],
                            "category": "cache",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 运行日志文件",
                            "description": "清理VSCode运行日志、错误报告和崩溃转储文件",
                            "paths": ["logs", "crashDumps", "exthost Crash Reports"],
                            "category": "logs",
                            "recommended": True,
                            "size_estimate": "5-20MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔧 工作区存储数据",
                            "description": "清理项目工作区配置、状态文件和自动备份数据",
                            "paths": ["User/workspaceStorage", "Workspaces", "Backups", "User/globalStorage"],
                            "category": "workspace",
                            "recommended": False,
                            "size_estimate": "10-100MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "📚 文件历史记录",
                            "description": "清理最近打开文件列表、搜索历史和文件访问记录",
                            "paths": ["User/History", "User/globalStorage/storage.json", "User/state/global.json"],
                            "category": "history",
                            "recommended": True,
                            "size_estimate": "1-10MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🌐 网络数据缓存",
                            "description": "清理网络请求缓存、离线数据和传输安全状态",
                            "paths": ["Network Persistent State", "TransportSecurity", "blob_storage", "Session Storage"],
                            "category": "network",
                            "recommended": True,
                            "size_estimate": "5-30MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🎨 主题和图标缓存",
                            "description": "清理主题文件缓存、图标缓存和UI资源文件",
                            "paths": ["User/themes", "CachedData", "logs/renderer*.log"],
                            "category": "ui_cache",
                            "recommended": True,
                            "size_estimate": "20-80MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔍 搜索索引缓存",
                            "description": "清理文件搜索索引、符号缓存和IntelliSense数据",
                            "paths": ["User/globalStorage/ms-vscode.vscode-typescript-next", "CachedData/*/CachedData"],
                            "category": "search_cache",
                            "recommended": True,
                            "size_estimate": "30-150MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📦 扩展数据存储",
                            "description": "清理扩展的本地数据存储和配置文件",
                            "paths": ["User/globalStorage/*/", "extensions/*/node_modules"],
                            "category": "extension_data",
                            "recommended": False,
                            "size_estimate": "50-300MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🔄 自动更新缓存",
                            "description": "清理VSCode自动更新下载的安装包和临时文件",
                            "paths": ["update", "*.vsix", "CachedData/*/update"],
                            "category": "update_cache",
                            "recommended": True,
                            "size_estimate": "100-500MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "⚙️ 用户配置文件",
                            "description": "重置用户设置、快捷键绑定、代码片段和任务配置",
                            "paths": ["User/settings.json", "User/keybindings.json", "User/snippets", "User/tasks.json"],
                            "category": "settings",
                            "recommended": False,
                            "size_estimate": "1-5MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 设备标识重置",
                            "description": "重置机器码、设备ID和激活状态信息",
                            "paths": ["machineid", "User/globalStorage/storage.json", "telemetry"],
                            "category": "machine_id",
                            "recommended": True,
                            "size_estimate": "1MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "cursor": {
                    "name": "Cursor AI Editor",
                    "items": [
                        {
                            "name": "🤖 AI模型数据缓存",
                            "description": "清理AI语言模型缓存、推理数据和模型权重文件",
                            "paths": ["ai-cache", "model-cache", "inference-cache", "llm-cache", "User/globalStorage/cursor.ai"],
                            "category": "ai_cache",
                            "recommended": True,
                            "size_estimate": "100-500MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "💬 AI对话历史记录",
                            "description": "清理与AI助手的对话记录、聊天历史和上下文数据",
                            "paths": ["chat-history", "conversations", "User/globalStorage/cursor-chat", "ai-conversations"],
                            "category": "chat_history",
                            "recommended": False,
                            "size_estimate": "10-50MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🧠 代码建议缓存",
                            "description": "清理AI代码补全建议、智能提示和代码分析缓存",
                            "paths": ["code-suggestions", "completions-cache", "ai-completions"],
                            "category": "suggestions_cache",
                            "recommended": True,
                            "size_estimate": "50-200MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔍 代码分析数据",
                            "description": "清理代码语义分析、错误检测和重构建议数据",
                            "paths": ["analysis-cache", "semantic-cache", "refactor-suggestions"],
                            "category": "analysis_cache",
                            "recommended": True,
                            "size_estimate": "30-150MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🗂️ 扩展和插件缓存",
                            "description": "清理扩展下载缓存、VSIX安装包和插件临时文件",
                            "paths": ["CachedExtensions", "CachedExtensionVSIXs", "extensions/.obsolete"],
                            "category": "cache",
                            "recommended": True,
                            "size_estimate": "50-200MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📊 GPU渲染缓存",
                            "description": "清理图形处理缓存、WebGL数据和渲染管线缓存",
                            "paths": ["GPUCache", "DawnCache", "WebGL Cache"],
                            "category": "cache",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🌐 网络请求缓存",
                            "description": "清理API请求缓存、在线服务数据和网络状态信息",
                            "paths": ["Network Persistent State", "api-cache", "online-cache"],
                            "category": "network_cache",
                            "recommended": True,
                            "size_estimate": "20-100MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 日志和诊断文件",
                            "description": "清理运行日志、错误报告、崩溃转储和诊断数据",
                            "paths": ["logs", "crashDumps", "diagnostics", "ai-logs"],
                            "category": "logs",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔧 工作区和项目数据",
                            "description": "清理项目工作区配置、状态文件和自动保存数据",
                            "paths": ["User/workspaceStorage", "Workspaces", "project-cache"],
                            "category": "workspace",
                            "recommended": False,
                            "size_estimate": "10-100MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "⚙️ AI助手配置",
                            "description": "重置AI助手设置、偏好配置和个性化数据",
                            "paths": ["User/ai-settings.json", "ai-preferences", "User/globalStorage/cursor.settings"],
                            "category": "ai_settings",
                            "recommended": False,
                            "size_estimate": "1-5MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 设备和激活信息",
                            "description": "重置机器码、设备标识符和Cursor激活状态",
                            "paths": ["machineid", "User/globalStorage/storage.json", "activation-cache"],
                            "category": "machine_id",
                            "recommended": True,
                            "size_estimate": "1MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "jetbrains": {
                    "name": "JetBrains IDEs",
                    "items": [
                        {
                            "name": "🗂️ 系统缓存和索引",
                            "description": "清理IDE系统缓存、文件索引、符号表和代码分析数据",
                            "paths": ["system/caches", "system/index", "system/tmp", "system/LocalHistory"],
                            "category": "cache",
                            "recommended": True,
                            "size_estimate": "200MB-1GB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 运行日志文件",
                            "description": "清理IDE运行日志、错误报告、性能分析和调试信息",
                            "paths": ["system/log", "system/logs", "system/diagnostic"],
                            "category": "logs",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔧 插件和扩展数据",
                            "description": "清理插件下载缓存、沙盒环境和插件临时文件",
                            "paths": ["system/plugins-sandbox", "system/plugins", "config/plugins"],
                            "category": "plugins",
                            "recommended": False,
                            "size_estimate": "50-200MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "📊 编译和构建缓存",
                            "description": "清理编译服务器缓存、构建输出和编译器临时文件",
                            "paths": ["system/compile-server", "system/compiler", "system/build"],
                            "category": "compile",
                            "recommended": True,
                            "size_estimate": "100-500MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🌐 Maven/Gradle构建工具",
                            "description": "清理Maven仓库缓存、Gradle构建缓存和依赖下载文件",
                            "paths": ["system/Maven", "system/gradle", "system/external_build_system"],
                            "category": "build_tools",
                            "recommended": False,
                            "size_estimate": "100MB-1GB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🔍 搜索和导航缓存",
                            "description": "清理全局搜索索引、导航历史和查找结果缓存",
                            "paths": ["system/index/shared_indexes", "system/caches/findUsages"],
                            "category": "search_cache",
                            "recommended": True,
                            "size_estimate": "50-300MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🎨 UI主题和外观缓存",
                            "description": "清理界面主题缓存、图标缓存和UI渲染数据",
                            "paths": ["system/caches/ui", "config/themes", "system/appearance"],
                            "category": "ui_cache",
                            "recommended": True,
                            "size_estimate": "20-100MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🚀 性能分析数据",
                            "description": "清理性能监控数据、内存快照和CPU分析文件",
                            "paths": ["system/snapshots", "system/profiler", "system/performance"],
                            "category": "profiler",
                            "recommended": True,
                            "size_estimate": "50-500MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔄 版本控制缓存",
                            "description": "清理Git/SVN缓存、版本历史和变更记录数据",
                            "paths": ["system/vcs", "system/git", "system/LocalHistory"],
                            "category": "vcs_cache",
                            "recommended": False,
                            "size_estimate": "30-200MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🗄️ 数据库和连接缓存",
                            "description": "清理数据库连接缓存、查询历史和Schema信息",
                            "paths": ["system/database", "system/jdbc", "config/consoles"],
                            "category": "database_cache",
                            "recommended": True,
                            "size_estimate": "10-100MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "⚙️ 用户配置和设置",
                            "description": "重置IDE配置、快捷键、代码模板和个人偏好设置",
                            "paths": ["config/options", "config/keymaps", "config/templates"],
                            "category": "settings",
                            "recommended": False,
                            "size_estimate": "5-20MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 许可证和激活信息",
                            "description": "重置IDE许可证信息、激活状态和用户认证数据",
                            "paths": ["config/eval", "system/license", "config/options/other.xml"],
                            "category": "license",
                            "recommended": True,
                            "size_estimate": "1-5MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "sublime": {
                    "name": "Sublime Text",
                    "items": [
                        {
                            "name": "📦 包和插件缓存",
                            "description": "清理Package Control下载缓存、插件安装包和临时文件",
                            "paths": ["Installed Packages", "Package Control.cache", "Package Control.ca-certs", "Package Control.ca-bundle"],
                            "category": "packages",
                            "recommended": True,
                            "size_estimate": "20-100MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 会话和工作区数据",
                            "description": "清理会话状态、打开文件记录、工作区配置和项目历史",
                            "paths": ["Local/Session.sublime_session", "Local/Auto Save Session.sublime_session", "Local/*.sublime_workspace", "Local/recently_used.json"],
                            "category": "session",
                            "recommended": False,
                            "size_estimate": "1-10MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🔍 搜索和索引缓存",
                            "description": "清理文件搜索索引、符号索引和代码导航缓存",
                            "paths": ["Index", "Local/FileHistory.json", "Local/Projects/*.sublime-project"],
                            "category": "search_cache",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📊 性能和日志数据",
                            "description": "清理性能监控数据、错误日志和调试信息",
                            "paths": ["Local/Log Files", "Local/Crash Reports", "Local/performance.log"],
                            "category": "logs",
                            "recommended": True,
                            "size_estimate": "5-20MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🎨 主题和UI缓存",
                            "description": "清理主题文件缓存、颜色方案和UI渲染数据",
                            "paths": ["Cache", "Local/Color Scheme - Default", "Packages/Theme - Default"],
                            "category": "ui_cache",
                            "recommended": True,
                            "size_estimate": "5-30MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔧 语法和代码分析缓存",
                            "description": "清理语法高亮缓存、代码补全数据和语言服务器缓存",
                            "paths": ["Local/Syntax Definitions", "Local/Completions", "LSP-*"],
                            "category": "syntax_cache",
                            "recommended": True,
                            "size_estimate": "10-40MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "⚙️ 用户配置和设置",
                            "description": "重置用户配置、快捷键设置、代码片段和个人偏好",
                            "paths": ["Packages/User/Preferences.sublime-settings", "Packages/User/Default.sublime-keymap", "Packages/User/*.sublime-snippet"],
                            "category": "settings",
                            "recommended": False,
                            "size_estimate": "1-5MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 许可证和激活信息",
                            "description": "重置Sublime Text许可证信息和用户认证数据",
                            "paths": ["Local/License.sublime_license", "Local/Settings/license.json"],
                            "category": "license",
                            "recommended": True,
                            "size_estimate": "1MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "visual_studio": {
                    "name": "Visual Studio",
                    "items": [
                        {
                            "name": "🗂️ 组件和扩展缓存",
                            "description": "清理Visual Studio组件缓存、扩展数据和MEF组件缓存",
                            "paths": ["ComponentModelCache", "Extensions", "MEFCacheBackup"],
                            "category": "cache",
                            "recommended": True,
                            "size_estimate": "100-500MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📊 IntelliSense智能感知",
                            "description": "清理代码智能感知缓存、符号数据库和代码分析缓存",
                            "paths": ["IntelliSense", "VC/vcpkgcache", "CodeAnalysisCache"],
                            "category": "intellisense",
                            "recommended": True,
                            "size_estimate": "50-200MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔨 构建和编译缓存",
                            "description": "清理MSBuild缓存、编译器临时文件和构建输出缓存",
                            "paths": ["MSBuild", "VCToolsCache", "BuildCache"],
                            "category": "build_cache",
                            "recommended": True,
                            "size_estimate": "100-800MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 项目和解决方案数据",
                            "description": "清理项目程序集缓存、解决方案用户文件和备份文件",
                            "paths": ["ProjectAssemblies", "Backup Files", "RecentProjects"],
                            "category": "project",
                            "recommended": False,
                            "size_estimate": "50-300MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🌐 NuGet包管理缓存",
                            "description": "清理NuGet包缓存、下载文件和包还原数据",
                            "paths": ["NuGet", "PackageCache", "packages"],
                            "category": "nuget_cache",
                            "recommended": False,
                            "size_estimate": "200MB-2GB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🔍 调试和诊断数据",
                            "description": "清理调试器缓存、性能分析数据和诊断工具缓存",
                            "paths": ["Debugger", "DiagnosticTools", "PerfView"],
                            "category": "debug_cache",
                            "recommended": True,
                            "size_estimate": "50-400MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🎨 设计器和UI缓存",
                            "description": "清理WPF/WinForms设计器缓存、XAML缓存和UI资源",
                            "paths": ["Designer", "XamlCache", "WpfCache"],
                            "category": "designer_cache",
                            "recommended": True,
                            "size_estimate": "30-150MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📱 移动开发缓存",
                            "description": "清理Xamarin缓存、Android模拟器数据和iOS构建缓存",
                            "paths": ["Xamarin", "AndroidCache", "iOSCache"],
                            "category": "mobile_cache",
                            "recommended": True,
                            "size_estimate": "100MB-1GB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔄 模板和项目模板",
                            "description": "清理项目模板缓存、向导数据和自定义模板",
                            "paths": ["ProjectTemplates", "ItemTemplates", "TemplateCache"],
                            "category": "template_cache",
                            "recommended": True,
                            "size_estimate": "20-100MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📊 遥测和使用数据",
                            "description": "清理使用情况统计、遥测数据和错误报告",
                            "paths": ["Telemetry", "ErrorReports", "UsageData"],
                            "category": "telemetry",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "⚙️ 用户设置和偏好",
                            "description": "重置IDE设置、窗口布局、工具栏配置和个人偏好",
                            "paths": ["Settings", "WindowLayouts", "UserPreferences"],
                            "category": "settings",
                            "recommended": False,
                            "size_estimate": "5-30MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 许可证和激活数据",
                            "description": "重置Visual Studio许可证信息和产品激活状态",
                            "paths": ["Licenses", "ProductKeys", "ActivationCache"],
                            "category": "license",
                            "recommended": True,
                            "size_estimate": "1-5MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "notepad_plus": {
                    "name": "Notepad++",
                    "items": [
                        {
                            "name": "📦 插件和扩展缓存",
                            "description": "清理插件管理器下载缓存、插件配置文件和临时数据",
                            "paths": ["plugins/disabled", "plugins/Config", "plugins/APIs", "updater/GUP.xml"],
                            "category": "plugins",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 会话和备份文件",
                            "description": "清理会话状态、自动备份文件、崩溃恢复数据和临时文件",
                            "paths": ["backup", "session.xml", "nppBackup", "tmp"],
                            "category": "session",
                            "recommended": False,
                            "size_estimate": "5-20MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "📚 文件历史和最近文档",
                            "description": "清理最近打开文件列表、搜索历史和文件访问记录",
                            "paths": ["config.xml", "session.xml", "nppLogNulContentCorruptionIssue.xml"],
                            "category": "history",
                            "recommended": True,
                            "size_estimate": "1-5MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🎨 主题和样式缓存",
                            "description": "清理主题文件缓存、语法高亮样式和UI配置",
                            "paths": ["themes", "stylers.xml", "langs.xml", "autoCompletion"],
                            "category": "ui_cache",
                            "recommended": True,
                            "size_estimate": "5-15MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔍 搜索和替换历史",
                            "description": "清理搜索历史、替换模式和正则表达式缓存",
                            "paths": ["config.xml", "FindHistory.xml", "ReplaceHistory.xml"],
                            "category": "search_history",
                            "recommended": True,
                            "size_estimate": "1-3MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📊 性能和日志数据",
                            "description": "清理性能监控数据、错误日志和调试信息",
                            "paths": ["nppLogNulContentCorruptionIssue.xml", "debugInfo.txt", "crash_dump"],
                            "category": "logs",
                            "recommended": True,
                            "size_estimate": "2-10MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔧 语言和编码配置",
                            "description": "清理语言定义文件、编码检测缓存和语法分析数据",
                            "paths": ["langs.xml", "localization", "functionList.xml"],
                            "category": "language_cache",
                            "recommended": True,
                            "size_estimate": "3-8MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "⚙️ 用户配置和设置",
                            "description": "重置用户配置、快捷键设置、工具栏布局和个人偏好",
                            "paths": ["config.xml", "shortcuts.xml", "contextMenu.xml", "toolbarIcons.xml"],
                            "category": "settings",
                            "recommended": False,
                            "size_estimate": "1-5MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 许可证和更新信息",
                            "description": "重置软件许可证信息和自动更新配置",
                            "paths": ["updater", "doLocalConf.xml", "nativeLang.xml"],
                            "category": "license",
                            "recommended": True,
                            "size_estimate": "1MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "android_studio": {
                    "name": "Android Studio",
                    "items": [
                        {
                            "name": "🗂️ 系统缓存和索引",
                            "description": "清理Android Studio系统缓存、文件索引、符号表和代码分析数据",
                            "paths": ["system/caches", "system/index", "system/tmp", "system/LocalHistory"],
                            "category": "cache",
                            "recommended": True,
                            "size_estimate": "500MB-2GB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📱 Android SDK和构建缓存",
                            "description": "清理SDK下载缓存、Gradle构建缓存、Maven仓库和依赖文件",
                            "paths": ["system/gradle", "system/Maven", "system/external_build_system", "system/compile-server"],
                            "category": "sdk_cache",
                            "recommended": False,
                            "size_estimate": "200MB-1GB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🔧 AVD和模拟器缓存",
                            "description": "清理Android虚拟设备缓存、模拟器数据和设备资源文件",
                            "paths": ["system/device-explorer", "system/device-art-resources", "system/android-sdk"],
                            "category": "avd_cache",
                            "recommended": True,
                            "size_estimate": "100-500MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 日志和诊断文件",
                            "description": "清理IDE运行日志、错误报告、性能分析和调试信息",
                            "paths": ["system/log", "system/logs", "system/diagnostic"],
                            "category": "logs",
                            "recommended": True,
                            "size_estimate": "20-100MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔍 代码分析和智能提示",
                            "description": "清理代码分析缓存、智能提示数据、符号索引和语法检查缓存",
                            "paths": ["system/caches/findUsages", "system/index/shared_indexes", "system/compiler"],
                            "category": "analysis_cache",
                            "recommended": True,
                            "size_estimate": "100-400MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🎨 UI主题和布局缓存",
                            "description": "清理界面主题缓存、布局预览数据和设计器资源文件",
                            "paths": ["system/caches/ui", "config/themes", "system/appearance"],
                            "category": "ui_cache",
                            "recommended": True,
                            "size_estimate": "50-200MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔌 插件和扩展数据",
                            "description": "清理插件下载缓存、扩展配置和插件临时文件",
                            "paths": ["system/plugins-sandbox", "system/plugins", "config/plugins"],
                            "category": "plugins",
                            "recommended": False,
                            "size_estimate": "50-300MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "📊 性能分析和监控",
                            "description": "清理性能监控数据、内存快照、CPU分析和APK分析缓存",
                            "paths": ["system/snapshots", "system/profiler", "system/performance"],
                            "category": "profiler",
                            "recommended": True,
                            "size_estimate": "100-800MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🌐 网络和下载缓存",
                            "description": "清理网络请求缓存、SDK下载文件和在线资源缓存",
                            "paths": ["system/download", "system/network-cache", "system/sdk-cache"],
                            "category": "network_cache",
                            "recommended": True,
                            "size_estimate": "50-300MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "⚙️ 用户配置和设置",
                            "description": "重置IDE配置、快捷键、代码模板、项目设置和个人偏好",
                            "paths": ["config/options", "config/keymaps", "config/templates"],
                            "category": "settings",
                            "recommended": False,
                            "size_estimate": "10-50MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 许可证和激活信息",
                            "description": "重置Android Studio许可证信息、激活状态和用户认证数据",
                            "paths": ["config/eval", "system/license", "config/options/other.xml"],
                            "category": "license",
                            "recommended": True,
                            "size_estimate": "1-5MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "eclipse": {
                    "name": "Eclipse IDE",
                    "items": [
                        {
                            "name": "🗂️ 工作区缓存",
                            "description": "清理工作区元数据和索引",
                            "paths": [".metadata/.plugins", ".metadata/.log"],
                            "category": "workspace",
                            "recommended": True,
                            "size_estimate": "100-500MB"
                        },
                        {
                            "name": "📦 插件缓存",
                            "description": "清理插件和更新站点缓存",
                            "paths": ["p2/org.eclipse.equinox.p2.core", "p2/org.eclipse.equinox.p2.repository"],
                            "category": "plugins",
                            "recommended": True,
                            "size_estimate": "50-200MB"
                        },
                        {
                            "name": "🔧 构建缓存",
                            "description": "清理项目构建输出",
                            "paths": [".metadata/.plugins/org.eclipse.core.resources", ".metadata/.plugins/org.eclipse.jdt.core"],
                            "category": "build",
                            "recommended": False,
                            "size_estimate": "50-300MB"
                        }
                    ]
                },
                "atom": {
                    "name": "Atom Editor",
                    "items": [
                        {
                            "name": "📦 包和主题缓存",
                            "description": "清理Atom包管理器下载缓存、主题文件和扩展数据",
                            "paths": ["packages", "dev/packages", ".apm", "themes"],
                            "category": "packages",
                            "recommended": True,
                            "size_estimate": "50-200MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🗂️ 应用程序缓存",
                            "description": "清理Electron应用缓存、GPU缓存和渲染进程数据",
                            "paths": ["GPUCache", "Code Cache", "DawnCache", "blob_storage"],
                            "category": "app_cache",
                            "recommended": True,
                            "size_estimate": "30-150MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📝 项目和会话数据",
                            "description": "清理项目历史、会话状态、最近文件和工作区配置",
                            "paths": ["projects.cson", "Application Support/Atom/storage", "Local Storage"],
                            "category": "session",
                            "recommended": False,
                            "size_estimate": "5-30MB",
                            "risk_level": "medium"
                        },
                        {
                            "name": "🔍 搜索和索引缓存",
                            "description": "清理文件搜索索引、符号缓存和代码导航数据",
                            "paths": ["IndexedDB", "databases", "search-index"],
                            "category": "search_cache",
                            "recommended": True,
                            "size_estimate": "20-100MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "📊 性能和日志数据",
                            "description": "清理性能监控数据、错误日志、崩溃报告和调试信息",
                            "paths": ["logs", "Crash Reports", "DiagnosticReports"],
                            "category": "logs",
                            "recommended": True,
                            "size_estimate": "10-50MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🌐 网络和更新缓存",
                            "description": "清理网络请求缓存、自动更新文件和在线资源缓存",
                            "paths": ["Network Persistent State", "update-cache", "TransportSecurity"],
                            "category": "network_cache",
                            "recommended": True,
                            "size_estimate": "15-80MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🎨 UI和渲染缓存",
                            "description": "清理界面渲染缓存、字体缓存和图标资源文件",
                            "paths": ["ShaderCache", "FontCache", "icon-cache"],
                            "category": "ui_cache",
                            "recommended": True,
                            "size_estimate": "10-40MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "🔧 语法和代码分析",
                            "description": "清理语法高亮缓存、代码分析数据和语言服务器缓存",
                            "paths": ["grammar-cache", "language-server-cache", "syntax-cache"],
                            "category": "syntax_cache",
                            "recommended": True,
                            "size_estimate": "15-60MB",
                            "risk_level": "low"
                        },
                        {
                            "name": "⚙️ 用户配置和设置",
                            "description": "重置用户配置、快捷键设置、代码片段和个人偏好",
                            "paths": ["config.cson", "keymap.cson", "snippets.cson", "styles.less"],
                            "category": "settings",
                            "recommended": False,
                            "size_estimate": "1-5MB",
                            "risk_level": "high"
                        },
                        {
                            "name": "🔑 许可证和遥测数据",
                            "description": "重置软件许可证信息、使用统计和遥测数据",
                            "paths": ["telemetry", "usage-data", "metrics"],
                            "category": "telemetry",
                            "recommended": True,
                            "size_estimate": "1-3MB",
                            "risk_level": "medium"
                        }
                    ]
                },
                "vim": {
                    "name": "Vim/Neovim",
                    "items": [
                        {
                            "name": "📦 插件缓存",
                            "description": "清理插件管理器缓存",
                            "paths": ["plugged", "bundle", ".vim/bundle"],
                            "category": "plugins",
                            "recommended": False,
                            "size_estimate": "20-100MB"
                        },
                        {
                            "name": "📝 交换文件",
                            "description": "清理临时和交换文件",
                            "paths": [".swp", ".swo", ".tmp", "swap"],
                            "category": "temp",
                            "recommended": True,
                            "size_estimate": "5-50MB"
                        },
                        {
                            "name": "📚 撤销历史",
                            "description": "清理撤销历史文件",
                            "paths": ["undodir", ".vim/undodir"],
                            "category": "history",
                            "recommended": False,
                            "size_estimate": "10-100MB"
                        }
                    ]
                },
                "emacs": {
                    "name": "Emacs",
                    "items": [
                        {
                            "name": "📦 包缓存",
                            "description": "清理ELPA包管理器缓存",
                            "paths": ["elpa", ".emacs.d/elpa"],
                            "category": "packages",
                            "recommended": False,
                            "size_estimate": "50-200MB"
                        },
                        {
                            "name": "🗂️ 自动保存",
                            "description": "清理自动保存和备份文件",
                            "paths": ["auto-save-list", ".emacs.d/auto-save-list"],
                            "category": "autosave",
                            "recommended": True,
                            "size_estimate": "5-30MB"
                        },
                        {
                            "name": "⚙️ 会话数据",
                            "description": "清理会话和历史数据",
                            "paths": ["session.*", ".emacs.d/session.*"],
                            "category": "session",
                            "recommended": False,
                            "size_estimate": "1-10MB"
                        }
                    ]
                },
                "system_cache": {
                    "name": "系统缓存",
                    "items": [
                        {
                            "name": "🗂️ 临时文件",
                            "description": "清理系统临时文件夹",
                            "paths": ["Temp", "tmp"],
                            "category": "temp",
                            "recommended": True,
                            "size_estimate": "100MB-2GB"
                        },
                        {
                            "name": "🌐 浏览器缓存",
                            "description": "清理Internet临时文件",
                            "paths": ["INetCache", "IECompatCache"],
                            "category": "browser",
                            "recommended": True,
                            "size_estimate": "50-500MB"
                        },
                        {
                            "name": "📝 系统日志",
                            "description": "清理Windows事件日志",
                            "paths": ["Windows/Logs", "Windows/Temp"],
                            "category": "logs",
                            "recommended": False,
                            "size_estimate": "10-100MB"
                        },
                        {
                            "name": "🔄 Windows更新缓存",
                            "description": "清理Windows Update下载缓存",
                            "paths": ["SoftwareDistribution/Download"],
                            "category": "update_cache",
                            "recommended": True,
                            "size_estimate": "500MB-5GB"
                        },
                        {
                            "name": "🗑️ 回收站",
                            "description": "清空回收站文件",
                            "paths": ["$Recycle.Bin"],
                            "category": "recycle",
                            "recommended": False,
                            "size_estimate": "100MB-10GB"
                        }
                    ]
                }
            }

            config = software_configs.get(software_type, {})
            software_name = config.get("name", software_type.title())
            cleanup_items = config.get("items", [])

            # 获取软件的基础路径
            base_paths = self._get_software_base_paths(software_type)

            # 按类别分组清理项目
            category_groups = {}

            # 为每个清理项目创建条目
            for cleanup_item in cleanup_items:
                category = cleanup_item.get("category", "other")

                # 如果有有效路径，使用实际路径；否则使用默认路径显示选项
                found_valid_path = False
                for base_path in base_paths:
                    if os.path.exists(base_path):
                        item = self._create_detailed_cleanup_item(
                            base_path,
                            software_type,
                            software_name,
                            cleanup_item
                        )
                        if item:
                            # 按类别分组
                            if category not in category_groups:
                                category_groups[category] = {
                                    "name": self._get_category_display_name(category),
                                    "type": "group",
                                    "software": software_type,
                                    "children": []
                                }
                            category_groups[category]["children"].append(item)
                            found_valid_path = True
                            break  # 找到一个有效路径就够了

                # 如果没有找到有效路径，仍然创建一个显示项目（但标记为不可用）
                if not found_valid_path and base_paths:
                    item = self._create_detailed_cleanup_item(
                        base_paths[0],  # 使用第一个路径作为默认
                        software_type,
                        software_name,
                        cleanup_item,
                        force_create=True  # 强制创建，即使路径不存在
                    )
                    if item:
                        # 按类别分组
                        if category not in category_groups:
                            category_groups[category] = {
                                "name": self._get_category_display_name(category),
                                "type": "group",
                                "software": software_type,
                                "children": []
                            }
                        category_groups[category]["children"].append(item)

            # 将分组转换为列表
            items = list(category_groups.values())

        except Exception as e:
            self.logger.error(f"扫描 {software_type} 失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            # 记录失败但继续返回已找到的项目

        # 确保返回值是有效列表
        if items is None:
            self.logger.warning(f"扫描 {software_type} 返回了None，使用空列表代替")
            return []

        return items

    def _create_cleanup_item(self, base_path, software_type, category):
        """创建清理项目"""
        try:
            import os

            # 定义类别信息
            category_info = {
                "cache": {"name": "缓存文件", "desc": "清理缓存文件释放空间", "recommended": True},
                "extensions": {"name": "扩展数据", "desc": "清理扩展和插件数据", "recommended": False},
                "user_settings": {"name": "用户设置", "desc": "重置用户配置文件", "recommended": False},
                "workspace_storage": {"name": "工作区存储", "desc": "清理工作区数据", "recommended": True},
                "machine_id": {"name": "机器码重置", "desc": "重置设备标识符", "recommended": True},
                "logs": {"name": "日志文件", "desc": "清理日志和错误报告", "recommended": True},
                "temp_files": {"name": "临时文件", "desc": "清理临时文件", "recommended": True}
            }

            info = category_info.get(category, {"name": category, "desc": f"清理{category}", "recommended": True})

            # 计算大小
            size = self._get_folder_size_fast(base_path) if os.path.exists(base_path) else 0

            return {
                "name": f"{info['name']} ({software_type.upper()})",
                "description": info['desc'],
                "base_path": base_path,
                "software": software_type,
                "category": category,
                "size": size,
                "recommended": info['recommended'],
                "enabled": info['recommended']
            }

        except Exception as e:
            self.logger.error(f"创建清理项目失败: {e}")
            return None

    def _get_software_base_paths(self, software_type):
        """获取软件的基础安装和配置路径"""
        import os
        user_home = os.path.expanduser("~")

        path_configs = {
            "vscode": [
                os.path.join(user_home, "AppData", "Roaming", "Code"),
                os.path.join(user_home, ".vscode"),
                os.path.join(user_home, "AppData", "Local", "Programs", "Microsoft VS Code")
            ],
            "cursor": [
                os.path.join(user_home, "AppData", "Roaming", "Cursor"),
                os.path.join(user_home, ".cursor"),
                os.path.join(user_home, "AppData", "Local", "Programs", "cursor")
            ],
            "jetbrains": [
                # 新版本路径 (2020.1+)
                os.path.join(user_home, "AppData", "Roaming", "JetBrains"),
                os.path.join(user_home, "AppData", "Local", "JetBrains"),
                # 旧版本路径模式
                os.path.join(user_home, ".IntelliJIdea2024.3"),
                os.path.join(user_home, ".IntelliJIdea2023.3"),
                os.path.join(user_home, ".PyCharm2024.3"),
                os.path.join(user_home, ".PyCharm2023.3"),
                os.path.join(user_home, ".WebStorm2024.3"),
                os.path.join(user_home, ".WebStorm2023.3"),
                os.path.join(user_home, ".CLion2024.3"),
                os.path.join(user_home, ".DataGrip2024.3"),
                os.path.join(user_home, ".Rider2024.3")
            ],
            "sublime": [
                os.path.join(user_home, "AppData", "Roaming", "Sublime Text 4"),
                os.path.join(user_home, "AppData", "Roaming", "Sublime Text 3"),
                os.path.join(user_home, "AppData", "Roaming", "Sublime Text"),
                os.path.join(user_home, ".config", "sublime-text-4"),
                os.path.join(user_home, ".config", "sublime-text-3")
            ],
            "visual_studio": [
                os.path.join(user_home, "AppData", "Local", "Microsoft", "VisualStudio"),
                os.path.join(user_home, "AppData", "Roaming", "Microsoft", "VisualStudio"),
                "C:\\Program Files\\Microsoft Visual Studio" if os.name == 'nt' else None,
                "C:\\Program Files (x86)\\Microsoft Visual Studio" if os.name == 'nt' else None
            ],
            "notepad_plus": [
                os.path.join(user_home, "AppData", "Roaming", "Notepad++"),
                "C:\\Program Files\\Notepad++" if os.name == 'nt' else None,
                "C:\\Program Files (x86)\\Notepad++" if os.name == 'nt' else None
            ],
            "android_studio": [
                os.path.join(user_home, "AppData", "Roaming", "Google", "AndroidStudio2024.2"),
                os.path.join(user_home, "AppData", "Roaming", "Google", "AndroidStudio2023.3"),
                os.path.join(user_home, "AppData", "Local", "Google", "AndroidStudio2024.2"),
                os.path.join(user_home, "AppData", "Local", "Google", "AndroidStudio2023.3"),
                os.path.join(user_home, ".android"),
                "C:\\Program Files\\Android\\Android Studio" if os.name == 'nt' else None
            ],
            "eclipse": [
                os.path.join(user_home, "eclipse-workspace"),
                os.path.join(user_home, ".eclipse"),
                os.path.join(user_home, "AppData", "Local", "Eclipse"),
                "C:\\Program Files\\Eclipse Foundation" if os.name == 'nt' else None,
                "C:\\eclipse" if os.name == 'nt' else None
            ],
            "vim": [
                os.path.join(user_home, ".vim"),
                os.path.join(user_home, ".config", "nvim"),
                os.path.join(user_home, "AppData", "Local", "nvim"),
                os.path.join(user_home, "vimfiles"),
                "C:\\Program Files\\Vim" if os.name == 'nt' else None
            ],
            "emacs": [
                os.path.join(user_home, ".emacs.d"),
                os.path.join(user_home, "AppData", "Roaming", ".emacs.d"),
                os.path.join(user_home, ".config", "emacs"),
                "C:\\Program Files\\Emacs" if os.name == 'nt' else None
            ],
            "atom": [
                os.path.join(user_home, "AppData", "Roaming", "Atom"),
                os.path.join(user_home, ".atom"),
                os.path.join(user_home, "AppData", "Local", "atom"),
                "C:\\Program Files\\Atom" if os.name == 'nt' else None,
                "C:\\Users\\<USER>\\AppData\\Local\\atom" if os.name == 'nt' else None
            ],
            "codeblocks": [
                os.path.join(user_home, "AppData", "Roaming", "CodeBlocks"),
                os.path.join(user_home, ".codeblocks"),
                "C:\\Program Files\\CodeBlocks" if os.name == 'nt' else None,
                "C:\\Program Files (x86)\\CodeBlocks" if os.name == 'nt' else None
            ],
            "devcpp": [
                os.path.join(user_home, "AppData", "Roaming", "Dev-Cpp"),
                "C:\\Program Files\\Dev-Cpp" if os.name == 'nt' else None,
                "C:\\Program Files (x86)\\Dev-Cpp" if os.name == 'nt' else None
            ],
            "qtcreator": [
                os.path.join(user_home, "AppData", "Roaming", "QtProject"),
                os.path.join(user_home, ".config", "QtProject"),
                "C:\\Program Files\\Qt" if os.name == 'nt' else None,
                "C:\\Qt" if os.name == 'nt' else None
            ],
            "system_cache": [
                os.path.join(user_home, "AppData", "Local", "Temp"),
                os.path.join(user_home, "AppData", "Local", "Microsoft", "Windows", "INetCache"),
                "C:\\Windows\\Temp" if os.name == 'nt' else "/tmp",
                "C:\\Windows\\SoftwareDistribution" if os.name == 'nt' else None,
                "C:\\$Recycle.Bin" if os.name == 'nt' else None
            ]
        }

        # 过滤掉None值并返回有效路径
        paths = path_configs.get(software_type, [])
        valid_paths = [path for path in paths if path is not None]

        self.logger.info(f"软件 {software_type} 的检测路径: {valid_paths}")
        return valid_paths

    def _create_detailed_cleanup_item(self, base_path, software_type, software_name, cleanup_config, force_create=False):
        """根据详细配置创建清理项目"""
        try:
            import os

            # 检查相关路径是否存在
            actual_paths = []
            total_size = 0

            for rel_path in cleanup_config.get("paths", []):
                full_path = os.path.join(base_path, rel_path)
                if os.path.exists(full_path):
                    actual_paths.append(full_path)
                    if os.path.isfile(full_path):
                        total_size += os.path.getsize(full_path)
                    elif os.path.isdir(full_path):
                        total_size += self._get_folder_size_fast(full_path)
                else:
                    # 即使路径不存在，也添加到列表中（用于显示）
                    if force_create:
                        actual_paths.append(full_path)

            # 如果没有找到任何相关路径且不强制创建，跳过此项目
            if not actual_paths and not force_create:
                return None

            return {
                "name": f"{cleanup_config['name']} - {software_name}",
                "description": f"{cleanup_config['description']} (预计: {cleanup_config.get('size_estimate', '未知')})",
                "base_path": base_path,
                "paths": cleanup_config.get("paths", []),  # 原始相对路径
                "actual_paths": actual_paths,  # 实际存在的完整路径
                "software": software_type,
                "category": cleanup_config.get("category", "other"),
                "size": total_size,
                "size_estimate": cleanup_config.get("size_estimate", "未知"),
                "recommended": cleanup_config.get("recommended", True),
                "enabled": cleanup_config.get("recommended", True),
                "cleanup_type": "detailed",
                "risk_level": cleanup_config.get("risk_level", "low")
            }

        except Exception as e:
            self.logger.error(f"创建详细清理项目失败: {e}")
            return None

    def _get_category_display_name(self, category):
        """获取分类的显示名称"""
        category_names = {
            "cache": "🗂️ 缓存文件",
            "logs": "📝 日志文件",
            "workspace": "🔧 工作区数据",
            "history": "📚 历史记录",
            "network": "🌐 网络缓存",
            "ui_cache": "🎨 界面缓存",
            "search_cache": "🔍 搜索缓存",
            "extension_data": "📦 扩展数据",
            "update_cache": "🔄 更新缓存",
            "ai_cache": "🤖 AI缓存",
            "ai_history": "💬 AI历史",
            "ai_suggestions": "🧠 AI建议",
            "ai_settings": "⚙️ AI设置",
            "machine_id": "🔑 设备信息",
            "plugins": "🔌 插件数据",
            "compile": "📊 编译缓存",
            "build_tools": "🌐 构建工具",
            "profiler": "🚀 性能数据",
            "telemetry": "📊 遥测数据",
            "settings": "⚙️ 用户设置",
            "license": "🔑 许可证",
            "session": "📝 会话数据",
            "language_cache": "🔧 语言缓存",
            "packages": "📦 包管理",
            "app_cache": "🗂️ 应用缓存",
            "temp": "🗂️ 临时文件",
            "browser": "🌐 浏览器缓存",
            "recycle": "🗑️ 回收站",
            "other": "🔧 其他项目"
        }
        return category_names.get(category, f"🔧 {category.title()}")
