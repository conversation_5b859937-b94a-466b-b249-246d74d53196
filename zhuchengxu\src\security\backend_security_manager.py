#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后台安全管理器
集成后台认证系统的安全管理
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Optional, Callable, Dict, Any

try:
    from src.utils.logger import get_logger
    from src.utils.config_manager import ConfigManager
    from src.security.backend_auth_client import BackendAuthClient
    from src.security.compact_login_dialog import CompactLoginDialog
except ImportError as e:
    import logging
    def get_logger():
        return logging.getLogger(__name__)

    class ConfigManager:
        def get(self, key, default=None):
            return default
        def set(self, key, value):
            pass

    # 如果无法导入认证客户端，创建一个占位符
    class BackendAuthClient:
        def __init__(self, api_url):
            self.api_url = api_url
            self.on_auth_success = None
            self.on_auth_failed = None
            self.on_auth_expired = None

        def auto_login(self):
            return False

        def login(self, username, password, qq_number=''):
            return {'success': False, 'message': '认证模块加载失败'}

        def logout(self):
            return True

        def is_logged_in(self):
            return False

        def get_user_info(self):
            return None

    # 如果无法导入紧凑对话框，使用备用方案
    CompactLoginDialog = None


# 原来的不安全的LoginDialog已被移除
# 现在只使用安全的FixedLoginDialog


class BackendSecurityManager:
    """后台安全管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化安全管理器
        
        Args:
            config: 配置字典
        """
        self.logger = get_logger()
        self.config_manager = ConfigManager()
        
        # 默认配置 - 不设置默认API地址
        default_config = {
            'auto_login': True,
            'remember_server': True
        }

        self.config = {**default_config, **(config or {})}

        # 认证客户端
        self.auth_client = None
        
        # 回调函数
        self.on_login_success: Optional[Callable] = None
        self.on_login_failed: Optional[Callable] = None
        self.on_logout: Optional[Callable] = None
        self.on_auth_expired: Optional[Callable] = None
        
        self.logger.info("🔐 后台安全管理器初始化完成")
    
    def initialize(self) -> bool:
        """
        初始化安全系统

        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("🔐 初始化后台安全系统...")

            # 获取用户保存的服务器地址
            saved_server = self.config_manager.get('auth_server_url', '')

            # 如果没有保存的服务器地址，直接显示登录对话框
            if not saved_server:
                self.logger.info("📋 未找到保存的服务器地址，显示登录对话框")
                return self._show_login_dialog()

            # 创建认证客户端
            self.auth_client = BackendAuthClient(saved_server)

            # 设置回调函数
            self.auth_client.on_auth_success = self._on_auth_success
            self.auth_client.on_auth_failed = self._on_auth_failed
            self.auth_client.on_auth_expired = self._on_auth_expired

            # 尝试自动登录
            if self.config.get('auto_login') and self.auth_client.auto_login():
                self.logger.info("✅ 自动登录成功")
                return True

            # 显示登录对话框
            return self._show_login_dialog()

        except Exception as e:
            self.logger.error(f"安全系统初始化失败: {e}")
            return False
    
    def _show_login_dialog(self) -> bool:
        """显示登录对话框"""
        try:
            self.logger.info("🔐 显示登录对话框")

            # 创建紧凑版登录对话框
            if CompactLoginDialog:
                dialog = CompactLoginDialog()
                result = dialog.show()
            else:
                # 如果无法导入登录对话框，拒绝登录
                messagebox.showerror("错误", "无法加载登录模块。")
                return False
            
            if not result or result.get('action') == 'cancel':
                self.logger.info("❌ 用户取消登录")
                return False
            
            # 处理登录
            username = result.get('username')
            password = result.get('password')
            server = result.get('server')
            qq_number = result.get('qq_number', '')

            # 创建或更新认证客户端
            if not self.auth_client:
                self.auth_client = BackendAuthClient(server)
                # 设置回调函数
                self.auth_client.on_auth_success = self._on_auth_success
                self.auth_client.on_auth_failed = self._on_auth_failed
                self.auth_client.on_auth_expired = self._on_auth_expired
            elif server != self.auth_client.api_url:
                self.auth_client.api_url = server

            # 保存服务器地址
            if self.config.get('remember_server'):
                self.config_manager.set('auth_server_url', server)

            # 执行登录
            login_result = self.auth_client.login(username, password, qq_number)
            
            if login_result.get('success'):
                return True
            else:
                # 登录失败，显示错误信息
                error_msg = login_result.get('message', '登录失败')
                error_code = login_result.get('code', '')

                # 针对QQ号相关错误的特殊处理
                if error_code == 'QQ_REQUIRED':
                    messagebox.showerror("需要填写QQ号",
                                       "首次登录必须填写QQ号！\n\n"
                                       "请在登录界面填写您的QQ号，方便后续技术支持。")
                elif error_code == 'QQ_INVALID':
                    messagebox.showerror("QQ号格式错误",
                                       "QQ号格式不正确！\n\n"
                                       "请输入5-11位数字的QQ号。")
                else:
                    messagebox.showerror("登录失败", f"登录失败：{error_msg}")

                # 递归显示登录对话框
                return self._show_login_dialog()
                
        except Exception as e:
            self.logger.error(f"登录对话框异常: {e}")
            messagebox.showerror("错误", f"登录异常：{e}")
            return False
    
    def _on_auth_success(self, user_info: Dict[str, Any]):
        """认证成功回调"""
        self.logger.info(f"✅ 认证成功，用户: {user_info.get('username')}")
        
        if self.on_login_success:
            self.on_login_success(user_info)
    
    def _on_auth_failed(self, error_msg: str):
        """认证失败回调"""
        self.logger.warning(f"❌ 认证失败: {error_msg}")
        
        if self.on_login_failed:
            self.on_login_failed(error_msg)
    
    def _on_auth_expired(self, error_msg: str):
        """认证过期回调 - 智能处理网络问题"""
        self.logger.warning(f"⚠️ 认证状态异常: {error_msg}")

        # 判断是否是网络问题
        is_network_issue = any(keyword in error_msg.lower() for keyword in [
            '网络', 'network', '连接', 'connection', '超时', 'timeout',
            'vpn', '代理', 'proxy', '防火墙', 'firewall'
        ])

        if is_network_issue:
            # 网络问题的友好提示
            title = "网络连接问题"
            message = (
                f"检测到网络连接异常，无法维持认证状态。\n\n"
                f"详细信息：{error_msg}\n\n"
                f"建议解决方案：\n"
                f"• 检查网络连接是否正常\n"
                f"• 如使用VPN，请尝试切换节点或关闭VPN\n"
                f"• 检查防火墙设置\n"
                f"• 稍后重试登录\n\n"
                f"是否现在重新登录？"
            )

            # 询问是否重新登录
            result = messagebox.askyesno(title, message)
            if result:
                # 用户选择重新登录
                if self._show_login_dialog():
                    return
            else:
                # 用户选择稍后处理
                self.logger.info("用户选择稍后处理网络问题")
                if self.on_auth_expired:
                    self.on_auth_expired("网络连接问题，用户选择稍后处理")
                return
        else:
            # 真正的认证过期
            title = "认证过期"
            message = f"认证已过期：{error_msg}\n\n请重新登录。"
            messagebox.showwarning(title, message)

            # 尝试重新登录
            if self._show_login_dialog():
                return

        if self.on_auth_expired:
            self.on_auth_expired(error_msg)
    
    def logout(self) -> bool:
        """用户登出"""
        try:
            if self.auth_client:
                result = self.auth_client.logout()
                
                if self.on_logout:
                    self.on_logout()
                
                return result
            
            return True
            
        except Exception as e:
            self.logger.error(f"登出异常: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.auth_client and self.auth_client.is_logged_in()
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if self.auth_client:
            return self.auth_client.get_user_info()
        return None

    def get_username(self) -> Optional[str]:
        """获取当前用户名"""
        user_info = self.get_current_user()
        if user_info:
            return user_info.get('username')
        return None
    
    def verify_permission(self, permission: str) -> bool:
        """验证权限"""
        if not self.is_authenticated():
            return False
        
        user_info = self.get_current_user()
        if not user_info:
            return False
        
        # 根据用户类型判断权限
        user_type = user_info.get('user_type', 'free')
        
        # 基础权限检查
        if permission == 'basic_usage':
            return True
        elif permission == 'advanced_features':
            return user_type in ['vip', 'premium']
        elif permission == 'premium_features':
            return user_type == 'premium'
        
        return False

    def get_server_address(self):
        """获取当前服务器地址"""
        try:
            # 优先从认证客户端获取（这是最新的地址）
            if self.auth_client and hasattr(self.auth_client, 'api_url') and self.auth_client.api_url:
                self.logger.debug(f"从认证客户端获取服务器地址: {self.auth_client.api_url}")
                return self.auth_client.api_url

            # 从配置管理器获取保存的地址
            saved_server = self.config_manager.get('auth_server_url', '')
            if saved_server:
                self.logger.debug(f"从配置文件获取服务器地址: {saved_server}")
                return saved_server

            # 默认地址
            self.logger.debug("使用默认服务器地址")
            return "http://127.0.0.1:81/api.php"
        except Exception as e:
            self.logger.error(f"获取服务器地址失败: {e}")
            return "http://127.0.0.1:81/api.php"

    def cleanup(self):
        """清理资源"""
        try:
            if self.auth_client:
                self.auth_client.logout()
            self.logger.info("🔐 安全管理器清理完成")
        except Exception as e:
            self.logger.error(f"安全管理器清理异常: {e}")
