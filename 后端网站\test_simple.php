<?php
// 简单测试页面
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>简单测试</h1>";

try {
    echo "<h2>1. PHP基础测试</h2>";
    echo "PHP版本: " . phpversion() . "<br>";
    echo "当前时间: " . date('Y-m-d H:i:s') . "<br>";
    
    echo "<h2>2. Session测试</h2>";
    session_start();
    echo "Session状态: " . session_status() . "<br>";
    echo "Session ID: " . session_id() . "<br>";
    
    echo "<h2>3. 文件包含测试</h2>";
    if (file_exists('includes/Database.php')) {
        require_once 'includes/Database.php';
        echo "✓ Database.php 加载成功<br>";
    } else {
        echo "✗ Database.php 不存在<br>";
    }
    
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "✓ database.php 配置加载成功<br>";
        echo "数据库主机: " . $config['host'] . "<br>";
        echo "数据库名: " . $config['database'] . "<br>";
    } else {
        echo "✗ database.php 配置不存在<br>";
    }
    
    echo "<h2>4. 数据库连接测试</h2>";
    if (isset($config)) {
        try {
            $database = new Database($config['host'], $config['database'], $config['username'], $config['password']);
            echo "✓ 数据库连接成功<br>";
            
            // 测试查询
            $result = $database->query("SELECT COUNT(*) as count FROM announcements");
            $row = $result->fetch();
            echo "公告数量: " . $row['count'] . "<br>";
            
        } catch (Exception $e) {
            echo "✗ 数据库连接失败: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>5. 测试完成</h2>";
    echo "如果以上测试都通过，说明基础环境正常。<br>";
    
} catch (Exception $e) {
    echo "<h2>错误</h2>";
    echo "错误信息: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
}
?>
