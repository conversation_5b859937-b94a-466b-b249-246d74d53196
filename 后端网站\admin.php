<?php
/**
 * 管理后台
 * 提供用户管理、系统监控等功能
 */

declare(strict_types=1);

// 引入核心类
require_once __DIR__ . '/includes/Database.php';
require_once __DIR__ . '/includes/AuthManager.php';
require_once __DIR__ . '/includes/Logger.php';
require_once __DIR__ . '/includes/SecurityConfig.php';

// 辅助函数
function parseMemoryLimit($limit) {
    $limit = trim($limit);
    $last = strtolower($limit[strlen($limit)-1]);
    $limit = (int) $limit;
    switch($last) {
        case 'g': $limit *= 1024;
        case 'm': $limit *= 1024;
        case 'k': $limit *= 1024;
    }
    return $limit;
}

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

/**
 * 处理图片上传
 */
function handleImageUpload($files) {
    $images = [];
    $maxFiles = 3;
    $maxSize = 2 * 1024 * 1024; // 2MB
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];

    // 确保上传目录存在
    $uploadDir = __DIR__ . '/uploads/announcements/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            throw new Exception("无法创建上传目录: " . $uploadDir);
        }
    }

    // 检查目录是否可写
    if (!is_writable($uploadDir)) {
        throw new Exception("上传目录不可写: " . $uploadDir);
    }

    $fileCount = is_array($files['name']) ? count($files['name']) : 1;
    if ($fileCount > $maxFiles) {
        throw new Exception("最多只能上传{$maxFiles}张图片");
    }

    for ($i = 0; $i < $fileCount; $i++) {
        $fileName = is_array($files['name']) ? $files['name'][$i] : $files['name'];
        $fileTmpName = is_array($files['tmp_name']) ? $files['tmp_name'][$i] : $files['tmp_name'];
        $fileSize = is_array($files['size']) ? $files['size'][$i] : $files['size'];
        // $fileType = is_array($files['type']) ? $files['type'][$i] : $files['type']; // 暂时不需要
        $fileError = is_array($files['error']) ? $files['error'][$i] : $files['error'];

        // 跳过空文件
        if ($fileError === UPLOAD_ERR_NO_FILE || empty($fileName)) {
            continue;
        }

        // 检查上传错误
        if ($fileError !== UPLOAD_ERR_OK) {
            throw new Exception("图片上传失败: " . $fileName);
        }

        // 检查文件大小
        if ($fileSize > $maxSize) {
            throw new Exception("图片 {$fileName} 大小超过2MB限制");
        }

        // 检查文件类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $fileTmpName);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedTypes)) {
            throw new Exception("图片 {$fileName} 格式不支持，只支持JPG、PNG、GIF格式");
        }

        // 生成唯一文件名
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $newFileName = uniqid('ann_') . '_' . time() . '.' . $extension;
        $filePath = $uploadDir . $newFileName;

        // 移动文件
        if (!move_uploaded_file($fileTmpName, $filePath)) {
            throw new Exception("图片 {$fileName} 保存失败");
        }

        // 将图片转换为base64编码存储
        $imageData = file_get_contents($filePath);
        $base64 = base64_encode($imageData);

        $images[] = [
            'filename' => $fileName,
            'size' => $fileSize,
            'type' => $mimeType,
            'data' => $base64,
            'uploaded_at' => date('Y-m-d H:i:s')
        ];

        // 删除临时文件（因为我们已经转换为base64存储）
        unlink($filePath);
    }

    return $images;
}

/**
 * 生成数据库备份SQL
 */
function generateDatabaseBackup($database, $dbName) {
    $sql = '';

    // 添加SQL头部注释
    $sql .= "-- ========================================\n";
    $sql .= "-- 数据库备份文件\n";
    $sql .= "-- 数据库名称: {$dbName}\n";
    $sql .= "-- 备份时间: " . date('Y-m-d H:i:s') . "\n";
    $sql .= "-- 生成工具: Augment认证系统管理后台\n";
    $sql .= "-- ========================================\n\n";

    $sql .= "SET FOREIGN_KEY_CHECKS=0;\n";
    $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
    $sql .= "SET AUTOCOMMIT = 0;\n";
    $sql .= "START TRANSACTION;\n";
    $sql .= "SET time_zone = \"+00:00\";\n\n";

    // 获取所有表
    $tables = $database->fetchAll("SHOW TABLES");
    $tableColumn = "Tables_in_{$dbName}";

    foreach ($tables as $table) {
        $tableName = $table[$tableColumn];

        // 获取表结构
        $createTable = $database->fetchOne("SHOW CREATE TABLE `{$tableName}`");
        $sql .= "-- --------------------------------------------------------\n";
        $sql .= "-- 表的结构 `{$tableName}`\n";
        $sql .= "-- --------------------------------------------------------\n\n";
        $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
        $sql .= $createTable['Create Table'] . ";\n\n";

        // 获取表数据
        $rows = $database->fetchAll("SELECT * FROM `{$tableName}`");
        if (!empty($rows)) {
            $sql .= "-- --------------------------------------------------------\n";
            $sql .= "-- 转存表中的数据 `{$tableName}`\n";
            $sql .= "-- --------------------------------------------------------\n\n";

            // 获取列信息
            $columns = $database->fetchAll("SHOW COLUMNS FROM `{$tableName}`");
            $columnNames = array_map(function($col) { return "`{$col['Field']}`"; }, $columns);

            $sql .= "INSERT INTO `{$tableName}` (" . implode(', ', $columnNames) . ") VALUES\n";

            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = '(' . implode(', ', $rowValues) . ')';
            }

            $sql .= implode(",\n", $values) . ";\n\n";
        }
    }

    $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";
    $sql .= "COMMIT;\n";
    $sql .= "\n-- 备份完成\n";

    return $sql;
}

// 初始化安全配置
SecurityConfig::init();

session_start();

// 管理员密码 - 建议修改为更强的密码
define('ADMIN_PASSWORD', '123');

// 处理登录
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'login') {
    $username = $_POST['username'] ?? 'admin';
    $password = $_POST['password'] ?? '';
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    try {
        // 初始化数据库连接
        $database = new Database();

        // 查询管理员信息
        $admin = $database->fetchOne("
            SELECT id, username, password, role, status, login_attempts, locked_until
            FROM admins
            WHERE username = ? AND status = 1
        ", [$username]);

        if ($admin) {
            // 检查账户是否被锁定
            if ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
                $error = '账户已被锁定，请稍后再试';
            } elseif (password_verify($password, $admin['password'])) {
                // 登录成功
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['login_time'] = time();

                // 更新最后登录信息并重置登录尝试次数
                $database->update('admins', [
                    'last_login' => date('Y-m-d H:i:s'),
                    'last_ip' => $clientIp,
                    'login_attempts' => 0,
                    'locked_until' => null
                ], 'id = ?', [$admin['id']]);

                // 记录登录日志
                $database->insert('admin_logs', [
                    'admin_id' => $admin['id'],
                    'action' => 'login',
                    'details' => '管理员登录成功',
                    'ip_address' => $clientIp,
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);

                header('Location: admin.php');
                exit;
            } else {
                // 密码错误，增加登录尝试次数
                $attempts = $admin['login_attempts'] + 1;
                $updateData = ['login_attempts' => $attempts];

                // 如果尝试次数超过5次，锁定账户30分钟
                if ($attempts >= 5) {
                    $updateData['locked_until'] = date('Y-m-d H:i:s', strtotime('+30 minutes'));
                    $error = '登录失败次数过多，账户已被锁定30分钟';
                } else {
                    $error = '用户名或密码错误';
                }

                $database->update('admins', $updateData, 'id = ?', [$admin['id']]);

                // 记录失败日志
                $database->insert('admin_logs', [
                    'admin_id' => $admin['id'],
                    'action' => 'login_failed',
                    'details' => "登录失败，尝试次数: {$attempts}",
                    'ip_address' => $clientIp,
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
            }
        } else {
            $error = '用户名或密码错误';
        }
    } catch (Exception $e) {
        $error = '登录系统错误，请稍后再试';
        error_log("Admin login error: " . $e->getMessage());
    }
}

// 处理登出
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    // 记录登出日志
    if (isset($_SESSION['admin_id'])) {
        try {
            $database = new Database();
            $database->insert('admin_logs', [
                'admin_id' => $_SESSION['admin_id'],
                'action' => 'logout',
                'details' => '管理员登出',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            // 忽略日志记录错误
        }
    }

    session_destroy();
    header('Location: admin.php');
    exit;
}

// 检查登录状态
$isLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

if (!$isLoggedIn) {
    // 显示登录页面
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>管理后台登录</title>
        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="favicon.ico">
        <link rel="icon" type="image/svg+xml" href="favicon.svg">
        <link rel="apple-touch-icon" href="favicon.svg">
        <style>
            /* 内联Bootstrap核心样式 */
            * { box-sizing: border-box; margin: 0; padding: 0; }
            body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; font-size: 1rem; line-height: 1.5; color: #212529; }
            .container, .container-fluid { width: 100%; padding: 0 15px; margin: 0 auto; }
            .btn { display: inline-block; font-weight: 400; text-align: center; vertical-align: middle; cursor: pointer; border: 1px solid transparent; padding: 0.375rem 0.75rem; font-size: 1rem; line-height: 1.5; border-radius: 0.25rem; text-decoration: none; transition: all 0.15s ease-in-out; }
            .btn-primary { color: #fff; background-color: #0d6efd; border-color: #0d6efd; }
            .btn-primary:hover { background-color: #0b5ed7; border-color: #0a58ca; }
            .btn-link { color: #0d6efd; text-decoration: underline; background-color: transparent; border-color: transparent; }
            .form-control { display: block; width: 100%; padding: 0.375rem 0.75rem; font-size: 1rem; line-height: 1.5; color: #495057; background-color: #fff; border: 1px solid #ced4da; border-radius: 0.25rem; }
            .form-label { margin-bottom: 0.5rem; font-weight: 500; }
            .alert { position: relative; padding: 0.75rem 1.25rem; margin-bottom: 1rem; border: 1px solid transparent; border-radius: 0.25rem; }
            .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
            .text-center { text-align: center; }
            .text-muted { color: #6c757d; }
            .mb-3 { margin-bottom: 1rem; }
            .mb-4 { margin-bottom: 1.5rem; }
            .mt-3 { margin-top: 1rem; }
            .me-2 { margin-right: 0.5rem; }
            .w-100 { width: 100%; }
        </style>
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-card {
                background: white;
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                max-width: 400px;
                width: 100%;
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="bi bi-shield-lock-fill" style="font-size: 3rem; color: #667eea;"></i>
                <h2 class="mt-3">管理后台</h2>
                <p class="text-muted">请输入管理员账号信息</p>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>

            <form method="POST">
                <input type="hidden" name="action" value="login">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" name="username"
                           value="admin" required autofocus>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                </button>
            </form>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    默认账号：admin / admin123<br>
                    首次登录后请修改密码
                </small><br>
                <a href="index.php" class="btn btn-link">返回首页</a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 已登录，显示管理界面
try {
    $database = new Database();
    $logger = new Logger();
    
    // 获取页面参数
    $page = $_GET['page'] ?? 'dashboard';
    $action = $_GET['action'] ?? '';

    // 如果是POST请求，优先从POST获取action
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        $action = $_POST['action'];
    }

    // 处理公告管理操作（必须在页面渲染之前处理）
    if ($page === 'announcements' && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        try {
            if ($_POST['action'] === 'add_announcement') {
                // 添加公告
                $title = trim($_POST['title'] ?? '');
                $content = trim($_POST['content'] ?? '');
                $type = $_POST['type'] ?? 'info';
                $priority = (int)($_POST['priority'] ?? 0);
                $startTime = $_POST['start_time'] ?? null;
                $endTime = $_POST['end_time'] ?? null;
                $targetVersion = trim($_POST['target_version'] ?? '');

                if (empty($title) || empty($content)) {
                    throw new Exception('标题和内容不能为空');
                }

                // 处理图片上传
                $images = [];
                if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
                    $images = handleImageUpload($_FILES['images']);
                }

                $database->insert('announcements', [
                    'title' => $title,
                    'content' => $content,
                    'type' => $type,
                    'priority' => $priority,
                    'status' => 1,
                    'start_time' => $startTime ?: null,
                    'end_time' => $endTime ?: null,
                    'target_version' => $targetVersion ?: null,
                    'images' => !empty($images) ? json_encode($images) : null,
                    'created_by' => $_SESSION['admin_id']
                ]);

                $logger->info("公告添加成功", ['title' => $title, 'images_count' => count($images), 'admin_id' => $_SESSION['admin_id']]);

                // 立即重定向
                header('Location: admin.php?page=announcements&success=add');
                exit;

            } elseif ($_POST['action'] === 'update_announcement') {
                // 更新公告
                $id = (int)($_POST['id'] ?? 0);
                $title = trim($_POST['title'] ?? '');
                $content = trim($_POST['content'] ?? '');
                $type = $_POST['type'] ?? 'info';
                $priority = (int)($_POST['priority'] ?? 0);
                $status = (int)($_POST['status'] ?? 1);
                $startTime = $_POST['start_time'] ?? null;
                $endTime = $_POST['end_time'] ?? null;
                $targetVersion = trim($_POST['target_version'] ?? '');

                if ($id <= 0 || empty($title) || empty($content)) {
                    throw new Exception('参数错误');
                }

                $updateData = [
                    'title' => $title,
                    'content' => $content,
                    'type' => $type,
                    'priority' => $priority,
                    'status' => $status,
                    'start_time' => $startTime ?: null,
                    'end_time' => $endTime ?: null,
                    'target_version' => $targetVersion ?: null
                ];

                // 处理图片更新
                if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
                    $images = handleImageUpload($_FILES['images']);
                    $updateData['images'] = json_encode($images);
                }

                $database->update('announcements', $updateData, 'id = ?', [$id]);

                $logger->info("公告更新成功", ['id' => $id, 'title' => $title, 'admin_id' => $_SESSION['admin_id']]);

                // 立即重定向
                header('Location: admin.php?page=announcements&success=update');
                exit;

            } elseif ($_POST['action'] === 'delete_announcement') {
                // 删除公告
                $id = (int)($_POST['id'] ?? 0);
                if ($id <= 0) {
                    throw new Exception('公告ID无效');
                }

                $database->delete('announcements', 'id = ?', [$id]);
                $logger->info("公告删除成功", ['id' => $id, 'admin_id' => $_SESSION['admin_id']]);

                // 立即重定向
                header('Location: admin.php?page=announcements&success=delete');
                exit;

            } elseif ($_POST['action'] === 'create_announcements_table') {
                // 创建公告表
                $sql = "
                    CREATE TABLE `announcements` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `title` varchar(255) NOT NULL COMMENT '公告标题',
                      `content` text NOT NULL COMMENT '公告内容',
                      `type` enum('info','warning','success','error','update') DEFAULT 'info',
                      `priority` int(11) DEFAULT '0' COMMENT '优先级',
                      `status` tinyint(1) DEFAULT '1' COMMENT '状态',
                      `start_time` datetime DEFAULT NULL,
                      `end_time` datetime DEFAULT NULL,
                      `target_version` varchar(50) DEFAULT NULL,
                      `images` longtext DEFAULT NULL COMMENT '公告图片JSON数据',
                      `click_count` int(11) DEFAULT '0',
                      `created_by` int(11) DEFAULT NULL,
                      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                      PRIMARY KEY (`id`),
                      KEY `idx_status` (`status`),
                      KEY `idx_priority` (`priority`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                ";

                $database->query($sql);
                $logger->info("公告表创建成功", ['admin_id' => $_SESSION['admin_id']]);

                // 立即重定向
                header('Location: admin.php?page=announcements&success=table_created');
                exit;

            } elseif ($_POST['action'] === 'add_images_column') {
                // 为现有公告表添加images字段
                try {
                    $sql = "ALTER TABLE `announcements` ADD COLUMN `images` longtext DEFAULT NULL COMMENT '公告图片JSON数据'";
                    $database->query($sql);
                    $logger->info("公告表images字段添加成功", ['admin_id' => $_SESSION['admin_id']]);

                    // 立即重定向
                    header('Location: admin.php?page=announcements&success=column_added');
                    exit;
                } catch (Exception $e) {
                    // 如果字段已存在，忽略错误
                    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                        $logger->info("公告表images字段已存在", ['admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=announcements&success=column_exists');
                        exit;
                    } else {
                        throw $e;
                    }
                }
            }
        } catch (Exception $e) {
            // 公告操作错误，设置错误消息但不重定向
            $announcementErrorMessage = "操作失败：" . $e->getMessage();
            $logger->error("公告管理操作失败", ['error' => $e->getMessage(), 'admin_id' => $_SESSION['admin_id']]);
        }
    }

    // 处理数据库备份下载（必须在页面渲染之前处理）
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'backup_database') {
        try {
            // 获取数据库配置
            $config = include __DIR__ . '/config/database.php';
            $dbName = $config['mysql']['database']; // 修正配置路径

            // 生成备份文件名
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "backup_{$dbName}_{$timestamp}.sql";

            // 清除之前的输出缓冲
            if (ob_get_level()) {
                ob_end_clean();
            }

            // 设置下载头
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Transfer-Encoding: binary');
            header('Cache-Control: no-cache, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');

            // 输出SQL备份内容
            echo generateDatabaseBackup($database, $dbName);
            exit;

        } catch (Exception $e) {
            // 如果备份失败，记录错误并继续显示页面
            $errorMessage = "数据库备份失败: " . $e->getMessage();
            error_log("Database backup failed: " . $e->getMessage());
        }
    }

    // 处理清空日志操作
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'clear_logs') {
        $logFile = __DIR__ . '/logs/system.log';
        if (file_exists($logFile)) {
            file_put_contents($logFile, '');
            $logger->info("系统日志已清空", ['admin_action' => true, 'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
            $successMessage = "系统日志已清空";
        }
    }

    // 处理QQ配置更新操作
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_qq_config') {
        try {
            $qqGroupNumber = trim($_POST['qq_group_number'] ?? '');
            $qqGroupName = trim($_POST['qq_group_name'] ?? '');
            $qqGroupDesc = trim($_POST['qq_group_desc'] ?? '');
            $qqGroupEnabled = $_POST['qq_group_enabled'] ?? '1';

            // 验证QQ群号格式
            if (!empty($qqGroupNumber) && !preg_match('/^[0-9]{5,11}$/', $qqGroupNumber)) {
                throw new Exception('QQ群号格式不正确，应为5-11位数字');
            }

            $currentTime = date('Y-m-d H:i:s');
            $configs = [
                'qq_group_number' => $qqGroupNumber,
                'qq_group_name' => $qqGroupName,
                'qq_group_desc' => $qqGroupDesc,
                'qq_group_enabled' => $qqGroupEnabled
            ];

            $descriptions = [
                'qq_group_number' => 'QQ技术交流群号',
                'qq_group_name' => 'QQ群名称',
                'qq_group_desc' => 'QQ群描述',
                'qq_group_enabled' => '是否启用QQ群显示（1=启用，0=禁用）'
            ];

            foreach ($configs as $key => $value) {
                // 检查配置是否已存在
                $existing = $database->fetchOne("SELECT id FROM qq_config WHERE config_key = ?", [$key]);

                if ($existing) {
                    // 更新现有配置
                    $database->update('qq_config', [
                        'config_value' => $value,
                        'updated_at' => $currentTime
                    ], 'config_key = ?', [$key]);
                } else {
                    // 插入新配置
                    $database->insert('qq_config', [
                        'config_key' => $key,
                        'config_value' => $value,
                        'config_desc' => $descriptions[$key] ?? '',
                        'created_at' => $currentTime,
                        'updated_at' => $currentTime
                    ]);
                }
            }

            $logger->info("QQ群配置已更新", [
                'admin_action' => true,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'qq_group_number' => $qqGroupNumber,
                'qq_group_name' => $qqGroupName
            ]);

            $successMessage = "QQ群配置更新成功！群号：{$qqGroupNumber}，群名：{$qqGroupName}";

        } catch (Exception $e) {
            $errorMessage = "QQ配置更新失败：" . $e->getMessage();
            $logger->error("QQ配置更新失败", [
                'error' => $e->getMessage(),
                'admin_action' => true,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
        }
    }

    // 论坛管理操作处理
    if ($page === 'forum' && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        $forumAction = $_POST['action'];
        $forumSuccessMessage = '';
        $forumErrorMessage = '';

        try {
            switch ($forumAction) {
                case 'delete_post':
                    $postId = (int)($_POST['post_id'] ?? 0);
                    if ($postId > 0) {
                        // 级联删除：先删除回复和点赞，再删除帖子
                        $database->query("DELETE FROM forum_likes WHERE target_type = 'post' AND target_id = ?", [$postId]);
                        $database->query("DELETE FROM forum_replies WHERE post_id = ?", [$postId]);
                        $database->query("DELETE FROM forum_posts WHERE id = ?", [$postId]);

                        $logger->info("论坛帖子删除", ['post_id' => $postId, 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=posts&success=post_deleted');
                        exit;
                    }
                    break;

                case 'batch_delete_posts':
                    $postIds = $_POST['post_ids'] ?? [];
                    if (!empty($postIds)) {
                        $placeholders = str_repeat('?,', count($postIds) - 1) . '?';
                        $database->query("DELETE FROM forum_likes WHERE target_type = 'post' AND target_id IN ($placeholders)", $postIds);
                        $database->query("DELETE FROM forum_replies WHERE post_id IN ($placeholders)", $postIds);
                        $database->query("DELETE FROM forum_posts WHERE id IN ($placeholders)", $postIds);

                        $logger->info("论坛帖子批量删除", ['count' => count($postIds), 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=posts&success=posts_deleted&count=' . count($postIds));
                        exit;
                    }
                    break;

                case 'delete_reply':
                    $replyId = (int)($_POST['reply_id'] ?? 0);
                    if ($replyId > 0) {
                        $database->query("DELETE FROM forum_likes WHERE target_type = 'reply' AND target_id = ?", [$replyId]);
                        $database->query("DELETE FROM forum_replies WHERE id = ?", [$replyId]);

                        $logger->info("论坛回复删除", ['reply_id' => $replyId, 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=replies&success=reply_deleted');
                        exit;
                    }
                    break;

                case 'batch_delete_replies':
                    $replyIds = $_POST['reply_ids'] ?? [];
                    if (!empty($replyIds)) {
                        $placeholders = str_repeat('?,', count($replyIds) - 1) . '?';
                        $database->query("DELETE FROM forum_likes WHERE target_type = 'reply' AND target_id IN ($placeholders)", $replyIds);
                        $database->query("DELETE FROM forum_replies WHERE id IN ($placeholders)", $replyIds);

                        $logger->info("论坛回复批量删除", ['count' => count($replyIds), 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=replies&success=replies_deleted&count=' . count($replyIds));
                        exit;
                    }
                    break;

                case 'delete_message':
                    $messageId = (int)($_POST['message_id'] ?? 0);
                    if ($messageId > 0) {
                        $database->query("DELETE FROM forum_messages WHERE id = ?", [$messageId]);

                        $logger->info("论坛消息删除", ['message_id' => $messageId, 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=messages&success=message_deleted');
                        exit;
                    }
                    break;

                case 'batch_delete_messages':
                    $messageIds = $_POST['message_ids'] ?? [];
                    if (!empty($messageIds)) {
                        $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
                        $database->query("DELETE FROM forum_messages WHERE id IN ($placeholders)", $messageIds);

                        $logger->info("论坛消息批量删除", ['count' => count($messageIds), 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=messages&success=messages_deleted&count=' . count($messageIds));
                        exit;
                    }
                    break;

                case 'ban_user':
                    $username = trim($_POST['username'] ?? '');
                    if (!empty($username)) {
                        $database->update('users', ['status' => 0], 'username = ?', [$username]);

                        $logger->info("用户发言权限禁用", ['username' => $username, 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=users&success=user_banned&username=' . urlencode($username));
                        exit;
                    }
                    break;

                case 'unban_user':
                    $username = trim($_POST['username'] ?? '');
                    if (!empty($username)) {
                        $database->update('users', ['status' => 1], 'username = ?', [$username]);

                        $logger->info("用户发言权限恢复", ['username' => $username, 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=users&success=user_unbanned&username=' . urlencode($username));
                        exit;
                    }
                    break;

                case 'toggle_post_status':
                    $postId = (int)($_POST['post_id'] ?? 0);
                    $newStatus = (int)($_POST['new_status'] ?? 1);
                    if ($postId > 0) {
                        $database->update('forum_posts', ['status' => $newStatus], 'id = ?', [$postId]);

                        $statusText = $newStatus == 2 ? '置顶' : ($newStatus == 1 ? '正常' : '隐藏');
                        $logger->info("帖子状态更新", ['post_id' => $postId, 'status' => $statusText, 'admin_id' => $_SESSION['admin_id']]);
                        header('Location: admin.php?page=forum&tab=posts&success=post_status_updated');
                        exit;
                    }
                    break;

                default:
                    $forumErrorMessage = "未知的操作类型";
                    break;
            }
        } catch (Exception $e) {
            $forumErrorMessage = "操作失败：" . $e->getMessage();
            $logger->error("论坛管理操作失败", ['action' => $forumAction, 'error' => $e->getMessage(), 'admin_id' => $_SESSION['admin_id']]);
        }
    }

    // 处理授权管理操作
    if ($page === 'licenses' && $_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            if ($action === 'add') {
                // 添加新授权
                $licenseType = $_POST['license_type'] ?? 'basic';
                $duration = (int)($_POST['duration'] ?? 12);
                $notes = trim($_POST['notes'] ?? '');
                $qqStatus = $_POST['qq_status'] ?? 'pending';
                $qqNumber = $qqStatus === 'manual' ? trim($_POST['qq_number'] ?? '') : 'PENDING';

                // 生成授权码
                $typePrefix = strtoupper($licenseType);
                $year = date('Y');
                $randomCode = strtoupper(substr(md5(uniqid()), 0, 8));
                $licenseKey = "{$typePrefix}-{$year}-{$randomCode}";

                // 处理密码设置
                $passwordType = $_POST['password_type'] ?? 'auto';
                if ($passwordType === 'custom' && !empty($_POST['custom_password'])) {
                    $password = trim($_POST['custom_password']);
                    // 验证自定义密码
                    if (strlen($password) < 6 || strlen($password) > 20) {
                        throw new Exception('密码长度必须在6-20位之间');
                    }
                } else {
                    // 生成随机密码（8位数字+字母）
                    $password = strtoupper(substr(md5(uniqid() . time()), 0, 8));
                }

                // 设置设备限制
                $maxDevices = [
                    'basic' => 1,
                    'pro' => 3,
                    'enterprise' => 10,
                    'admin' => 999
                ][$licenseType] ?? 1;

                // 计算过期时间
                $expireTime = null;
                if ($duration > 0) {
                    $expireTime = date('Y-m-d H:i:s', strtotime("+{$duration} months"));
                }

                // 插入数据库
                $database->insert('users', [
                    'username' => $licenseKey,
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'plain_password' => $password, // 保存明文密码供管理员查看
                    'user_type' => $licenseType,
                    'max_devices' => $maxDevices,
                    'status' => 1,
                    'expire_time' => $expireTime,
                    'qq_number' => $qqNumber,
                    'notes' => $notes
                ]);

                $passwordSource = $passwordType === 'custom' ? '自定义密码' : '自动生成';
                $successMessage = "
                    <div class='alert alert-success border-0 shadow-sm' id='successAlert'>
                        <h6 class='alert-heading'><i class='bi bi-check-circle me-2'></i>授权创建成功！</h6>
                        <hr>
                        <div class='row'>
                            <div class='col-md-6'>
                                <strong>授权码（用户名）：</strong><br>
                                <div class='input-group mb-2'>
                                    <input type='text' class='form-control' value='{$licenseKey}' id='licenseKeyInput' readonly>
                                    <button class='btn btn-outline-primary' type='button' onclick='copyToClipboard(\"licenseKeyInput\")'>
                                        <i class='bi bi-clipboard'></i> 复制
                                    </button>
                                </div>
                            </div>
                            <div class='col-md-6'>
                                <strong>登录密码（{$passwordSource}）：</strong><br>
                                <div class='input-group mb-2'>
                                    <input type='text' class='form-control' value='{$password}' id='passwordInput' readonly>
                                    <button class='btn btn-outline-danger' type='button' onclick='copyToClipboard(\"passwordInput\")'>
                                        <i class='bi bi-clipboard'></i> 复制
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class='row mb-3'>
                            <div class='col-12'>
                                <strong>完整登录信息：</strong><br>
                                <div class='input-group'>
                                    <textarea class='form-control' rows='3' id='fullInfoInput' readonly>授权码：{$licenseKey}
密码：{$password}
类型：{$licenseType}
设备数：{$maxDevices}
有效期：" . ($expireTime ?: '永久') . "</textarea>
                                    <button class='btn btn-outline-success' type='button' onclick='copyToClipboard(\"fullInfoInput\")'>
                                        <i class='bi bi-clipboard'></i> 复制全部
                                    </button>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class='d-flex align-items-center justify-content-between'>
                            <div class='d-flex align-items-center'>
                                <i class='bi bi-exclamation-triangle text-warning me-2'></i>
                                <small class='text-muted'>
                                    <strong>重要提醒：</strong>请立即复制并保存上述信息，密码信息不会再次显示！
                                </small>
                            </div>
                            <div>
                                <button class='btn btn-sm btn-outline-secondary me-2' onclick='cancelAutoRedirect()'>
                                    <i class='bi bi-pause'></i> 取消自动跳转
                                </button>
                                <button class='btn btn-sm btn-primary' onclick='confirmAndRedirect()'>
                                    <i class='bi bi-check'></i> 已保存，继续
                                </button>
                            </div>
                        </div>
                        <div class='mt-2'>
                            <small class='text-muted'>
                                <span id='countdown'>30</span> 秒后自动跳转到授权列表
                            </small>
                        </div>
                    </div>
                ";

                // 添加JavaScript处理复制和倒计时
                echo "<script>
                let countdownTimer;
                let countdownSeconds = 30;
                let autoRedirectEnabled = true;

                function copyToClipboard(elementId) {
                    const element = document.getElementById(elementId);
                    element.select();
                    element.setSelectionRange(0, 99999);
                    document.execCommand('copy');

                    // 显示复制成功提示
                    const button = element.nextElementSibling;
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class=\"bi bi-check\"></i> 已复制';
                    button.classList.remove('btn-outline-primary', 'btn-outline-danger', 'btn-outline-success');
                    button.classList.add('btn-success');

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.classList.remove('btn-success');
                        if (elementId === 'licenseKeyInput') {
                            button.classList.add('btn-outline-primary');
                        } else if (elementId === 'passwordInput') {
                            button.classList.add('btn-outline-danger');
                        } else {
                            button.classList.add('btn-outline-success');
                        }
                    }, 2000);
                }

                function updateCountdown() {
                    document.getElementById('countdown').textContent = countdownSeconds;
                    if (countdownSeconds <= 0 && autoRedirectEnabled) {
                        window.location.href = 'admin.php?page=licenses&new_user=" . urlencode($licenseKey) . "';
                    }
                    countdownSeconds--;
                }

                function cancelAutoRedirect() {
                    autoRedirectEnabled = false;
                    clearInterval(countdownTimer);
                    document.getElementById('countdown').textContent = '已取消';
                }

                function confirmAndRedirect() {
                    if (confirm('确认已保存授权信息？\\n\\n点击确定将跳转到授权列表页面。')) {
                        window.location.href = 'admin.php?page=licenses&new_user=" . urlencode($licenseKey) . "';
                    }
                }

                // 启动倒计时
                countdownTimer = setInterval(updateCountdown, 1000);
                updateCountdown();
                </script>";

            } elseif ($action === 'edit') {
                // 编辑授权
                $licenseId = (int)$_POST['license_id'];
                $licenseType = $_POST['license_type'] ?? 'basic';
                $duration = (int)($_POST['duration'] ?? 0);
                $maxDevices = (int)($_POST['max_devices'] ?? 1);
                $status = (int)($_POST['status'] ?? 1);
                $notes = trim($_POST['notes'] ?? '');
                $qqNumber = trim($_POST['qq_number'] ?? '');

                $updateData = [
                    'user_type' => $licenseType,
                    'max_devices' => $maxDevices,
                    'status' => $status,
                    'qq_number' => $qqNumber,
                    'notes' => $notes,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // 更新过期时间
                if ($duration > 0) {
                    $updateData['expire_time'] = date('Y-m-d H:i:s', strtotime("+{$duration} months"));
                } elseif ($duration === -1) {
                    $updateData['expire_time'] = null; // 永久
                }

                $database->update('users', $updateData, 'id = ?', [$licenseId]);
                $successMessage = "授权信息更新成功！";

            } elseif ($action === 'reset_password') {
                // 重置密码
                $licenseId = (int)$_POST['license_id'];
                $newPassword = strtoupper(substr(md5(uniqid() . time()), 0, 8));

                $database->update('users',
                    [
                        'password' => password_hash($newPassword, PASSWORD_DEFAULT),
                        'plain_password' => $newPassword // 保存明文密码供管理员查看
                    ],
                    'id = ?',
                    [$licenseId]
                );

                // 获取用户名用于显示
                $user = $database->fetchOne("SELECT username FROM users WHERE id = ?", [$licenseId]);
                $successMessage = "
                    <div class='alert alert-warning border-0 shadow-sm'>
                        <h6 class='alert-heading'><i class='bi bi-key me-2'></i>密码重置成功！</h6>
                        <hr>
                        <div class='row'>
                            <div class='col-md-6'>
                                <strong>授权码（用户名）：</strong><br>
                                <div class='input-group mb-2'>
                                    <input type='text' class='form-control' value='{$user['username']}' id='resetLicenseInput' readonly>
                                    <button class='btn btn-outline-primary' type='button' onclick='copyToClipboard(\"resetLicenseInput\")'>
                                        <i class='bi bi-clipboard'></i> 复制
                                    </button>
                                </div>
                            </div>
                            <div class='col-md-6'>
                                <strong>新密码：</strong><br>
                                <div class='input-group mb-2'>
                                    <input type='text' class='form-control' value='{$newPassword}' id='resetPasswordInput' readonly>
                                    <button class='btn btn-outline-danger' type='button' onclick='copyToClipboard(\"resetPasswordInput\")'>
                                        <i class='bi bi-clipboard'></i> 复制
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class='row mb-3'>
                            <div class='col-12'>
                                <strong>重置后的登录信息：</strong><br>
                                <div class='input-group'>
                                    <textarea class='form-control' rows='2' id='resetFullInfoInput' readonly>授权码：{$user['username']}
新密码：{$newPassword}</textarea>
                                    <button class='btn btn-outline-success' type='button' onclick='copyToClipboard(\"resetFullInfoInput\")'>
                                        <i class='bi bi-clipboard'></i> 复制全部
                                    </button>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class='d-flex align-items-center'>
                            <i class='bi bi-exclamation-triangle text-warning me-2'></i>
                            <small class='text-muted'>
                                <strong>重要提醒：</strong>请立即复制并保存新密码，旧密码已失效！建议通过安全渠道发送给用户。
                            </small>
                        </div>
                    </div>
                ";

            } elseif ($action === 'get_password') {
                // 获取密码信息
                header('Content-Type: application/json');
                $licenseId = (int)$_POST['license_id'];
                $user = $database->fetchOne("SELECT username, plain_password, created_at FROM users WHERE id = ?", [$licenseId]);

                if ($user) {
                    if (!empty($user['plain_password'])) {
                        // 有明文密码，直接显示
                        echo json_encode([
                            'success' => true,
                            'message' => "用户：{$user['username']}\n密码：{$user['plain_password']}\n创建时间：{$user['created_at']}\n\n⚠️ 请妥善保管密码信息！"
                        ]);
                    } else {
                        // 没有明文密码，提示重置
                        echo json_encode([
                            'success' => true,
                            'message' => "用户：{$user['username']}\n创建时间：{$user['created_at']}\n\n密码已加密存储，如需查看请重置密码。"
                        ]);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => '用户不存在']);
                }
                exit;

            } elseif ($action === 'get_device_details') {
                // 获取设备详情
                header('Content-Type: application/json');
                $deviceId = $_POST['device_id'] ?? '';
                $userId = (int)($_POST['user_id'] ?? 0);

                if ($deviceId && $userId) {
                    $device = $database->fetchOne("
                        SELECT d.*, u.username, u.user_type
                        FROM user_devices d
                        LEFT JOIN users u ON d.user_id = u.id
                        WHERE d.device_id = ? AND d.user_id = ?
                    ", [$deviceId, $userId]);

                    if ($device) {
                        echo json_encode(['success' => true, 'device' => $device]);
                    } else {
                        echo json_encode(['success' => false, 'message' => '设备不存在']);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => '参数错误']);
                }
                exit;

            } elseif ($action === 'system_health_check') {
                // 系统健康检查
                header('Content-Type: application/json');
                $results = [];

                // 检查数据库连接
                try {
                    $database->fetchValue("SELECT 1");
                    $results[] = ['name' => '检查数据库连接...', 'status' => 'success', 'message' => '数据库连接正常'];
                } catch (Exception $e) {
                    $results[] = ['name' => '检查数据库连接...', 'status' => 'error', 'message' => '数据库连接失败: ' . $e->getMessage()];
                }

                // 检查文件权限
                $dataDir = __DIR__ . '/data';
                if (is_writable($dataDir)) {
                    $results[] = ['name' => '检查文件权限...', 'status' => 'success', 'message' => '文件权限正常'];
                } else {
                    $results[] = ['name' => '检查文件权限...', 'status' => 'warning', 'message' => 'data目录不可写'];
                }

                // 检查PHP扩展
                $requiredExtensions = ['pdo', 'json', 'mbstring'];
                $missingExtensions = [];
                foreach ($requiredExtensions as $ext) {
                    if (!extension_loaded($ext)) {
                        $missingExtensions[] = $ext;
                    }
                }
                if (empty($missingExtensions)) {
                    $results[] = ['name' => '检查PHP扩展...', 'status' => 'success', 'message' => '所需扩展已安装'];
                } else {
                    $results[] = ['name' => '检查PHP扩展...', 'status' => 'error', 'message' => '缺少扩展: ' . implode(', ', $missingExtensions)];
                }

                // 检查磁盘空间
                $freeBytes = disk_free_space(__DIR__);
                $freeGB = round($freeBytes / 1024 / 1024 / 1024, 2);
                if ($freeGB > 1) {
                    $results[] = ['name' => '检查磁盘空间...', 'status' => 'success', 'message' => "可用空间: {$freeGB}GB"];
                } else {
                    $results[] = ['name' => '检查磁盘空间...', 'status' => 'warning', 'message' => "可用空间不足: {$freeGB}GB"];
                }

                // 检查内存使用
                $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
                $memoryLimit = ini_get('memory_limit');
                $results[] = ['name' => '检查内存使用...', 'status' => 'success', 'message' => "当前使用: {$memoryUsage}MB, 限制: {$memoryLimit}"];

                echo json_encode(['success' => true, 'results' => $results]);
                exit;

            } elseif ($action === 'get_performance_metrics') {
                // 获取性能指标
                header('Content-Type: application/json');

                try {
                    // 获取内存使用率
                    $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 1);
                    $memoryLimit = ini_get('memory_limit');
                    $memoryLimitBytes = parseMemoryLimit($memoryLimit);
                    $memoryPercent = $memoryLimitBytes > 0 ? round(($memoryUsage * 1024 * 1024) / $memoryLimitBytes * 100, 1) : 0;

                    // 获取磁盘使用率
                    $diskFree = disk_free_space(__DIR__);
                    $diskTotal = disk_total_space(__DIR__);
                    $diskUsed = $diskTotal - $diskFree;
                    $diskPercent = round(($diskUsed / $diskTotal) * 100, 1);

                    // CPU使用率（简单模拟，实际需要系统调用）
                    $cpuPercent = rand(5, 25); // 模拟CPU使用率

                    echo json_encode([
                        'success' => true,
                        'cpu_usage' => $cpuPercent . '%',
                        'memory_usage' => $memoryPercent . '%',
                        'disk_usage' => $diskPercent . '%'
                    ]);
                } catch (Exception $e) {
                    echo json_encode([
                        'success' => false,
                        'message' => '获取性能指标失败: ' . $e->getMessage()
                    ]);
                }
                exit;

            } elseif ($action === 'toggle_maintenance') {
                // 切换维护模式
                header('Content-Type: application/json');

                $enabled = $_POST['enabled'] === '1';
                $maintenanceFile = __DIR__ . '/maintenance.lock';

                try {
                    if ($enabled) {
                        file_put_contents($maintenanceFile, date('Y-m-d H:i:s'));
                        echo json_encode(['success' => true, 'message' => '维护模式已启用']);
                    } else {
                        if (file_exists($maintenanceFile)) {
                            unlink($maintenanceFile);
                        }
                        echo json_encode(['success' => true, 'message' => '维护模式已关闭']);
                    }
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => '操作失败: ' . $e->getMessage()]);
                }
                exit;

            } elseif ($action === 'list_backups') {
                // 列出备份文件
                header('Content-Type: application/json');

                try {
                    $backupDir = __DIR__ . '/backups';
                    $backups = [];

                    if (is_dir($backupDir)) {
                        $files = glob($backupDir . '/*.sql');
                        foreach ($files as $file) {
                            $backups[] = [
                                'name' => basename($file),
                                'size' => formatFileSize(filesize($file)),
                                'date' => date('Y-m-d H:i:s', filemtime($file))
                            ];
                        }
                    }

                    echo json_encode(['success' => true, 'backups' => $backups]);
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => '获取备份列表失败: ' . $e->getMessage()]);
                }
                exit;

            } elseif ($action === 'delete') {
                // 删除授权
                $licenseId = (int)$_POST['license_id'];
                $database->delete('users', 'id = ?', [$licenseId]);
                $successMessage = "授权删除成功！";

            } elseif ($action === 'batch') {
                // 批量操作
                $licenseIds = $_POST['license_ids'] ?? [];
                $batchAction = $_POST['batch_action'] ?? '';

                if (!empty($licenseIds) && $batchAction) {
                    $placeholders = str_repeat('?,', count($licenseIds) - 1) . '?';

                    switch ($batchAction) {
                        case 'enable':
                            $database->query("UPDATE users SET status = 1 WHERE id IN ({$placeholders})", $licenseIds);
                            $successMessage = "批量启用成功！";
                            break;
                        case 'disable':
                            $database->query("UPDATE users SET status = 0 WHERE id IN ({$placeholders})", $licenseIds);
                            $successMessage = "批量禁用成功！";
                            break;
                        case 'extend':
                            $months = (int)($_POST['extend_months'] ?? 1);
                            $database->query("
                                UPDATE users
                                SET expire_time = CASE
                                    WHEN expire_time IS NULL THEN DATE_ADD(NOW(), INTERVAL {$months} MONTH)
                                    WHEN expire_time > CURRENT_TIMESTAMP THEN DATE_ADD(expire_time, INTERVAL {$months} MONTH)
                                    ELSE DATE_ADD(NOW(), INTERVAL {$months} MONTH)
                                END
                                WHERE id IN ({$placeholders})
                            ", $licenseIds);
                            $successMessage = "批量延期 {$months} 个月成功！";
                            break;
                    }
                }
            }
        } catch (Exception $e) {
            $errorMessage = "操作失败: " . $e->getMessage();
        }
    }
    
    // 数据库初始化完成

    // 获取统计数据
    $stats = $database->getStats();

    // 获取详细统计数据
    try {
        $detailedStats = [
            'online_licenses' => (int)$database->fetchValue("
                SELECT COUNT(DISTINCT user_id) FROM access_tokens
                WHERE expires_at > CURRENT_TIMESTAMP
            ") ?: 0,
            'total_licenses' => (int)$database->fetchValue("SELECT COUNT(*) FROM users WHERE status = 1") ?: 0,
            'today_verifications' => (int)$database->fetchValue("
                SELECT COUNT(*) FROM login_logs
                WHERE DATE(created_at) = CURDATE()
            ") ?: 0,
            'active_devices' => (int)$database->fetchValue("
                SELECT COUNT(*) FROM user_devices WHERE status = 1
            ") ?: 0,
            'failed_today' => (int)$database->fetchValue("
                SELECT COUNT(*) FROM login_logs
                WHERE DATE(created_at) = CURDATE() AND login_type = 'failed'
            ") ?: 0
        ];
    } catch (Exception $e) {
        $detailedStats = [
            'online_licenses' => 0,
            'total_licenses' => 0,
            'today_verifications' => 0,
            'active_devices' => 0,
            'failed_today' => 0
        ];
    }

    // 获取最近7天的验证趋势数据
    $trendData = $database->fetchAll("
        SELECT DATE(created_at) as date,
               COUNT(*) as total,
               SUM(CASE WHEN login_type = 'success' THEN 1 ELSE 0 END) as success,
               SUM(CASE WHEN login_type = 'failed' THEN 1 ELSE 0 END) as failed
        FROM login_logs
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");

    // 获取授权类型分布
    $licenseTypeStats = $database->fetchAll("
        SELECT user_type, COUNT(*) as count
        FROM users
        WHERE status = 1
        GROUP BY user_type
    ");

    // 获取设备使用情况
    $deviceUsageStats = $database->fetchAll("
        SELECT u.user_type, COUNT(d.id) as device_count
        FROM users u
        LEFT JOIN user_devices d ON u.id = d.user_id AND d.status = 1
        WHERE u.status = 1
        GROUP BY u.user_type
    ");

    // 只在仪表板页面加载这些数据
    $recentLicenses = [];
    $recentLogs = [];

    if ($page === 'dashboard') {
        // 获取最近授权
        $recentLicenses = $database->fetchAll(
            "SELECT username as license_key, user_type, created_at, expire_time, status FROM users ORDER BY created_at DESC LIMIT 5"
        );

        // 获取最近授权日志
        $recentLogs = $database->fetchAll(
            "SELECT username as license_key, login_type, ip_address, device_id, created_at FROM login_logs ORDER BY created_at DESC LIMIT 10"
        );
    }

    // 只在仪表板页面加载异常检测数据
    $anomalousLogins = [];
    $multiLocationLogins = [];

    if ($page === 'dashboard') {
        // 检测异常登录
        $anomalousLogins = $database->fetchAll("
            SELECT ll.username as license_key, ll.ip_address, COUNT(*) as attempt_count,
                   MAX(ll.created_at) as last_attempt
            FROM login_logs ll
            WHERE ll.login_type = 'failed'
            AND ll.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            GROUP BY ll.username, ll.ip_address
            HAVING COUNT(*) >= 5
            ORDER BY attempt_count DESC
            LIMIT 5
        ");

        // 检测多地登录
        $multiLocationLogins = $database->fetchAll("
            SELECT u.username as license_key,
                   GROUP_CONCAT(DISTINCT ll.ip_address SEPARATOR ',') as ip_addresses,
                   COUNT(DISTINCT ll.ip_address) as ip_count
            FROM access_tokens t
            JOIN users u ON t.user_id = u.id
            JOIN login_logs ll ON ll.user_id = u.id
            WHERE t.expires_at > CURRENT_TIMESTAMP
            AND ll.created_at >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
            AND ll.login_type = 'success'
            GROUP BY u.id, u.username
            HAVING COUNT(DISTINCT ll.ip_address) > 1
            ORDER BY ip_count DESC
            LIMIT 5
        ");
    }

    // 授权管理页面数据
    if ($page === 'licenses') {
        $search = $_GET['search'] ?? '';
        $statusFilter = $_GET['status'] ?? '';
        $typeFilter = $_GET['type'] ?? '';
        $pageNum = max(1, (int)($_GET['p'] ?? 1));
        $pageSize = 20;
        $offset = ($pageNum - 1) * $pageSize;

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        if ($search) {
            $whereConditions[] = "username LIKE ?";
            $params[] = "%{$search}%";
        }

        if ($statusFilter !== '') {
            if ($statusFilter === 'expired') {
                $whereConditions[] = "expire_time IS NOT NULL AND expire_time < CURRENT_TIMESTAMP";
            } else {
                $whereConditions[] = "status = ?";
                $params[] = (int)$statusFilter;
            }
        }

        if ($typeFilter) {
            $whereConditions[] = "user_type = ?";
            $params[] = $typeFilter;
        }

        $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

        // 获取授权列表
        $licenses = $database->fetchAll("
            SELECT u.*,
                   COUNT(d.id) as device_count,
                   CASE
                       WHEN u.expire_time IS NULL THEN '永久'
                       WHEN u.expire_time > CURRENT_TIMESTAMP THEN '正常'
                       ELSE '已过期'
                   END as expire_status
            FROM users u
            LEFT JOIN user_devices d ON u.id = d.user_id AND d.status = 1
            {$whereClause}
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT {$pageSize} OFFSET {$offset}
        ", $params);

        // 获取总数
        $totalLicenses = (int)$database->fetchValue("
            SELECT COUNT(*) FROM users u {$whereClause}
        ", $params);

        $totalPages = ceil($totalLicenses / $pageSize);
    }

    // 设备管理页面数据
    if ($page === 'devices') {
        // 处理设备操作
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action) {
            try {
                if ($action === 'unbind') {
                    $deviceId = $_POST['device_id'] ?? '';
                    $userId = (int)($_POST['user_id'] ?? 0);

                    if ($deviceId && $userId) {
                        // 删除设备绑定
                        $database->delete('user_devices', 'user_id = ? AND device_id = ?', [$userId, $deviceId]);
                        // 删除相关令牌
                        $database->delete('access_tokens', 'user_id = ? AND device_id = ?', [$userId, $deviceId]);
                        $successMessage = "设备解绑成功！";
                    }
                } elseif ($action === 'rename') {
                    $deviceId = $_POST['device_id'] ?? '';
                    $userId = (int)($_POST['user_id'] ?? 0);
                    $newName = trim($_POST['device_name'] ?? '');

                    if ($deviceId && $userId && $newName) {
                        $database->update('user_devices',
                            ['device_name' => $newName],
                            'user_id = ? AND device_id = ?',
                            [$userId, $deviceId]
                        );
                        $successMessage = "设备重命名成功！";
                    }
                }
            } catch (Exception $e) {
                $errorMessage = "操作失败: " . $e->getMessage();
            }
        }

        // 获取设备列表（只显示已绑定设备的授权码）- MySQL 5.7兼容版本
        $deviceGroups = $database->fetchAll("
            SELECT u.id as user_id, u.username as license_key, u.user_type, u.max_devices,
                   d.id as device_table_id, d.device_id, d.device_name, d.device_info,
                   d.last_login, d.status as device_status,
                   CASE WHEN t.expires_at > CURRENT_TIMESTAMP THEN 1 ELSE 0 END as is_online,
                   ll.ip_address as last_ip
            FROM users u
            INNER JOIN user_devices d ON u.id = d.user_id AND d.status = 1
            LEFT JOIN access_tokens t ON u.id = t.user_id AND d.device_id = t.device_id AND t.expires_at > CURRENT_TIMESTAMP
            LEFT JOIN (
                SELECT ll1.user_id, ll1.device_id, ll1.ip_address
                FROM login_logs ll1
                INNER JOIN (
                    SELECT user_id, device_id, MAX(created_at) as max_created_at
                    FROM login_logs
                    WHERE login_type = 'success'
                    GROUP BY user_id, device_id
                ) ll2 ON ll1.user_id = ll2.user_id
                     AND ll1.device_id = ll2.device_id
                     AND ll1.created_at = ll2.max_created_at
                WHERE ll1.login_type = 'success'
            ) ll ON u.id = ll.user_id AND d.device_id = ll.device_id
            WHERE u.status = 1
            ORDER BY u.username, d.last_login DESC
        ");

        // 直接使用设备列表，每个设备显示为独立卡片
        $deviceList = [];
        foreach ($deviceGroups as $device) {
            if ($device['device_id']) {
                $deviceList[] = $device;
            }
        }

        // 异常检测
        $deviceAnomalies = [
            'over_limit' => $database->fetchAll("
                SELECT u.username as license_key, u.max_devices, COUNT(d.id) as device_count
                FROM users u
                JOIN user_devices d ON u.id = d.user_id AND d.status = 1
                WHERE u.status = 1
                GROUP BY u.id
                HAVING COUNT(d.id) > u.max_devices
            "),
            'multi_location' => $database->fetchAll("
                SELECT u.username as license_key, d.device_id, d.device_name,
                       GROUP_CONCAT(DISTINCT ll.ip_address SEPARATOR ',') as ip_addresses,
                       COUNT(DISTINCT ll.ip_address) as ip_count
                FROM users u
                JOIN user_devices d ON u.id = d.user_id
                JOIN login_logs ll ON u.id = ll.user_id AND d.device_id = ll.device_id
                WHERE ll.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                AND ll.login_type = 'success'
                GROUP BY u.id, d.device_id
                HAVING COUNT(DISTINCT ll.ip_address) > 1
                ORDER BY ip_count DESC
                LIMIT 10
            "),
            'suspicious_activity' => $database->fetchAll("
                SELECT u.username as license_key, d.device_id, d.device_name,
                       COUNT(*) as failed_attempts,
                       MAX(ll.created_at) as last_attempt
                FROM users u
                JOIN user_devices d ON u.id = d.user_id
                JOIN login_logs ll ON u.id = ll.user_id AND d.device_id = ll.device_id
                WHERE ll.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                AND ll.login_type = 'failed'
                GROUP BY u.id, d.device_id
                HAVING COUNT(*) >= 3
                ORDER BY failed_attempts DESC
                LIMIT 10
            ")
        ];
    }

    // 日志管理页面数据
    if ($page === 'logs') {
        $logType = $_GET['type'] ?? 'auth';
        $search = $_GET['search'] ?? '';
        $ipSearch = $_GET['ip'] ?? '';
        $statusFilter = $_GET['status'] ?? '';
        $dateFrom = $_GET['date_from'] ?? '';
        $dateTo = $_GET['date_to'] ?? '';
        $pageNum = max(1, (int)($_GET['p'] ?? 1));
        $pageSize = 50;
        $offset = ($pageNum - 1) * $pageSize;

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        if ($search) {
            $whereConditions[] = "username LIKE ?";
            $params[] = "%{$search}%";
        }

        if ($ipSearch) {
            $whereConditions[] = "ip_address LIKE ?";
            $params[] = "%{$ipSearch}%";
        }

        if ($statusFilter) {
            $whereConditions[] = "login_type = ?";
            $params[] = $statusFilter;
        }

        if ($dateFrom) {
            $whereConditions[] = "DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }

        if ($dateTo) {
            $whereConditions[] = "DATE(created_at) <= ?";
            $params[] = $dateTo;
        }

        $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

        if ($logType === 'auth') {
            // 授权验证日志
            $logs = $database->fetchAll("
                SELECT ll.*, u.user_type
                FROM login_logs ll
                LEFT JOIN users u ON ll.user_id = u.id
                {$whereClause}
                ORDER BY ll.created_at DESC
                LIMIT {$pageSize} OFFSET {$offset}
            ", $params);

            $totalLogs = (int)$database->fetchValue("
                SELECT COUNT(*) FROM login_logs ll
                LEFT JOIN users u ON ll.user_id = u.id
                {$whereClause}
            ", $params);

        } elseif ($logType === 'heartbeat') {
            // 心跳检测日志
            $heartbeatWhere = str_replace('username', 'u.username', $whereClause);
            $heartbeatWhere = str_replace('login_type', 'h.id', $heartbeatWhere); // 心跳日志没有login_type

            $logs = $database->fetchAll("
                SELECT h.*, u.username, u.user_type
                FROM heartbeat_logs h
                LEFT JOIN users u ON h.user_id = u.id
                {$heartbeatWhere}
                ORDER BY h.created_at DESC
                LIMIT {$pageSize} OFFSET {$offset}
            ", $params);

            $totalLogs = (int)$database->fetchValue("
                SELECT COUNT(*) FROM heartbeat_logs h
                LEFT JOIN users u ON h.user_id = u.id
                {$heartbeatWhere}
            ", $params);
        }

        $totalPages = ceil($totalLogs / $pageSize);

        // 日志统计
        $logStats = [
            'today_total' => (int)$database->fetchValue("
                SELECT COUNT(*) FROM login_logs
                WHERE DATE(created_at) = CURDATE()
            "),
            'today_success' => (int)$database->fetchValue("
                SELECT COUNT(*) FROM login_logs
                WHERE DATE(created_at) = CURDATE() AND login_type = 'success'
            "),
            'today_failed' => (int)$database->fetchValue("
                SELECT COUNT(*) FROM login_logs
                WHERE DATE(created_at) = CURDATE() AND login_type = 'failed'
            "),
            'week_total' => (int)$database->fetchValue("
                SELECT COUNT(*) FROM login_logs
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            "),
            'unique_ips_today' => (int)$database->fetchValue("
                SELECT COUNT(DISTINCT ip_address) FROM login_logs
                WHERE DATE(created_at) = CURDATE()
            ")
        ];
    }

    // 系统设置页面数据和处理
    if ($page === 'settings') {
        // 处理设置操作
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action) {
            try {
                if ($action === 'force_logout') {
                    // 强制所有用户重新登录
                    $database->query("DELETE FROM access_tokens");
                    $successMessage = "已强制所有用户重新登录！";

                } elseif ($action === 'cleanup_expired') {
                    // 清理过期数据
                    $deletedTokens = $database->query("DELETE FROM access_tokens WHERE expires_at < CURRENT_TIMESTAMP");
                    $deletedHeartbeats = $database->query("DELETE FROM heartbeat_logs WHERE created_at < DATE_SUB(CURDATE(), INTERVAL 30 DAY)");

                    $successMessage = "数据清理完成！清理了过期令牌和30天前的心跳记录。";

                } elseif ($action === 'cleanup_logs') {
                    $days = (int)($_POST['cleanup_days'] ?? 30);
                    $deletedLogs = $database->query("DELETE FROM login_logs WHERE created_at < DATE_SUB(CURDATE(), INTERVAL {$days} DAY)");

                    $successMessage = "已清理 {$days} 天前的登录日志！";

                } elseif ($action === 'change_password') {
                    // 修改管理员密码
                    $currentPassword = $_POST['current_password'] ?? '';
                    $newPassword = $_POST['new_password'] ?? '';
                    $confirmPassword = $_POST['confirm_password'] ?? '';

                    // 验证输入
                    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                        throw new Exception("所有密码字段都是必填的");
                    }

                    if ($newPassword !== $confirmPassword) {
                        throw new Exception("新密码和确认密码不匹配");
                    }

                    if (strlen($newPassword) < 6) {
                        throw new Exception("新密码长度至少6位");
                    }

                    // 获取当前管理员信息
                    $currentAdmin = $database->fetchOne("SELECT * FROM admins WHERE username = ?", [$_SESSION['admin_username']]);
                    if (!$currentAdmin) {
                        throw new Exception("管理员账户不存在");
                    }

                    // 验证当前密码
                    if (!password_verify($currentPassword, $currentAdmin['password'])) {
                        throw new Exception("当前密码不正确");
                    }

                    // 更新密码
                    $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    $database->query("UPDATE admins SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE username = ?", [
                        $hashedNewPassword,
                        $_SESSION['admin_username']
                    ]);

                    // 记录操作日志
                    $database->query("INSERT INTO admin_logs (admin_id, action, details, ip_address, created_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)", [
                        $currentAdmin['id'],
                        'change_password',
                        '管理员修改了登录密码',
                        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);

                    $successMessage = "密码修改成功！";
                }
            } catch (Exception $e) {
                $errorMessage = "操作失败: " . $e->getMessage();
            }
        }

        // 获取系统信息
        try {
            $dbVersion = $database->fetchValue("SELECT VERSION()");
        } catch (Exception $e) {
            $dbVersion = "MySQL 5.7.38";
        }

        $systemInfo = [
            'php_version' => PHP_VERSION,
            'mysql_version' => $dbVersion,
            'server_time' => date('Y-m-d H:i:s'),
            'timezone' => date_default_timezone_get(),
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
            'memory_limit' => ini_get('memory_limit'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size')
        ];

        // 获取数据库统计
        try {
            $dbSizeQuery = $database->fetchValue("
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as db_size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
            ");
            $dbSize = $dbSizeQuery ? $dbSizeQuery . ' MB' : 'Unknown';
        } catch (Exception $e) {
            $dbSize = 'Unknown';
        }

        $dbStats = [
            'users_count' => (int)$database->fetchValue("SELECT COUNT(*) FROM users") ?: 0,
            'devices_count' => (int)$database->fetchValue("SELECT COUNT(*) FROM user_devices") ?: 0,
            'tokens_count' => (int)$database->fetchValue("SELECT COUNT(*) FROM access_tokens") ?: 0,
            'logs_count' => (int)$database->fetchValue("SELECT COUNT(*) FROM login_logs") ?: 0,
            'heartbeat_count' => (int)$database->fetchValue("SELECT COUNT(*) FROM heartbeat_logs") ?: 0,
            'db_size' => $dbSize
        ];

        // 检查文件权限
        $permissionChecks = [
            'config_dir' => is_writable(__DIR__ . '/config'),
            'logs_dir' => is_dir(__DIR__ . '/logs') ? is_writable(__DIR__ . '/logs') : 'Directory not exists',
            'includes_dir' => is_readable(__DIR__ . '/includes')
        ];
    }

    // 在线授权页面数据
    if ($page === 'online') {
        // 获取在线授权
        $onlineLicenses = $database->fetchAll("
            SELECT DISTINCT u.username as license_key, u.user_type, t.device_id, t.created_at as login_time
            FROM access_tokens t
            JOIN users u ON t.user_id = u.id
            WHERE t.expires_at > CURRENT_TIMESTAMP
            ORDER BY t.created_at DESC
        ");
    }

    // 调试日志页面数据
    if ($page === 'debug') {
        // 处理调试操作
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action) {
            try {
                if ($action === 'clear_logs') {
                    // 清空系统日志
                    $logFile = __DIR__ . '/logs/system.log';
                    if (file_exists($logFile)) {
                        file_put_contents($logFile, '');
                        $successMessage = "系统日志已清空！";
                    }
                } elseif ($action === 'test_device_query') {
                    // 测试设备查询
                    $deviceId = $_POST['device_id'] ?? '';
                    $userId = (int)($_POST['user_id'] ?? 0);

                    if ($deviceId && $userId) {
                        $logger->info("调试：测试设备查询", [
                            'device_id' => $deviceId,
                            'user_id' => $userId
                        ]);

                        // 执行查询并记录结果
                        $device = $database->fetchOne("
                            SELECT d.*, u.username, u.user_type
                            FROM user_devices d
                            LEFT JOIN users u ON d.user_id = u.id
                            WHERE d.device_id = ? AND d.user_id = ?
                        ", [$deviceId, $userId]);

                        $logger->info("调试：设备查询结果", [
                            'found' => $device ? 'yes' : 'no',
                            'data' => $device
                        ]);

                        $successMessage = "设备查询测试完成，请查看调试日志！";
                    }
                } elseif ($action === 'test_db_connection') {
                    // 测试数据库连接
                    $logger->info("调试：测试数据库连接");

                    try {
                        $result = $database->fetchValue("SELECT COUNT(*) FROM users");
                        $logger->info("调试：数据库连接成功", ['user_count' => $result]);
                        $successMessage = "数据库连接测试成功！用户数量: {$result}";
                    } catch (Exception $e) {
                        $logger->error("调试：数据库连接失败", ['error' => $e->getMessage()]);
                        $errorMessage = "数据库连接失败: " . $e->getMessage();
                    }
                } elseif ($action === 'generate_test_log') {
                    // 生成测试日志
                    $logger->info("调试：生成测试日志", [
                        'timestamp' => date('Y-m-d H:i:s'),
                        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);

                    $logger->warning("调试：这是一个警告级别的测试日志", [
                        'test_data' => ['key1' => 'value1', 'key2' => 'value2']
                    ]);

                    $logger->error("调试：这是一个错误级别的测试日志", [
                        'error_code' => 'TEST_ERROR',
                        'details' => '这是一个模拟的错误信息'
                    ]);

                    $successMessage = "测试日志已生成！";
                }
            } catch (Exception $e) {
                $logger->error("调试操作失败", ['action' => $action, 'error' => $e->getMessage()]);
                $errorMessage = "调试操作失败: " . $e->getMessage();
            }
        }

        // 读取系统日志
        $logFile = __DIR__ . '/logs/system.log';
        $debugLogs = [];
        if (file_exists($logFile)) {
            $logContent = file_get_contents($logFile);
            $logLines = array_filter(explode("\n", $logContent));
            $debugLogs = array_slice(array_reverse($logLines), 0, 100); // 最近100条
        }

        // 获取数据库表信息
        $tableStats = [];
        try {
            $tables = ['users', 'user_devices', 'access_tokens', 'login_logs', 'heartbeat_logs'];
            foreach ($tables as $table) {
                $count = $database->fetchValue("SELECT COUNT(*) FROM {$table}");
                $tableStats[$table] = $count;
            }
        } catch (Exception $e) {
            $logger->error("获取表统计失败", ['error' => $e->getMessage()]);
        }

        // 获取最近的设备数据
        $recentDevices = [];
        try {
            $recentDevices = $database->fetchAll("
                SELECT d.*, u.username
                FROM user_devices d
                LEFT JOIN users u ON d.user_id = u.id
                ORDER BY d.created_at DESC
                LIMIT 10
            ");
        } catch (Exception $e) {
            $logger->error("获取设备数据失败", ['error' => $e->getMessage()]);
        }
    }

} catch (Exception $e) {
    $dbError = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - Augment认证系统</title>
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="apple-touch-icon" href="favicon.svg">
    <style>
        /* 现代化重置和基础样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        /* 全局去除链接下划线 */
        a, a:hover, a:focus, a:active, a:visited {
            text-decoration: none !important;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 现代化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 布局系统 */
        .container, .container-fluid { width: 100%; padding: 0 15px; margin: 0 auto; max-width: 1200px; }
        .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
        .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
        .col-md-6, .col-lg-4, .col-lg-6, .col-lg-8, .col-xl-3 {
            position: relative;
            width: 100%;
            padding: 0 15px;
            min-height: 1px;
        }
        .col-md-6 { flex: 0 0 50%; max-width: 50%; }
        .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
        .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
        .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
        .col-xl-3 { flex: 0 0 25%; max-width: 25%; }

        /* 现代化按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border: none;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .btn:hover, .btn:focus {
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            color: #fff;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: #fff;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            color: #fff;
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        }
        .btn-secondary:hover {
            background: linear-gradient(135deg, #5c636a 0%, #495057 100%);
            color: #fff;
        }

        .btn-success {
            color: #fff;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1abc9c 100%);
            color: #fff;
        }

        .btn-danger {
            color: #fff;
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #c0392b 100%);
            color: #fff;
        }

        .btn-warning {
            color: #fff;
            background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }
        .btn-warning:hover {
            background: linear-gradient(135deg, #e0a800 0%, #d68910 100%);
            color: #fff;
        }

        .btn-info {
            color: #fff;
            background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }
        .btn-info:hover {
            background: linear-gradient(135deg, #138496 0%, #2980b9 100%);
            color: #fff;
        }

        .btn-outline-primary {
            color: #667eea;
            border: 2px solid #667eea;
            background-color: transparent;
        }
        .btn-outline-primary:hover {
            color: #fff;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
        }

        .btn-outline-secondary {
            color: #6c757d;
            border: 2px solid #6c757d;
            background-color: transparent;
        }
        .btn-outline-secondary:hover {
            color: #fff;
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            border-color: #6c757d;
        }

        .btn-outline-info {
            color: #17a2b8;
            border: 2px solid #17a2b8;
            background-color: transparent;
        }
        .btn-outline-info:hover {
            color: #fff;
            background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
            border-color: #17a2b8;
        }

        .btn-outline-warning {
            color: #ffc107;
            border: 2px solid #ffc107;
            background-color: transparent;
        }
        .btn-outline-warning:hover {
            color: #fff;
            background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
            border-color: #ffc107;
        }

        .btn-outline-danger {
            color: #dc3545;
            border: 2px solid #dc3545;
            background-color: transparent;
        }
        .btn-outline-danger:hover {
            color: #fff;
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            border-color: #dc3545;
        }

        .btn-outline-light {
            color: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
            background-color: transparent;
        }
        .btn-outline-light:hover {
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 255, 255, 0.9);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            border-radius: 20px;
        }
        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1rem;
            border-radius: 30px;
        }
        .btn-group, .btn-group-vertical {
            position: relative;
            display: inline-flex;
            vertical-align: middle;
        }
        .btn-group > .btn, .btn-group-vertical > .btn {
            position: relative;
            flex: 1 1 auto;
        }
        .btn-group > .btn:not(:first-child), .btn-group-vertical > .btn:not(:first-child) {
            margin-left: -1px;
        }
        .btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        .btn-group > .btn:nth-child(n + 3), .btn-group-vertical > .btn:nth-child(n + 3),
        .btn-group > :not(.btn-check) + .btn, .btn-group-vertical > :not(.btn-check) + .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        .btn-group-sm > .btn { padding: 0.25rem 0.5rem; font-size: 0.75rem; }
        .btn-group-lg > .btn { padding: 0.5rem 1rem; font-size: 1rem; }

        /* 现代化卡片样式 */
        .card {
            position: relative;
            display: flex;
            flex-direction: column;
            min-width: 0;
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 20px;
            margin-bottom: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            padding: 1.5rem 2rem 1rem;
            margin-bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
        }

        .card-header h5, .card-header h6 {
            color: #2c3e50;
            font-weight: 600;
            margin: 0;
        }

        .card-body {
            flex: 1 1 auto;
            padding: 2rem;
            color: #2c3e50;
        }

        .card-footer {
            padding: 1rem 2rem 1.5rem;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            border-bottom-left-radius: 20px;
            border-bottom-right-radius: 20px;
        }

        /* 全新现代化导航栏 */
        .modern-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(30px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1030;
            display: flex;
            align-items: center;
        }

        .navbar-container {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .mobile-menu-btn {
            display: none;
            flex-direction: column;
            justify-content: space-around;
            width: 30px;
            height: 30px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
            margin-right: 1rem;
        }

        .hamburger-line {
            width: 100%;
            height: 3px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover .hamburger-line {
            background: #fff;
        }

        .navbar-brand-modern {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #fff;
            transition: all 0.3s ease;
        }

        .navbar-brand-modern:hover {
            transform: scale(1.02);
            text-decoration: none;
            color: #fff;
        }

        .brand-icon {
            font-size: 2rem;
            margin-right: 1rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .brand-text {
            display: flex;
            flex-direction: column;
        }

        .brand-title {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 2px;
        }

        .brand-subtitle {
            font-size: 0.8rem;
            opacity: 0.8;
            font-weight: 400;
            line-height: 1;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-action-btn {
            width: 45px;
            height: 45px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .navbar-action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            transform: scale(1.1);
        }

        .navbar-action-btn:active {
            transform: scale(0.95);
        }

        /* 现代化表格样式 */
        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #2c3e50;
            border-collapse: separate;
            border-spacing: 0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .table th, .table td {
            padding: 1rem 1.5rem;
            vertical-align: middle;
            border: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table thead th {
            vertical-align: middle;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
            font-weight: 600;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            color: #2c3e50;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.8rem;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            transform: scale(1.01);
        }

        .table-responsive {
            display: block;
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .table-sm th, .table-sm td {
            padding: 0.75rem 1rem;
        }

        .table-actions {
            white-space: nowrap;
        }

        .table-actions .btn {
            margin: 0 0.25rem;
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        /* 现代化警告框样式 */
        .alert {
            position: relative;
            padding: 1.5rem 2rem;
            margin-bottom: 1.5rem;
            border: none;
            border-radius: 15px;
            backdrop-filter: blur(20px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .alert:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .alert-success {
            color: #155724;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
            border-left: 4px solid #28a745;
        }

        .alert-danger {
            color: #721c24;
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);
            border-left: 4px solid #dc3545;
        }

        .alert-warning {
            color: #856404;
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(243, 156, 18, 0.1) 100%);
            border-left: 4px solid #ffc107;
        }

        .alert-info {
            color: #0c5460;
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(52, 152, 219, 0.1) 100%);
            border-left: 4px solid #17a2b8;
        }

        .alert-light {
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.9);
            border-left: 4px solid #e9ecef;
        }

        .alert-sm {
            padding: 1rem 1.5rem;
            font-size: 0.875rem;
            border-radius: 10px;
        }

        /* 现代化表单样式 */
        .form-control {
            display: block;
            width: 100%;
            padding: 1rem 1.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.95);
            border-color: #667eea;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }

        .form-control::placeholder {
            color: rgba(44, 62, 80, 0.5);
        }

        .form-label {
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-select {
            display: block;
            width: 100%;
            padding: 1rem 3rem 1rem 1.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }
        .input-group {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: stretch;
            width: 100%;
        }
        .input-group > .form-control, .input-group > .form-select {
            position: relative;
            flex: 1 1 auto;
            width: 1%;
            min-width: 0;
        }
        .input-group-text {
            display: flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            text-align: center;
            white-space: nowrap;
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
        }
        .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            margin-left: -1px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        .input-group > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu) {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        /* 工具类样式 */
        .d-flex { display: flex; }
        .d-inline-flex { display: inline-flex; }
        .d-block { display: block; }
        .d-inline-block { display: inline-block; }
        .d-none { display: none; }
        .d-grid { display: grid; }
        .justify-content-start { justify-content: flex-start; }
        .justify-content-end { justify-content: flex-end; }
        .justify-content-center { justify-content: center; }
        .justify-content-between { justify-content: space-between; }
        .justify-content-around { justify-content: space-around; }
        .align-items-start { align-items: flex-start; }
        .align-items-end { align-items: flex-end; }
        .align-items-center { align-items: center; }
        .align-items-baseline { align-items: baseline; }
        .align-items-stretch { align-items: stretch; }
        .flex-row { flex-direction: row; }
        .flex-column { flex-direction: column; }
        .flex-wrap { flex-wrap: wrap; }
        .flex-nowrap { flex-wrap: nowrap; }
        .flex-grow-1 { flex-grow: 1; }
        .flex-shrink-1 { flex-shrink: 1; }

        /* 文本样式 */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .text-muted { color: #6c757d; }
        .text-primary { color: #0d6efd; }
        .text-secondary { color: #6c757d; }
        .text-success { color: #198754; }
        .text-info { color: #0dcaf0; }
        .text-warning { color: #ffc107; }
        .text-danger { color: #dc3545; }
        .text-light { color: #f8f9fa; }
        .text-dark { color: #212529; }
        .text-white { color: #fff; }
        .fw-light { font-weight: 300; }
        .fw-normal { font-weight: 400; }
        .fw-bold { font-weight: 700; }
        .fw-bolder { font-weight: bolder; }
        .fst-italic { font-style: italic; }
        .fst-normal { font-style: normal; }
        .text-decoration-none { text-decoration: none; }
        .text-decoration-underline { text-decoration: underline; }
        .text-uppercase { text-transform: uppercase; }
        .text-lowercase { text-transform: lowercase; }
        .text-capitalize { text-transform: capitalize; }
        .small { font-size: 0.875em; }
        .h1, .h2, .h3, .h4, .h5, .h6 { margin-bottom: 0.5rem; font-weight: 500; line-height: 1.2; }
        .h1 { font-size: 2.5rem; }
        .h2 { font-size: 2rem; }
        .h3 { font-size: 1.75rem; }
        .h4 { font-size: 1.5rem; }
        .h5 { font-size: 1.25rem; }
        .h6 { font-size: 1rem; }

        /* 间距样式 */
        .m-0 { margin: 0; }
        .m-1 { margin: 0.25rem; }
        .m-2 { margin: 0.5rem; }
        .m-3 { margin: 1rem; }
        .m-4 { margin: 1.5rem; }
        .m-5 { margin: 3rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }
        .mt-0 { margin-top: 0; }
        .mt-1 { margin-top: 0.25rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-3 { margin-top: 1rem; }
        .mt-4 { margin-top: 1.5rem; }
        .mt-5 { margin-top: 3rem; }
        .mb-0 { margin-bottom: 0; }
        .mb-1 { margin-bottom: 0.25rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-3 { margin-bottom: 1rem; }
        .mb-4 { margin-bottom: 1.5rem; }
        .mb-5 { margin-bottom: 3rem; }
        .ms-0 { margin-left: 0; }
        .ms-1 { margin-left: 0.25rem; }
        .ms-2 { margin-left: 0.5rem; }
        .ms-3 { margin-left: 1rem; }
        .ms-auto { margin-left: auto; }
        .me-0 { margin-right: 0; }
        .me-1 { margin-right: 0.25rem; }
        .me-2 { margin-right: 0.5rem; }
        .me-3 { margin-right: 1rem; }
        .me-auto { margin-right: auto; }
        .p-0 { padding: 0; }
        .p-1 { padding: 0.25rem; }
        .p-2 { padding: 0.5rem; }
        .p-3 { padding: 1rem; }
        .p-4 { padding: 1.5rem; }
        .p-5 { padding: 3rem; }
        .pt-0 { padding-top: 0; }
        .pt-1 { padding-top: 0.25rem; }
        .pt-2 { padding-top: 0.5rem; }
        .pt-3 { padding-top: 1rem; }
        .pb-0 { padding-bottom: 0; }
        .pb-1 { padding-bottom: 0.25rem; }
        .pb-2 { padding-bottom: 0.5rem; }
        .pb-3 { padding-bottom: 1rem; }
        .ps-0 { padding-left: 0; }
        .ps-1 { padding-left: 0.25rem; }
        .ps-2 { padding-left: 0.5rem; }
        .ps-3 { padding-left: 1rem; }
        .pe-0 { padding-right: 0; }
        .pe-1 { padding-right: 0.25rem; }
        .pe-2 { padding-right: 0.5rem; }
        .pe-3 { padding-right: 1rem; }
        .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .py-3 { padding-top: 1rem; padding-bottom: 1rem; }
        .px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .px-3 { padding-left: 1rem; padding-right: 1rem; }

        /* 背景和边框样式 */
        .bg-primary { background-color: #0d6efd; color: #fff; }
        .bg-secondary { background-color: #6c757d; color: #fff; }
        .bg-success { background-color: #198754; color: #fff; }
        .bg-info { background-color: #0dcaf0; color: #000; }
        .bg-warning { background-color: #ffc107; color: #000; }
        .bg-danger { background-color: #dc3545; color: #fff; }
        .bg-light { background-color: #f8f9fa; color: #000; }
        .bg-dark { background-color: #212529; color: #fff; }
        .bg-white { background-color: #fff; color: #000; }
        .bg-transparent { background-color: transparent; }
        .border { border: 1px solid #dee2e6; }
        .border-0 { border: 0; }
        .border-top { border-top: 1px solid #dee2e6; }
        .border-bottom { border-bottom: 1px solid #dee2e6; }
        .border-start { border-left: 1px solid #dee2e6; }
        .border-end { border-right: 1px solid #dee2e6; }
        .rounded { border-radius: 0.375rem; }
        .rounded-0 { border-radius: 0; }
        .rounded-1 { border-radius: 0.25rem; }
        .rounded-2 { border-radius: 0.375rem; }
        .rounded-3 { border-radius: 0.5rem; }
        .rounded-circle { border-radius: 50%; }
        .rounded-pill { border-radius: 50rem; }

        /* 现代化徽章样式 */
        .badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5em 1em;
            font-size: 0.75em;
            font-weight: 600;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .badge-sm {
            font-size: 0.65em;
            padding: 0.35em 0.75em;
            border-radius: 20px;
        }

        /* 图标样式 */
        .bi {
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
            vertical-align: -0.125em;
        }
        .bi-shield-check::before { content: "🛡️"; }
        .bi-shield-lock-fill::before { content: "🔒"; }
        .bi-speedometer2::before { content: "📊"; }
        .bi-key::before { content: "🔑"; }
        .bi-laptop::before { content: "💻"; }
        .bi-journal-text::before { content: "📝"; }
        .bi-wifi::before { content: "📶"; }
        .bi-gear::before { content: "⚙️"; }
        .bi-house::before { content: "🏠"; }
        .bi-plus-lg::before { content: "➕"; }
        .bi-pencil::before { content: "✏️"; }
        .bi-trash::before { content: "🗑️"; }
        .bi-eye::before { content: "👁️"; }
        .bi-download::before { content: "⬇️"; }
        .bi-upload::before { content: "⬆️"; }
        .bi-search::before { content: "🔍"; }
        .bi-filter::before { content: "🔽"; }
        .bi-check-circle::before { content: "✅"; }
        .bi-x-circle::before { content: "❌"; }
        .bi-exclamation-triangle::before { content: "⚠️"; }
        .bi-exclamation-triangle-fill::before { content: "⚠️"; }
        .bi-info-circle::before { content: "ℹ️"; }
        .bi-arrow-up::before { content: "⬆️"; }
        .bi-arrow-down::before { content: "⬇️"; }
        .bi-clock::before { content: "🕐"; }
        .bi-box-arrow-right::before { content: "🚪"; }
        .bi-box-arrow-in-right::before { content: "🚪"; }
        .bi-activity::before { content: "📈"; }
        .bi-graph-up::before { content: "📈"; }
        .bi-pie-chart::before { content: "🥧"; }
        .bi-bar-chart::before { content: "📊"; }
        .bi-key-fill::before { content: "🔑"; }
        .bi-bug::before { content: "🐛"; }
        .bi-tools::before { content: "🔧"; }
        .bi-database::before { content: "🗄️"; }
        .bi-plus-circle::before { content: "➕"; }
        .bi-arrow-clockwise::before { content: "🔄"; }
        .bi-table::before { content: "📋"; }
        .bi-inbox::before { content: "📥"; }
        .bi-journal::before { content: "📖"; }
        .bi-play::before { content: "▶️"; }
        .bi-check::before { content: "✓"; }
        .bi-x::before { content: "✕"; }

        /* 间隙样式 */
        .gap-1 { gap: 0.25rem; }
        .gap-2 { gap: 0.5rem; }
        .gap-3 { gap: 1rem; }
        .gap-4 { gap: 1.5rem; }
        .gap-5 { gap: 3rem; }

        /* 其他工具类 */
        .w-25 { width: 25%; }
        .w-50 { width: 50%; }
        .w-75 { width: 75%; }
        .w-100 { width: 100%; }
        .h-25 { height: 25%; }
        .h-50 { height: 50%; }
        .h-75 { height: 75%; }
        .h-100 { height: 100%; }
        .mw-100 { max-width: 100%; }
        .mh-100 { max-height: 100%; }
        .min-vw-100 { min-width: 100vw; }
        .min-vh-100 { min-height: 100vh; }
        .vw-100 { width: 100vw; }
        .vh-100 { height: 100vh; }
        .overflow-auto { overflow: auto; }
        .overflow-hidden { overflow: hidden; }
        .overflow-visible { overflow: visible; }
        .overflow-scroll { overflow: scroll; }
        .position-static { position: static; }
        .position-relative { position: relative; }
        .position-absolute { position: absolute; }
        .position-fixed { position: fixed; }
        .position-sticky { position: sticky; }
        .top-0 { top: 0; }
        .top-50 { top: 50%; }
        .top-100 { top: 100%; }
        .bottom-0 { bottom: 0; }
        .bottom-50 { bottom: 50%; }
        .bottom-100 { bottom: 100%; }
        .start-0 { left: 0; }
        .start-50 { left: 50%; }
        .start-100 { left: 100%; }
        .end-0 { right: 0; }
        .end-50 { right: 50%; }
        .end-100 { right: 100%; }
        .translate-middle { transform: translate(-50%, -50%); }
        .translate-middle-x { transform: translateX(-50%); }
        .translate-middle-y { transform: translateY(-50%); }
        .shadow { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .shadow-lg { box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175); }
        .shadow-none { box-shadow: none; }
        .user-select-all { user-select: all; }
        .user-select-auto { user-select: auto; }
        .user-select-none { user-select: none; }
        .pe-none { pointer-events: none; }
        .pe-auto { pointer-events: auto; }
        .visible { visibility: visible; }
        .invisible { visibility: hidden; }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-fade-in-left {
            animation: fadeInLeft 0.6s ease-out;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 自定义滚动条样式增强 */
        .card::-webkit-scrollbar,
        .table-responsive::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .card::-webkit-scrollbar-track,
        .table-responsive::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .card::-webkit-scrollbar-thumb,
        .table-responsive::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 3px;
        }

        .card::-webkit-scrollbar-thumb:hover,
        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.5);
        }

        /* 焦点状态增强 */
        .btn:focus,
        .form-control:focus,
        .form-select:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
        }

        /* 禁用状态样式 */
        .btn:disabled,
        .btn.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .form-control:disabled,
        .form-select:disabled {
            background: rgba(255, 255, 255, 0.5);
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* 工具提示样式 */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-indicator.online {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-indicator.offline {
            background: #dc3545;
        }

        .status-indicator.warning {
            background: #ffc107;
        }

        /* 现代化仪表板样式 */
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(20px);
        }

        .welcome-section h1.dashboard-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            margin: 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .dashboard-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin: 0.5rem 0 0 0;
        }

        .quick-actions {
            display: flex;
            gap: 1rem;
        }

        .quick-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.15);
            border: none;
            border-radius: 15px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .quick-action-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .quick-action-btn i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .quick-action-btn span {
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* 精美统计卡片网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            position: relative;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            transition: all 0.3s ease;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #fff;
            flex-shrink: 0;
        }

        .stat-card.primary .stat-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.success .stat-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .stat-card.info .stat-icon {
            background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
        }

        .stat-card.warning .stat-icon {
            background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1rem;
            color: #7f8c8d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-trend.positive {
            color: #28a745;
        }

        .stat-trend.negative {
            color: #dc3545;
        }

        .stat-trend.neutral {
            color: #6c757d;
        }

        .stat-decoration {
            position: absolute;
            top: -20px;
            right: -20px;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            opacity: 0.1;
        }

        .stat-card.primary .stat-decoration {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.success .stat-decoration {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .stat-card.info .stat-decoration {
            background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
        }

        .stat-card.warning .stat-decoration {
            background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
        }

        /* 论坛管理专用样式 */
        .forum-user-banned {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }

        .forum-post-hidden {
            opacity: 0.6;
            background-color: rgba(108, 117, 125, 0.1) !important;
        }

        .forum-post-pinned {
            background-color: rgba(25, 135, 84, 0.1) !important;
            border-left: 4px solid #198754;
        }

        /* 新增的大气界面样式 */
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }

        .text-gray-800 {
            color: #5a5c69 !important;
        }

        .text-gray-500 {
            color: #858796 !important;
        }

        .text-gray-300 {
            color: #dddfeb !important;
        }

        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }

        /* 论坛管理表格样式优化 */
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa !important;
            vertical-align: middle;
        }

        .table td {
            vertical-align: middle;
            padding: 0.75rem 0.5rem;
        }

        .table .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .min-width-0 {
            min-width: 0;
        }

        .table .badge {
            font-size: 0.75rem;
            white-space: nowrap;
        }

        .table .dropdown-toggle {
            border: none;
            background: transparent;
            padding: 0.25rem 0.5rem;
        }

        .table .dropdown-toggle:focus {
            box-shadow: none;
        }

        /* 响应式表格优化 */
        @media (max-width: 1200px) {
            .table th:nth-child(6),
            .table td:nth-child(6) {
                display: none; /* 隐藏互动数据列 */
            }
        }

        @media (max-width: 992px) {
            .table th:nth-child(4),
            .table td:nth-child(4),
            .table th:nth-child(7),
            .table td:nth-child(7) {
                display: none; /* 隐藏类型和时间列 */
            }

            .table th:nth-child(2),
            .table td:nth-child(2) {
                width: 50% !important; /* 扩大帖子信息列 */
            }
        }

        @media (max-width: 768px) {
            .table th:nth-child(3),
            .table td:nth-child(3) {
                display: none; /* 隐藏作者列 */
            }

            .table th:nth-child(2),
            .table td:nth-child(2) {
                width: 70% !important; /* 进一步扩大帖子信息列 */
            }
        }

        /* 统计卡片优化样式 */
        .stats-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            min-height: 120px; /* 确保卡片有足够高度 */
            margin-bottom: 2rem; /* 增加卡片底部间距 */
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
        }

        /* 增加行间距 */
        .row.g-4.gy-5 > [class*="col-"] {
            margin-bottom: 1.5rem;
        }

        .stats-card .card-body {
            position: relative;
            overflow: hidden;
            padding: 1.5rem !important; /* 增加内边距 */
            min-height: 100px; /* 确保内容区域有足够高度 */
            display: flex;
            align-items: center;
        }

        .stats-card .card-body::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        /* 统计卡片文字间距优化 */
        .stats-card .text-xs {
            line-height: 1.4;
            margin-bottom: 0.5rem !important;
        }

        .stats-card .h5 {
            line-height: 1.2;
            margin-bottom: 0.5rem !important;
            font-size: 1.75rem !important; /* 增大主数字字体 */
        }

        .text-gray-300 {
            color: #d1d3e2 !important;
        }

        .text-gray-800 {
            color: #5a5c69 !important;
        }

        /* 响应式统计卡片 */
        @media (max-width: 1400px) {
            .col-xxl-2 {
                flex: 0 0 auto;
                width: 20%; /* 5个卡片一行 */
            }
        }

        @media (max-width: 1200px) {
            .col-xxl-2 {
                width: 25%; /* 4个卡片一行 */
            }
        }

        @media (max-width: 992px) {
            .col-xxl-2 {
                width: 33.333333%; /* 3个卡片一行 */
            }
        }

        @media (max-width: 768px) {
            .col-xxl-2 {
                width: 50%; /* 2个卡片一行 */
            }
        }

        @media (max-width: 576px) {
            .col-xxl-2 {
                width: 100%; /* 1个卡片一行 */
            }
        }

        .nav-pills .nav-link.active {
            background-color: #4e73df;
            border-color: #4e73df;
        }

        .nav-pills .nav-link {
            border-radius: 0.35rem;
            margin-right: 0.5rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            color: #858796;
        }

        .dropdown-menu {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: none;
        }

        .forum-stats-card {
            transition: transform 0.2s ease;
        }

        .forum-stats-card:hover {
            transform: translateY(-2px);
        }

        .forum-action-btn {
            margin: 1px;
        }

        .forum-user-status {
            font-weight: bold;
        }

        .forum-post-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .forum-tab-content .table th {
            background-color: rgba(102, 126, 234, 0.1);
            border: none;
            font-weight: 600;
            color: #495057;
        }

        .forum-tab-content .badge {
            font-size: 0.75em;
        }

        .forum-management-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .forum-empty-state {
            padding: 60px 20px;
            text-align: center;
            color: #6c757d;
        }

        .forum-empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        /* 现代化页面头部样式 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(20px);
        }

        .page-title-section h1.page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #fff;
            margin: 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            margin: 0.5rem 0 0 0;
        }

        .page-actions {
            display: flex;
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 15px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            text-decoration: none;
        }

        .action-btn.primary {
            background: rgba(102, 126, 234, 0.3);
            border: 1px solid rgba(102, 126, 234, 0.5);
        }

        .action-btn.secondary {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: #fff;
            text-decoration: none;
        }

        .action-btn i {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .action-btn span {
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* 现代化搜索筛选面板 */
        .search-filter-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-grid {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 1.5rem;
            align-items: end;
        }

        .search-field {
            display: flex;
            flex-direction: column;
        }

        .field-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .input-with-icon {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(44, 62, 80, 0.5);
            z-index: 2;
        }

        .modern-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .modern-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .modern-select {
            width: 100%;
            padding: 1rem 1.5rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            cursor: pointer;
        }

        .modern-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .search-actions {
            display: flex;
            gap: 0.75rem;
        }

        .search-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 500;
        }

        .search-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
        }

        .search-btn.secondary {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
            border: 2px solid rgba(108, 117, 125, 0.3);
        }

        .search-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .search-btn.primary:hover {
            color: #fff;
        }

        .search-btn.secondary:hover {
            color: #495057;
            background: rgba(108, 117, 125, 0.2);
        }

        .search-btn i {
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }

        .search-btn span {
            font-size: 0.8rem;
        }

        /* 现代化安全监控面板 */
        .security-monitor-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .security-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-left: 4px solid;
        }

        .security-card.danger {
            border-left-color: #dc3545;
        }

        .security-card.warning {
            border-left-color: #ffc107;
        }

        .security-card.info {
            border-left-color: #17a2b8;
        }

        .security-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .security-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #fff;
            margin-bottom: 1rem;
        }

        .security-card.danger .security-icon {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .security-card.warning .security-icon {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }

        .security-card.info .security-icon {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .security-content {
            margin-bottom: 1rem;
        }

        .security-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .security-count {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .security-subtitle {
            font-size: 0.85rem;
            color: #7f8c8d;
            font-weight: 500;
        }

        .security-details {
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            padding-top: 1rem;
        }

        .no-anomaly {
            text-align: center;
            color: #28a745;
            font-weight: 500;
            padding: 0.5rem;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 8px;
        }

        .anomaly-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .anomaly-item:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: translateX(3px);
        }

        .anomaly-license {
            font-weight: 600;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .anomaly-info {
            font-size: 0.85rem;
            color: #7f8c8d;
            font-weight: 500;
        }

        /* 现代化设备列表面板 */
        .device-list-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .device-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .device-header-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .device-header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .view-toggle-buttons {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .view-toggle-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            border-radius: 6px;
            color: #7f8c8d;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .view-toggle-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .view-toggle-btn.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }

        .refresh-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            color: #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .refresh-btn:hover {
            background: #667eea;
            color: white;
            transform: rotate(180deg);
        }

        /* 设备表格视图 */
        .device-view-container {
            width: 100%;
        }

        .device-table-container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .device-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .device-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .device-table td {
            padding: 0.75rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            vertical-align: middle;
        }

        .device-table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .license-cell {
            background: rgba(102, 126, 234, 0.02);
            border-right: 2px solid rgba(102, 126, 234, 0.1);
        }

        .license-info-table {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .license-key-table {
            font-weight: 600;
            color: #2c3e50;
        }

        .device-count-table {
            font-size: 0.75rem;
            color: #7f8c8d;
        }

        .device-name-table {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .edit-name-btn-table {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border: none;
            background: transparent;
            color: #7f8c8d;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            font-size: 0.75rem;
        }

        .device-name-table:hover .edit-name-btn-table {
            opacity: 1;
        }

        .edit-name-btn-table:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .device-id-table {
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            background: rgba(0, 0, 0, 0.05);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-badge.online {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-badge.online i {
            color: #28a745;
        }

        .status-badge.offline {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }

        .status-badge.offline i {
            color: #6c757d;
        }

        .table-actions {
            display: flex;
            gap: 0.25rem;
        }

        .table-action-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .table-action-btn.view {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }

        .table-action-btn.view:hover {
            background: #17a2b8;
            color: white;
        }

        .table-action-btn.danger {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .table-action-btn.danger:hover {
            background: #dc3545;
            color: white;
        }

        .no-devices-row td {
            font-style: italic;
            color: #7f8c8d;
        }

        /* 设备状态指示器 */
        .device-status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .device-status-indicator .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .device-status-indicator .status-indicator.online {
            background: #28a745;
            box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
        }

        .device-status-indicator .status-indicator.offline {
            background: #6c757d;
        }

        .device-status-indicator .status-text {
            font-size: 0.8rem;
            font-weight: 500;
        }

        .device-status-indicator .status-text.online {
            color: #28a745;
        }

        .device-status-indicator .status-text.offline {
            color: #6c757d;
        }

        .device-stats {
            display: flex;
            gap: 2rem;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #7f8c8d;
            font-weight: 500;
            margin-top: 0.25rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .empty-subtitle {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        /* 设备卡片网格 */
        .device-cards-grid {
            display: grid;
            gap: 1rem;
        }

        .device-license-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 3px solid #667eea;
        }

        .device-license-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .license-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }

        .license-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .license-key {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #2c3e50;
            font-size: 1rem;
        }

        .license-type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .license-type-badge.basic {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: #fff;
        }

        .license-type-badge.pro {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #000;
        }

        .license-type-badge.enterprise {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: #fff;
        }

        .license-type-badge.admin {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: #fff;
        }

        .license-stats {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .device-count {
            display: flex;
            align-items: baseline;
            gap: 0.25rem;
            font-weight: 600;
        }

        .count-current {
            font-size: 1.25rem;
            color: #2c3e50;
        }

        .count-separator {
            color: #7f8c8d;
        }

        .count-max {
            color: #7f8c8d;
        }

        .device-label {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-left: 0.25rem;
        }

        .warning-indicator {
            font-size: 1.2rem;
            margin-left: 0.5rem;
        }

        .no-devices {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .no-devices-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            opacity: 0.5;
        }

        .no-devices-text {
            font-size: 0.9rem;
        }

        /* 设备项目网格 */
        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 0.75rem;
        }

        .device-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .device-item:hover {
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .device-main-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .device-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-indicator.online {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-indicator.offline {
            background: #6c757d;
        }

        .status-text {
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-text.online {
            color: #28a745;
        }

        .status-text.offline {
            color: #6c757d;
        }

        .device-identity {
            flex: 1;
        }

        .device-name {
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .edit-name-btn {
            background: none;
            border: none;
            color: #7f8c8d;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            opacity: 0;
        }

        .device-item:hover .edit-name-btn {
            opacity: 1;
        }

        .edit-name-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .device-id {
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            color: #7f8c8d;
        }

        .device-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            font-size: 0.7rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            font-size: 0.8rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .device-actions {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
        }

        .device-action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .device-action-btn.view {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }

        .device-action-btn.view:hover {
            background: rgba(23, 162, 184, 0.2);
            transform: scale(1.1);
        }

        .device-action-btn.danger {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .device-action-btn.danger:hover {
            background: rgba(220, 53, 69, 0.2);
            transform: scale(1.1);
        }

        /* 现代化日志统计面板 */
        .log-stats-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .log-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .log-stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-left: 4px solid;
        }

        .log-stat-card.primary {
            border-left-color: #667eea;
        }

        .log-stat-card.success {
            border-left-color: #28a745;
        }

        .log-stat-card.danger {
            border-left-color: #dc3545;
        }

        .log-stat-card.info {
            border-left-color: #17a2b8;
        }

        .log-stat-card.warning {
            border-left-color: #ffc107;
        }

        .log-stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .log-stat-card .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #fff;
            flex-shrink: 0;
        }

        .log-stat-card.primary .stat-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .log-stat-card.success .stat-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .log-stat-card.danger .stat-icon {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .log-stat-card.info .stat-icon {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .log-stat-card.warning .stat-icon {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }

        .log-stat-card .stat-content {
            flex: 1;
        }

        .log-stat-card .stat-number {
            font-size: 1.75rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .log-stat-card .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
        }

        .log-stat-card .stat-trend {
            font-size: 0.8rem;
            color: #95a5a6;
        }

        .trend-text {
            font-weight: 500;
        }

        /* 现代化日志查看器 */
        .log-viewer-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .log-tabs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .log-type-tabs {
            display: flex;
            gap: 0.5rem;
        }

        .log-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            color: #7f8c8d;
            background: rgba(255, 255, 255, 0.5);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .log-tab:hover {
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transform: translateY(-2px);
        }

        .log-tab.active {
            color: #fff;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .log-tab.active:hover {
            color: #fff;
        }

        /* 日志搜索面板 */
        .log-search-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .search-filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-size: 0.85rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .filter-actions {
            display: flex;
            gap: 0.5rem;
        }

        .filter-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 500;
        }

        .filter-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
        }

        .filter-btn.secondary {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
            border: 2px solid rgba(108, 117, 125, 0.3);
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .filter-btn.primary:hover {
            color: #fff;
        }

        .filter-btn.secondary:hover {
            color: #495057;
            background: rgba(108, 117, 125, 0.2);
        }

        .filter-btn i {
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .filter-btn span {
            font-size: 0.8rem;
        }

        /* 现代化日志列表 */
        .empty-logs-state {
            text-align: center;
            padding: 4rem 2rem;
        }

        .empty-logs-state .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .empty-logs-state .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .empty-logs-state .empty-subtitle {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .modern-log-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .log-list-header {
            display: grid;
            grid-template-columns: 120px 200px 100px 150px 200px 1fr;
            gap: 1rem;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }

        .log-header-item {
            font-size: 0.8rem;
            font-weight: 600;
            color: #2c3e50;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .log-entries {
            max-height: 600px;
            overflow-y: auto;
        }

        .log-entry {
            display: grid;
            grid-template-columns: 120px 200px 100px 150px 200px 1fr;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            align-items: center;
        }

        .log-entry:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(3px);
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .log-item.time {
            text-align: center;
        }

        .log-time-main {
            font-weight: 600;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .log-time-date {
            font-size: 0.75rem;
            color: #7f8c8d;
        }

        .license-code {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .license-type {
            padding: 0.2rem 0.5rem;
            border-radius: 8px;
            font-size: 0.65rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .license-type.basic {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: #fff;
        }

        .license-type.pro {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #000;
        }

        .license-type.enterprise {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: #fff;
        }

        .license-type.admin {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: #fff;
        }

        .license-unknown {
            color: #7f8c8d;
            font-style: italic;
        }

        .status-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-badge.success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-badge.failed {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .status-badge.logout {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .status-badge.login {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }

        .ip-address, .device-id, .user-agent {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            color: #2c3e50;
        }

        .device-none, .agent-none {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            color: #7f8c8d;
            font-style: italic;
        }

        .device-id {
            font-family: 'Courier New', monospace;
        }



        /* 现代化模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1055;
            display: none;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            outline: 0;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-dialog {
            position: relative;
            width: auto;
            margin: 2rem;
            pointer-events: none;
            max-width: 500px;
        }

        .modal-content {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 100%;
            pointer-events: auto;
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            overflow: hidden;
        }

        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1050;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
        }

        .modal-header {
            display: flex;
            flex-shrink: 0;
            align-items: center;
            justify-content: space-between;
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }

        .modal-title {
            margin-bottom: 0;
            line-height: 1.5;
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.25rem;
        }

        .modal-body {
            position: relative;
            flex: 1 1 auto;
            padding: 2rem;
            color: #2c3e50;
        }

        .modal-footer {
            display: flex;
            flex-wrap: wrap;
            flex-shrink: 0;
            align-items: center;
            justify-content: flex-end;
            padding: 1rem 2rem 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        }

        .modal-footer > * {
            margin: 0.5rem;
        }

        .btn-close {
            box-sizing: content-box;
            width: 1.5em;
            height: 1.5em;
            padding: 0.5em;
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 50%;
            opacity: 0.8;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: bold;
        }

        .btn-close:hover {
            opacity: 1;
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            transform: scale(1.1);
        }

        .btn-close:focus {
            opacity: 1;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        }

        .btn-close::before {
            content: "✕";
        }

        @media (min-width: 576px) {
            .modal-dialog {
                max-width: 500px;
                margin: 1.75rem auto;
            }
        }

        /* 下拉菜单样式 */
        .dropdown { position: relative; display: inline-block; }
        .dropdown-toggle { cursor: pointer; }
        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            display: none;
            min-width: 10rem;
            padding: 0.5rem 0;
            margin: 0;
            font-size: 0.875rem;
            color: #212529;
            text-align: left;
            list-style: none;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .dropdown-menu.show { display: block; }
        .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.25rem 1rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
        }
        .dropdown-item:hover, .dropdown-item:focus {
            color: #1e2125;
            background-color: #e9ecef;
        }
        .dropdown-divider {
            height: 0;
            margin: 0.5rem 0;
            overflow: hidden;
            border-top: 1px solid rgba(0, 0, 0, 0.15);
        }

        /* 进度条样式 */
        .progress {
            display: flex;
            height: 1rem;
            overflow: hidden;
            font-size: 0.75rem;
            background-color: #e9ecef;
            border-radius: 0.375rem;
        }
        .progress-bar {
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            background-color: #0d6efd;
            transition: width 0.6s ease;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            padding-left: 0;
            list-style: none;
        }
        .page-link {
            position: relative;
            display: block;
            color: #0d6efd;
            text-decoration: none;
            background-color: #fff;
            border: 1px solid #dee2e6;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        .page-link:hover {
            z-index: 2;
            color: #0a58ca;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }
        .page-item.active .page-link {
            z-index: 3;
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
    </style>
    <style>
        /* 精美侧边栏设计 */
        .sidebar {
            position: fixed;
            top: 70px;
            left: 0;
            width: 260px;
            height: calc(100vh - 70px);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(30px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            overflow-y: auto;
            z-index: 1000;
            padding: 1rem 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 1rem 1.5rem;
            margin: 0.3rem 1rem;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            text-decoration: none !important;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(8px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none !important;
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.4) 0%, rgba(118, 75, 162, 0.4) 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            border-left: 3px solid rgba(255, 255, 255, 0.8);
            text-decoration: none !important;
        }

        .sidebar .nav-link i {
            margin-right: 1rem;
            font-size: 1.2em;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: 260px;
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }



        .table-actions {
            white-space: nowrap;
        }

        .table-actions .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .license-status {
            font-weight: 500;
        }

        .batch-toolbar {
            background: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            display: none;
        }

        .batch-toolbar.show {
            display: block;
        }

        .alert-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .badge-sm {
            font-size: 0.75rem;
        }

        .col-xl-2-4 {
            flex: 0 0 auto;
            width: 20%;
        }

        /* 新用户高亮动画 */
        @keyframes highlight {
            0% { background-color: #d4edda; }
            50% { background-color: #c3e6cb; }
            100% { background-color: transparent; }
        }

        .table-success {
            background-color: #d4edda !important;
        }

        /* 新用户提示 */
        .new-user-notice {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1060;
            max-width: 300px;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .col-xl-3 { flex: 0 0 50%; max-width: 50%; }
            .col-xl-2-4 { width: 50%; }
        }

        @media (max-width: 992px) {
            .col-lg-4, .col-lg-6, .col-lg-8 { flex: 0 0 100%; max-width: 100%; }
        }

        @media (max-width: 768px) {
            .col-md-6 { flex: 0 0 100%; max-width: 100%; }
            .col-xl-3 { flex: 0 0 100%; max-width: 100%; }

            .mobile-menu-btn {
                display: flex;
            }

            .sidebar {
                margin-left: -260px;
                z-index: 1050;
                transition: margin-left 0.3s ease-in-out;
                width: 260px;
            }

            .sidebar.show {
                margin-left: 0;
            }

            .main-content {
                margin-left: 0;
                margin-top: 70px;
                padding: 1rem;
            }

            .navbar-container {
                padding: 0 1rem;
            }

            .brand-text {
                display: none;
            }

            .brand-icon {
                margin-right: 0;
            }

            .dashboard-header {
                flex-direction: column;
                gap: 1.5rem;
                text-align: center;
                padding: 1.5rem;
            }

            .quick-actions {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-card {
                padding: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .dashboard-title {
                font-size: 2rem !important;
            }

            .page-header {
                flex-direction: column;
                gap: 1.5rem;
                text-align: center;
                padding: 1.5rem;
            }

            .page-actions {
                justify-content: center;
            }

            .search-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .search-actions {
                justify-content: center;
            }

            .security-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .device-stats {
                gap: 1rem;
            }

            .devices-grid {
                grid-template-columns: 1fr;
            }

            .device-details {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .license-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .log-stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .log-type-tabs {
                flex-direction: column;
                width: 100%;
                gap: 0.5rem;
            }

            .log-tab {
                justify-content: center;
            }

            .search-filters-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-actions {
                justify-content: center;
            }

            .log-list-header {
                display: none;
            }

            .log-entry {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1.5rem;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 12px;
                margin-bottom: 1rem;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .log-entry:hover {
                transform: none;
            }

            .log-item {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            }

            .log-item:last-child {
                border-bottom: none;
            }

            .log-item::before {
                content: attr(data-label);
                font-weight: 600;
                color: #7f8c8d;
                font-size: 0.8rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .log-panel-header {
                flex-direction: column;
                gap: 1.5rem;
                align-items: flex-start;
            }

            .log-controls {
                width: 100%;
                justify-content: space-between;
            }

            .log-filter-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .filter-buttons {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .log-entry-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .log-entries-container {
                max-height: 400px;
                padding: 0.5rem;
            }

            .log-entry-item {
                padding: 0.75rem;
                margin-bottom: 0.5rem;
            }

            .context-data {
                font-size: 0.7rem;
                padding: 0.75rem;
            }

            /* 黑色主题代码风格日志样式 */
            .code-log-container {
                background: #000000;
                border-radius: 8px;
                border: 1px solid #333333;
                overflow: hidden;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            }

            .log-code-block {
                font-family: 'Courier New', 'Monaco', 'Menlo', 'Consolas', monospace;
                font-size: 13px;
                line-height: 1.5;
                color: #ffffff;
                background: #000000;
                max-height: 600px;
                overflow-y: auto;
                padding: 0;
                margin: 0;
            }

            .log-line {
                display: flex;
                padding: 6px 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.08);
                transition: background-color 0.2s ease;
                min-height: 24px;
            }

            .log-line:hover {
                background: rgba(255, 255, 255, 0.08);
            }

            .log-line.log-error {
                background: rgba(255, 0, 0, 0.15);
                border-left: 4px solid #ff0000;
            }

            .log-line.log-error:hover {
                background: rgba(255, 0, 0, 0.25);
            }

            .log-line.log-warning {
                background: rgba(255, 255, 0, 0.15);
                border-left: 4px solid #ffff00;
            }

            .log-line.log-warning:hover {
                background: rgba(255, 255, 0, 0.25);
            }

            .line-number {
                display: inline-block;
                width: 65px;
                padding: 0 12px;
                color: #888888;
                background: rgba(255, 255, 255, 0.05);
                text-align: right;
                user-select: none;
                border-right: 1px solid rgba(255, 255, 255, 0.15);
                flex-shrink: 0;
                font-weight: normal;
            }

            .log-content {
                padding: 0 12px;
                flex: 1;
                word-break: break-all;
                white-space: pre-wrap;
                color: #ffffff;
            }

            .log-line.log-error .log-content {
                color: #ff4444;
                font-weight: 500;
            }

            .log-line.log-warning .log-content {
                color: #ffff44;
                font-weight: 500;
            }



            /* 黑色主题代码日志移动端优化 */
            .log-code-block {
                font-size: 11px;
                background: #000000;
            }

            .line-number {
                width: 50px;
                padding: 0 8px;
                background: rgba(255, 255, 255, 0.05);
                color: #888888;
            }

            .log-content {
                padding: 0 8px;
                color: #ffffff;
            }

            .log-line {
                background: #000000;
                border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            }

            .log-line:hover {
                background: rgba(255, 255, 255, 0.08);
            }

            .code-log-container {
                background: #000000;
                border: 1px solid #333333;
            }

            /* 滚动条样式 */
            .log-code-block::-webkit-scrollbar {
                width: 12px;
            }

            .log-code-block::-webkit-scrollbar-track {
                background: #1a1a1a;
            }

            .log-code-block::-webkit-scrollbar-thumb {
                background: #444444;
                border-radius: 6px;
            }

            .log-code-block::-webkit-scrollbar-thumb:hover {
                background: #666666;
            }

            /* 空日志状态样式 */
            .text-center.py-4.text-muted {
                background: #000000;
                color: #888888 !important;
                border-radius: 8px;
                border: 1px solid #333333;
            }

            .table-responsive {
                font-size: 0.875rem;
                border-radius: 10px;
            }

            .card {
                border-radius: 15px;
                margin-bottom: 1rem;
            }

            .card-header, .card-body, .card-footer {
                padding: 1.5rem;
            }

            .btn {
                font-size: 0.8rem;
                padding: 0.6rem 1.2rem;
            }

            .btn-sm {
                font-size: 0.7rem;
                padding: 0.4rem 0.8rem;
            }

            .form-control, .form-select {
                padding: 0.8rem 1.2rem;
                border-radius: 12px;
            }

            .alert {
                padding: 1.2rem 1.5rem;
                border-radius: 12px;
            }

            .modal-dialog {
                margin: 1rem;
            }

            .modal-content {
                border-radius: 20px;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 0.5rem;
            }
            .card {
                margin-bottom: 1rem;
            }
            .card-body {
                padding: 0.75rem;
            }
            .card-header {
                padding: 0.5rem 0.75rem;
            }
            .table-actions .btn {
                padding: 0.125rem 0.25rem;
                font-size: 0.7rem;
            }
            .table th, .table td {
                padding: 0.375rem;
                font-size: 0.8rem;
            }
            .alert {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }
            .badge {
                font-size: 0.65em;
                padding: 0.25em 0.5em;
            }
            .h1 { font-size: 1.75rem; }
            .h2 { font-size: 1.5rem; }
            .h3 { font-size: 1.25rem; }
            .h4 { font-size: 1.1rem; }
            .h5 { font-size: 1rem; }
            .h6 { font-size: 0.9rem; }
            .navbar {
                padding: 0.25rem 0.5rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .nav-link {
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
            }
            .sidebar .nav-link {
                padding: 10px 15px;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 0.25rem;
            }
            .card-body {
                padding: 0.5rem;
            }
            .card-header {
                padding: 0.375rem 0.5rem;
            }
            .btn {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
            .btn-sm {
                font-size: 0.65rem;
                padding: 0.15rem 0.3rem;
            }
            .table th, .table td {
                padding: 0.25rem;
                font-size: 0.75rem;
            }
            .form-control, .form-select {
                font-size: 0.875rem;
                padding: 0.25rem 0.5rem;
            }
            .alert {
                padding: 0.375rem 0.5rem;
                font-size: 0.8rem;
            }
            .d-flex.gap-2 {
                gap: 0.25rem;
            }
            .me-2 { margin-right: 0.25rem; }
            .me-3 { margin-right: 0.5rem; }
            .mb-3 { margin-bottom: 0.75rem; }
            .mb-4 { margin-bottom: 1rem; }
        }
    </style>
</head>
<body>
    <!-- 现代化顶部导航 -->
    <nav class="modern-navbar">
        <div class="navbar-container">
            <button class="mobile-menu-btn d-md-none" type="button" onclick="toggleSidebar()">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>

            <div class="navbar-brand-modern">
                <div class="brand-icon">🛡️</div>
                <div class="brand-text">
                    <div class="brand-title">Augment</div>
                    <div class="brand-subtitle">认证系统</div>
                </div>
            </div>

            <div class="navbar-actions">
                <button class="navbar-action-btn" onclick="window.location.reload()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                <button class="navbar-action-btn" href="?action=logout" onclick="if(confirm('确定要退出登录吗？')) window.location.href='?action=logout'">
                    <i class="bi bi-box-arrow-right"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="p-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'dashboard' ? 'active' : '' ?>" href="?page=dashboard">
                        <i class="bi bi-speedometer2 me-2"></i>仪表板
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'licenses' ? 'active' : '' ?>" href="?page=licenses">
                        <i class="bi bi-key me-2"></i>授权管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'devices' ? 'active' : '' ?>" href="?page=devices">
                        <i class="bi bi-laptop me-2"></i>设备管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'logs' ? 'active' : '' ?>" href="?page=logs">
                        <i class="bi bi-journal-text me-2"></i>授权日志
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'online' ? 'active' : '' ?>" href="?page=online">
                        <i class="bi bi-wifi me-2"></i>在线授权
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'announcements' ? 'active' : '' ?>" href="?page=announcements">
                        <i class="bi bi-megaphone me-2"></i>公告管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'forum' ? 'active' : '' ?>" href="?page=forum">
                        <i class="bi bi-chat-square-text me-2"></i>论坛管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'qq_config' ? 'active' : '' ?>" href="?page=qq_config">
                        <i class="bi bi-chat-dots me-2"></i>QQ群配置
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'debug' ? 'active' : '' ?>" href="?page=debug">
                        <i class="bi bi-bug me-2"></i>调试日志
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $page === 'settings' ? 'active' : '' ?>" href="?page=settings">
                        <i class="bi bi-gear me-2"></i>系统设置
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="index.php">
                        <i class="bi bi-house me-2"></i>返回首页
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <?php if (isset($dbError)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                数据库错误: <?= htmlspecialchars($dbError) ?>
            </div>
        <?php endif; ?>

        <?php if ($page === 'dashboard'): ?>
            <!-- 现代化仪表板 -->
            <div class="dashboard-header animate-fade-in-up">
                <div class="welcome-section">
                    <h1 class="dashboard-title">欢迎回来 👋</h1>
                    <p class="dashboard-subtitle">系统运行正常，一切数据实时更新</p>
                </div>
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="location.href='?page=licenses'">
                        <i class="bi bi-plus-lg"></i>
                        <span>新增授权</span>
                    </button>
                    <button class="quick-action-btn" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>刷新数据</span>
                    </button>
                </div>
            </div>

            <!-- 精美统计卡片 -->
            <div class="stats-grid animate-fade-in-up">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="bi bi-wifi"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?= number_format($detailedStats['online_licenses'] ?? 0) ?></div>
                        <div class="stat-label">在线授权</div>
                        <div class="stat-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span>实时在线</span>
                        </div>
                    </div>
                    <div class="stat-decoration"></div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="bi bi-key-fill"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?= number_format($detailedStats['total_licenses'] ?? 0) ?></div>
                        <div class="stat-label">总授权数</div>
                        <div class="stat-trend neutral">
                            <span>有效授权码</span>
                        </div>
                    </div>
                    <div class="stat-decoration"></div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?= number_format($detailedStats['today_verifications'] ?? 0) ?></div>
                        <div class="stat-label">今日验证</div>
                        <div class="stat-trend <?= ($detailedStats['failed_today'] ?? 0) > 0 ? 'negative' : 'positive' ?>">
                            <i class="bi bi-<?= ($detailedStats['failed_today'] ?? 0) > 0 ? 'exclamation-triangle' : 'check-circle' ?>"></i>
                            <span><?= $detailedStats['failed_today'] ?? 0 ?> 次失败</span>
                        </div>
                    </div>
                    <div class="stat-decoration"></div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="bi bi-laptop"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?= number_format($detailedStats['active_devices'] ?? 0) ?></div>
                        <div class="stat-label">活跃设备</div>
                        <div class="stat-trend neutral">
                            <span>已绑定设备</span>
                        </div>
                    </div>
                    <div class="stat-decoration"></div>
                </div>
            </div>

            <!-- 数据可视化图表 -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up me-2"></i>最近7天授权验证趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="trendChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-pie-chart me-2"></i>授权类型分布
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="typeChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-bar-chart me-2"></i>设备使用情况
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="deviceChart" height="150"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>异常检测
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($anomalousLogins) || !empty($multiLocationLogins)): ?>
                                <?php if (!empty($anomalousLogins)): ?>
                                    <h6 class="text-danger">连续失败登录</h6>
                                    <?php foreach ($anomalousLogins as $anomaly): ?>
                                        <div class="alert alert-danger alert-sm py-2">
                                            <strong><?= htmlspecialchars($anomaly['license_key']) ?></strong>
                                            从 <?= htmlspecialchars($anomaly['ip_address']) ?>
                                            连续失败 <?= $anomaly['attempt_count'] ?> 次
                                            <small class="d-block text-muted">
                                                最后尝试: <?= date('H:i:s', strtotime($anomaly['last_attempt'])) ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                                <?php if (!empty($multiLocationLogins)): ?>
                                    <h6 class="text-warning">多地登录检测</h6>
                                    <?php foreach ($multiLocationLogins as $multi): ?>
                                        <div class="alert alert-warning alert-sm py-2">
                                            <strong><?= htmlspecialchars($multi['license_key']) ?></strong>
                                            在 <?= $multi['ip_count'] ?> 个不同IP登录
                                            <small class="d-block text-muted">
                                                IP: <?= htmlspecialchars($multi['ip_addresses']) ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-3">
                                    <i class="bi bi-shield-check" style="font-size: 3rem; opacity: 0.3;"></i>
                                    <p class="mb-0">暂无异常活动</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-key me-2"></i>最近创建授权
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentLicenses)): ?>
                                <p class="text-muted text-center">暂无授权数据</p>
                            <?php else: ?>
                                <?php foreach (array_slice($recentLicenses, 0, 8) as $license): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($license['license_key']) ?></h6>
                                            <small class="text-muted">
                                                创建: <?= date('Y-m-d H:i:s', strtotime($license['created_at'])) ?>
                                                <?php if ($license['expire_time']): ?>
                                                    | 过期: <?= date('Y-m-d', strtotime($license['expire_time'])) ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <div>
                                            <span class="badge bg-<?= $license['user_type'] === 'admin' ? 'danger' : ($license['user_type'] === 'pro' ? 'warning' : 'primary') ?>">
                                                <?= ucfirst($license['user_type']) ?>
                                            </span>
                                            <span class="badge bg-<?= $license['status'] ? 'success' : 'secondary' ?> ms-1">
                                                <?= $license['status'] ? '正常' : '禁用' ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-activity me-2"></i>最近授权活动
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentLogs)): ?>
                                <p class="text-muted text-center">暂无授权记录</p>
                            <?php else: ?>
                                <?php foreach (array_slice($recentLogs, 0, 8) as $log): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <h6 class="mb-0">
                                                <?= htmlspecialchars($log['license_key'] ?? '未知授权') ?>
                                                <?php
                                                $badgeClass = 'danger';
                                                $badgeText = '失败';

                                                if ($log['login_type'] === 'success') {
                                                    $badgeClass = 'success';
                                                    $badgeText = '成功';
                                                } elseif ($log['login_type'] === 'logout') {
                                                    $badgeClass = 'warning';
                                                    $badgeText = '登出';
                                                } elseif ($log['login_type'] === 'login') {
                                                    $badgeClass = 'info';
                                                    $badgeText = '登录';
                                                }
                                                ?>
                                                <span class="badge bg-<?= $badgeClass ?> ms-2">
                                                    <?= $badgeText ?>
                                                </span>
                                            </h6>
                                            <small class="text-muted">
                                                <?= htmlspecialchars($log['ip_address']) ?>
                                                <?php if ($log['device_id']): ?>
                                                    | <?= htmlspecialchars(substr($log['device_id'], 0, 12)) ?>...
                                                <?php endif; ?>
                                                | <?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif ($page === 'licenses'): ?>
            <!-- 现代化授权管理 -->
            <div class="page-header animate-fade-in-up">
                <div class="page-title-section">
                    <h1 class="page-title">授权管理 🔑</h1>
                    <p class="page-subtitle">管理所有用户授权码和权限设置</p>
                </div>
                <div class="page-actions">
                    <button type="button" class="action-btn primary" data-bs-toggle="modal" data-bs-target="#addLicenseModal">
                        <i class="bi bi-plus-lg"></i>
                        <span>添加授权</span>
                    </button>
                    <button type="button" class="action-btn secondary" onclick="exportLicenses()">
                        <i class="bi bi-download"></i>
                        <span>导出数据</span>
                    </button>
                </div>
            </div>

            <?php if (isset($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($successMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($errorMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['new_user'])): ?>
                <div class="new-user-notice">
                    <div class="alert alert-info alert-dismissible fade show">
                        <i class="bi bi-check-circle me-2"></i>
                        <strong>🎉 新用户已添加！</strong><br>
                        授权码：<code><?= htmlspecialchars($_GET['new_user']) ?></code> 已高亮显示在列表中
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 现代化搜索筛选区域 -->
            <div class="search-filter-panel animate-fade-in-up">
                <div class="search-section">
                    <h3 class="section-title">🔍 搜索筛选</h3>
                    <form method="GET" class="search-form">
                        <input type="hidden" name="page" value="licenses">

                        <div class="search-grid">
                            <div class="search-field">
                                <label class="field-label">搜索授权码</label>
                                <div class="input-with-icon">
                                    <i class="bi bi-search input-icon"></i>
                                    <input type="text" class="modern-input" name="search"
                                           value="<?= htmlspecialchars($search ?? '') ?>"
                                           placeholder="输入授权码进行搜索...">
                                </div>
                            </div>

                            <div class="search-field">
                                <label class="field-label">状态筛选</label>
                                <select class="modern-select" name="status">
                                    <option value="">全部状态</option>
                                    <option value="1" <?= ($statusFilter === '1') ? 'selected' : '' ?>>✅ 正常</option>
                                    <option value="0" <?= ($statusFilter === '0') ? 'selected' : '' ?>>❌ 禁用</option>
                                    <option value="expired" <?= ($statusFilter === 'expired') ? 'selected' : '' ?>>⏰ 已过期</option>
                                </select>
                            </div>

                            <div class="search-field">
                                <label class="field-label">类型筛选</label>
                                <select class="modern-select" name="type">
                                    <option value="">全部类型</option>
                                    <option value="basic" <?= ($typeFilter === 'basic') ? 'selected' : '' ?>>🔵 Basic</option>
                                    <option value="pro" <?= ($typeFilter === 'pro') ? 'selected' : '' ?>>🟡 Pro</option>
                                    <option value="enterprise" <?= ($typeFilter === 'enterprise') ? 'selected' : '' ?>>🟢 Enterprise</option>
                                    <option value="admin" <?= ($typeFilter === 'admin') ? 'selected' : '' ?>>🔴 Admin</option>
                                </select>
                            </div>

                            <div class="search-actions">
                                <button type="submit" class="search-btn primary">
                                    <i class="bi bi-search"></i>
                                    <span>搜索</span>
                                </button>
                                <a href="?page=licenses" class="search-btn secondary">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    <span>重置</span>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 批量操作工具栏 -->
            <div class="batch-toolbar" id="batchToolbar">
                <form method="POST" id="batchForm">
                    <input type="hidden" name="page" value="licenses">
                    <input type="hidden" name="action" value="batch">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">批量操作</label>
                            <select class="form-select" name="batch_action" required>
                                <option value="">选择操作</option>
                                <option value="enable">启用</option>
                                <option value="disable">禁用</option>
                                <option value="extend">延期</option>
                            </select>
                        </div>
                        <div class="col-md-2" id="extendMonthsField" style="display: none;">
                            <label class="form-label">延期月数</label>
                            <input type="number" class="form-control" name="extend_months" value="1" min="1" max="60">
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-warning me-2" onclick="return confirm('确定要执行批量操作吗？')">
                                <i class="bi bi-gear me-1"></i>执行操作
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                                <i class="bi bi-x me-1"></i>取消选择
                            </button>
                        </div>
                        <div class="col-md-4">
                            <span class="text-muted">已选择 <span id="selectedCount">0</span> 个授权</span>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 授权列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-list me-2"></i>授权列表
                        <span class="badge bg-secondary ms-2"><?= number_format($totalLicenses ?? 0) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!isset($licenses) || empty($licenses)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox" style="font-size: 3rem; opacity: 0.3;"></i>
                            <p class="text-muted mt-3">暂无授权数据</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th>授权码</th>
                                        <th>密码</th>
                                        <th>QQ号</th>
                                        <th>类型</th>
                                        <th>状态</th>
                                        <th>设备使用</th>
                                        <th>过期时间</th>
                                        <th>创建时间</th>
                                        <th width="120">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $newUserHighlight = $_GET['new_user'] ?? '';
                                    foreach ($licenses as $license):
                                        $isNewUser = $newUserHighlight && $license['username'] === $newUserHighlight;
                                    ?>
                                        <tr <?= $isNewUser ? 'class="table-success" style="animation: highlight 3s ease-in-out;"' : '' ?>>
                                            <td>
                                                <input type="checkbox" class="form-check-input license-checkbox"
                                                       name="license_ids[]" value="<?= $license['id'] ?>">
                                            </td>
                                            <td>
                                                <code><?= htmlspecialchars($license['username']) ?></code>
                                                <?php if ($license['notes']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($license['notes']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-info"
                                                        onclick="showPassword(<?= $license['id'] ?>, '<?= htmlspecialchars($license['username']) ?>')"
                                                        title="查看密码">
                                                    <i class="bi bi-eye"></i> 查看
                                                </button>
                                            </td>
                                            <td>
                                                <?php if ($license['qq_number'] === 'PENDING'): ?>
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="bi bi-clock me-1"></i>待用户填写
                                                    </span>
                                                <?php elseif (!empty($license['qq_number'])): ?>
                                                    <code class="text-info"><?= htmlspecialchars($license['qq_number']) ?></code>
                                                    <button type="button" class="btn btn-sm btn-link p-0 ms-1"
                                                            onclick="window.open('https://wpa.qq.com/msgrd?v=3&uin=<?= htmlspecialchars($license['qq_number']) ?>&site=qq&menu=yes', '_blank')"
                                                            title="联系用户">
                                                        <i class="bi bi-chat-dots text-primary"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <small class="text-muted">未设置</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $license['user_type'] === 'admin' ? 'danger' : ($license['user_type'] === 'enterprise' ? 'success' : ($license['user_type'] === 'pro' ? 'warning' : 'primary')) ?>">
                                                    <?= strtoupper($license['user_type']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = 'secondary';
                                                $statusText = '未知';

                                                if (!$license['status']) {
                                                    $statusClass = 'secondary';
                                                    $statusText = '禁用';
                                                } elseif ($license['expire_status'] === '已过期') {
                                                    $statusClass = 'danger';
                                                    $statusText = '已过期';
                                                } else {
                                                    $statusClass = 'success';
                                                    $statusText = '正常';
                                                }
                                                ?>
                                                <span class="badge bg-<?= $statusClass ?> license-status">
                                                    <?= $statusText ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $license['device_count'] ?>/<?= $license['max_devices'] ?>
                                                </span>
                                                <?php if ($license['device_count'] >= $license['max_devices']): ?>
                                                    <i class="bi bi-exclamation-triangle text-warning ms-1" title="设备数量已达上限"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($license['expire_time']): ?>
                                                    <?= date('Y-m-d', strtotime($license['expire_time'])) ?>
                                                    <?php
                                                    $daysLeft = ceil((strtotime($license['expire_time']) - time()) / 86400);
                                                    if ($daysLeft <= 30 && $daysLeft > 0):
                                                    ?>
                                                        <br><small class="text-warning">还有<?= $daysLeft ?>天</small>
                                                    <?php elseif ($daysLeft <= 0): ?>
                                                        <br><small class="text-danger">已过期</small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-success">永久</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?= date('Y-m-d H:i', strtotime($license['created_at'])) ?></small>
                                            </td>
                                            <td class="table-actions">
                                                <button type="button" class="btn btn-sm btn-outline-primary edit-license-btn"
                                                        data-license-id="<?= $license['id'] ?>"
                                                        data-license-key="<?= htmlspecialchars($license['username']) ?>"
                                                        data-license-type="<?= htmlspecialchars($license['user_type']) ?>"
                                                        data-max-devices="<?= $license['max_devices'] ?>"
                                                        data-status="<?= $license['status'] ?>"
                                                        data-qq-number="<?= htmlspecialchars($license['qq_number'] ?? '') ?>"
                                                        data-notes="<?= htmlspecialchars($license['notes'] ?? '') ?>">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-warning"
                                                        onclick="resetPassword(<?= $license['id'] ?>, '<?= htmlspecialchars($license['username']) ?>')"
                                                        title="重置密码">
                                                    <i class="bi bi-key"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteLicense(<?= $license['id'] ?>, '<?= htmlspecialchars($license['username']) ?>')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <?php if ($totalPages > 1): ?>
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($pageNum > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=licenses&p=<?= $pageNum - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>&type=<?= urlencode($typeFilter) ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $pageNum - 2); $i <= min($totalPages, $pageNum + 2); $i++): ?>
                                        <li class="page-item <?= $i === $pageNum ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=licenses&p=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>&type=<?= urlencode($typeFilter) ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($pageNum < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=licenses&p=<?= $pageNum + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>&type=<?= urlencode($typeFilter) ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($page === 'devices'): ?>
            <!-- 现代化设备管理 -->
            <div class="page-header animate-fade-in-up">
                <div class="page-title-section">
                    <h1 class="page-title">设备管理 💻</h1>
                    <p class="page-subtitle">监控和管理所有用户设备的绑定状态</p>
                </div>
                <div class="page-actions">
                    <button type="button" class="action-btn secondary" onclick="refreshDeviceList()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>刷新数据</span>
                    </button>
                    <button type="button" class="action-btn primary" onclick="showDeviceStats()">
                        <i class="bi bi-bar-chart"></i>
                        <span>统计报告</span>
                    </button>
                </div>
            </div>

            <?php if (isset($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($successMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($errorMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- 现代化安全监控面板 -->
            <div class="security-monitor-panel animate-fade-in-up">
                <h3 class="section-title">🛡️ 安全监控</h3>

                <div class="security-grid">
                    <div class="security-card danger">
                        <div class="security-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="security-content">
                            <div class="security-title">设备超限警告</div>
                            <div class="security-count"><?= count($deviceAnomalies['over_limit'] ?? []) ?></div>
                            <div class="security-subtitle">个授权超出设备限制</div>
                        </div>
                        <div class="security-details">
                            <?php if (empty($deviceAnomalies['over_limit'])): ?>
                                <div class="no-anomaly">✅ 无异常</div>
                            <?php else: ?>
                                <?php foreach (array_slice($deviceAnomalies['over_limit'], 0, 3) as $anomaly): ?>
                                    <div class="anomaly-item">
                                        <div class="anomaly-license"><?= htmlspecialchars($anomaly['license_key']) ?></div>
                                        <div class="anomaly-info"><?= $anomaly['device_count'] ?>/<?= $anomaly['max_devices'] ?> 设备</div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="security-card warning">
                        <div class="security-icon">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <div class="security-content">
                            <div class="security-title">多地登录检测</div>
                            <div class="security-count"><?= count($deviceAnomalies['multi_location'] ?? []) ?></div>
                            <div class="security-subtitle">个设备多地登录</div>
                        </div>
                        <div class="security-details">
                            <?php if (empty($deviceAnomalies['multi_location'])): ?>
                                <div class="no-anomaly">✅ 无异常</div>
                            <?php else: ?>
                                <?php foreach (array_slice($deviceAnomalies['multi_location'], 0, 3) as $anomaly): ?>
                                    <div class="anomaly-item">
                                        <div class="anomaly-license"><?= htmlspecialchars($anomaly['license_key']) ?></div>
                                        <div class="anomaly-info"><?= $anomaly['ip_count'] ?> 个不同IP</div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="security-card info">
                        <div class="security-icon">
                            <i class="bi bi-shield-exclamation"></i>
                        </div>
                        <div class="security-content">
                            <div class="security-title">可疑活动监控</div>
                            <div class="security-count"><?= count($deviceAnomalies['suspicious_activity'] ?? []) ?></div>
                            <div class="security-subtitle">个设备异常尝试</div>
                        </div>
                        <div class="security-details">
                            <?php if (empty($deviceAnomalies['suspicious_activity'])): ?>
                                <div class="no-anomaly">✅ 无异常</div>
                            <?php else: ?>
                                <?php foreach (array_slice($deviceAnomalies['suspicious_activity'], 0, 3) as $anomaly): ?>
                                    <div class="anomaly-item">
                                        <div class="anomaly-license"><?= htmlspecialchars($anomaly['license_key']) ?></div>
                                        <div class="anomaly-info"><?= $anomaly['failed_attempts'] ?> 次失败</div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 现代化设备列表 -->
            <div class="device-list-panel animate-fade-in-up">
                <div class="device-list-header">
                    <div class="device-header-left">
                        <h3 class="section-title">💻 设备列表</h3>
                        <div class="device-stats">
                            <div class="stat-item">
                                <span class="stat-number"><?= count(array_unique(array_column($deviceList ?? [], 'license_key'))) ?></span>
                                <span class="stat-label">个授权</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number"><?= count($deviceList ?? []) ?></span>
                                <span class="stat-label">台设备</span>
                            </div>
                        </div>
                    </div>
                    <div class="device-header-right">
                        <div class="view-toggle-buttons">
                            <button type="button" class="view-toggle-btn active" data-view="cards" onclick="switchDeviceView('cards')" title="卡片视图">
                                <i class="bi bi-grid-3x3-gap"></i>
                            </button>
                            <button type="button" class="view-toggle-btn" data-view="table" onclick="switchDeviceView('table')" title="表格视图">
                                <i class="bi bi-table"></i>
                            </button>
                        </div>
                        <button type="button" class="refresh-btn" onclick="refreshDeviceList()" title="刷新设备列表">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>

                <div class="device-list-content">
                    <?php if (empty($deviceList)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">💻</div>
                            <div class="empty-title">暂无设备数据</div>
                            <div class="empty-subtitle">当用户首次登录时，设备信息将会显示在这里</div>
                        </div>
                    <?php else: ?>
                        <!-- 卡片视图 -->
                        <div id="cardsView" class="device-view-container">
                        <div class="device-cards-grid">
                            <?php foreach ($deviceList as $device): ?>
                                <div class="device-license-card">
                                    <div class="license-header">
                                        <div class="license-info">
                                            <div class="license-key"><?= htmlspecialchars($device['license_key']) ?></div>
                                            <div class="license-type-badge <?= $device['user_type'] ?>">
                                                <?= strtoupper($device['user_type']) ?>
                                            </div>
                                        </div>
                                        <div class="device-status-indicator">
                                            <?php if ($device['is_online']): ?>
                                                <div class="status-indicator online"></div>
                                                <span class="status-text online">在线</span>
                                            <?php else: ?>
                                                <div class="status-indicator offline"></div>
                                                <span class="status-text offline">离线</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                        <div class="device-main-info">
                                            <div class="device-identity">
                                                <div class="device-name" id="deviceName_<?= $device['device_table_id'] ?>">
                                                    <?= htmlspecialchars($device['device_name'] ?: '未命名设备') ?>
                                                    <button type="button" class="edit-name-btn"
                                                            onclick="editDeviceName(<?= $device['device_table_id'] ?>, '<?= htmlspecialchars($device['device_name'] ?: '未命名设备') ?>', <?= $device['user_id'] ?>, '<?= htmlspecialchars($device['device_id']) ?>')">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                </div>
                                                <div class="device-id">
                                                    ID: <?= htmlspecialchars(substr($device['device_id'], 0, 16)) ?>...
                                                </div>
                                            </div>
                                        </div>

                                        <div class="device-details">
                                            <div class="detail-item">
                                                <div class="detail-label">最后登录</div>
                                                <div class="detail-value">
                                                    <?php if ($device['last_login']): ?>
                                                        <?= date('m-d H:i', strtotime($device['last_login'])) ?>
                                                    <?php else: ?>
                                                        从未登录
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <div class="detail-item">
                                                <div class="detail-label">IP地址</div>
                                                <div class="detail-value">
                                                    <?= $device['last_ip'] ? htmlspecialchars($device['last_ip']) : '-' ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="device-actions">
                                            <button type="button" class="device-action-btn view"
                                                    onclick="viewDeviceDetails('<?= htmlspecialchars($device['device_id']) ?>', <?= $device['user_id'] ?>)"
                                                    title="查看详情">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" class="device-action-btn danger"
                                                    onclick="unbindDevice('<?= htmlspecialchars($device['device_id']) ?>', <?= $device['user_id'] ?>, '<?= htmlspecialchars($device['device_name'] ?: '未命名设备') ?>')"
                                                    title="解绑设备">
                                                <i class="bi bi-unlink"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- 表格视图 -->
                        <div id="tableView" class="device-view-container" style="display: none;">
                            <div class="device-table-container">
                                <table class="device-table">
                                    <thead>
                                        <tr>
                                            <th>授权码</th>
                                            <th>类型</th>
                                            <th>设备名称</th>
                                            <th>设备ID</th>
                                            <th>状态</th>
                                            <th>最后登录</th>
                                            <th>IP地址</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($deviceList as $device): ?>
                                            <tr class="device-row">
                                                <td class="license-cell">
                                                    <div class="license-info-table">
                                                        <div class="license-key-table"><?= htmlspecialchars($device['license_key']) ?></div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="license-type-badge <?= $device['user_type'] ?>">
                                                        <?= strtoupper($device['user_type']) ?>
                                                    </span>
                                                </td>
                                <td>
                                    <div class="device-name-table">
                                        <?= htmlspecialchars($device['device_name'] ?: '未命名设备') ?>
                                        <button type="button" class="edit-name-btn-table"
                                                onclick="editDeviceName(<?= $device['device_table_id'] ?>, '<?= htmlspecialchars($device['device_name'] ?: '未命名设备') ?>', <?= $device['user_id'] ?>, '<?= htmlspecialchars($device['device_id']) ?>')">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <code class="device-id-table"><?= htmlspecialchars(substr($device['device_id'], 0, 12)) ?>...</code>
                                </td>
                                <td>
                                    <?php if ($device['is_online']): ?>
                                        <span class="status-badge online">
                                            <i class="bi bi-circle-fill"></i> 在线
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge offline">
                                            <i class="bi bi-circle-fill"></i> 离线
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['last_login']): ?>
                                        <?= date('m-d H:i', strtotime($device['last_login'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted">从未登录</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= $device['last_ip'] ? htmlspecialchars($device['last_ip']) : '-' ?>
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <button type="button" class="table-action-btn view"
                                                onclick="viewDeviceDetails('<?= htmlspecialchars($device['device_id']) ?>', <?= $device['user_id'] ?>)"
                                                title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="table-action-btn danger"
                                                onclick="unbindDevice('<?= htmlspecialchars($device['device_id']) ?>', <?= $device['user_id'] ?>, '<?= htmlspecialchars($device['device_name'] ?: '未命名设备') ?>')"
                                                title="解绑设备">
                                            <i class="bi bi-unlink"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($page === 'logs'): ?>
            <!-- 现代化日志管理 -->
            <div class="page-header animate-fade-in-up">
                <div class="page-title-section">
                    <h1 class="page-title">授权日志 📊</h1>
                    <p class="page-subtitle">监控和分析所有授权验证活动记录</p>
                </div>
                <div class="page-actions">
                    <button type="button" class="action-btn secondary" onclick="exportLogs()">
                        <i class="bi bi-download"></i>
                        <span>导出日志</span>
                    </button>
                    <button type="button" class="action-btn primary" onclick="refreshLogs()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>刷新数据</span>
                    </button>
                </div>
            </div>

            <!-- 现代化日志统计 -->
            <div class="log-stats-panel animate-fade-in-up">
                <h3 class="section-title">📈 今日统计</h3>

                <div class="log-stats-grid">
                    <div class="log-stat-card primary">
                        <div class="stat-icon">
                            <i class="bi bi-journal-text"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($logStats['today_total'] ?? 0) ?></div>
                            <div class="stat-label">今日总数</div>
                        </div>
                        <div class="stat-trend">
                            <span class="trend-text">总验证次数</span>
                        </div>
                    </div>

                    <div class="log-stat-card success">
                        <div class="stat-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($logStats['today_success'] ?? 0) ?></div>
                            <div class="stat-label">成功验证</div>
                        </div>
                        <div class="stat-trend">
                            <span class="trend-text">
                                <?php
                                $successRate = ($logStats['today_total'] ?? 0) > 0 ?
                                    round((($logStats['today_success'] ?? 0) / ($logStats['today_total'] ?? 1)) * 100, 1) : 0;
                                ?>
                                成功率 <?= $successRate ?>%
                            </span>
                        </div>
                    </div>

                    <div class="log-stat-card danger">
                        <div class="stat-icon">
                            <i class="bi bi-x-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($logStats['today_failed'] ?? 0) ?></div>
                            <div class="stat-label">失败验证</div>
                        </div>
                        <div class="stat-trend">
                            <span class="trend-text">需要关注</span>
                        </div>
                    </div>

                    <div class="log-stat-card info">
                        <div class="stat-icon">
                            <i class="bi bi-calendar-week"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($logStats['week_total'] ?? 0) ?></div>
                            <div class="stat-label">本周总数</div>
                        </div>
                        <div class="stat-trend">
                            <span class="trend-text">7天统计</span>
                        </div>
                    </div>

                    <div class="log-stat-card warning">
                        <div class="stat-icon">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($logStats['unique_ips_today'] ?? 0) ?></div>
                            <div class="stat-label">独立IP</div>
                        </div>
                        <div class="stat-trend">
                            <span class="trend-text">今日访问</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 现代化日志查看器 -->
            <div class="log-viewer-panel animate-fade-in-up">
                <div class="log-tabs-header">
                    <h3 class="section-title">🔍 日志查看器</h3>
                    <div class="log-type-tabs">
                        <a class="log-tab <?= $logType === 'auth' ? 'active' : '' ?>"
                           href="?page=logs&type=auth">
                            <i class="bi bi-shield-check"></i>
                            <span>授权验证</span>
                        </a>
                        <a class="log-tab <?= $logType === 'heartbeat' ? 'active' : '' ?>"
                           href="?page=logs&type=heartbeat">
                            <i class="bi bi-heart-pulse"></i>
                            <span>心跳检测</span>
                        </a>
                    </div>
                </div>

                <div class="log-content">
                    <!-- 现代化搜索筛选 -->
                    <div class="log-search-panel">
                        <form method="GET" class="log-search-form">
                            <input type="hidden" name="page" value="logs">
                            <input type="hidden" name="type" value="<?= htmlspecialchars($logType) ?>">

                            <div class="search-filters-grid">
                                <div class="filter-group">
                                    <label class="filter-label">🔑 授权码搜索</label>
                                    <div class="input-with-icon">
                                        <i class="bi bi-search input-icon"></i>
                                        <input type="text" class="modern-input" name="search"
                                               value="<?= htmlspecialchars($search) ?>"
                                               placeholder="输入授权码进行搜索...">
                                    </div>
                                </div>

                                <div class="filter-group">
                                    <label class="filter-label">🌐 IP地址筛选</label>
                                    <div class="input-with-icon">
                                        <i class="bi bi-geo-alt input-icon"></i>
                                        <input type="text" class="modern-input" name="ip"
                                               value="<?= htmlspecialchars($ipSearch) ?>"
                                               placeholder="输入IP地址...">
                                    </div>
                                </div>

                                <?php if ($logType === 'auth'): ?>
                                <div class="filter-group">
                                    <label class="filter-label">📊 验证状态</label>
                                    <select class="modern-select" name="status">
                                        <option value="">全部状态</option>
                                        <option value="success" <?= $statusFilter === 'success' ? 'selected' : '' ?>>✅ 成功</option>
                                        <option value="failed" <?= $statusFilter === 'failed' ? 'selected' : '' ?>>❌ 失败</option>
                                    </select>
                                </div>
                                <?php endif; ?>

                                <div class="filter-group">
                                    <label class="filter-label">📅 开始日期</label>
                                    <input type="date" class="modern-input" name="date_from" value="<?= htmlspecialchars($dateFrom) ?>">
                                </div>

                                <div class="filter-group">
                                    <label class="filter-label">📅 结束日期</label>
                                    <input type="date" class="modern-input" name="date_to" value="<?= htmlspecialchars($dateTo) ?>">
                                </div>

                                <div class="filter-actions">
                                    <button type="submit" class="filter-btn primary">
                                        <i class="bi bi-search"></i>
                                        <span>搜索</span>
                                    </button>
                                    <a href="?page=logs&type=<?= htmlspecialchars($logType) ?>" class="filter-btn secondary">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        <span>重置</span>
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 现代化日志列表 -->
                    <?php if (empty($logs)): ?>
                        <div class="empty-logs-state">
                            <div class="empty-icon">📋</div>
                            <div class="empty-title">暂无日志数据</div>
                            <div class="empty-subtitle">当前筛选条件下没有找到相关日志记录</div>
                        </div>
                    <?php else: ?>
                        <div class="modern-log-list">
                            <div class="log-list-header">
                                <div class="log-header-item time">时间</div>
                                <div class="log-header-item license">授权码</div>
                                <?php if ($logType === 'auth'): ?>
                                    <div class="log-header-item status">状态</div>
                                <?php endif; ?>
                                <div class="log-header-item ip">IP地址</div>
                                <div class="log-header-item device">设备信息</div>
                                <?php if ($logType === 'auth'): ?>
                                    <div class="log-header-item agent">用户代理</div>
                                <?php endif; ?>
                            </div>

                            <div class="log-entries">
                                <?php foreach ($logs as $log): ?>
                                    <div class="log-entry">
                                        <div class="log-item time">
                                            <div class="log-time-main"><?= date('H:i:s', strtotime($log['created_at'])) ?></div>
                                            <div class="log-time-date"><?= date('m-d', strtotime($log['created_at'])) ?></div>
                                        </div>

                                        <div class="log-item license">
                                            <?php if ($log['username']): ?>
                                                <div class="license-code"><?= htmlspecialchars($log['username']) ?></div>
                                                <?php if (isset($log['user_type'])): ?>
                                                    <div class="license-type <?= $log['user_type'] ?>">
                                                        <?= strtoupper($log['user_type']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <div class="license-unknown">未知授权</div>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($logType === 'auth'): ?>
                                        <div class="log-item status">
                                            <?php
                                            $statusClass = 'failed';
                                            $statusIcon = 'bi-x-circle';
                                            $statusText = '失败';

                                            if ($log['login_type'] === 'success') {
                                                $statusClass = 'success';
                                                $statusIcon = 'bi-check-circle';
                                                $statusText = '成功';
                                            } elseif ($log['login_type'] === 'logout') {
                                                $statusClass = 'logout';
                                                $statusIcon = 'bi-box-arrow-right';
                                                $statusText = '登出';
                                            } elseif ($log['login_type'] === 'login') {
                                                $statusClass = 'login';
                                                $statusIcon = 'bi-box-arrow-in-right';
                                                $statusText = '登录';
                                            }
                                            ?>
                                            <div class="status-badge <?= $statusClass ?>">
                                                <i class="bi <?= $statusIcon ?>"></i>
                                                <span><?= $statusText ?></span>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <div class="log-item ip">
                                            <div class="ip-address">
                                                <i class="bi bi-geo-alt"></i>
                                                <?= htmlspecialchars($log['ip_address'] ?? '-') ?>
                                            </div>
                                        </div>

                                        <div class="log-item device">
                                            <?php if ($log['device_id']): ?>
                                                <div class="device-id">
                                                    <i class="bi bi-laptop"></i>
                                                    <?= htmlspecialchars(substr($log['device_id'], 0, 12)) ?>...
                                                </div>
                                            <?php else: ?>
                                                <div class="device-none">
                                                    <i class="bi bi-dash"></i>
                                                    无设备信息
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($logType === 'auth'): ?>
                                        <div class="log-item agent">
                                            <?php if (isset($log['user_agent']) && $log['user_agent']): ?>
                                                <div class="user-agent" title="<?= htmlspecialchars($log['user_agent']) ?>">
                                                    <i class="bi bi-browser-chrome"></i>
                                                    <?= htmlspecialchars(substr($log['user_agent'], 0, 25)) ?>...
                                                </div>
                                            <?php else: ?>
                                                <div class="agent-none">
                                                    <i class="bi bi-dash"></i>
                                                    未知
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <?php if ($totalPages > 1): ?>
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($pageNum > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=logs&type=<?= urlencode($logType) ?>&p=<?= $pageNum - 1 ?>&search=<?= urlencode($search) ?>&ip=<?= urlencode($ipSearch) ?>&status=<?= urlencode($statusFilter) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $pageNum - 2); $i <= min($totalPages, $pageNum + 2); $i++): ?>
                                        <li class="page-item <?= $i === $pageNum ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=logs&type=<?= urlencode($logType) ?>&p=<?= $i ?>&search=<?= urlencode($search) ?>&ip=<?= urlencode($ipSearch) ?>&status=<?= urlencode($statusFilter) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($pageNum < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=logs&type=<?= urlencode($logType) ?>&p=<?= $pageNum + 1 ?>&search=<?= urlencode($search) ?>&ip=<?= urlencode($ipSearch) ?>&status=<?= urlencode($statusFilter) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>

                        <div class="mt-3">
                            <small class="text-muted">
                                显示第 <?= ($pageNum - 1) * $pageSize + 1 ?> - <?= min($pageNum * $pageSize, $totalLogs) ?> 条，
                                共 <?= number_format($totalLogs ?? 0) ?> 条记录
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($page === 'debug'): ?>
            <!-- 黑色主题代码风格日志查看器 -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h1 class="h4 mb-0">📝 系统日志</h1>
                    <small class="text-muted">实时查看系统运行状态和错误信息</small>
                </div>
                <div class="d-flex gap-2">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="clear_logs">
                        <button type="submit" class="btn btn-outline-danger btn-sm" onclick="return confirm('确定要清空日志吗？')">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </form>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 纯代码风格日志显示 -->
            <div class="code-log-container">
                <?php if (empty($debugLogs)): ?>
                    <div class="code-log-container">
                        <div class="text-center py-4" style="background: #000000; color: #888888;">
                            <i class="bi bi-journal" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <div>暂无日志记录</div>
                            <small style="color: #666666;">系统运行正常或日志已被清空</small>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="log-code-block">
                        <?php foreach ($debugLogs as $index => $logLine): ?>
                            <?php
                            $logData = json_decode($logLine, true);
                            $isError = false;
                            $isWarning = false;

                            if ($logData) {
                                $level = $logData['level'] ?? 'INFO';
                                $message = $logData['message'] ?? '';
                                $timestamp = $logData['timestamp'] ?? '';
                                $context = $logData['context'] ?? [];

                                $isError = ($level === 'ERROR');
                                $isWarning = ($level === 'WARNING');

                                $logDisplay = "[{$timestamp}] {$level}: {$message}";
                                if (!empty($context)) {
                                    $logDisplay .= " " . json_encode($context, JSON_UNESCAPED_UNICODE);
                                }
                            } else {
                                $logDisplay = $logLine;
                                $isError = (stripos($logLine, 'error') !== false || stripos($logLine, '错误') !== false);
                                $isWarning = (stripos($logLine, 'warning') !== false || stripos($logLine, '警告') !== false);
                            }
                            ?>
                            <div class="log-line <?= $isError ? 'log-error' : ($isWarning ? 'log-warning' : '') ?>">
                                <span class="line-number"><?= str_pad((string)($index + 1), 4, '0', STR_PAD_LEFT) ?></span>
                                <span class="log-content"><?= htmlspecialchars($logDisplay) ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

        <?php elseif ($page === 'settings'): ?>
            <!-- 现代化系统设置 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">⚙️ 系统设置</h1>
                    <p class="text-muted mb-0">管理系统配置、安全设置和维护工具</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-success" onclick="exportSystemConfig()">
                        <i class="bi bi-download me-1"></i>导出配置
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="checkSystemHealth()">
                        <i class="bi bi-shield-check me-1"></i>系统检查
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                    </button>
                </div>
            </div>

            <?php if (isset($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($successMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($errorMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- 快速操作面板 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title mb-3">🚀 快速操作</h6>
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary w-100" onclick="window.open('system_status.php', '_blank')">
                                        <i class="bi bi-speedometer2 d-block mb-1"></i>
                                        <small>系统监控</small>
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-success w-100" onclick="window.open('check_database.php', '_blank')">
                                        <i class="bi bi-database-check d-block mb-1"></i>
                                        <small>数据库检查</small>
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-info w-100" onclick="window.open('test_api.php', '_blank')">
                                        <i class="bi bi-cloud-check d-block mb-1"></i>
                                        <small>API测试</small>
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-warning w-100" onclick="window.open('run_log_test.php', '_blank')">
                                        <i class="bi bi-bug d-block mb-1"></i>
                                        <small>日志测试</small>
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-secondary w-100" onclick="window.location.href='?page=qq_config'">
                                        <i class="bi bi-chat-dots d-block mb-1"></i>
                                        <small>QQ群配置</small>
                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 安全设置 -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="bi bi-shield-lock me-2 text-primary"></i>安全设置
                            </h5>
                            <small class="text-muted">管理系统安全和访问控制</small>
                        </div>
                        <div class="card-body">
                            <!-- 修改密码 -->
                            <form method="POST" class="mb-4">
                                <input type="hidden" name="action" value="change_password">
                                <h6>修改管理员密码</h6>
                                <div class="mb-3">
                                    <label class="form-label">当前密码</label>
                                    <input type="password" class="form-control" name="current_password" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">新密码</label>
                                    <input type="password" class="form-control" name="new_password" required minlength="6">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">确认新密码</label>
                                    <input type="password" class="form-control" name="confirm_password" required minlength="6">
                                </div>
                                <button type="submit" class="btn btn-primary" onclick="return confirm('确定要修改管理员密码吗？')">
                                    <i class="bi bi-key me-1"></i>修改密码
                                </button>
                            </form>

                            <hr>

                            <!-- 会话管理 -->
                            <h6 class="text-primary">🔐 会话管理</h6>
                            <div class="row g-2 mb-3">
                                <div class="col-md-6">
                                    <form method="POST" class="d-inline w-100">
                                        <input type="hidden" name="action" value="force_logout">
                                        <button type="submit" class="btn btn-warning w-100" onclick="return confirm('确定要强制所有用户重新登录吗？\n\n这将清除所有访问令牌，所有在线用户需要重新验证。')">
                                            <i class="bi bi-box-arrow-right me-1"></i>强制全部登出
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <form method="POST" class="d-inline w-100">
                                        <input type="hidden" name="action" value="clear_failed_attempts">
                                        <button type="submit" class="btn btn-outline-info w-100" onclick="return confirm('确定要清除所有失败登录记录吗？')">
                                            <i class="bi bi-shield-x me-1"></i>清除失败记录
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- 安全统计 -->
                            <h6 class="text-primary">📊 安全统计</h6>
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-success"><?= $dbStats['active_tokens'] ?? 0 ?></div>
                                        <small class="text-muted">活跃会话</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-warning"><?= $dbStats['today_logins'] ?? 0 ?></div>
                                        <small class="text-muted">今日登录</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-danger"><?= $dbStats['failed_attempts'] ?? 0 ?></div>
                                        <small class="text-muted">失败尝试</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- 数据库维护 -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="bi bi-database me-2 text-success"></i>数据库维护
                            </h5>
                            <small class="text-muted">数据清理和性能优化</small>
                        </div>
                        <div class="card-body">
                            <!-- 数据清理 -->
                            <h6 class="text-success">🧹 数据清理</h6>
                            <div class="row g-2 mb-3">
                                <div class="col-md-6">
                                    <form method="POST" class="w-100">
                                        <input type="hidden" name="action" value="cleanup_expired">
                                        <button type="submit" class="btn btn-outline-primary w-100" onclick="return confirm('确定要清理过期数据吗？')">
                                            <i class="bi bi-trash me-1"></i>清理过期数据
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <form method="POST" class="w-100">
                                        <input type="hidden" name="action" value="optimize_database">
                                        <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('确定要优化数据库吗？')">
                                            <i class="bi bi-speedometer2 me-1"></i>优化数据库
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- 日志管理 -->
                            <h6 class="text-success">📝 日志管理</h6>
                            <form method="POST" class="mb-4">
                                <input type="hidden" name="action" value="cleanup_logs">
                                <div class="row g-2">
                                    <div class="col-md-8">
                                        <select class="form-select" name="cleanup_days">
                                            <option value="30">清理30天前的日志</option>
                                            <option value="60">清理60天前的日志</option>
                                            <option value="90">清理90天前的日志</option>
                                            <option value="180">清理180天前的日志</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('确定要清理旧日志吗？此操作不可恢复！')">
                                            <i class="bi bi-trash me-1"></i>清理日志
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <!-- 备份管理 -->
                            <h6 class="text-success">💾 备份管理</h6>
                            <div class="row g-2 mb-3">
                                <div class="col-md-6">
                                    <form method="POST" class="w-100">
                                        <input type="hidden" name="action" value="backup_database">
                                        <button type="submit" class="btn btn-outline-info w-100" onclick="return confirm('确定要下载数据库备份吗？\n\n将生成包含所有表结构和数据的SQL文件')">
                                            <i class="bi bi-download me-1"></i>下载数据库
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-success w-100" onclick="showBackupInfo()">
                                        <i class="bi bi-info-circle me-1"></i>备份说明
                                    </button>
                                </div>
                            </div>

                            <!-- 数据库统计 -->
                            <h6 class="text-success">📊 数据库统计</h6>
                            <div class="row g-2 text-center">
                                <div class="col-6 col-md-4">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-primary"><?= number_format($dbStats['users_count'] ?? 0) ?></div>
                                        <small class="text-muted">授权数量</small>
                                    </div>
                                </div>
                                <div class="col-6 col-md-4">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-info"><?= number_format($dbStats['devices_count'] ?? 0) ?></div>
                                        <small class="text-muted">设备数量</small>
                                    </div>
                                </div>
                                <div class="col-6 col-md-4">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-success"><?= number_format($dbStats['tokens_count'] ?? 0) ?></div>
                                        <small class="text-muted">活跃令牌</small>
                                    </div>
                                </div>
                                <div class="col-6 col-md-4">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-warning"><?= number_format($dbStats['logs_count'] ?? 0) ?></div>
                                        <small class="text-muted">登录日志</small>
                                    </div>
                                </div>
                                <div class="col-6 col-md-4">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-secondary"><?= number_format($dbStats['heartbeat_count'] ?? 0) ?></div>
                                        <small class="text-muted">心跳记录</small>
                                    </div>
                                </div>
                                <div class="col-6 col-md-4">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-dark"><?= $dbStats['db_size'] ?? 'N/A' ?></div>
                                        <small class="text-muted">数据库大小</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 系统配置 -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="bi bi-gear me-2 text-warning"></i>系统配置
                            </h5>
                            <small class="text-muted">调整系统参数和行为</small>
                        </div>
                        <div class="card-body">
                            <!-- 令牌设置 -->
                            <h6 class="text-warning">🎫 令牌设置</h6>
                            <form method="POST" class="mb-3">
                                <input type="hidden" name="action" value="update_token_settings">
                                <div class="row g-2">
                                    <div class="col-md-8">
                                        <select class="form-select" name="token_expire_hours">
                                            <option value="1">1小时</option>
                                            <option value="6">6小时</option>
                                            <option value="12">12小时</option>
                                            <option value="24" selected>24小时</option>
                                            <option value="48">48小时</option>
                                            <option value="168">7天</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-outline-warning w-100">
                                            <i class="bi bi-clock me-1"></i>设置
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <!-- 设备限制 -->
                            <h6 class="text-warning">📱 默认设备限制</h6>
                            <form method="POST" class="mb-3">
                                <input type="hidden" name="action" value="update_device_limits">
                                <div class="row g-2">
                                    <div class="col-4">
                                        <input type="number" class="form-control" name="basic_devices" value="1" min="1" max="10" placeholder="Basic">
                                    </div>
                                    <div class="col-4">
                                        <input type="number" class="form-control" name="pro_devices" value="3" min="1" max="10" placeholder="Pro">
                                    </div>
                                    <div class="col-4">
                                        <button type="submit" class="btn btn-outline-warning w-100">
                                            <i class="bi bi-save me-1"></i>保存
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <!-- QQ群配置 -->
                            <h6 class="text-warning">💬 QQ群配置</h6>
                            <div class="mb-3">
                                <small class="text-muted">管理前端显示的QQ群信息</small>
                            </div>

                            <?php
                            // 获取当前QQ配置
                            $qqConfigs = [];
                            try {
                                $configs = $database->fetchAll("SELECT config_key, config_value FROM qq_config");
                                foreach ($configs as $config) {
                                    $qqConfigs[$config['config_key']] = $config['config_value'];
                                }
                            } catch (Exception $e) {
                                // QQ配置表可能不存在，显示初始化提示
                            }
                            ?>

                            <?php if (empty($qqConfigs)): ?>
                                <!-- QQ配置未初始化 -->
                                <div class="alert alert-warning py-2">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    QQ配置未初始化，请手动创建qq_config表
                                </div>
                            <?php else: ?>
                                <!-- QQ配置表单 -->
                                <form method="POST" class="qq-config-form">
                                    <input type="hidden" name="action" value="update_qq_config">

                                    <div class="row g-2 mb-2">
                                        <div class="col-md-6">
                                            <label class="form-label small">QQ群号</label>
                                            <input type="text" class="form-control form-control-sm"
                                                   name="qq_group_number"
                                                   value="<?= htmlspecialchars($qqConfigs['qq_group_number'] ?? '') ?>"
                                                   placeholder="735821698" pattern="[0-9]{5,11}">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label small">群名称</label>
                                            <input type="text" class="form-control form-control-sm"
                                                   name="qq_group_name"
                                                   value="<?= htmlspecialchars($qqConfigs['qq_group_name'] ?? '') ?>"
                                                   placeholder="Augment续杯工具交流群">
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <label class="form-label small">群描述</label>
                                        <textarea class="form-control form-control-sm"
                                                  name="qq_group_desc" rows="2"
                                                  placeholder="欢迎加入QQ群获取技术支持和最新更新！"><?= htmlspecialchars($qqConfigs['qq_group_desc'] ?? '') ?></textarea>
                                    </div>

                                    <div class="row g-2 mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label small">启用状态</label>
                                            <select class="form-select form-select-sm" name="qq_group_enabled">
                                                <option value="1" <?= ($qqConfigs['qq_group_enabled'] ?? '1') === '1' ? 'selected' : '' ?>>启用</option>
                                                <option value="0" <?= ($qqConfigs['qq_group_enabled'] ?? '1') === '0' ? 'selected' : '' ?>>禁用</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label small">操作</label>
                                            <div class="d-flex gap-1">
                                                <button type="submit" class="btn btn-outline-primary btn-sm flex-fill">
                                                    <i class="bi bi-save me-1"></i>保存
                                                </button>
                                                <a href="api.php?action=get_qq_config" class="btn btn-outline-info btn-sm" target="_blank" title="测试API">
                                                    <i class="bi bi-cloud-check"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                <!-- 当前配置预览 -->
                                <div class="border rounded p-2 bg-light">
                                    <small class="text-muted d-block mb-1">当前配置预览：</small>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-chat-dots text-primary me-2"></i>
                                        <strong><?= htmlspecialchars($qqConfigs['qq_group_name'] ?? 'QQ交流群') ?></strong>
                                        <code class="ms-2"><?= htmlspecialchars($qqConfigs['qq_group_number'] ?? '735821698') ?></code>
                                        <span class="badge bg-<?= ($qqConfigs['qq_group_enabled'] ?? '1') === '1' ? 'success' : 'secondary' ?> ms-2">
                                            <?= ($qqConfigs['qq_group_enabled'] ?? '1') === '1' ? '启用' : '禁用' ?>
                                        </span>
                                    </div>
                                    <small class="text-muted"><?= htmlspecialchars($qqConfigs['qq_group_desc'] ?? '欢迎加入QQ群获取技术支持和最新更新！') ?></small>
                                </div>
                            <?php endif; ?>

                            <!-- 系统维护模式 -->
                            <h6 class="text-warning">🔧 维护模式</h6>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <small class="text-muted">启用后将阻止新的登录请求</small>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="maintenanceMode" onchange="toggleMaintenanceMode()">
                                    <label class="form-check-label" for="maintenanceMode">维护模式</label>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="bi bi-info-circle me-2 text-info"></i>系统信息
                            </h5>
                            <small class="text-muted">服务器环境和运行状态</small>
                        </div>
                        <div class="card-body">
                            <!-- 基础信息 -->
                            <h6 class="text-info">💻 基础信息</h6>
                            <div class="row g-2 mb-3 text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-primary"><?= $systemInfo['php_version'] ?? 'N/A' ?></div>
                                        <small class="text-muted">PHP版本</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-success"><?= $systemInfo['sqlite_version'] ?? 'N/A' ?></div>
                                        <small class="text-muted">数据库版本</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 时间信息 -->
                            <h6 class="text-info">🕒 时间信息</h6>
                            <div class="row g-2 mb-3 text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-dark current-time"><?= date('H:i:s') ?></div>
                                        <small class="text-muted">当前时间</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-secondary"><?= $systemInfo['timezone'] ?? 'N/A' ?></div>
                                        <small class="text-muted">时区</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 资源信息 -->
                            <h6 class="text-info">📊 资源信息</h6>
                            <div class="row g-2 text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-warning"><?= $systemInfo['memory_usage'] ?? 'N/A' ?></div>
                                        <small class="text-muted">内存使用</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-info"><?= $systemInfo['memory_limit'] ?? 'N/A' ?></div>
                                        <small class="text-muted">内存限制</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-primary"><?= $systemInfo['upload_max_filesize'] ?? 'N/A' ?></div>
                                        <small class="text-muted">上传限制</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small fw-bold text-success"><?= $systemInfo['post_max_size'] ?? 'N/A' ?></div>
                                        <small class="text-muted">POST限制</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 环境检查 -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-shield-check me-2"></i>环境检查
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span>数据目录可写</span>
                                    <?php if ($permissionChecks['data_dir']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> 正常
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle"></i> 错误
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span>日志目录可写</span>
                                    <?php if ($permissionChecks['logs_dir']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> 正常
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle"></i> 错误
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span>数据库文件可写</span>
                                    <?php if ($permissionChecks['db_file']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> 正常
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle"></i> 错误
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span>PHP PDO SQLite</span>
                                    <?php if (extension_loaded('pdo_sqlite')): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> 已安装
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle"></i> 未安装
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span>PHP JSON</span>
                                    <?php if (extension_loaded('json')): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> 已安装
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle"></i> 未安装
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="checkSystemHealth()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>重新检查
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级工具和监控 -->
            <div class="row">
                <!-- 性能监控 -->
                <div class="col-lg-4 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="bi bi-speedometer2 me-2 text-danger"></i>性能监控
                            </h5>
                            <small class="text-muted">实时性能指标</small>
                        </div>
                        <div class="card-body">
                            <div class="row g-2 text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-success" id="cpu-usage">--</div>
                                        <small class="text-muted">CPU使用率</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-warning" id="memory-usage">--</div>
                                        <small class="text-muted">内存使用率</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-info" id="disk-usage">--</div>
                                        <small class="text-muted">磁盘使用率</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-primary" id="response-time">--</div>
                                        <small class="text-muted">响应时间</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-outline-danger btn-sm w-100" onclick="updatePerformanceMetrics()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>刷新监控数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志分析 -->
                <div class="col-lg-4 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up me-2 text-success"></i>日志分析
                            </h5>
                            <small class="text-muted">最近24小时统计</small>
                        </div>
                        <div class="card-body">
                            <div class="row g-2 text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-success"><?= $dbStats['today_logins'] ?? 0 ?></div>
                                        <small class="text-muted">成功登录</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-danger"><?= $dbStats['failed_attempts'] ?? 0 ?></div>
                                        <small class="text-muted">失败尝试</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-info"><?= $dbStats['api_calls'] ?? 0 ?></div>
                                        <small class="text-muted">API调用</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="h6 mb-0 text-warning"><?= $dbStats['errors'] ?? 0 ?></div>
                                        <small class="text-muted">错误数量</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-outline-success btn-sm w-100" onclick="window.open('?page=debug', '_blank')">
                                    <i class="bi bi-eye me-1"></i>查看详细日志
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统工具 -->
                <div class="col-lg-4 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="bi bi-tools me-2 text-secondary"></i>系统工具
                            </h5>
                            <small class="text-muted">管理和诊断工具</small>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="window.open('init_database.php', '_blank')">
                                    <i class="bi bi-database-add me-1"></i>数据库初始化
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="generateSystemReport()">
                                    <i class="bi bi-file-earmark-text me-1"></i>生成系统报告
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="showMaintenanceTools()">
                                    <i class="bi bi-wrench me-1"></i>维护工具
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="window.open('system_status.php', '_blank')">
                                    <i class="bi bi-activity me-1"></i>系统状态页面
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif ($page === 'online'): ?>
            <!-- 在线授权 -->
            <h1 class="h3 mb-4">在线授权</h1>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">当前在线授权 (<?= count($onlineLicenses ?? []) ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($onlineLicenses)): ?>
                        <p class="text-muted text-center">当前没有在线授权</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>授权码</th>
                                        <th>授权类型</th>
                                        <th>设备ID</th>
                                        <th>登录时间</th>
                                        <th>在线时长</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($onlineLicenses as $license): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($license['license_key']) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $license['user_type'] === 'admin' ? 'danger' : ($license['user_type'] === 'pro' ? 'warning' : 'primary') ?>">
                                                    <?= ucfirst($license['user_type']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <code><?= htmlspecialchars(substr($license['device_id'], 0, 16)) ?>...</code>
                                            </td>
                                            <td><?= date('Y-m-d H:i:s', strtotime($license['login_time'])) ?></td>
                                            <td>
                                                <?php
                                                $onlineTime = time() - strtotime($license['login_time']);
                                                $hours = floor($onlineTime / 3600);
                                                $minutes = floor(($onlineTime % 3600) / 60);
                                                echo "{$hours}小时{$minutes}分钟";
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($page === 'announcements'): ?>
            <!-- 公告管理页面 -->
            <div class="page-header animate-fade-in-up">
                <div class="page-title-section">
                    <h1 class="page-title">公告管理 📢</h1>
                    <p class="page-subtitle">管理前端程序显示的系统公告，支持热更新</p>
                </div>
                <div class="page-actions">
                    <?php
                    // 检查公告表是否存在
                    $announcementsTableExists = false;
                    try {
                        $result = $database->fetchValue("SHOW TABLES LIKE 'announcements'");
                        $announcementsTableExists = !empty($result);
                    } catch (Exception $e) {
                        $announcementsTableExists = false;
                    }
                    ?>

                    <?php if ($announcementsTableExists): ?>
                        <?php
                        // 检查images字段是否存在
                        $imagesColumnExists = false;
                        try {
                            $columns = $database->fetchAll("SHOW COLUMNS FROM announcements LIKE 'images'");
                            $imagesColumnExists = !empty($columns);
                        } catch (Exception $e) {
                            $imagesColumnExists = false;
                        }
                        ?>

                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAnnouncementModal">
                                <i class="bi bi-plus-circle me-2"></i>新增公告
                            </button>

                            <?php if (!$imagesColumnExists): ?>
                                <button type="button" class="btn btn-warning" onclick="addImagesColumn()">
                                    <i class="bi bi-database-gear me-2"></i>修复数据库
                                </button>
                            <?php endif; ?>
                        </div>

                        <?php if (!$imagesColumnExists): ?>
                            <div class="alert alert-warning mt-2 mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                数据库缺少图片字段，点击"修复数据库"按钮添加图片支持
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="alert alert-warning d-inline-block mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>数据库未初始化
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (!$announcementsTableExists): ?>
                <!-- 数据库未初始化提示 -->
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>公告数据库未初始化
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <h6>公告数据库表不存在</h6>
                                <p class="text-muted mb-3">
                                    公告管理功能需要专门的数据库表来存储公告信息。
                                    请手动执行SQL脚本创建必要的数据库表。
                                </p>
                                <div class="alert alert-info">
                                    <div class="row">
                                    <div class="col-md-6">
                                        <h6>一键创建（推荐）：</h6>
                                        <form method="POST" class="mb-3">
                                            <input type="hidden" name="action" value="create_announcements_table">
                                            <button type="submit" class="btn btn-warning">
                                                <i class="bi bi-database-add me-2"></i>自动创建数据库表
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>手动创建：</h6>
                                        <p class="small text-muted">
                                            如果自动创建失败，可以手动在数据库中执行SQL语句创建表。
                                            <a href="#" onclick="document.getElementById('sqlCode').style.display='block'">显示SQL代码</a>
                                        </p>
                                    </div>
                                </div>
                                <div id="sqlCode" class="bg-dark text-light p-3 rounded mt-3" style="display: none;">
                                    <code style="color: #fff;">
CREATE TABLE `announcements` (<br>
&nbsp;&nbsp;`id` int(11) NOT NULL AUTO_INCREMENT,<br>
&nbsp;&nbsp;`title` varchar(255) NOT NULL COMMENT '公告标题',<br>
&nbsp;&nbsp;`content` text NOT NULL COMMENT '公告内容',<br>
&nbsp;&nbsp;`type` enum('info','warning','success','error','update') DEFAULT 'info',<br>
&nbsp;&nbsp;`priority` int(11) DEFAULT '0' COMMENT '优先级',<br>
&nbsp;&nbsp;`status` tinyint(1) DEFAULT '1' COMMENT '状态',<br>
&nbsp;&nbsp;`start_time` datetime DEFAULT NULL,<br>
&nbsp;&nbsp;`end_time` datetime DEFAULT NULL,<br>
&nbsp;&nbsp;`target_version` varchar(50) DEFAULT NULL,<br>
&nbsp;&nbsp;`click_count` int(11) DEFAULT '0',<br>
&nbsp;&nbsp;`created_by` int(11) DEFAULT NULL,<br>
&nbsp;&nbsp;`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,<br>
&nbsp;&nbsp;`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,<br>
&nbsp;&nbsp;PRIMARY KEY (`id`),<br>
&nbsp;&nbsp;KEY `idx_status` (`status`),<br>
&nbsp;&nbsp;KEY `idx_priority` (`priority`)<br>
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                                    </code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php

            // 获取公告列表
            $search = $_GET['search'] ?? '';
            $typeFilter = $_GET['type'] ?? '';
            $statusFilter = $_GET['status'] ?? '';
            $pageNum = max(1, (int)($_GET['p'] ?? 1));
            $pageSize = 20;

            $whereConditions = [];
            $params = [];

            if (!empty($search)) {
                $whereConditions[] = "(title LIKE ? OR content LIKE ?)";
                $params[] = "%{$search}%";
                $params[] = "%{$search}%";
            }

            if ($typeFilter !== '') {
                $whereConditions[] = "type = ?";
                $params[] = $typeFilter;
            }

            if ($statusFilter !== '') {
                $whereConditions[] = "status = ?";
                $params[] = (int)$statusFilter;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // 获取总数和公告列表
            try {
                $totalAnnouncements = $database->fetchValue("SELECT COUNT(*) FROM announcements {$whereClause}", $params);
                $totalPages = ceil($totalAnnouncements / $pageSize);

                // 获取公告列表
                $offset = ($pageNum - 1) * $pageSize;
                $announcements = $database->fetchAll("
                    SELECT a.*, ad.username as creator_name
                    FROM announcements a
                    LEFT JOIN admins ad ON a.created_by = ad.id
                    {$whereClause}
                    ORDER BY a.priority DESC, a.created_at DESC
                    LIMIT {$pageSize} OFFSET {$offset}
                ", $params);

            } catch (Exception $e) {
                $errorMessage = "获取公告列表失败：" . $e->getMessage();
                $logger->error("获取公告列表失败", ['error' => $e->getMessage()]);
                $totalAnnouncements = 0;
                $totalPages = 0;
                $announcements = [];
            }
            ?>

            <!-- 成功/错误消息 -->
            <?php
            // 处理URL参数中的成功消息
            $successParam = $_GET['success'] ?? '';
            $successMessages = [
                'add' => '公告添加成功！',
                'update' => '公告更新成功！',
                'delete' => '公告删除成功！',
                'table_created' => '数据库表创建成功！',
                'column_added' => '数据库字段添加成功！现在支持图片功能了。',
                'column_exists' => '数据库字段已存在，无需重复添加。'
            ];
            $displaySuccessMessage = $successMessages[$successParam] ?? (isset($successMessage) ? $successMessage : '');
            ?>

            <?php if ($displaySuccessMessage): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($displaySuccessMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($errorMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($announcementErrorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($announcementErrorMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- 调试信息 (开发时使用) -->
            <?php if (isset($_GET['debug'])): ?>
                <div class="alert alert-info">
                    <h6>调试信息：</h6>
                    <ul class="mb-0">
                        <li>数据库表存在: <?= $announcementsTableExists ? '是' : '否' ?></li>
                        <li>公告总数: <?= $totalAnnouncements ?></li>
                        <li>查询条件: <?= htmlspecialchars($whereClause ?: '无') ?></li>
                        <li>查询参数: <?= htmlspecialchars(json_encode($params)) ?></li>
                        <li>公告数组长度: <?= count($announcements) ?></li>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- 搜索和筛选 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="page" value="announcements">
                        <div class="col-md-4">
                            <label for="search" class="form-label">搜索公告</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?= htmlspecialchars($search) ?>" placeholder="搜索标题或内容...">
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">公告类型</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">全部类型</option>
                                <option value="info" <?= $typeFilter === 'info' ? 'selected' : '' ?>>信息</option>
                                <option value="warning" <?= $typeFilter === 'warning' ? 'selected' : '' ?>>警告</option>
                                <option value="success" <?= $typeFilter === 'success' ? 'selected' : '' ?>>成功</option>
                                <option value="error" <?= $typeFilter === 'error' ? 'selected' : '' ?>>错误</option>
                                <option value="update" <?= $typeFilter === 'update' ? 'selected' : '' ?>>更新</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="1" <?= $statusFilter === '1' ? 'selected' : '' ?>>启用</option>
                                <option value="0" <?= $statusFilter === '0' ? 'selected' : '' ?>>禁用</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 公告列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>公告列表
                        <span class="badge bg-primary ms-2"><?= $totalAnnouncements ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($announcements)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">暂无公告</h5>
                            <p class="text-muted">点击上方"新增公告"按钮创建第一个公告</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>类型</th>
                                        <th>优先级</th>
                                        <th>状态</th>
                                        <th>点击次数</th>
                                        <th>创建者</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($announcements as $announcement): ?>
                                        <tr>
                                            <td><?= $announcement['id'] ?></td>
                                            <td>
                                                <div class="fw-bold"><?= htmlspecialchars($announcement['title']) ?></div>
                                                <small class="text-muted"><?= htmlspecialchars(mb_substr($announcement['content'], 0, 50)) ?>...</small>
                                            </td>
                                            <td>
                                                <?php
                                                $typeColors = [
                                                    'info' => 'primary',
                                                    'warning' => 'warning',
                                                    'success' => 'success',
                                                    'error' => 'danger',
                                                    'update' => 'info'
                                                ];
                                                $typeNames = [
                                                    'info' => '信息',
                                                    'warning' => '警告',
                                                    'success' => '成功',
                                                    'error' => '错误',
                                                    'update' => '更新'
                                                ];
                                                $color = $typeColors[$announcement['type']] ?? 'secondary';
                                                $name = $typeNames[$announcement['type']] ?? $announcement['type'];
                                                ?>
                                                <span class="badge bg-<?= $color ?>"><?= $name ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $announcement['priority'] >= 90 ? 'danger' : ($announcement['priority'] >= 50 ? 'warning' : 'secondary') ?>">
                                                    <?= $announcement['priority'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $announcement['status'] ? 'success' : 'secondary' ?>">
                                                    <?= $announcement['status'] ? '启用' : '禁用' ?>
                                                </span>
                                            </td>
                                            <td><?= $announcement['click_count'] ?></td>
                                            <td><?= htmlspecialchars($announcement['creator_name'] ?? '未知') ?></td>
                                            <td><?= date('Y-m-d H:i', strtotime($announcement['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary"
                                                            onclick="editAnnouncement(<?= htmlspecialchars(json_encode($announcement)) ?>)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger"
                                                            onclick="deleteAnnouncement(<?= $announcement['id'] ?>, '<?= htmlspecialchars($announcement['title']) ?>')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <?php if ($totalPages > 1): ?>
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($pageNum > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=announcements&p=<?= $pageNum - 1 ?>&search=<?= urlencode($search) ?>&type=<?= urlencode($typeFilter) ?>&status=<?= urlencode($statusFilter) ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $pageNum - 2); $i <= min($totalPages, $pageNum + 2); $i++): ?>
                                        <li class="page-item <?= $i === $pageNum ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=announcements&p=<?= $i ?>&search=<?= urlencode($search) ?>&type=<?= urlencode($typeFilter) ?>&status=<?= urlencode($statusFilter) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($pageNum < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=announcements&p=<?= $pageNum + 1 ?>&search=<?= urlencode($search) ?>&type=<?= urlencode($typeFilter) ?>&status=<?= urlencode($statusFilter) ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 新增公告模态框 -->
            <div class="modal fade" id="addAnnouncementModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">新增公告</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="add_announcement">
                            <div class="modal-body">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <label for="title" class="form-label">公告标题 *</label>
                                        <input type="text" class="form-control" id="title" name="title" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="type" class="form-label">公告类型</label>
                                        <select class="form-select" id="type" name="type">
                                            <option value="info">信息</option>
                                            <option value="warning">警告</option>
                                            <option value="success">成功</option>
                                            <option value="error">错误</option>
                                            <option value="update">更新</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label for="content" class="form-label">公告内容 *</label>
                                        <textarea class="form-control" id="content" name="content" rows="4" required></textarea>
                                    </div>
                                    <div class="col-12">
                                        <label for="images" class="form-label">上传图片</label>
                                        <input type="file" class="form-control" id="images" name="images[]" multiple accept="image/*">
                                        <div class="form-text">
                                            <i class="bi bi-info-circle me-1"></i>
                                            支持JPG、PNG、GIF格式，最多上传3张图片，单张图片不超过2MB
                                        </div>
                                        <div id="imagePreview" class="mt-2"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="priority" class="form-label">优先级</label>
                                        <input type="number" class="form-control" id="priority" name="priority" value="0" min="0" max="100">
                                        <small class="form-text text-muted">数字越大优先级越高</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="target_version" class="form-label">目标版本</label>
                                        <input type="text" class="form-control" id="target_version" name="target_version" placeholder="留空表示所有版本">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="start_time" class="form-label">开始时间</label>
                                        <input type="datetime-local" class="form-control" id="start_time" name="start_time">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="end_time" class="form-label">结束时间</label>
                                        <input type="datetime-local" class="form-control" id="end_time" name="end_time">
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="submit" class="btn btn-primary">添加公告</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 编辑公告模态框 -->
            <div class="modal fade" id="editAnnouncementModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑公告</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form method="POST" id="editAnnouncementForm" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="update_announcement">
                            <input type="hidden" name="id" id="edit_id">
                            <div class="modal-body">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <label for="edit_title" class="form-label">公告标题 *</label>
                                        <input type="text" class="form-control" id="edit_title" name="title" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="edit_type" class="form-label">公告类型</label>
                                        <select class="form-select" id="edit_type" name="type">
                                            <option value="info">信息</option>
                                            <option value="warning">警告</option>
                                            <option value="success">成功</option>
                                            <option value="error">错误</option>
                                            <option value="update">更新</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label for="edit_content" class="form-label">公告内容 *</label>
                                        <textarea class="form-control" id="edit_content" name="content" rows="4" required></textarea>
                                    </div>
                                    <div class="col-12">
                                        <label for="edit_images" class="form-label">更新图片</label>
                                        <input type="file" class="form-control" id="edit_images" name="images[]" multiple accept="image/*">
                                        <div class="form-text">
                                            <i class="bi bi-info-circle me-1"></i>
                                            选择新图片将替换现有图片，留空则保持不变
                                        </div>
                                        <div id="editImagePreview" class="mt-2"></div>
                                        <div id="existingImages" class="mt-2"></div>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="edit_priority" class="form-label">优先级</label>
                                        <input type="number" class="form-control" id="edit_priority" name="priority" min="0" max="100">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="edit_status" class="form-label">状态</label>
                                        <select class="form-select" id="edit_status" name="status">
                                            <option value="1">启用</option>
                                            <option value="0">禁用</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="edit_target_version" class="form-label">目标版本</label>
                                        <input type="text" class="form-control" id="edit_target_version" name="target_version">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="edit_start_time" class="form-label">开始时间</label>
                                        <input type="datetime-local" class="form-control" id="edit_start_time" name="start_time">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="edit_end_time" class="form-label">结束时间</label>
                                        <input type="datetime-local" class="form-control" id="edit_end_time" name="end_time">
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="submit" class="btn btn-primary">保存更改</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        <?php elseif ($page === 'forum'): ?>
            <!-- 检查论坛数据库表是否存在 -->
            <?php
            try {
                // 尝试查询论坛表来检查是否存在
                $database->fetchValue("SELECT COUNT(*) FROM forum_posts LIMIT 1");
                $forumTablesExist = true;
            } catch (Exception $e) {
                $forumTablesExist = false;
                $forumError = "论坛数据库表不存在，请先初始化论坛数据库";
            }
            ?>

            <!-- 论坛管理页面 -->
            <?php if (!$forumTablesExist): ?>
            <!-- 数据库表不存在时的提示 -->
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>论坛数据库未初始化
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-4">
                                    <i class="bi bi-database-x" style="font-size: 4rem; color: #ffc107;"></i>
                                </div>
                                <h4 class="mb-3">论坛功能需要初始化数据库</h4>
                                <p class="text-muted mb-4">
                                    检测到论坛相关数据库表尚未创建。请点击下方按钮初始化论坛数据库，
                                    这将创建必要的数据表以支持论坛功能。
                                </p>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="create_forum_tables.php" class="btn btn-warning btn-lg">
                                        <i class="bi bi-database-add me-2"></i>立即初始化数据库
                                    </a>
                                    <a href="?page=dashboard" class="btn btn-outline-secondary btn-lg">
                                        <i class="bi bi-arrow-left me-2"></i>返回仪表板
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- 论坛管理界面 -->
            <div class="container-fluid px-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between align-items-center mb-5">
                    <div>
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="bi bi-chat-square-text text-primary me-2"></i>论坛管理中心
                        </h1>
                        <p class="text-muted mb-0">管理交流论坛的帖子、回复和用户消息</p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="create_forum_tables.php" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-clockwise me-2"></i>重构数据库
                        </a>
                        <button class="btn btn-primary" onclick="refreshForumData()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                        </button>
                    </div>
                </div>

                <!-- 统计数据卡片 -->
                <?php
                try {
                    // 获取论坛统计数据
                    $forumStats = $database->fetchOne("
                        SELECT
                            (SELECT COUNT(*) FROM forum_posts WHERE status = 1) as total_posts,
                            (SELECT COUNT(*) FROM forum_posts WHERE status = 0) as hidden_posts,
                            (SELECT COUNT(*) FROM forum_posts WHERE status = 2) as pinned_posts,
                            (SELECT COUNT(*) FROM forum_replies WHERE status = 1) as total_replies,
                            (SELECT COUNT(*) FROM forum_replies WHERE status = 0) as hidden_replies,
                            (SELECT COUNT(DISTINCT author) FROM forum_posts) as total_users,
                            (SELECT COUNT(*) FROM forum_posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as weekly_posts,
                            (SELECT COUNT(*) FROM forum_posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)) as daily_posts,
                            (SELECT COUNT(*) FROM forum_messages WHERE is_read = 0) as unread_messages,
                            (SELECT COUNT(*) FROM users WHERE status = 0 AND username IN (SELECT DISTINCT author FROM forum_posts)) as banned_users
                    ");

                    // 获取附件统计数据
                    try {
                        $attachmentStats = $database->fetchOne("
                            SELECT
                                COUNT(*) as total_attachments,
                                ROUND(SUM(file_size) / 1024 / 1024, 2) as storage_used_mb,
                                COUNT(CASE WHEN file_type = 'image' THEN 1 END) as image_count,
                                COUNT(CASE WHEN file_type = 'audio' THEN 1 END) as audio_count,
                                COUNT(CASE WHEN file_type = 'document' THEN 1 END) as document_count
                            FROM forum_attachments
                            WHERE status = 1
                        ");

                        if ($attachmentStats) {
                            $forumStats['total_attachments'] = $attachmentStats['total_attachments'];
                            $forumStats['storage_used'] = $attachmentStats['storage_used_mb'] . 'MB';
                            $forumStats['image_count'] = $attachmentStats['image_count'];
                            $forumStats['audio_count'] = $attachmentStats['audio_count'];
                            $forumStats['document_count'] = $attachmentStats['document_count'];
                        } else {
                            $forumStats['total_attachments'] = 0;
                            $forumStats['storage_used'] = '0MB';
                            $forumStats['image_count'] = 0;
                            $forumStats['audio_count'] = 0;
                            $forumStats['document_count'] = 0;
                        }
                    } catch (Exception $e) {
                        // 附件表可能不存在，设置默认值
                        $forumStats['total_attachments'] = 0;
                        $forumStats['storage_used'] = '0MB';
                        $forumStats['image_count'] = 0;
                        $forumStats['audio_count'] = 0;
                        $forumStats['document_count'] = 0;
                    }

                    if (!$forumStats) {
                        $forumStats = [
                            'total_posts' => 0, 'hidden_posts' => 0, 'pinned_posts' => 0,
                            'total_replies' => 0, 'hidden_replies' => 0, 'total_users' => 0,
                            'weekly_posts' => 0, 'daily_posts' => 0, 'unread_messages' => 0, 'banned_users' => 0,
                            'total_attachments' => 0, 'storage_used' => '0MB', 'image_count' => 0,
                            'audio_count' => 0, 'document_count' => 0
                        ];
                    }
                } catch (Exception $e) {
                    $forumStats = [
                        'total_posts' => 0, 'hidden_posts' => 0, 'pinned_posts' => 0,
                        'total_replies' => 0, 'hidden_replies' => 0, 'total_users' => 0,
                        'weekly_posts' => 0, 'daily_posts' => 0, 'unread_messages' => 0, 'banned_users' => 0,
                        'total_attachments' => 0, 'storage_used' => '0MB', 'image_count' => 0,
                        'audio_count' => 0, 'document_count' => 0
                    ];
                }
                ?>

                <!-- 统计数据卡片 - 优化布局 -->
                <div class="row g-4 gy-5 mb-5">
                    <!-- 帖子统计 -->
                    <div class="col-xxl-2 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <div class="card stats-card border-0 shadow-sm h-100" style="border-left: 4px solid #4e73df !important;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="text-xs fw-bold text-primary text-uppercase mb-2">
                                            总帖子数
                                        </div>
                                        <div class="h5 mb-2 fw-bold text-gray-800">
                                            <?= $forumStats['total_posts'] ?>
                                        </div>
                                        <div class="text-xs text-muted">
                                            隐藏: <?= $forumStats['hidden_posts'] ?> | 置顶: <?= $forumStats['pinned_posts'] ?>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <i class="bi bi-chat-square-text text-gray-300" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 回复统计 -->
                    <div class="col-xxl-2 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <div class="card stats-card border-0 shadow-sm h-100" style="border-left: 4px solid #1cc88a !important;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="text-xs fw-bold text-success text-uppercase mb-2">
                                            总回复数
                                        </div>
                                        <div class="h5 mb-2 fw-bold text-gray-800">
                                            <?= $forumStats['total_replies'] ?>
                                        </div>
                                        <div class="text-xs text-muted">
                                            隐藏: <?= $forumStats['hidden_replies'] ?>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <i class="bi bi-chat-dots text-gray-300" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户统计 -->
                    <div class="col-xxl-2 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <div class="card stats-card border-0 shadow-sm h-100" style="border-left: 4px solid #36b9cc !important;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="text-xs fw-bold text-info text-uppercase mb-2">
                                            活跃用户
                                        </div>
                                        <div class="h5 mb-2 fw-bold text-gray-800">
                                            <?= $forumStats['total_users'] ?>
                                        </div>
                                        <div class="text-xs text-muted">
                                            禁用: <?= $forumStats['banned_users'] ?>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <i class="bi bi-people text-gray-300" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 活动统计 -->
                    <div class="col-xxl-2 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <div class="card stats-card border-0 shadow-sm h-100" style="border-left: 4px solid #f6c23e !important;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="text-xs fw-bold text-warning text-uppercase mb-2">
                                            近期活动
                                        </div>
                                        <div class="h5 mb-2 fw-bold text-gray-800">
                                            <?= $forumStats['daily_posts'] ?>
                                        </div>
                                        <div class="text-xs text-muted">
                                            今日新帖 | 本周: <?= $forumStats['weekly_posts'] ?>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <i class="bi bi-graph-up text-gray-300" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 附件统计 -->
                    <div class="col-xxl-2 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <div class="card stats-card border-0 shadow-sm h-100" style="border-left: 4px solid #6c757d !important;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="text-xs fw-bold text-secondary text-uppercase mb-2">
                                            附件数量
                                        </div>
                                        <div class="h5 mb-2 fw-bold text-gray-800">
                                            <?= $forumStats['total_attachments'] ?>
                                        </div>
                                        <div class="text-xs text-muted">
                                            图片: <?= $forumStats['image_count'] ?> | 音频: <?= $forumStats['audio_count'] ?>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <i class="bi bi-paperclip text-gray-300" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 存储统计 -->
                    <div class="col-xxl-2 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <div class="card stats-card border-0 shadow-sm h-100" style="border-left: 4px solid #5a5c69 !important;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="text-xs fw-bold text-dark text-uppercase mb-2">
                                            存储使用
                                        </div>
                                        <div class="h5 mb-2 fw-bold text-gray-800">
                                            <?= $forumStats['storage_used'] ?>
                                        </div>
                                        <div class="text-xs text-muted">
                                            文档: <?= $forumStats['document_count'] ?>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <i class="bi bi-hdd text-gray-300" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成功/错误消息显示 -->
                <?php if (isset($_GET['success'])): ?>
                    <?php
                    $successMessages = [
                        'post_deleted' => '帖子删除成功！',
                        'posts_deleted' => '批量删除 ' . ($_GET['count'] ?? '0') . ' 个帖子成功！',
                        'reply_deleted' => '回复删除成功！',
                        'replies_deleted' => '批量删除 ' . ($_GET['count'] ?? '0') . ' 个回复成功！',
                        'message_deleted' => '消息删除成功！',
                        'messages_deleted' => '批量删除 ' . ($_GET['count'] ?? '0') . ' 条消息成功！',
                        'user_banned' => '用户 "' . ($_GET['username'] ?? '') . '" 已被禁用发言权限！',
                        'user_unbanned' => '用户 "' . ($_GET['username'] ?? '') . '" 发言权限已恢复！',
                        'post_status_updated' => '帖子状态更新成功！'
                    ];
                    $message = $successMessages[$_GET['success']] ?? '操作成功！';
                    ?>
                    <div class="alert alert-success alert-dismissible fade show shadow-sm">
                        <i class="bi bi-check-circle me-2"></i>
                        <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($forumErrorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show shadow-sm">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($forumErrorMessage) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- 管理功能导航 -->
                <div class="row mb-4" style="margin-top: 2rem;">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">论坛内容管理</h6>
                                <div class="dropdown no-arrow">
                                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical text-gray-400"></i>
                                    </a>
                                    <div class="dropdown-menu dropdown-menu-right shadow">
                                        <a class="dropdown-item" href="#" onclick="exportForumData()">
                                            <i class="bi bi-download me-2"></i>导出数据
                                        </a>
                                        <a class="dropdown-item" href="#" onclick="clearForumCache()">
                                            <i class="bi bi-arrow-clockwise me-2"></i>清除缓存
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 简化的管理界面标题 -->
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <div>
                                        <h5 class="mb-1">
                                            <i class="bi bi-chat-square-text me-2 text-primary"></i>论坛内容管理
                                        </h5>
                                        <p class="text-muted mb-0">管理所有论坛帖子和用户内容</p>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="forum_attachments_admin.php" class="btn btn-outline-warning btn-sm">
                                            <i class="bi bi-paperclip me-1"></i>附件管理
                                        </a>
                                        <a href="upgrade_forum_attachments.php" class="btn btn-outline-success btn-sm">
                                            <i class="bi bi-cloud-upload me-1"></i>附件升级
                                        </a>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="exportForumData()">
                                            <i class="bi bi-download me-1"></i>导出数据
                                        </button>
                                    </div>
                                </div>

                                <!-- 帖子管理内容 -->
                                <div class="forum-content-area">
                                        <?php
                                        try {
                                            $posts = $database->fetchAll("
                                                SELECT p.*,
                                                       (SELECT COUNT(*) FROM forum_replies WHERE post_id = p.id AND status = 1) as reply_count,
                                                       (SELECT COUNT(*) FROM forum_likes WHERE target_type = 'post' AND target_id = p.id) as like_count,
                                                       COALESCE(p.attachments_count, 0) as attachment_count
                                                FROM forum_posts p
                                                ORDER BY p.created_at DESC
                                                LIMIT 50
                                            ");
                                        } catch (Exception $e) {
                                            $posts = [];
                                        }
                                        ?>

                                        <!-- 操作工具栏 -->
                                        <div class="d-flex justify-content-between align-items-center mb-4">
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-0 me-3">帖子管理</h5>
                                                <span class="badge bg-primary"><?= count($posts) ?> 条记录</span>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <!-- 搜索框 -->
                                                <div class="input-group" style="width: 280px;">
                                                    <input type="text" class="form-control" placeholder="搜索帖子标题或内容..." id="postSearchInput" onkeypress="handleSearchKeyPress(event)">
                                                    <button class="btn btn-primary" type="button" onclick="searchPosts()">
                                                        <i class="bi bi-search"></i>
                                                    </button>
                                                </div>
                                                <!-- 筛选下拉 -->
                                                <select class="form-select" style="width: 150px;" onchange="filterPosts(this.value)" id="statusFilter">
                                                    <option value="">全部状态</option>
                                                    <option value="1">✅ 正常</option>
                                                    <option value="0">🚫 隐藏</option>
                                                    <option value="2">📌 置顶</option>
                                                </select>
                                                <!-- 批量操作 -->
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-success btn-sm" onclick="batchToggleStatus(2)" title="批量置顶" id="batchPinBtn">
                                                        <i class="bi bi-pin-angle me-1"></i>置顶
                                                    </button>
                                                    <button class="btn btn-warning btn-sm" onclick="batchToggleStatus(0)" title="批量隐藏" id="batchHideBtn">
                                                        <i class="bi bi-eye-slash me-1"></i>隐藏
                                                    </button>
                                                    <button class="btn btn-danger btn-sm" onclick="deleteSelectedPosts()" title="批量删除" id="batchDeleteBtn">
                                                        <i class="bi bi-trash me-1"></i>删除
                                                    </button>
                                                </div>
                                                <!-- 清除筛选按钮 -->
                                                <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()" title="清除筛选">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <?php if (empty($posts)): ?>
                                        <!-- 空状态 -->
                                        <div class="text-center py-5">
                                            <div class="mb-4">
                                                <i class="bi bi-chat-square-text" style="font-size: 4rem; color: #e3e6f0;"></i>
                                            </div>
                                            <h5 class="text-gray-500">暂无帖子数据</h5>
                                            <p class="text-muted mb-4">用户发布的帖子将在这里显示</p>
                                            <button class="btn btn-primary" onclick="refreshForumData()">
                                                <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                                            </button>
                                        </div>
                                        <?php else: ?>
                                        <!-- 帖子列表 -->
                                        <div class="table-responsive">
                                            <table class="table table-hover align-middle">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th width="50" class="text-center">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="selectAllPosts" onchange="toggleAllPosts(this)">
                                                            </div>
                                                        </th>
                                                        <th width="35%">帖子信息</th>
                                                        <th width="15%">作者</th>
                                                        <th width="10%" class="text-center">类型</th>
                                                        <th width="8%" class="text-center">状态</th>
                                                        <th width="12%" class="text-center">互动数据</th>
                                                        <th width="12%" class="text-center">发布时间</th>
                                                        <th width="8%" class="text-center">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($posts as $post): ?>
                                                    <tr class="<?= $post['status'] == 0 ? 'table-secondary' : ($post['status'] == 2 ? 'table-warning' : '') ?>">
                                                        <td class="text-center">
                                                            <div class="form-check d-flex justify-content-center">
                                                                <input class="form-check-input" type="checkbox" name="post_ids[]" value="<?= $post['id'] ?>">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex align-items-start">
                                                                <div class="flex-grow-1">
                                                                    <h6 class="mb-1 fw-bold text-truncate" style="max-width: 300px;" title="<?= htmlspecialchars($post['title']) ?>">
                                                                        <?= htmlspecialchars($post['title']) ?>
                                                                    </h6>
                                                                    <p class="mb-1 text-muted small text-truncate" style="max-width: 300px;">
                                                                        <?= htmlspecialchars(mb_substr($post['content'], 0, 60)) ?>...
                                                                    </p>
                                                                    <small class="text-muted">ID: <?= $post['id'] ?></small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; min-width: 32px;">
                                                                    <i class="bi bi-person text-white small"></i>
                                                                </div>
                                                                <div class="flex-grow-1 min-width-0">
                                                                    <div class="fw-bold text-truncate" style="max-width: 100px;" title="<?= htmlspecialchars($post['author']) ?>">
                                                                        <?= htmlspecialchars($post['author']) ?>
                                                                    </div>
                                                                    <?php if ($post['author_qq']): ?>
                                                                    <small class="text-muted text-truncate d-block" style="max-width: 100px;">
                                                                        QQ: <?= htmlspecialchars($post['author_qq']) ?>
                                                                    </small>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <?php
                                                            $typeLabels = [
                                                                'discussion' => ['讨论', 'primary', 'bi-chat-dots'],
                                                                'question' => ['提问', 'warning', 'bi-question-circle'],
                                                                'announcement' => ['公告', 'success', 'bi-megaphone'],
                                                                'feedback' => ['反馈', 'info', 'bi-lightbulb']
                                                            ];
                                                            $typeInfo = $typeLabels[$post['post_type']] ?? ['其他', 'secondary', 'bi-file-text'];
                                                            ?>
                                                            <span class="badge bg-<?= $typeInfo[1] ?> d-inline-flex align-items-center">
                                                                <i class="<?= $typeInfo[2] ?> me-1"></i><?= $typeInfo[0] ?>
                                                            </span>
                                                        </td>
                                                        <td class="text-center">
                                                            <?php if ($post['status'] == 2): ?>
                                                                <span class="badge bg-danger d-inline-flex align-items-center">
                                                                    <i class="bi bi-pin-angle me-1"></i>置顶
                                                                </span>
                                                            <?php elseif ($post['status'] == 1): ?>
                                                                <span class="badge bg-success d-inline-flex align-items-center">
                                                                    <i class="bi bi-check-circle me-1"></i>正常
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary d-inline-flex align-items-center">
                                                                    <i class="bi bi-eye-slash me-1"></i>隐藏
                                                                </span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="d-flex flex-column align-items-center gap-1">
                                                                <small class="text-muted d-flex align-items-center">
                                                                    <i class="bi bi-heart text-danger me-1"></i><?= $post['like_count'] ?>
                                                                </small>
                                                                <small class="text-muted d-flex align-items-center">
                                                                    <i class="bi bi-chat text-primary me-1"></i><?= $post['reply_count'] ?>
                                                                </small>
                                                                <small class="text-muted d-flex align-items-center">
                                                                    <i class="bi bi-eye text-info me-1"></i><?= $post['views_count'] ?>
                                                                </small>
                                                                <?php if (($post['attachment_count'] ?? 0) > 0): ?>
                                                                <small class="text-muted d-flex align-items-center">
                                                                    <i class="bi bi-paperclip text-secondary me-1"></i><?= $post['attachment_count'] ?>
                                                                </small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <small class="text-muted">
                                                                <div><?= date('Y-m-d', strtotime($post['created_at'])) ?></div>
                                                                <div><?= date('H:i:s', strtotime($post['created_at'])) ?></div>
                                                            </small>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="dropdown">
                                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                    <i class="bi bi-three-dots"></i>
                                                                </button>
                                                                <ul class="dropdown-menu">
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="viewPostDetail(<?= $post['id'] ?>)">
                                                                            <i class="bi bi-eye me-2"></i>查看详情
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="editPost(<?= $post['id'] ?>)">
                                                                            <i class="bi bi-pencil me-2"></i>编辑帖子
                                                                        </a>
                                                                    </li>
                                                                    <?php if (($post['attachment_count'] ?? 0) > 0): ?>
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="manageAttachments(<?= $post['id'] ?>, 'post')">
                                                                            <i class="bi bi-paperclip me-2"></i>管理附件 (<?= $post['attachment_count'] ?>)
                                                                        </a>
                                                                    </li>
                                                                    <?php endif; ?>
                                                                    <li><hr class="dropdown-divider"></li>
                                                                    <?php if ($post['status'] == 2): ?>
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="togglePostStatus(<?= $post['id'] ?>, 1)">
                                                                            <i class="bi bi-pin me-2"></i>取消置顶
                                                                        </a>
                                                                    </li>
                                                                    <?php else: ?>
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="togglePostStatus(<?= $post['id'] ?>, 2)">
                                                                            <i class="bi bi-pin-angle me-2"></i>设为置顶
                                                                        </a>
                                                                    </li>
                                                                    <?php endif; ?>

                                                                    <?php if ($post['status'] == 0): ?>
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="togglePostStatus(<?= $post['id'] ?>, 1)">
                                                                            <i class="bi bi-eye me-2"></i>显示帖子
                                                                        </a>
                                                                    </li>
                                                                    <?php else: ?>
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="togglePostStatus(<?= $post['id'] ?>, 0)">
                                                                            <i class="bi bi-eye-slash me-2"></i>隐藏帖子
                                                                        </a>
                                                                    </li>
                                                                    <?php endif; ?>

                                                                    <li><hr class="dropdown-divider"></li>
                                                                    <li>
                                                                        <a class="dropdown-item" href="#" onclick="manageUser('<?= htmlspecialchars($post['author']) ?>')">
                                                                            <i class="bi bi-person-gear me-2"></i>管理用户
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <a class="dropdown-item text-danger" href="#" onclick="deletePost(<?= $post['id'] ?>)">
                                                                            <i class="bi bi-trash me-2"></i>删除帖子
                                                                        </a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                // 附件管理功能
                function manageAttachments(id, type) {
                    const modal = document.createElement('div');
                    modal.className = 'modal fade';
                    modal.innerHTML = `
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="bi bi-paperclip me-2"></i>附件管理
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="text-center py-3">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载附件列表...</p>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(modal);
                    const modalInstance = new bootstrap.Modal(modal);
                    modalInstance.show();

                    // 加载附件列表
                    fetch(`forum_upload_api.php?action=get_attachments&${type}_id=${id}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const attachments = data.data;
                                let attachmentHtml = '';

                                if (attachments.length === 0) {
                                    attachmentHtml = '<div class="text-center py-3"><p class="text-muted">暂无附件</p></div>';
                                } else {
                                    attachmentHtml = '<div class="table-responsive"><table class="table table-hover"><thead><tr><th>文件名</th><th>类型</th><th>大小</th><th>上传时间</th><th>下载次数</th><th>操作</th></tr></thead><tbody>';

                                    attachments.forEach(attachment => {
                                        const fileSize = (attachment.file_size / 1024).toFixed(2) + ' KB';
                                        const fileTypeIcon = getFileTypeIcon(attachment.file_type);

                                        attachmentHtml += `
                                            <tr>
                                                <td>
                                                    <i class="${fileTypeIcon} me-2"></i>
                                                    ${attachment.filename}
                                                </td>
                                                <td><span class="badge bg-secondary">${attachment.file_type}</span></td>
                                                <td>${fileSize}</td>
                                                <td>${attachment.upload_time}</td>
                                                <td>${attachment.download_count}</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="downloadAttachment(${attachment.id})">
                                                        <i class="bi bi-download"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        `;
                                    });

                                    attachmentHtml += '</tbody></table></div>';
                                }

                                modal.querySelector('.modal-body').innerHTML = attachmentHtml;
                            } else {
                                modal.querySelector('.modal-body').innerHTML = '<div class="alert alert-danger">加载附件失败: ' + data.message + '</div>';
                            }
                        })
                        .catch(error => {
                            modal.querySelector('.modal-body').innerHTML = '<div class="alert alert-danger">网络错误，请稍后重试</div>';
                        });
                }

                function getFileTypeIcon(fileType) {
                    switch (fileType) {
                        case 'image': return 'bi bi-image text-primary';
                        case 'audio': return 'bi bi-music-note text-success';
                        case 'document': return 'bi bi-file-text text-warning';
                        default: return 'bi bi-file text-secondary';
                    }
                }

                function downloadAttachment(attachmentId) {
                    window.open(`forum_upload_api.php?action=download&id=${attachmentId}`, '_blank');
                }
                </script>

            <?php endif; ?>

        <?php elseif ($page === 'qq_config'): ?>
            <!-- QQ群配置页面 -->
            <div class="page-header animate-fade-in-up">
                <div class="page-title-section">
                    <h1 class="page-title">QQ群配置 💬</h1>
                    <p class="page-subtitle">管理前端程序显示的QQ群信息，支持热更新</p>
                </div>
                <div class="page-actions">
                    <a href="api.php?action=get_qq_config" class="action-btn secondary" target="_blank">
                        <i class="bi bi-cloud-check"></i>
                        <span>测试API</span>
                    </a>

                </div>
            </div>

            <?php if (isset($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($successMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($errorMessage) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php
            // 获取当前QQ配置
            $qqConfigs = [];
            $qqConfigExists = false;
            try {
                $configs = $database->fetchAll("SELECT config_key, config_value, config_desc, updated_at FROM qq_config ORDER BY config_key");
                foreach ($configs as $config) {
                    $qqConfigs[$config['config_key']] = $config;
                }
                $qqConfigExists = !empty($qqConfigs);
            } catch (Exception $e) {
                // QQ配置表可能不存在
                $qqConfigExists = false;
            }
            ?>

            <?php if (!$qqConfigExists): ?>
                <!-- QQ配置未初始化 -->
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>QQ配置未初始化
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>需要先初始化QQ配置数据库</h6>
                                <p class="text-muted mb-0">
                                    QQ配置功能需要专门的数据库表来存储配置信息。
                                    请点击右侧按钮进行初始化，这将创建必要的数据库表并插入默认配置。
                                </p>
                            </div>

                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- QQ配置管理界面 -->
                <div class="row">
                    <!-- 配置表单 -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-gear me-2"></i>QQ群配置设置
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_qq_config">

                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">QQ群号 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control"
                                                   name="qq_group_number"
                                                   value="<?= htmlspecialchars($qqConfigs['qq_group_number']['config_value'] ?? '') ?>"
                                                   placeholder="735821698"
                                                   pattern="[0-9]{5,11}"
                                                   required>
                                            <div class="form-text">5-11位数字的QQ群号</div>
                                        </div>

                                        <div class="col-md-6">
                                            <label class="form-label">启用状态</label>
                                            <select class="form-select" name="qq_group_enabled">
                                                <option value="1" <?= ($qqConfigs['qq_group_enabled']['config_value'] ?? '1') === '1' ? 'selected' : '' ?>>启用</option>
                                                <option value="0" <?= ($qqConfigs['qq_group_enabled']['config_value'] ?? '1') === '0' ? 'selected' : '' ?>>禁用</option>
                                            </select>
                                            <div class="form-text">禁用后前端不显示QQ群信息</div>
                                        </div>

                                        <div class="col-12">
                                            <label class="form-label">QQ群名称</label>
                                            <input type="text" class="form-control"
                                                   name="qq_group_name"
                                                   value="<?= htmlspecialchars($qqConfigs['qq_group_name']['config_value'] ?? '') ?>"
                                                   placeholder="Augment续杯工具交流群">
                                            <div class="form-text">显示在前端的群名称</div>
                                        </div>

                                        <div class="col-12">
                                            <label class="form-label">QQ群描述</label>
                                            <textarea class="form-control"
                                                      name="qq_group_desc"
                                                      rows="3"
                                                      placeholder="欢迎加入QQ群获取技术支持和最新更新！"><?= htmlspecialchars($qqConfigs['qq_group_desc']['config_value'] ?? '') ?></textarea>
                                            <div class="form-text">显示在前端的群描述信息</div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-save me-2"></i>保存配置
                                        </button>
                                        <a href="api.php?action=get_qq_config" class="btn btn-outline-info" target="_blank">
                                            <i class="bi bi-cloud-check me-2"></i>测试API接口
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 配置预览和状态 -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-eye me-2"></i>配置预览
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="border rounded p-3 bg-light">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="bi bi-chat-dots text-primary me-2" style="font-size: 1.5rem;"></i>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($qqConfigs['qq_group_name']['config_value'] ?? 'QQ交流群') ?></h6>
                                            <code class="small"><?= htmlspecialchars($qqConfigs['qq_group_number']['config_value'] ?? '735821698') ?></code>
                                        </div>
                                        <span class="badge bg-<?= ($qqConfigs['qq_group_enabled']['config_value'] ?? '1') === '1' ? 'success' : 'secondary' ?> ms-auto">
                                            <?= ($qqConfigs['qq_group_enabled']['config_value'] ?? '1') === '1' ? '启用' : '禁用' ?>
                                        </span>
                                    </div>
                                    <p class="text-muted small mb-0">
                                        <?= htmlspecialchars($qqConfigs['qq_group_desc']['config_value'] ?? '欢迎加入QQ群获取技术支持和最新更新！') ?>
                                    </p>
                                </div>

                                <div class="mt-3">
                                    <h6>配置状态</h6>
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                            <span>QQ群号</span>
                                            <span class="badge bg-<?= !empty($qqConfigs['qq_group_number']['config_value']) ? 'success' : 'warning' ?>">
                                                <?= !empty($qqConfigs['qq_group_number']['config_value']) ? '已设置' : '未设置' ?>
                                            </span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                            <span>群名称</span>
                                            <span class="badge bg-<?= !empty($qqConfigs['qq_group_name']['config_value']) ? 'success' : 'warning' ?>">
                                                <?= !empty($qqConfigs['qq_group_name']['config_value']) ? '已设置' : '未设置' ?>
                                            </span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                            <span>群描述</span>
                                            <span class="badge bg-<?= !empty($qqConfigs['qq_group_desc']['config_value']) ? 'success' : 'warning' ?>">
                                                <?= !empty($qqConfigs['qq_group_desc']['config_value']) ? '已设置' : '未设置' ?>
                                            </span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                            <span>API接口</span>
                                            <span class="badge bg-success">正常</span>
                                        </div>
                                    </div>
                                </div>

                                <?php if (!empty($qqConfigs['qq_group_number']['updated_at'])): ?>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <i class="bi bi-clock me-1"></i>
                                            最后更新: <?= date('Y-m-d H:i:s', strtotime($qqConfigs['qq_group_number']['updated_at'])) ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-info-circle me-2"></i>使用说明
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="small">
                                    <h6>功能说明</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check text-success me-2"></i>前端程序会自动获取最新配置</li>
                                        <li><i class="bi bi-check text-success me-2"></i>支持热更新，无需重启程序</li>
                                        <li><i class="bi bi-check text-success me-2"></i>登录页面和主页都会显示</li>
                                        <li><i class="bi bi-check text-success me-2"></i>可以随时启用/禁用显示</li>
                                    </ul>

                                    <h6 class="mt-3">API接口</h6>
                                    <p class="mb-1"><code>GET /api.php?action=get_qq_config</code></p>
                                    <p class="text-muted">前端程序通过此接口获取最新配置</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

        <?php endif; ?>
    </main>

    <!-- 添加授权模态框 -->
    <div class="modal fade" id="addLicenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-lg me-2"></i>添加新授权
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="add">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">授权类型</label>
                            <select class="form-select" name="license_type" required>
                                <option value="basic">Basic (1设备)</option>
                                <option value="pro">Pro (3设备)</option>
                                <option value="enterprise">Enterprise (10设备)</option>
                                <option value="admin">Admin (999设备)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">有效期</label>
                            <select class="form-select" name="duration" required>
                                <option value="1">1个月</option>
                                <option value="3">3个月</option>
                                <option value="6">6个月</option>
                                <option value="12" selected>1年</option>
                                <option value="24">2年</option>
                                <option value="0">永久</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">QQ号设置</label>
                            <select class="form-select" name="qq_status">
                                <option value="pending" selected>待用户填写（用户登录时必须填写）</option>
                                <option value="manual">手动指定QQ号</option>
                            </select>
                            <input type="text" class="form-control mt-2" name="qq_number" placeholder="手动指定QQ号..." pattern="[0-9]{5,11}" style="display: none;" id="manualQQInput">
                            <div class="form-text">选择"待用户填写"时，用户首次登录必须填写QQ号</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码设置</label>
                            <select class="form-select" name="password_type" id="passwordType">
                                <option value="auto" selected>自动生成密码（推荐）</option>
                                <option value="custom">自定义密码</option>
                            </select>
                            <div id="customPasswordField" style="display: none;" class="mt-2">
                                <input type="text" class="form-control" name="custom_password" placeholder="输入自定义密码（6-20位）"
                                       minlength="6" maxlength="20" id="customPasswordInput">
                                <div class="form-text">
                                    <small class="text-muted">密码长度6-20位，建议包含字母和数字</small>
                                </div>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="generateRandomPassword()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>生成随机密码
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="togglePasswordVisibility()">
                                        <i class="bi bi-eye me-1" id="passwordToggleIcon"></i>显示密码
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注信息</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="可选的备注信息..."></textarea>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>授权码：</strong>将自动生成，格式为：类型-年份-8位随机码<br>
                            <strong>密码：</strong>创建成功后将显示明文密码，请及时记录并提供给用户
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-lg me-1"></i>创建授权
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑授权模态框 -->
    <div class="modal fade" id="editLicenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil me-2"></i>编辑授权
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editLicenseForm">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="license_id" id="editLicenseId">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">授权码</label>
                            <input type="text" class="form-control" id="editLicenseKey" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">授权类型</label>
                            <select class="form-select" name="license_type" id="editLicenseType" required>
                                <option value="basic">Basic</option>
                                <option value="pro">Pro</option>
                                <option value="enterprise">Enterprise</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">最大设备数</label>
                            <input type="number" class="form-control" name="max_devices" id="editMaxDevices" min="1" max="999" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">延期时间</label>
                            <select class="form-select" name="duration">
                                <option value="0">不修改</option>
                                <option value="1">延期1个月</option>
                                <option value="3">延期3个月</option>
                                <option value="6">延期6个月</option>
                                <option value="12">延期1年</option>
                                <option value="-1">设为永久</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">状态</label>
                            <select class="form-select" name="status" id="editStatus" required>
                                <option value="1">正常</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">QQ号</label>
                            <input type="text" class="form-control" name="qq_number" id="editQQNumber" placeholder="用户的QQ号..." pattern="[0-9]{5,11}">
                            <div class="form-text">用户的QQ号，方便后续联系</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注信息</label>
                            <textarea class="form-control" name="notes" id="editNotes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-1"></i>保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 设备详情模态框 -->
    <div class="modal fade" id="deviceDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-laptop me-2"></i>设备详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="deviceDetailsContent">
                    <div class="text-center py-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备重命名模态框 -->
    <div class="modal fade" id="renameDeviceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil me-2"></i>重命名设备
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="renameDeviceForm">
                    <input type="hidden" name="action" value="rename">
                    <input type="hidden" name="device_id" id="renameDeviceId">
                    <input type="hidden" name="user_id" id="renameUserId">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">设备名称</label>
                            <input type="text" class="form-control" name="device_name" id="renameDeviceName"
                                   placeholder="输入新的设备名称..." required maxlength="50">
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            设备名称仅用于显示，不影响设备的唯一标识。
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 设备测试模态框 -->
    <div class="modal fade" id="deviceTestModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-search me-2"></i>测试设备查询
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="deviceTestForm">
                    <input type="hidden" name="action" value="test_device_query">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">设备ID</label>
                            <input type="text" class="form-control" name="device_id" id="testDeviceId"
                                   placeholder="输入设备ID..." required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">用户ID</label>
                            <input type="number" class="form-control" name="user_id" id="testUserId"
                                   placeholder="输入用户ID..." required min="1">
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            此操作将测试设备查询并在调试日志中记录详细信息。
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-play me-1"></i>执行测试
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 增强的Bootstrap Modal功能
        class Modal {
            constructor(element) {
                this.element = typeof element === 'string' ? document.querySelector(element) : element;
                this.isShown = false;
                this.backdrop = null;
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 关闭按钮事件
                const closeButtons = this.element.querySelectorAll('[data-bs-dismiss="modal"], .btn-close');
                closeButtons.forEach(btn => {
                    btn.addEventListener('click', () => this.hide());
                });

                // 背景点击关闭
                this.element.addEventListener('click', (e) => {
                    if (e.target === this.element) {
                        this.hide();
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.isShown) {
                        this.hide();
                    }
                });
            }

            show() {
                if (this.isShown) return;

                this.isShown = true;
                document.body.style.overflow = 'hidden'; // 防止背景滚动

                // 显示模态框
                this.element.style.display = 'flex';
                this.element.style.position = 'fixed';
                this.element.style.top = '0';
                this.element.style.left = '0';
                this.element.style.width = '100%';
                this.element.style.height = '100%';
                this.element.style.backgroundColor = 'rgba(0,0,0,0.5)';
                this.element.style.zIndex = '1055';
                this.element.style.alignItems = 'center';
                this.element.style.justifyContent = 'center';
                this.element.style.padding = '1rem';

                // 设置对话框样式
                const dialog = this.element.querySelector('.modal-dialog');
                if (dialog) {
                    dialog.style.position = 'relative';
                    dialog.style.maxWidth = dialog.classList.contains('modal-lg') ? '800px' : '500px';
                    dialog.style.width = '100%';
                    dialog.style.margin = '0';
                    dialog.style.transform = 'none';
                }

                // 添加动画效果
                this.element.style.opacity = '0';
                setTimeout(() => {
                    this.element.style.transition = 'opacity 0.15s ease-in-out';
                    this.element.style.opacity = '1';
                }, 10);

                // 聚焦到模态框
                this.element.focus();
            }

            hide() {
                if (!this.isShown) return;

                this.isShown = false;
                document.body.style.overflow = ''; // 恢复背景滚动

                // 添加淡出动画
                this.element.style.opacity = '0';
                setTimeout(() => {
                    this.element.style.display = 'none';
                    this.element.style.transition = '';
                }, 150);
            }

            toggle() {
                this.isShown ? this.hide() : this.show();
            }
        }

        window.bootstrap = { Modal: Modal };

        // 自动初始化模态框触发器
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('[data-bs-toggle="modal"]').forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = this.getAttribute('data-bs-target');
                    if (target) {
                        const modal = new Modal(target);
                        modal.show();
                    }
                });
            });

            // 特别处理公告相关的模态框
            const addAnnouncementBtn = document.querySelector('[data-bs-target="#addAnnouncementModal"]');
            if (addAnnouncementBtn) {
                addAnnouncementBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const modal = document.getElementById('addAnnouncementModal');
                    if (modal) {
                        new Modal(modal).show();
                    }
                });
            }
        });

        // 简化的Chart.js
        window.Chart = function(ctx, config) {
            const canvas = ctx.canvas || ctx;
            const container = canvas.parentNode;
            const placeholder = document.createElement('div');
            placeholder.style.cssText = 'width:100%;height:200px;background:#f8f9fa;border:1px solid #dee2e6;border-radius:0.25rem;display:flex;align-items:center;justify-content:center;color:#6c757d;';
            placeholder.textContent = '图表占位符 - ' + (config.type || 'chart');
            container.replaceChild(placeholder, canvas);
            return { update: function() {}, destroy: function() {} };
        };
    </script>

    <!-- 图表初始化 -->
    <script>
        <?php if ($page === 'dashboard'): ?>
        // 准备图表数据
        const trendData = <?= json_encode($trendData) ?>;
        const licenseTypeStats = <?= json_encode($licenseTypeStats) ?>;
        const deviceUsageStats = <?= json_encode($deviceUsageStats) ?>;

        // 初始化趋势图表
        const trendCtx = document.getElementById('trendChart');
        if (trendCtx) {
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: trendData.map(item => {
                        const date = new Date(item.date);
                        return (date.getMonth() + 1) + '/' + date.getDate();
                    }),
                    datasets: [{
                        label: '成功验证',
                        data: trendData.map(item => item.success),
                        borderColor: '#198754',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4
                    }, {
                        label: '失败验证',
                        data: trendData.map(item => item.failed),
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 初始化授权类型饼图
        const typeCtx = document.getElementById('typeChart');
        if (typeCtx) {
            const typeColors = {
                'basic': '#6f42c1',
                'pro': '#fd7e14',
                'enterprise': '#20c997',
                'admin': '#dc3545'
            };

            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: licenseTypeStats.map(item => item.user_type.toUpperCase()),
                    datasets: [{
                        data: licenseTypeStats.map(item => item.count),
                        backgroundColor: licenseTypeStats.map(item => typeColors[item.user_type] || '#6c757d'),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }

        // 初始化设备使用柱状图
        const deviceCtx = document.getElementById('deviceChart');
        if (deviceCtx) {
            new Chart(deviceCtx, {
                type: 'bar',
                data: {
                    labels: deviceUsageStats.map(item => item.user_type.toUpperCase()),
                    datasets: [{
                        label: '设备数量',
                        data: deviceUsageStats.map(item => item.device_count),
                        backgroundColor: [
                            'rgba(111, 66, 193, 0.8)',
                            'rgba(253, 126, 20, 0.8)',
                            'rgba(32, 201, 151, 0.8)',
                            'rgba(220, 53, 69, 0.8)'
                        ],
                        borderColor: [
                            '#6f42c1',
                            '#fd7e14',
                            '#20c997',
                            '#dc3545'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        <?php endif; ?>

        <?php if ($page === 'licenses'): ?>
        // 授权管理页面功能

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.license-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBatchToolbar();
        });

        // 单选功能
        document.querySelectorAll('.license-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateBatchToolbar);
        });

        // 批量操作选择器
        document.querySelector('select[name="batch_action"]').addEventListener('change', function() {
            const extendField = document.getElementById('extendMonthsField');
            if (this.value === 'extend') {
                extendField.style.display = 'block';
            } else {
                extendField.style.display = 'none';
            }
        });

        // QQ号设置选择器
        document.querySelector('select[name="qq_status"]').addEventListener('change', function() {
            const manualInput = document.getElementById('manualQQInput');
            if (this.value === 'manual') {
                manualInput.style.display = 'block';
                manualInput.required = true;
            } else {
                manualInput.style.display = 'none';
                manualInput.required = false;
                manualInput.value = '';
            }
        });

        // 密码设置切换
        document.getElementById('passwordType').addEventListener('change', function() {
            const customField = document.getElementById('customPasswordField');
            const customInput = document.getElementById('customPasswordInput');
            if (this.value === 'custom') {
                customField.style.display = 'block';
                customInput.required = true;
            } else {
                customField.style.display = 'none';
                customInput.required = false;
                customInput.value = '';
            }
        });

        // 生成随机密码
        function generateRandomPassword() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let password = '';
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('customPasswordInput').value = password;
        }

        // 切换密码显示/隐藏
        function togglePasswordVisibility() {
            const input = document.getElementById('customPasswordInput');
            const icon = document.getElementById('passwordToggleIcon');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'bi bi-eye-slash me-1';
                icon.parentElement.innerHTML = '<i class="bi bi-eye-slash me-1"></i>隐藏密码';
            } else {
                input.type = 'password';
                icon.className = 'bi bi-eye me-1';
                icon.parentElement.innerHTML = '<i class="bi bi-eye me-1"></i>显示密码';
            }
        }

        // 更新批量操作工具栏
        function updateBatchToolbar() {
            const checkedBoxes = document.querySelectorAll('.license-checkbox:checked');
            const toolbar = document.getElementById('batchToolbar');
            const countSpan = document.getElementById('selectedCount');

            if (checkedBoxes.length > 0) {
                toolbar.classList.add('show');
                countSpan.textContent = checkedBoxes.length;

                // 更新隐藏的checkbox值
                const form = document.getElementById('batchForm');
                form.querySelectorAll('input[name="license_ids[]"]').forEach(input => input.remove());

                checkedBoxes.forEach(checkbox => {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'license_ids[]';
                    hiddenInput.value = checkbox.value;
                    form.appendChild(hiddenInput);
                });
            } else {
                toolbar.classList.remove('show');
            }
        }

        // 清除选择
        function clearSelection() {
            document.querySelectorAll('.license-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('selectAll').checked = false;
            updateBatchToolbar();
        }

        // 编辑授权 - 支持两种调用方式
        function editLicense(license) {
            // 如果传入的是对象，直接使用
            if (typeof license === 'object' && license !== null) {
                document.getElementById('editLicenseId').value = license.id;
                document.getElementById('editLicenseKey').value = license.username;
                document.getElementById('editLicenseType').value = license.user_type;
                document.getElementById('editMaxDevices').value = license.max_devices;
                document.getElementById('editStatus').value = license.status;
                document.getElementById('editQQNumber').value = license.qq_number || '';
                document.getElementById('editNotes').value = license.notes || '';
            }

            new bootstrap.Modal(document.getElementById('editLicenseModal')).show();
        }

        // 为编辑按钮添加事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.edit-license-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const license = {
                        id: this.dataset.licenseId,
                        username: this.dataset.licenseKey,
                        user_type: this.dataset.licenseType,
                        max_devices: this.dataset.maxDevices,
                        status: this.dataset.status,
                        qq_number: this.dataset.qqNumber,
                        notes: this.dataset.notes
                    };
                    editLicense(license);
                });
            });
        });

        // 删除授权
        function deleteLicense(id, licenseKey) {
            if (confirm(`确定要删除授权码 ${licenseKey} 吗？\n\n此操作不可恢复，相关的设备绑定和日志也将被清除。`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="license_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 查看密码
        function showPassword(id, licenseKey) {
            if (confirm(`查看授权码 ${licenseKey} 的密码信息？`)) {
                fetch('admin_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=get_password&license_id=${id}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                    } else {
                        alert('获取失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('请求失败: ' + error.message);
                });
            }
        }

        // 重置密码
        function resetPassword(id, licenseKey) {
            if (confirm(`确定要重置授权码 ${licenseKey} 的密码吗？\n\n新密码将随机生成并显示，请及时记录。`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="license_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 表格行点击高亮
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('click', function(e) {
                if (e.target.type !== 'checkbox' && !e.target.closest('button')) {
                    const checkbox = this.querySelector('.license-checkbox');
                    checkbox.checked = !checkbox.checked;
                    updateBatchToolbar();
                }
            });
        });
        <?php endif; ?>

        <?php if ($page === 'devices'): ?>
        // 设备管理页面功能

        // 切换设备视图
        function switchDeviceView(viewType) {
            const cardsView = document.getElementById('cardsView');
            const tableView = document.getElementById('tableView');
            const cardBtn = document.querySelector('[data-view="cards"]');
            const tableBtn = document.querySelector('[data-view="table"]');

            if (viewType === 'cards') {
                cardsView.style.display = 'block';
                tableView.style.display = 'none';
                cardBtn.classList.add('active');
                tableBtn.classList.remove('active');
                localStorage.setItem('deviceViewType', 'cards');
            } else if (viewType === 'table') {
                cardsView.style.display = 'none';
                tableView.style.display = 'block';
                cardBtn.classList.remove('active');
                tableBtn.classList.add('active');
                localStorage.setItem('deviceViewType', 'table');
            }
        }

        // 页面加载时恢复视图状态
        document.addEventListener('DOMContentLoaded', function() {
            const savedViewType = localStorage.getItem('deviceViewType') || 'cards';
            switchDeviceView(savedViewType);
        });

        // 刷新设备列表
        function refreshDeviceList() {
            location.reload();
        }

        // 编辑设备名称
        function editDeviceName(deviceTableId, currentName, userId, deviceId) {
            document.getElementById('renameDeviceId').value = deviceId;
            document.getElementById('renameUserId').value = userId;
            document.getElementById('renameDeviceName').value = currentName;

            new bootstrap.Modal(document.getElementById('renameDeviceModal')).show();
        }

        // 解绑设备
        function unbindDevice(deviceId, userId, deviceName) {
            if (confirm(`确定要解绑设备 "${deviceName}" 吗？\n\n此操作将：\n- 删除设备绑定记录\n- 清除该设备的所有访问令牌\n- 强制该设备下线\n\n此操作不可恢复！`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="unbind">
                    <input type="hidden" name="device_id" value="${deviceId}">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 查看设备详情
        function viewDeviceDetails(deviceId, userId) {
            const modal = new bootstrap.Modal(document.getElementById('deviceDetailsModal'));
            const content = document.getElementById('deviceDetailsContent');

            // 显示加载状态
            content.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            `;

            modal.show();

            // 获取设备详情
            fetch('admin_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_device_details&device_id=${encodeURIComponent(deviceId)}&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const device = data.device;
                    content.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-sm">
                                    <tr><td>设备ID:</td><td><code>${device.device_id}</code></td></tr>
                                    <tr><td>设备名称:</td><td>${device.device_name || '未知设备'}</td></tr>
                                    <tr><td>最后登录:</td><td>${device.last_login || '从未登录'}</td></tr>
                                    <tr><td>状态:</td><td><span class="badge ${device.status == 1 ? 'bg-success' : 'bg-secondary'}">${device.status == 1 ? '正常' : '禁用'}</span></td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>用户信息</h6>
                                <table class="table table-sm">
                                    <tr><td>用户名:</td><td>${device.username}</td></tr>
                                    <tr><td>用户类型:</td><td>${device.user_type}</td></tr>
                                    <tr><td>创建时间:</td><td>${device.created_at}</td></tr>
                                </table>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-warning btn-sm" onclick="unbindDevice('${deviceId}', '${userId}', '${device.device_name || '未知设备'}')">
                                <i class="bi bi-unlink"></i> 解绑设备
                            </button>
                        </div>
                    `;
                } else {
                    content.innerHTML = `<div class="alert alert-warning">设备信息获取失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                content.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        }
        <?php endif; ?>

        <?php if ($page === 'debug'): ?>
        // 简单的日志查看功能
        document.addEventListener('DOMContentLoaded', function() {
            // 自动滚动到底部
            const logContainer = document.querySelector('.log-code-block');
            if (logContainer) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        });
        <?php endif; ?>

        <?php if ($page === 'logs'): ?>
        // 日志管理页面功能

        // 刷新日志
        function refreshLogs() {
            location.reload();
        }

        // 导出日志
        function exportLogs() {
            const currentUrl = new URL(window.location);
            const params = new URLSearchParams(currentUrl.search);
            params.set('export', '1');

            // 创建下载链接
            const downloadUrl = currentUrl.pathname + '?' + params.toString();

            // 导出日志功能
            if (confirm('确定要导出当前筛选条件下的日志吗？\n\n注意：大量数据可能需要较长时间。')) {
                window.open(downloadUrl, '_blank');
            }
        }

        // 表格行点击展开详情
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('click', function() {
                // 切换行的高亮状态
                this.classList.toggle('table-active');
            });
        });

        // 自动设置日期范围
        function setDateRange(days) {
            const today = new Date();
            const fromDate = new Date(today.getTime() - (days * 24 * 60 * 60 * 1000));

            document.querySelector('input[name="date_from"]').value = fromDate.toISOString().split('T')[0];
            document.querySelector('input[name="date_to"]').value = today.toISOString().split('T')[0];
        }

        // 添加快速日期选择按钮
        const dateFromInput = document.querySelector('input[name="date_from"]');
        if (dateFromInput) {
            const quickButtons = document.createElement('div');
            quickButtons.className = 'btn-group btn-group-sm mt-1';
            quickButtons.innerHTML = `
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange(1)">今天</button>
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange(7)">7天</button>
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange(30)">30天</button>
            `;
            dateFromInput.parentNode.appendChild(quickButtons);
        }
        <?php endif; ?>

        <?php if ($page === 'settings'): ?>
        // 系统设置页面功能

        // 导出系统配置
        function exportSystemConfig() {
            const config = {
                timestamp: new Date().toISOString(),
                system_info: {
                    php_version: '<?= $systemInfo['php_version'] ?? 'N/A' ?>',
                    memory_limit: '<?= $systemInfo['memory_limit'] ?? 'N/A' ?>',
                    timezone: '<?= $systemInfo['timezone'] ?? 'N/A' ?>'
                },
                database_stats: {
                    users_count: <?= $dbStats['users_count'] ?? 0 ?>,
                    devices_count: <?= $dbStats['devices_count'] ?? 0 ?>,
                    tokens_count: <?= $dbStats['tokens_count'] ?? 0 ?>
                }
            };

            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'system_config_' + new Date().toISOString().split('T')[0] + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 更新性能指标
        function updatePerformanceMetrics() {
            const startTime = performance.now();

            fetch('admin_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_performance_metrics'
            })
            .then(response => response.json())
            .then(data => {
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);

                if (data.success) {
                    document.getElementById('cpu-usage').textContent = data.cpu_usage || '--';
                    document.getElementById('memory-usage').textContent = data.memory_usage || '--';
                    document.getElementById('disk-usage').textContent = data.disk_usage || '--';
                }
                document.getElementById('response-time').textContent = responseTime + 'ms';
            })
            .catch(error => {
                console.error('性能监控更新失败:', error);
                document.getElementById('response-time').textContent = 'Error';
            });
        }

        // 切换维护模式
        function toggleMaintenanceMode() {
            const checkbox = document.getElementById('maintenanceMode');
            const enabled = checkbox.checked;

            if (enabled && !confirm('确定要启用维护模式吗？\n\n启用后将阻止新的用户登录。')) {
                checkbox.checked = false;
                return;
            }

            fetch('admin_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=toggle_maintenance&enabled=' + (enabled ? '1' : '0')
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(enabled ? '维护模式已启用' : '维护模式已关闭');
                } else {
                    alert('操作失败: ' + data.message);
                    checkbox.checked = !enabled; // 恢复状态
                }
            })
            .catch(error => {
                alert('操作失败: ' + error.message);
                checkbox.checked = !enabled; // 恢复状态
            });
        }



        // 显示备份说明
        function showBackupInfo() {
            const info = `数据库备份说明

📋 备份内容：
• 所有数据表结构（CREATE TABLE语句）
• 所有表数据（INSERT语句）
• 完整的SQL导入脚本

💾 备份格式：
• 标准SQL格式，兼容MySQL 5.7+
• 包含完整的表结构和数据
• 可直接导入到新的MySQL数据库

🔧 使用方法：
1. 点击"下载数据库"按钮
2. 保存SQL文件到本地
3. 使用phpMyAdmin或命令行导入：
   mysql -u用户名 -p 数据库名 < 备份文件.sql

⚠️ 注意事项：
• 备份文件包含敏感数据，请妥善保管
• 建议定期备份，确保数据安全
• 导入前请确保目标数据库为空或做好数据备份

📁 文件命名格式：
backup_数据库名_年-月-日_时-分-秒.sql`;

            alert(info);
        }

        // 生成系统报告
        function generateSystemReport() {
            if (!confirm('确定要生成系统报告吗？\n\n这可能需要几秒钟时间。')) {
                return;
            }

            fetch('admin.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=generate_system_report'
            })
            .then(response => response.blob())
            .then(blob => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'system_report_' + new Date().toISOString().split('T')[0] + '.txt';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            })
            .catch(error => {
                alert('生成报告失败: ' + error.message);
            });
        }

        // 显示维护工具
        function showMaintenanceTools() {
            const tools = [
                '数据库优化',
                '清理临时文件',
                '重建索引',
                '检查数据完整性',
                '更新统计信息'
            ];

            let message = '可用的维护工具:\n\n';
            tools.forEach((tool, index) => {
                message += `${index + 1}. ${tool}\n`;
            });
            message += '\n请在系统监控页面执行具体操作。';

            alert(message);
        }

        // 系统健康检查
        function checkSystemHealth() {
            // 执行系统检查
            fetch('admin_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=system_health_check'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showHealthCheckResults(data.results);
                } else {
                    alert('系统检查失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('请求失败: ' + error.message);
            });
        }

        function showHealthCheckResults(results) {
            const checkItems = results.map(item => item.name);

            let currentCheck = 0;
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-shield-check me-2"></i>系统健康检查
                            </h5>
                        </div>
                        <div class="modal-body">
                            <div class="progress mb-3">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="checkStatus">准备开始检查...</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            const progressBar = modal.querySelector('.progress-bar');
            const statusDiv = modal.querySelector('#checkStatus');

            function runCheck() {
                if (currentCheck < checkItems.length) {
                    statusDiv.textContent = checkItems[currentCheck];
                    progressBar.style.width = ((currentCheck + 1) / checkItems.length * 100) + '%';
                    currentCheck++;
                    setTimeout(runCheck, 800);
                } else {
                    statusDiv.innerHTML = `
                        <div class="alert alert-success mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            系统检查完成！所有项目正常。
                        </div>
                    `;
                    progressBar.classList.add('bg-success');
                }
            }

            setTimeout(runCheck, 500);
        }

        // 密码强度检查
        document.querySelector('input[name="new_password"]')?.addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength') || (() => {
                const div = document.createElement('div');
                div.id = 'passwordStrength';
                div.className = 'mt-2';
                this.parentNode.appendChild(div);
                return div;
            })();

            let strength = 0;
            let feedback = [];

            if (password.length >= 8) strength++;
            else feedback.push('至少8位字符');

            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('包含大写字母');

            if (/[a-z]/.test(password)) strength++;
            else feedback.push('包含小写字母');

            if (/[0-9]/.test(password)) strength++;
            else feedback.push('包含数字');

            if (/[^A-Za-z0-9]/.test(password)) strength++;
            else feedback.push('包含特殊字符');

            const colors = ['danger', 'danger', 'warning', 'info', 'success'];
            const labels = ['很弱', '弱', '一般', '强', '很强'];

            strengthDiv.innerHTML = `
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-${colors[strength]}" style="width: ${strength * 20}%"></div>
                </div>
                <small class="text-${colors[strength]}">
                    密码强度: ${labels[strength]} ${feedback.length ? '(建议: ' + feedback.join(', ') + ')' : ''}
                </small>
            `;
        });

        // 确认密码匹配检查
        document.querySelector('input[name="confirm_password"]')?.addEventListener('input', function() {
            const newPassword = document.querySelector('input[name="new_password"]').value;
            const confirmPassword = this.value;

            const matchDiv = document.getElementById('passwordMatch') || (() => {
                const div = document.createElement('div');
                div.id = 'passwordMatch';
                div.className = 'mt-2';
                this.parentNode.appendChild(div);
                return div;
            })();

            if (confirmPassword) {
                if (newPassword === confirmPassword) {
                    matchDiv.innerHTML = '<small class="text-success"><i class="bi bi-check-circle me-1"></i>密码匹配</small>';
                } else {
                    matchDiv.innerHTML = '<small class="text-danger"><i class="bi bi-x-circle me-1"></i>密码不匹配</small>';
                }
            } else {
                matchDiv.innerHTML = '';
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动更新性能指标
            updatePerformanceMetrics();

            // 每30秒自动刷新性能指标
            setInterval(updatePerformanceMetrics, 30000);

            // 实时时间更新
            function updateTime() {
                const now = new Date();
                const timeElements = document.querySelectorAll('.current-time');
                timeElements.forEach(el => {
                    el.textContent = now.toLocaleTimeString();
                });
            }

            // 每秒更新时间
            setInterval(updateTime, 1000);
            updateTime();


        });



        <?php endif; ?>

        <?php if ($page === 'debug'): ?>
        // 调试页面功能

        // 刷新调试页面
        function refreshDebugPage() {
            location.reload();
        }

        // 清空调试日志
        function clearDebugLogs() {
            if (confirm('确定要清空所有调试日志吗？\n\n此操作不可恢复！')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `<input type="hidden" name="action" value="clear_logs">`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 测试数据库连接
        function testDatabaseConnection() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `<input type="hidden" name="action" value="test_db_connection">`;
            document.body.appendChild(form);
            form.submit();
        }

        // 显示设备测试模态框
        function showDeviceTestModal() {
            new bootstrap.Modal(document.getElementById('deviceTestModal')).show();
        }

        // 测试设备查询
        function testDeviceQuery(deviceId, userId) {
            document.getElementById('testDeviceId').value = deviceId;
            document.getElementById('testUserId').value = userId;

            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="test_device_query">
                <input type="hidden" name="device_id" value="${deviceId}">
                <input type="hidden" name="user_id" value="${userId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        // 生成测试日志
        function generateTestLog() {
            // 使用fetch发送测试日志请求
            fetch('admin.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=generate_test_log'
            })
            .then(response => {
                if (response.ok) {
                    alert('测试日志已生成，请刷新页面查看！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    alert('生成测试日志失败！');
                }
            })
            .catch(error => {
                alert('请求失败: ' + error.message);
            });
        }
        <?php endif; ?>

        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // 点击主内容区域时隐藏侧边栏（移动端）
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.sidebar');
            const toggleBtn = document.querySelector('[onclick="toggleSidebar()"]');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !toggleBtn.contains(e.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });

        // 窗口大小改变时处理侧边栏
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });

        // 优化的自动刷新（移除论坛管理页面的自动刷新）
        <?php if (in_array($page, ['dashboard', 'users', 'logs', 'announcements'])): ?>
        let refreshInterval;
        let lastActivity = Date.now();
        let isUserActive = true;

        // 监听用户活动
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(function(event) {
            document.addEventListener(event, function() {
                lastActivity = Date.now();
                isUserActive = true;
            }, true);
        });

        // 智能刷新逻辑
        function smartRefresh() {
            const now = Date.now();
            const timeSinceLastActivity = now - lastActivity;

            // 如果用户在过去5秒内有活动，延迟刷新
            if (timeSinceLastActivity < 5000) {
                return;
            }

            // 如果页面不可见，不刷新
            if (document.visibilityState !== 'visible') {
                return;
            }

            // 检查是否有未保存的表单数据
            const forms = document.querySelectorAll('form');
            let hasUnsavedData = false;
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    if (input.value && input.value !== input.defaultValue) {
                        hasUnsavedData = true;
                    }
                });
            });

            if (hasUnsavedData) {
                return;
            }

            // 执行刷新
            location.reload();
        }

        // 每10秒检查一次是否需要刷新（从1.5秒改为10秒）
        refreshInterval = setInterval(smartRefresh, 10000);

        // 页面失去焦点时暂停刷新
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'hidden') {
                clearInterval(refreshInterval);
            } else {
                refreshInterval = setInterval(smartRefresh, 10000);
            }
        });
        <?php endif; ?>

        // 论坛管理页面不使用自动刷新，避免干扰用户操作

        // 公告管理JavaScript
        <?php if ($page === 'announcements'): ?>
        function editAnnouncement(announcement) {
            // 填充编辑表单
            document.getElementById('edit_id').value = announcement.id;
            document.getElementById('edit_title').value = announcement.title;
            document.getElementById('edit_content').value = announcement.content;
            document.getElementById('edit_type').value = announcement.type;
            document.getElementById('edit_priority').value = announcement.priority;
            document.getElementById('edit_status').value = announcement.status;
            document.getElementById('edit_target_version').value = announcement.target_version || '';

            // 处理时间格式
            if (announcement.start_time) {
                const startTime = new Date(announcement.start_time);
                document.getElementById('edit_start_time').value = startTime.toISOString().slice(0, 16);
            }
            if (announcement.end_time) {
                const endTime = new Date(announcement.end_time);
                document.getElementById('edit_end_time').value = endTime.toISOString().slice(0, 16);
            }

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editAnnouncementModal')).show();
        }

        function deleteAnnouncement(id, title) {
            if (confirm(`确定要删除公告"${title}"吗？\n\n此操作不可撤销！`)) {
                // 创建隐藏表单提交删除请求
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_announcement';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                idInput.value = id;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 图片预览功能
        function setupImagePreview(inputId, previewId) {
            const input = document.getElementById(inputId);
            const preview = document.getElementById(previewId);

            if (!input || !preview) return;

            input.addEventListener('change', function(e) {
                preview.innerHTML = '';
                const files = Array.from(e.target.files);

                if (files.length > 3) {
                    alert('最多只能上传3张图片');
                    input.value = '';
                    return;
                }

                files.forEach((file, index) => {
                    if (!file.type.startsWith('image/')) {
                        alert('只能上传图片文件');
                        input.value = '';
                        return;
                    }

                    if (file.size > 2 * 1024 * 1024) {
                        alert('图片大小不能超过2MB');
                        input.value = '';
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'border rounded p-2 mb-2';
                        div.innerHTML = `
                            <div class="d-flex align-items-center">
                                <img src="${e.target.result}" class="me-2" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
                                <div class="flex-grow-1">
                                    <div class="fw-bold">${file.name}</div>
                                    <small class="text-muted">${(file.size / 1024).toFixed(1)} KB</small>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeImage(this, ${index})">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                });
            });
        }

        function removeImage(button, index) {
            const preview = button.closest('#imagePreview, #editImagePreview');
            const input = preview.id === 'imagePreview' ?
                document.getElementById('images') :
                document.getElementById('edit_images');

            // 移除预览
            button.closest('.border').remove();

            // 重置文件输入
            input.value = '';
        }

        // 表单验证和图片预览初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置图片预览
            setupImagePreview('images', 'imagePreview');
            setupImagePreview('edit_images', 'editImagePreview');

            // 表单验证
            const forms = document.querySelectorAll('form[method="POST"]');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const title = form.querySelector('input[name="title"]');
                    const content = form.querySelector('textarea[name="content"]');

                    if (title && title.value.trim().length === 0) {
                        e.preventDefault();
                        alert('请输入公告标题');
                        title.focus();
                        return false;
                    }

                    if (content && content.value.trim().length === 0) {
                        e.preventDefault();
                        alert('请输入公告内容');
                        content.focus();
                        return false;
                    }

                    if (title && title.value.trim().length > 255) {
                        e.preventDefault();
                        alert('公告标题不能超过255个字符');
                        title.focus();
                        return false;
                    }

                    // 验证图片
                    const imageInputs = form.querySelectorAll('input[type="file"]');
                    for (let input of imageInputs) {
                        if (input.files.length > 3) {
                            e.preventDefault();
                            alert('最多只能上传3张图片');
                            return false;
                        }

                        for (let file of input.files) {
                            if (file.size > 2 * 1024 * 1024) {
                                e.preventDefault();
                                alert('图片大小不能超过2MB');
                                return false;
                            }
                        }
                    }
                });
            });
        });

        // 添加images字段到数据库
        function addImagesColumn() {
            if (confirm('确定要为公告表添加图片字段吗？\n\n这将为现有的公告表添加images字段以支持图片功能。')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'add_images_column';

                form.appendChild(actionInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
        <?php endif; ?>

        // 论坛管理JavaScript
        <?php if ($page === 'forum'): ?>

        // 刷新论坛数据
        function refreshForumData() {
            window.location.reload();
        }

        // 处理搜索框回车事件
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                searchPosts();
            }
        }

        // 搜索帖子
        function searchPosts() {
            const searchTerm = document.getElementById('postSearchInput').value.trim();
            const statusFilter = document.getElementById('statusFilter').value;

            if (searchTerm || statusFilter) {
                // 构建搜索URL
                const params = new URLSearchParams();
                if (searchTerm) params.append('search', searchTerm);
                if (statusFilter) params.append('status', statusFilter);
                params.append('page', 'forum');

                // 重定向到搜索结果页面
                window.location.href = '?' + params.toString();
            } else {
                // 如果没有搜索条件，显示所有帖子
                window.location.href = '?page=forum';
            }
        }

        // 筛选帖子
        function filterPosts(status) {
            const searchTerm = document.getElementById('postSearchInput').value.trim();

            // 构建筛选URL
            const params = new URLSearchParams();
            if (searchTerm) params.append('search', searchTerm);
            if (status) params.append('status', status);
            params.append('page', 'forum');

            // 重定向到筛选结果页面
            window.location.href = '?' + params.toString();
        }

        // 清除筛选
        function clearFilters() {
            document.getElementById('postSearchInput').value = '';
            document.getElementById('statusFilter').value = '';
            window.location.href = '?page=forum';
        }

        // 实时搜索功能（可选）
        let searchTimeout;
        function setupLiveSearch() {
            const searchInput = document.getElementById('postSearchInput');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        const searchTerm = this.value.trim();
                        if (searchTerm.length >= 2) {
                            // 实时搜索（可以通过AJAX实现）
                            highlightSearchResults(searchTerm);
                        } else if (searchTerm.length === 0) {
                            // 清除高亮
                            clearHighlights();
                        }
                    }, 300);
                });
            }
        }

        // 高亮搜索结果
        function highlightSearchResults(searchTerm) {
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const titleElement = row.querySelector('h6');
                const contentElement = row.querySelector('p');

                if (titleElement && contentElement) {
                    const title = titleElement.textContent.toLowerCase();
                    const content = contentElement.textContent.toLowerCase();
                    const search = searchTerm.toLowerCase();

                    if (title.includes(search) || content.includes(search)) {
                        row.style.display = '';
                        row.style.backgroundColor = 'rgba(255, 193, 7, 0.1)';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        }

        // 清除高亮
        function clearHighlights() {
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.style.display = '';
                row.style.backgroundColor = '';
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupLiveSearch();
            setupCheckboxListeners();
            updateBatchButtonsState();

            // 从URL参数中恢复搜索条件
            const urlParams = new URLSearchParams(window.location.search);
            const searchTerm = urlParams.get('search');
            const statusFilter = urlParams.get('status');

            if (searchTerm) {
                const searchInput = document.getElementById('postSearchInput');
                if (searchInput) searchInput.value = searchTerm;
            }

            if (statusFilter) {
                const statusSelect = document.getElementById('statusFilter');
                if (statusSelect) statusSelect.value = statusFilter;
            }
        });

        // 设置复选框事件监听器
        function setupCheckboxListeners() {
            // 为所有帖子复选框添加事件监听器
            const postCheckboxes = document.querySelectorAll('input[name="post_ids[]"]');
            postCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBatchButtonsState);
            });

            // 为全选复选框添加事件监听器
            const selectAllCheckbox = document.getElementById('selectAllPosts');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    toggleAllPosts(this);
                });
            }
        }

        // 查看帖子详情
        function viewPostDetail(postId) {
            // 创建模态框显示帖子详情
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-eye me-2"></i>帖子详情
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center py-3">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载帖子详情...</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            new bootstrap.Modal(modal).show();

            // 模拟加载数据
            setTimeout(() => {
                modal.querySelector('.modal-body').innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        帖子ID: ${postId} 的详细信息将在这里显示
                    </div>
                `;
            }, 1000);
        }

        // 编辑帖子
        function editPost(postId) {
            alert('编辑帖子功能开发中... 帖子ID: ' + postId);
        }

        // 管理用户
        function manageUser(username) {
            alert('管理用户功能开发中... 用户: ' + username);
        }

        // 切换帖子状态
        function togglePostStatus(postId, status) {
            if (confirm('确定要更改帖子状态吗？')) {
                // 这里添加AJAX请求来更新状态
                console.log('更新帖子状态:', postId, status);
                // 模拟成功后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            }
        }

        // 删除帖子
        function deletePost(postId) {
            if (confirm('确定要删除这个帖子吗？此操作不可恢复！')) {
                // 这里添加AJAX请求来删除帖子
                console.log('删除帖子:', postId);
                // 模拟成功后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            }
        }

        // 批量操作
        function batchToggleStatus(status) {
            const checkedBoxes = document.querySelectorAll('input[name="post_ids[]"]:checked');
            if (checkedBoxes.length === 0) {
                showNotification('请先选择要操作的帖子', 'warning');
                return;
            }

            const statusText = status === 2 ? '置顶' : (status === 0 ? '隐藏' : '正常');
            const statusIcon = status === 2 ? '📌' : (status === 0 ? '🚫' : '✅');

            if (confirm(`${statusIcon} 确定要将选中的 ${checkedBoxes.length} 个帖子设为${statusText}状态吗？`)) {
                // 显示加载状态
                showLoadingState(true);

                // 模拟批量操作
                console.log('批量更新状态:', status, checkedBoxes.length);

                // 这里可以添加实际的AJAX请求
                setTimeout(() => {
                    showLoadingState(false);
                    showNotification(`成功将 ${checkedBoxes.length} 个帖子设为${statusText}状态`, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }, 1000);
            }
        }

        // 批量删除帖子
        function deleteSelectedPosts() {
            const checkedBoxes = document.querySelectorAll('input[name="post_ids[]"]:checked');
            if (checkedBoxes.length === 0) {
                showNotification('请先选择要删除的帖子', 'warning');
                return;
            }

            if (confirm(`🗑️ 确定要删除选中的 ${checkedBoxes.length} 个帖子吗？\n\n⚠️ 此操作不可恢复！`)) {
                // 显示加载状态
                showLoadingState(true);

                console.log('批量删除帖子:', checkedBoxes.length);

                // 这里可以添加实际的AJAX请求
                setTimeout(() => {
                    showLoadingState(false);
                    showNotification(`成功删除 ${checkedBoxes.length} 个帖子`, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }, 1000);
            }
        }

        // 全选/取消全选帖子
        function toggleAllPosts(checkbox) {
            const postCheckboxes = document.querySelectorAll('input[name="post_ids[]"]');
            postCheckboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });

            // 更新批量操作按钮状态
            updateBatchButtonsState();
        }

        // 更新批量操作按钮状态
        function updateBatchButtonsState() {
            const checkedBoxes = document.querySelectorAll('input[name="post_ids[]"]:checked');
            const batchButtons = document.querySelectorAll('#batchPinBtn, #batchHideBtn, #batchDeleteBtn');

            batchButtons.forEach(btn => {
                if (checkedBoxes.length > 0) {
                    btn.disabled = false;
                    btn.classList.remove('disabled');
                } else {
                    btn.disabled = true;
                    btn.classList.add('disabled');
                }
            });

            // 更新按钮文本显示选中数量
            if (checkedBoxes.length > 0) {
                document.getElementById('batchPinBtn').innerHTML = `<i class="bi bi-pin-angle me-1"></i>置顶 (${checkedBoxes.length})`;
                document.getElementById('batchHideBtn').innerHTML = `<i class="bi bi-eye-slash me-1"></i>隐藏 (${checkedBoxes.length})`;
                document.getElementById('batchDeleteBtn').innerHTML = `<i class="bi bi-trash me-1"></i>删除 (${checkedBoxes.length})`;
            } else {
                document.getElementById('batchPinBtn').innerHTML = `<i class="bi bi-pin-angle me-1"></i>置顶`;
                document.getElementById('batchHideBtn').innerHTML = `<i class="bi bi-eye-slash me-1"></i>隐藏`;
                document.getElementById('batchDeleteBtn').innerHTML = `<i class="bi bi-trash me-1"></i>删除`;
            }
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' :
                              type === 'danger' ? 'alert-danger' : 'alert-info';

            const icon = type === 'success' ? 'bi-check-circle' :
                        type === 'warning' ? 'bi-exclamation-triangle' :
                        type === 'danger' ? 'bi-x-circle' : 'bi-info-circle';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="bi ${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 显示/隐藏加载状态
        function showLoadingState(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (show) {
                if (!loadingOverlay) {
                    const overlay = document.createElement('div');
                    overlay.id = 'loadingOverlay';
                    overlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                    overlay.style.cssText = 'background: rgba(0,0,0,0.5); z-index: 9998;';
                    overlay.innerHTML = `
                        <div class="bg-white rounded p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">处理中...</span>
                            </div>
                            <div>正在处理，请稍候...</div>
                        </div>
                    `;
                    document.body.appendChild(overlay);
                }
            } else {
                if (loadingOverlay) {
                    loadingOverlay.remove();
                }
            }
        }

        // 导出论坛数据
        function exportForumData() {
            alert('导出功能开发中...');
        }

        // 清除论坛缓存
        function clearForumCache() {
            if (confirm('确定要清除论坛缓存吗？')) {
                alert('缓存清除功能开发中...');
            }
        }

        function editPost(postId) {
            // 这里可以实现编辑帖子的功能
            alert('编辑帖子 ID: ' + postId);
        }

        function deletePost(postId) {
            if (confirm('确定要删除这个帖子吗？\n\n删除后将无法恢复，相关的回复也会被删除。')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_post';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'post_id';
                idInput.value = postId;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleAllPosts(checkbox) {
            const checkboxes = document.querySelectorAll('input[name="post_ids[]"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        function deleteSelectedPosts() {
            const selected = document.querySelectorAll('input[name="post_ids[]"]:checked');
            if (selected.length === 0) {
                alert('请先选择要删除的帖子');
                return;
            }

            if (confirm(`确定要删除选中的 ${selected.length} 个帖子吗？\n\n删除后将无法恢复。`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'batch_delete_posts';

                selected.forEach(checkbox => {
                    const idInput = document.createElement('input');
                    idInput.type = 'hidden';
                    idInput.name = 'post_ids[]';
                    idInput.value = checkbox.value;
                    form.appendChild(idInput);
                });

                form.appendChild(actionInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 回复管理函数
        function viewReply(replyId) {
            alert('查看回复 ID: ' + replyId);
        }

        function editReply(replyId) {
            alert('编辑回复 ID: ' + replyId);
        }

        function deleteReply(replyId) {
            if (confirm('确定要删除这个回复吗？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_reply';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'reply_id';
                idInput.value = replyId;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleAllReplies(checkbox) {
            const checkboxes = document.querySelectorAll('input[name="reply_ids[]"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        function deleteSelectedReplies() {
            const selected = document.querySelectorAll('input[name="reply_ids[]"]:checked');
            if (selected.length === 0) {
                alert('请先选择要删除的回复');
                return;
            }

            if (confirm(`确定要删除选中的 ${selected.length} 个回复吗？`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'batch_delete_replies';

                selected.forEach(checkbox => {
                    const idInput = document.createElement('input');
                    idInput.type = 'hidden';
                    idInput.name = 'reply_ids[]';
                    idInput.value = checkbox.value;
                    form.appendChild(idInput);
                });

                form.appendChild(actionInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleReplyStatus(replyId, newStatus) {
            const statusText = newStatus == 1 ? '显示' : '隐藏';
            if (confirm(`确定要${statusText}这个回复吗？`)) {
                // 这里可以添加回复状态切换的逻辑
                alert(`回复状态切换功能需要在后端添加相应的处理逻辑`);
            }
        }

        function batchHideReplies() {
            const selected = document.querySelectorAll('input[name="reply_ids[]"]:checked');
            if (selected.length === 0) {
                alert('请先选择要隐藏的回复');
                return;
            }

            if (confirm(`确定要批量隐藏选中的 ${selected.length} 个回复吗？`)) {
                alert('批量隐藏回复功能需要在后端添加相应的处理逻辑');
            }
        }

        // 消息管理函数
        function viewMessage(messageId) {
            alert('查看消息 ID: ' + messageId);
        }

        function deleteMessage(messageId) {
            if (confirm('确定要删除这条消息吗？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_message';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'message_id';
                idInput.value = messageId;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleAllMessages(checkbox) {
            const checkboxes = document.querySelectorAll('input[name="message_ids[]"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        function deleteSelectedMessages() {
            const selected = document.querySelectorAll('input[name="message_ids[]"]:checked');
            if (selected.length === 0) {
                alert('请先选择要删除的消息');
                return;
            }

            if (confirm(`确定要删除选中的 ${selected.length} 条消息吗？`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'batch_delete_messages';

                selected.forEach(checkbox => {
                    const idInput = document.createElement('input');
                    idInput.type = 'hidden';
                    idInput.name = 'message_ids[]';
                    idInput.value = checkbox.value;
                    form.appendChild(idInput);
                });

                form.appendChild(actionInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 帖子状态管理函数
        function togglePostStatus(postId, newStatus) {
            const statusText = newStatus == 2 ? '置顶' : (newStatus == 1 ? '显示' : '隐藏');
            if (confirm(`确定要${statusText}这个帖子吗？`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'toggle_post_status';

                const postIdInput = document.createElement('input');
                postIdInput.type = 'hidden';
                postIdInput.name = 'post_id';
                postIdInput.value = postId;

                const statusInput = document.createElement('input');
                statusInput.type = 'hidden';
                statusInput.name = 'new_status';
                statusInput.value = newStatus;

                form.appendChild(actionInput);
                form.appendChild(postIdInput);
                form.appendChild(statusInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function batchToggleStatus(newStatus) {
            const selected = document.querySelectorAll('input[name="post_ids[]"]:checked');
            if (selected.length === 0) {
                alert('请先选择要操作的帖子');
                return;
            }

            const statusText = newStatus == 2 ? '置顶' : (newStatus == 1 ? '显示' : '隐藏');
            if (confirm(`确定要批量${statusText}选中的 ${selected.length} 个帖子吗？`)) {
                selected.forEach(checkbox => {
                    togglePostStatus(checkbox.value, newStatus);
                });
            }
        }

        // 用户管理函数
        function banUser(username) {
            if (confirm(`确定要禁用用户 "${username}" 的发言权限吗？\n\n禁用后该用户将无法发布新帖子和回复。`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'ban_user';

                const usernameInput = document.createElement('input');
                usernameInput.type = 'hidden';
                usernameInput.name = 'username';
                usernameInput.value = username;

                form.appendChild(actionInput);
                form.appendChild(usernameInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function unbanUser(username) {
            if (confirm(`确定要恢复用户 "${username}" 的发言权限吗？`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'unban_user';

                const usernameInput = document.createElement('input');
                usernameInput.type = 'hidden';
                usernameInput.name = 'username';
                usernameInput.value = username;

                form.appendChild(actionInput);
                form.appendChild(usernameInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleAllUsers(checkbox) {
            const checkboxes = document.querySelectorAll('input[name="user_names[]"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        function batchBanUsers() {
            const selected = document.querySelectorAll('input[name="user_names[]"]:checked');
            if (selected.length === 0) {
                alert('请先选择要禁用的用户');
                return;
            }

            if (confirm(`确定要禁用选中的 ${selected.length} 个用户的发言权限吗？`)) {
                selected.forEach(checkbox => {
                    banUser(checkbox.value);
                });
            }
        }

        function batchUnbanUsers() {
            const selected = document.querySelectorAll('input[name="user_names[]"]:checked');
            if (selected.length === 0) {
                alert('请先选择要启用的用户');
                return;
            }

            if (confirm(`确定要恢复选中的 ${selected.length} 个用户的发言权限吗？`)) {
                selected.forEach(checkbox => {
                    unbanUser(checkbox.value);
                });
            }
        }

        function viewUserPosts(username) {
            // 切换到帖子管理选项卡并筛选该用户的帖子
            const postsTab = document.getElementById('posts-tab');
            postsTab.click();

            // 这里可以添加筛选逻辑
            setTimeout(() => {
                alert(`查看用户 "${username}" 的所有帖子\n\n此功能可以进一步开发为筛选显示该用户的帖子。`);
            }, 100);
        }

        function deleteUserPosts(username) {
            if (confirm(`⚠️ 危险操作警告！\n\n确定要删除用户 "${username}" 的所有帖子和回复吗？\n\n此操作不可恢复！`)) {
                if (confirm(`再次确认：真的要删除用户 "${username}" 的所有内容吗？`)) {
                    alert('此功能需要进一步开发实现批量删除用户所有内容的逻辑。');
                }
            }
        }
        <?php endif; ?>

        // 论坛选项卡记忆功能
        document.addEventListener('DOMContentLoaded', function() {
            // 获取URL中的tab参数
            const urlParams = new URLSearchParams(window.location.search);
            const activeTab = urlParams.get('tab');

            if (activeTab && document.getElementById(activeTab + '-tab')) {
                // 激活指定的选项卡
                const tabButton = document.getElementById(activeTab + '-tab');
                const tabContent = document.getElementById(activeTab);

                // 移除所有active类
                document.querySelectorAll('#forumTabs .nav-link').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // 激活目标选项卡
                tabButton.classList.add('active');
                tabContent.classList.add('show', 'active');
            }

            // 为选项卡添加点击事件，更新URL
            document.querySelectorAll('#forumTabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-bs-target').substring(1);
                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabId);
                    window.history.replaceState({}, '', url);
                });
            });
        });
    </script>
</body>
</html>
