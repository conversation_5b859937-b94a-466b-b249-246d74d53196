"""
邮箱后台监控进程 - 专门用于监控邮箱变化
只有在检测到新邮件时才通知UI更新，大幅提升性能
"""

import threading
import time
import json
import hashlib
from typing import Dict, List, Any, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QTimer


class MailboxMonitor(QObject):
    """邮箱后台监控器"""
    
    # 定义信号
    new_emails_detected = pyqtSignal(list, int)  # 新邮件检测信号 (邮件列表, 新邮件数量)
    email_count_changed = pyqtSignal(int)        # 邮件数量变化信号
    monitor_status_changed = pyqtSignal(str)     # 监控状态变化信号
    
    def __init__(self):
        super().__init__()
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # 邮箱配置
        self.email_config = None
        self.monitor_interval = 5  # 监控间隔（秒）
        
        # 邮件缓存
        self.cached_emails = []
        self.cached_email_hash = ""
        self.last_email_count = 0
        
        # 统计信息
        self.check_count = 0
        self.update_count = 0
        self.start_time = None
        
        print("✅ 邮箱监控器初始化完成")
        
    def start_monitoring(self, email_config: Dict[str, Any], interval: int = 5):
        """开始监控邮箱"""
        try:
            if self.is_monitoring:
                print("⚠️ 邮箱监控已在运行中")
                return False
                
            self.email_config = email_config
            self.monitor_interval = interval
            self.stop_event.clear()
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(
                target=self._monitor_worker,
                daemon=True,
                name="MailboxMonitor"
            )
            
            self.monitor_thread.start()
            self.is_monitoring = True
            self.start_time = time.time()
            
            print(f"🚀 邮箱监控已启动，监控间隔: {interval}秒")
            self.monitor_status_changed.emit("🔄 监控中...")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动邮箱监控失败: {e}")
            return False
            
    def stop_monitoring(self):
        """停止监控邮箱"""
        try:
            if not self.is_monitoring:
                return
                
            print("🛑 正在停止邮箱监控...")
            
            # 设置停止事件
            self.stop_event.set()
            
            # 等待监控线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2.0)
                
            self.is_monitoring = False
            self.monitor_status_changed.emit("⏸️ 已停止")
            
            # 输出统计信息
            if self.start_time:
                runtime = time.time() - self.start_time
                print(f"📊 监控统计: 运行{runtime:.1f}秒, 检查{self.check_count}次, 更新{self.update_count}次")
                
            print("✅ 邮箱监控已停止")
            
        except Exception as e:
            print(f"❌ 停止邮箱监控失败: {e}")
            
    def _monitor_worker(self):
        """监控工作线程"""
        try:
            print(f"📧 邮箱监控线程启动 (线程ID: {threading.get_ident()})")
            
            while not self.stop_event.is_set():
                try:
                    # 检查邮箱变化
                    self._check_mailbox_changes()
                    
                    # 等待下次检查
                    if self.stop_event.wait(self.monitor_interval):
                        break  # 收到停止信号
                        
                except Exception as e:
                    print(f"❌ 邮箱检查失败: {e}")
                    # 出错时等待一段时间再继续
                    if self.stop_event.wait(min(self.monitor_interval, 10)):
                        break
                        
        except Exception as e:
            print(f"❌ 邮箱监控线程异常: {e}")
        finally:
            print(f"📧 邮箱监控线程结束 (线程ID: {threading.get_ident()})")
            
    def _check_mailbox_changes(self):
        """检查邮箱变化"""
        try:
            self.check_count += 1
            
            # 获取当前邮件
            current_emails = self._fetch_emails()
            if current_emails is None:
                return
                
            # 计算邮件哈希值
            current_hash = self._calculate_email_hash(current_emails)
            current_count = len(current_emails)
            
            # 检查是否有变化
            has_changes = False
            new_email_count = 0
            
            if current_hash != self.cached_email_hash:
                # 邮件内容有变化
                has_changes = True
                
                # 计算新邮件数量
                if current_count > self.last_email_count:
                    new_email_count = current_count - self.last_email_count
                    
                print(f"📬 检测到邮件变化: {self.last_email_count} → {current_count} 封邮件")
                if new_email_count > 0:
                    print(f"🆕 发现 {new_email_count} 封新邮件")
                    
                # 更新缓存
                self.cached_emails = current_emails
                self.cached_email_hash = current_hash
                self.last_email_count = current_count
                self.update_count += 1
                
                # 发送信号通知UI更新
                self.new_emails_detected.emit(current_emails, new_email_count)
                self.email_count_changed.emit(current_count)
                
            else:
                # 无变化，静默检查
                if self.check_count % 12 == 0:  # 每分钟输出一次状态
                    print(f"📊 邮箱无变化 (已检查{self.check_count}次, {current_count}封邮件)")
                    
        except Exception as e:
            print(f"❌ 检查邮箱变化失败: {e}")
            
    def _fetch_emails(self) -> Optional[List[Dict[str, Any]]]:
        """获取邮件列表"""
        try:
            if not self.email_config:
                return None
                
            # 导入邮件API
            import sys
            from pathlib import Path
            
            project_root = Path(__file__).parent.parent.parent
            if str(project_root) not in sys.path:
                sys.path.insert(0, str(project_root))
                
            from src.email_service.email_api import EmailAPI
            
            # 创建API实例并获取邮件
            api = EmailAPI(self.email_config)
            emails = api.get_emails()
            
            return emails if emails else []
            
        except Exception as e:
            print(f"❌ 获取邮件失败: {e}")
            return None
            
    def _calculate_email_hash(self, emails: List[Dict[str, Any]]) -> str:
        """计算邮件列表的哈希值"""
        try:
            if not emails:
                return ""
                
            # 提取关键信息用于哈希计算
            email_signatures = []
            for email in emails:
                signature = {
                    'id': email.get('id', ''),
                    'subject': email.get('subject', ''),
                    'from': email.get('from', ''),
                    'date': email.get('date', ''),
                    'size': len(str(email.get('body', '')))
                }
                email_signatures.append(signature)
                
            # 计算哈希值
            content = json.dumps(email_signatures, sort_keys=True)
            hash_value = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            return hash_value
            
        except Exception as e:
            print(f"❌ 计算邮件哈希失败: {e}")
            return ""
            
    def get_cached_emails(self) -> List[Dict[str, Any]]:
        """获取缓存的邮件"""
        return self.cached_emails.copy()
        
    def get_monitor_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        runtime = time.time() - self.start_time if self.start_time else 0
        
        return {
            'is_monitoring': self.is_monitoring,
            'runtime_seconds': runtime,
            'check_count': self.check_count,
            'update_count': self.update_count,
            'cached_email_count': len(self.cached_emails),
            'monitor_interval': self.monitor_interval,
            'efficiency': (self.update_count / max(self.check_count, 1)) * 100  # 更新效率百分比
        }
        
    def force_check(self):
        """强制检查一次邮箱"""
        if self.is_monitoring:
            # 在监控线程中，通过缩短等待时间来触发检查
            self.stop_event.set()
            self.stop_event.clear()
        else:
            # 如果没有监控，执行一次性检查
            self._check_mailbox_changes()
            
    def update_config(self, email_config: Dict[str, Any]):
        """更新邮箱配置"""
        self.email_config = email_config
        print("✅ 邮箱配置已更新")
        
    def set_monitor_interval(self, interval: int):
        """设置监控间隔"""
        if interval < 1:
            interval = 1
        elif interval > 300:  # 最大5分钟
            interval = 300
            
        self.monitor_interval = interval
        print(f"✅ 监控间隔已设置为 {interval} 秒")


# 全局邮箱监控器实例
_mailbox_monitor = None

def get_mailbox_monitor() -> MailboxMonitor:
    """获取全局邮箱监控器"""
    global _mailbox_monitor
    if _mailbox_monitor is None:
        _mailbox_monitor = MailboxMonitor()
    return _mailbox_monitor

def start_mailbox_monitoring(email_config: Dict[str, Any], interval: int = 5) -> bool:
    """启动邮箱监控"""
    monitor = get_mailbox_monitor()
    return monitor.start_monitoring(email_config, interval)

def stop_mailbox_monitoring():
    """停止邮箱监控"""
    monitor = get_mailbox_monitor()
    monitor.stop_monitoring()

def get_monitoring_stats() -> Dict[str, Any]:
    """获取监控统计信息"""
    monitor = get_mailbox_monitor()
    return monitor.get_monitor_stats()
