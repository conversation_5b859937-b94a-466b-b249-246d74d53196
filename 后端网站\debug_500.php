<?php
// 500错误调试页面
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>500错误调试</h1>";

try {
    echo "<h2>1. 基础PHP测试</h2>";
    echo "PHP版本: " . phpversion() . "<br>";
    echo "当前时间: " . date('Y-m-d H:i:s') . "<br>";
    
    echo "<h2>2. 检查数据库配置</h2>";
    if (file_exists('config/database.php')) {
        echo "✓ 配置文件存在<br>";
        try {
            $config = require 'config/database.php';
            echo "✓ 配置文件加载成功<br>";
            echo "数据库类型: " . ($config['type'] ?? '未设置') . "<br>";
            if (isset($config['mysql'])) {
                echo "MySQL主机: " . $config['mysql']['host'] . "<br>";
                echo "MySQL数据库: " . $config['mysql']['database'] . "<br>";
            }
        } catch (Exception $e) {
            echo "✗ 配置文件加载失败: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "✗ 配置文件不存在<br>";
    }
    
    echo "<h2>3. 检查Database类</h2>";
    if (file_exists('includes/Database.php')) {
        echo "✓ Database.php 存在<br>";
        try {
            require_once 'includes/Database.php';
            echo "✓ Database类加载成功<br>";
            
            // 测试数据库连接
            $database = new Database();
            echo "✓ 数据库连接成功<br>";
            
            // 测试查询
            $result = $database->query("SELECT COUNT(*) as count FROM announcements");
            $row = $result->fetch();
            echo "公告数量: " . $row['count'] . "<br>";
            
        } catch (Exception $e) {
            echo "✗ Database类测试失败: " . $e->getMessage() . "<br>";
            echo "错误文件: " . $e->getFile() . "<br>";
            echo "错误行号: " . $e->getLine() . "<br>";
        }
    } else {
        echo "✗ Database.php 不存在<br>";
    }
    
    echo "<h2>4. 检查其他依赖</h2>";
    $files = ['includes/Logger.php', 'includes/SecurityConfig.php', 'includes/AuthManager.php'];
    foreach ($files as $file) {
        if (file_exists($file)) {
            echo "✓ {$file} 存在<br>";
            try {
                require_once $file;
                echo "✓ {$file} 加载成功<br>";
            } catch (Exception $e) {
                echo "✗ {$file} 加载失败: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "✗ {$file} 不存在<br>";
        }
    }
    
    echo "<h2>5. 测试Session</h2>";
    session_start();
    echo "✓ Session启动成功<br>";
    echo "Session ID: " . session_id() . "<br>";
    
    echo "<h2>6. 模拟admin.php核心逻辑</h2>";
    
    // 模拟登录状态
    $_SESSION['admin_id'] = 2;
    $_SESSION['admin_username'] = 'admin';
    echo "✓ 模拟登录成功<br>";
    
    // 测试公告查询
    if (isset($database)) {
        try {
            $announcements = $database->fetchAll("SELECT id, title, type, status FROM announcements LIMIT 5");
            echo "✓ 公告查询成功，找到 " . count($announcements) . " 条记录<br>";
        } catch (Exception $e) {
            echo "✗ 公告查询失败: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>7. 检查上传目录</h2>";
    $uploadDir = __DIR__ . '/uploads/announcements/';
    if (!is_dir($uploadDir)) {
        if (mkdir($uploadDir, 0755, true)) {
            echo "✓ 创建上传目录成功<br>";
        } else {
            echo "✗ 创建上传目录失败<br>";
        }
    } else {
        echo "✓ 上传目录已存在<br>";
    }
    
    echo "<h2>8. 测试完成</h2>";
    echo "如果以上测试都通过，那么问题可能在admin.php的特定代码段中。<br>";
    echo "<a href='admin.php?page=announcements' target='_blank'>尝试访问admin.php</a><br>";
    
} catch (Exception $e) {
    echo "<h2>严重错误</h2>";
    echo "错误信息: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    echo "错误堆栈: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>9. PHP错误日志</h2>";
echo "请检查您的PHP错误日志文件，通常位于：<br>";
echo "- XAMPP: C:\\xampp\\apache\\logs\\error.log<br>";
echo "- WAMP: C:\\wamp\\logs\\php_error.log<br>";
echo "- 或者您的Web服务器配置的错误日志位置<br>";
?>
