"""
PyQt论坛页面 - 参考后端API规范
支持帖子浏览、发布、回复等完整论坛功能
"""

import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.gui.pyqt_ui.pyqt_design_system import DS
from src.utils.logger import get_logger


class ForumPostCard(QFrame):
    """论坛帖子卡片"""
    
    clicked = pyqtSignal(dict)
    
    def __init__(self, post_data):
        super().__init__()
        self.post_data = post_data
        self.logger = get_logger()
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI - 现代化卡片设计"""
        self.setMinimumHeight(110)  # 改为最小高度，允许内容自适应
        self.setMaximumHeight(140)  # 设置最大高度限制
        self.setCursor(Qt.PointingHandCursor)
        
        # 根据帖子类型设置样式
        type_colors = {
            'discussion': DS.COLORS['neon_cyan'],
            'question': DS.COLORS['neon_orange'],
            'announcement': DS.COLORS['neon_red'],
            'feedback': DS.COLORS['neon_green']
        }
        
        post_type = self.post_data.get('post_type', 'discussion')
        accent_color = type_colors.get(post_type, DS.COLORS['neon_cyan'])
        
        # 置顶帖子特殊样式
        is_pinned = self.post_data.get('status') == 2
        bg_color = '#2d2d2d' if not is_pinned else '#3d2d1d'
        
        self.setStyleSheet(f"""
            ForumPostCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {bg_color},
                    stop:1 #1a1a1a);
                border: 1px solid #444444;
                border-left: 4px solid {accent_color.name()};
                border-radius: 12px;
                margin: 3px 0;
                padding: 2px;
            }}
            ForumPostCard:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3d3d3d,
                    stop:1 #2d2d2d);
                border: 1px solid {accent_color.name()};
                transform: translateY(-2px);
            }}
        """)
        
        # 主布局 - 优化间距
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)  # 减少边距
        layout.setSpacing(12)  # 减少间距
        
        # 左侧内容区域
        content_layout = QVBoxLayout()
        content_layout.setSpacing(8)
        
        # 头部信息
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        # 帖子类型图标
        type_icons = {
            'discussion': '💬',
            'question': '❓',
            'announcement': '📢',
            'feedback': '💡'
        }
        
        type_icon = QLabel(type_icons.get(post_type, '💬'))
        type_icon.setFont(QFont('Segoe UI Emoji', 14))
        header_layout.addWidget(type_icon)
        
        # 置顶标识
        if is_pinned:
            pin_label = QLabel('📌 置顶')
            pin_label.setStyleSheet(f"color: {DS.COLORS['neon_red'].name()}; font-weight: bold; font-size: 10px;")
            header_layout.addWidget(pin_label)
        
        # 标题
        title = self.post_data.get('title', '无标题')
        title_label = QLabel(title)
        title_label.setFont(DS.get_font('heading_sm'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        title_label.setWordWrap(True)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        content_layout.addLayout(header_layout)
        
        # 内容预览
        content = self.post_data.get('content', '')
        preview = content[:80] + '...' if len(content) > 80 else content
        content_label = QLabel(preview)
        content_label.setFont(DS.get_font('body_sm'))
        content_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        content_label.setWordWrap(True)
        content_layout.addWidget(content_label)
        
        # 底部信息
        footer_layout = QHBoxLayout()
        footer_layout.setSpacing(20)
        
        # 作者信息
        author = self.post_data.get('author', '匿名')
        author_label = QLabel(f"👤 {author}")
        author_label.setFont(DS.get_font('caption'))
        author_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        footer_layout.addWidget(author_label)
        
        # 发布时间
        created_at = self.post_data.get('created_at', '')
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                time_str = dt.strftime('%m-%d %H:%M')
                time_label = QLabel(f"🕒 {time_str}")
                time_label.setFont(DS.get_font('caption'))
                time_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
                footer_layout.addWidget(time_label)
            except:
                pass
        
        footer_layout.addStretch()
        content_layout.addLayout(footer_layout)
        
        layout.addLayout(content_layout, 3)
        
        # 右侧统计信息
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(5)
        stats_layout.setAlignment(Qt.AlignCenter)
        
        # 回复数 - 安全的类型转换
        replies_count = self._safe_int(self.post_data.get('replies_count', 0))
        replies_label = QLabel(f"{replies_count}")
        replies_label.setFont(DS.get_font('heading_md'))
        replies_label.setStyleSheet(f"color: {DS.COLORS['neon_cyan'].name()}; font-weight: bold;")
        replies_label.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(replies_label)

        replies_text = QLabel("回复")
        replies_text.setFont(DS.get_font('caption'))
        replies_text.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        replies_text.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(replies_text)

        # 点赞按钮 - 可点击的点赞功能
        likes_count = self._safe_int(self.post_data.get('likes_count', 0))
        self.like_button = QPushButton(f"👍 {likes_count}")
        self.like_button.setFont(DS.get_font('caption'))

        # 检查是否已点赞（需要从父页面获取点赞状态）
        post_id = self.post_data.get('id')
        is_liked = False
        if hasattr(self.parent(), 'user_likes') and post_id in self.parent().user_likes:
            is_liked = True

        # 设置点赞按钮样式
        like_color = DS.COLORS['neon_pink'] if is_liked else DS.COLORS['neon_green']
        self.like_button.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: 1px solid {like_color.name()};
                color: {like_color.name()};
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {like_color.name()};
                color: {DS.COLORS['bg_primary'].name()};
            }}
        """)

        # 连接点击事件
        self.like_button.clicked.connect(lambda: self._on_like_clicked(post_id))
        stats_layout.addWidget(self.like_button)

        # 浏览数 - 安全的类型转换
        views_count = self._safe_int(self.post_data.get('views_count', 0))
        if views_count > 0:
            views_label = QLabel(f"👁️ {views_count}")
            views_label.setFont(DS.get_font('caption'))
            views_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
            views_label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(views_label)
        
        layout.addLayout(stats_layout, 1)

    def _safe_int(self, value, default=0):
        """安全的整数转换"""
        try:
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                return int(value) if value.isdigit() else default
            else:
                return default
        except (ValueError, TypeError):
            return default

    def _on_like_clicked(self, post_id):
        """点赞按钮点击处理"""
        try:
            # 获取父页面并调用点赞方法
            parent_page = self.parent()
            while parent_page and not hasattr(parent_page, '_toggle_like'):
                parent_page = parent_page.parent()

            if parent_page and hasattr(parent_page, '_toggle_like'):
                parent_page._toggle_like(post_id, self)
            else:
                print(f"无法找到父页面的点赞方法，post_id: {post_id}")

        except Exception as e:
            print(f"点赞点击处理失败: {e}")

    def update_like_status(self, likes_count, is_liked):
        """更新点赞状态"""
        try:
            self.like_button.setText(f"👍 {likes_count}")
            like_color = DS.COLORS['neon_pink'] if is_liked else DS.COLORS['neon_green']
            self.like_button.setStyleSheet(f"""
                QPushButton {{
                    background: transparent;
                    border: 1px solid {like_color.name()};
                    color: {like_color.name()};
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background: {like_color.name()};
                    color: {DS.COLORS['bg_primary'].name()};
                }}
            """)
        except Exception as e:
            print(f"更新点赞状态失败: {e}")

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.post_data)
        super().mousePressEvent(event)


class ForumLoader(QThread):
    """论坛数据加载线程"""

    posts_loaded = pyqtSignal(list, dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, api_url, action='get_posts', **kwargs):
        super().__init__()
        self.api_url = api_url
        self.action = action
        self.params = kwargs
        
    def run(self):
        """运行加载任务"""
        try:
            # 检查是否被中断
            if self.isInterruptionRequested():
                return

            if self.action == 'get_posts':
                self._load_posts()
        except Exception as e:
            if not self.isInterruptionRequested():
                self.error_occurred.emit(str(e))
            
    def _load_posts(self):
        """加载帖子列表 - 使用统一的API客户端"""
        try:
            # 检查是否被中断
            if self.isInterruptionRequested():
                return

            # 使用统一的API客户端
            try:
                from utils.forum_api_client import get_forum_api_client
            except ImportError:
                from src.utils.forum_api_client import get_forum_api_client
            api_client = get_forum_api_client()

            # 再次检查中断
            if self.isInterruptionRequested():
                return

            # 调用API获取帖子
            page = self.params.get('page', 1)
            limit = self.params.get('limit', 20)
            post_type = self.params.get('type', '')

            success, response = api_client.get_posts(page=page, limit=limit, post_type=post_type)

            # 检查中断
            if self.isInterruptionRequested():
                return

            if success and not self.isInterruptionRequested():
                posts = response.get('data', {}).get('posts', [])
                pagination = response.get('data', {}).get('pagination', {})
                self.posts_loaded.emit(posts, pagination)
            elif not self.isInterruptionRequested():
                error_msg = response.get('message', '获取帖子失败')
                self.error_occurred.emit(error_msg)

        except Exception as e:
            if not self.isInterruptionRequested():
                self.error_occurred.emit(f"API调用失败: {str(e)}")
            



class PyQtForumPage(QWidget):
    """PyQt论坛页面"""
    
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.logger = get_logger()
        self.posts = []
        self.current_page = 1
        self.current_type = ''
        self.loader_thread = None
        self.stats_loader = None

        # 用户点赞状态管理
        self.user_likes = set()
        self.current_username = 'guest'
        
        # 设置基本样式
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QPushButton {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 8px 16px;
                color: {DS.COLORS['text_primary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
            QPushButton:pressed {{
                background-color: {DS.COLORS['neon_cyan'].name()};
            }}
            QScrollArea {{
                border: none;
                background: transparent;
            }}
        """)
        
        self._setup_ui()
        self._load_forum_data()
        
    def _setup_ui(self):
        """设置UI - 现代化布局设计"""
        # 主布局 - 使用更合理的边距和间距
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)  # 减少边距，增加内容空间
        main_layout.setSpacing(15)  # 统一间距

        # 头部区域
        header_layout = QVBoxLayout()
        header_layout.setSpacing(12)  # 减少头部间距
        
        # 标题和操作栏 - 现代化设计
        title_layout = QHBoxLayout()
        title_layout.setSpacing(15)  # 减少间距
        title_layout.setContentsMargins(5, 5, 5, 5)  # 添加内边距

        # 标题
        title_label = QLabel("🏛️ 社区论坛")
        title_label.setFont(DS.get_font('heading_xl'))
        title_label.setStyleSheet(f"""
            color: {DS.COLORS['text_primary'].name()};
            font-weight: bold;
            padding: 5px 0;
        """)
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 按钮容器 - 统一样式
        button_container = QHBoxLayout()
        button_container.setSpacing(10)

        # 发帖按钮 - 优化样式
        new_post_btn = QPushButton("✏️ 发布新帖")
        new_post_btn.setMinimumSize(120, 36)  # 设置最小尺寸
        new_post_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {DS.COLORS['neon_green'].name()},
                    stop:1 {DS.COLORS['neon_green'].darker(120).name()});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {DS.COLORS['neon_green'].lighter(110).name()},
                    stop:1 {DS.COLORS['neon_green'].name()});
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['neon_green'].darker(130).name()};
            }}
        """)
        new_post_btn.clicked.connect(self._show_new_post_dialog)
        button_container.addWidget(new_post_btn)

        # 刷新按钮 - 统一样式
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setMinimumSize(80, 36)
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                padding: 8px 16px;
                color: {DS.COLORS['text_primary'].name()};
                font-weight: bold;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
            }}
        """)
        refresh_btn.clicked.connect(self._load_forum_data)
        button_container.addWidget(refresh_btn)

        title_layout.addLayout(button_container)
        
        header_layout.addLayout(title_layout)
        
        # 分类筛选 - 现代化标签页设计
        filter_container = QFrame()
        filter_container.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 10px;
                padding: 8px;
            }}
        """)

        filter_layout = QHBoxLayout(filter_container)
        filter_layout.setContentsMargins(10, 8, 10, 8)
        filter_layout.setSpacing(8)

        filter_label = QLabel("分类:")
        filter_label.setFont(DS.get_font('body_md'))
        filter_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; font-weight: bold;")
        filter_layout.addWidget(filter_label)

        # 分类按钮 - 标签页样式
        self.filter_buttons = {}
        categories = [
            ('', '全部', '📋'),
            ('discussion', '讨论', '💬'),
            ('question', '问答', '❓'),
            ('announcement', '公告', '📢'),
            ('feedback', '反馈', '💡')
        ]

        for cat_type, cat_name, cat_icon in categories:
            btn = QPushButton(f"{cat_icon} {cat_name}")
            btn.setCheckable(True)
            btn.setMinimumSize(80, 32)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: transparent;
                    border: 1px solid transparent;
                    border-radius: 6px;
                    padding: 6px 12px;
                    color: {DS.COLORS['text_secondary'].name()};
                    font-weight: bold;
                    font-size: 12px;
                }}
                QPushButton:hover {{
                    background: {DS.COLORS['bg_tertiary'].name()};
                    color: {DS.COLORS['text_primary'].name()};
                }}
                QPushButton:checked {{
                    background: {DS.COLORS['neon_cyan'].name()};
                    color: white;
                    border-color: {DS.COLORS['neon_cyan'].name()};
                }}
            """)
            btn.clicked.connect(lambda checked, t=cat_type: self._filter_by_type(t))
            self.filter_buttons[cat_type] = btn
            filter_layout.addWidget(btn)

        # 默认选中"全部"
        self.filter_buttons[''].setChecked(True)

        filter_layout.addStretch()
        header_layout.addWidget(filter_container)
        
        main_layout.addLayout(header_layout)
        
        # 帖子列表区域
        self._setup_posts_area(main_layout)
        
    def _setup_posts_area(self, main_layout):
        """设置帖子列表区域 - 现代化滚动设计"""
        # 创建帖子区域容器
        posts_container = QFrame()
        posts_container.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 12px;
            }}
        """)

        container_layout = QVBoxLayout(posts_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # 滚动区域 - 优化样式
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background: transparent;
                border-radius: 12px;
            }}
            QScrollBar:vertical {{
                background: {DS.COLORS['bg_primary'].name()};
                width: 8px;
                border-radius: 4px;
                margin: 0;
            }}
            QScrollBar::handle:vertical {{
                background: {DS.COLORS['neon_cyan'].name()};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {DS.COLORS['neon_cyan'].lighter(120).name()};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}
        """)

        # 滚动内容
        self.scroll_content = QWidget()
        self.scroll_content.setStyleSheet("background: transparent;")
        self.posts_layout = QVBoxLayout(self.scroll_content)
        self.posts_layout.setContentsMargins(15, 15, 15, 15)  # 添加内边距
        self.posts_layout.setSpacing(12)  # 统一间距
        self.posts_layout.setAlignment(Qt.AlignTop)

        scroll_area.setWidget(self.scroll_content)
        container_layout.addWidget(scroll_area)

        main_layout.addWidget(posts_container)

        # 状态标签 - 现代化设计
        self.status_label = QLabel("正在加载论坛数据...")
        self.status_label.setFont(DS.get_font('body_lg'))
        self.status_label.setStyleSheet(f"""
            color: {DS.COLORS['text_secondary'].name()};
            padding: 40px;
            background: {DS.COLORS['bg_secondary'].name()};
            border: 1px solid {DS.COLORS['glass_border'].name()};
            border-radius: 12px;
            margin: 10px;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.status_label)
        
    def _load_forum_data(self):
        """加载论坛数据 - 使用统一的API客户端"""
        # 停止现有的加载线程
        self._stop_loading_thread()

        # 更新状态
        self.status_label.setText("正在加载论坛数据...")
        self.status_label.show()

        # 清空现有帖子
        self._clear_posts()

        # 启动加载线程 - 使用统一的API客户端
        self.loader_thread = ForumLoader(
            api_url="",  # API客户端会自动处理URL
            action='get_posts',
            page=self.current_page,
            limit=20,
            type=self.current_type
        )
        self.loader_thread.posts_loaded.connect(self._on_posts_loaded)
        self.loader_thread.error_occurred.connect(self._on_load_error)
        self.loader_thread.finished.connect(self._on_thread_finished)
        self.loader_thread.start()

        if self.logger:
            self.logger.info("开始加载论坛数据")

    def _stop_loading_thread(self):
        """停止所有加载线程"""
        # 停止主加载线程
        if self.loader_thread and self.loader_thread.isRunning():
            try:
                # 断开信号连接，避免处理过期的信号
                self.loader_thread.posts_loaded.disconnect()
                self.loader_thread.error_occurred.disconnect()
                self.loader_thread.finished.disconnect()

                # 请求线程停止
                self.loader_thread.requestInterruption()

                # 等待线程结束，最多等待1秒
                if not self.loader_thread.wait(1000):
                    # 如果线程没有在1秒内结束，强制终止
                    self.loader_thread.terminate()
                    self.loader_thread.wait(500)

                if self.logger:
                    self.logger.info("论坛加载线程已停止")

            except Exception as e:
                if self.logger:
                    self.logger.warning(f"停止论坛加载线程时出错: {e}")

        self.loader_thread = None

        # 停止统计加载线程
        if hasattr(self, 'stats_loader') and self.stats_loader and self.stats_loader.isRunning():
            try:
                self.stats_loader.stats_loaded.disconnect()
                self.stats_loader.requestInterruption()

                if not self.stats_loader.wait(1000):
                    self.stats_loader.terminate()
                    self.stats_loader.wait(500)

                if self.logger:
                    self.logger.info("统计加载线程已停止")

            except Exception as e:
                if self.logger:
                    self.logger.warning(f"停止统计加载线程时出错: {e}")

        self.stats_loader = None

    def _on_thread_finished(self):
        """线程完成回调"""
        if self.logger:
            self.logger.debug("论坛加载线程已完成")

    def _clear_posts(self):
        """清空帖子列表"""
        while self.posts_layout.count():
            child = self.posts_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
    def _on_posts_loaded(self, posts, pagination):
        """帖子加载完成"""
        self.posts = posts
        self._display_posts()

        # 加载用户点赞状态
        self._load_user_likes()

        if self.logger:
            self.logger.info(f"论坛帖子加载完成，共 {len(posts)} 条")
            
    def _on_load_error(self, error_message):
        """加载失败"""
        self.status_label.setText(f"加载失败: {error_message}")
        
        if self.logger:
            self.logger.error(f"论坛数据加载失败: {error_message}")
            
    def _display_posts(self):
        """显示帖子列表"""
        # 隐藏状态标签
        self.status_label.hide()

        # 清空现有帖子
        self._clear_posts()

        if not self.posts:
            # 显示空状态
            empty_label = QLabel("暂无帖子")
            empty_label.setFont(DS.get_font('body_lg'))
            empty_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; padding: 40px;")
            empty_label.setAlignment(Qt.AlignCenter)
            self.posts_layout.addWidget(empty_label)
        else:
            # 显示帖子卡片
            for post in self.posts:
                card = ForumPostCard(post)
                card.clicked.connect(self._show_post_detail)
                self.posts_layout.addWidget(card)

        # 添加弹性空间
        self.posts_layout.addStretch()
        
    def _filter_by_type(self, post_type):
        """按类型筛选"""
        # 更新按钮状态
        for btn_type, btn in self.filter_buttons.items():
            btn.setChecked(btn_type == post_type)
        
        # 更新筛选条件并重新加载
        self.current_type = post_type
        self.current_page = 1
        self._load_forum_data()
        
    def _show_post_detail(self, post_data):
        """显示帖子详情"""
        try:
            from .forum_dialogs import PostDetailDialog
            dialog = PostDetailDialog(post_data, self)
            dialog.exec_()

        except Exception as e:
            if self.logger:
                self.logger.error(f"显示帖子详情失败: {e}")

    def _show_new_post_dialog(self):
        """显示发帖对话框"""
        try:
            from .forum_dialogs import NewPostDialog
            dialog = NewPostDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # 发帖成功，刷新列表
                self._load_forum_data()

        except Exception as e:
            if self.logger:
                self.logger.error(f"显示发帖对话框失败: {e}")

    def _toggle_like(self, post_id, post_card=None):
        """切换点赞状态"""
        try:
            # 获取当前用户名
            username = self._get_current_username()
            if username == 'guest':
                QMessageBox.warning(self, "提示", "请先登录后再点赞")
                return

            # 查找帖子
            post = next((p for p in self.posts if p['id'] == post_id), None)
            if not post:
                self.logger.warning(f"未找到帖子 ID: {post_id}")
                return

            # 调用API切换点赞状态
            self._toggle_like_api(post_id, username, post, post_card)

        except Exception as e:
            if self.logger:
                self.logger.error(f"点赞操作失败: {e}")
            QMessageBox.critical(self, "错误", f"点赞操作失败: {str(e)}")

    def _toggle_like_api(self, post_id, username, post, post_card):
        """异步调用点赞API"""
        from PyQt5.QtCore import QThread, pyqtSignal

        class LikeToggler(QThread):
            success = pyqtSignal(dict, bool)  # response, is_liked
            error = pyqtSignal(str)

            def __init__(self, post_id, username):
                super().__init__()
                self.post_id = post_id
                self.username = username

            def run(self):
                try:
                    try:
                        from utils.forum_api_client import get_forum_api_client
                    except ImportError:
                        from src.utils.forum_api_client import get_forum_api_client
                    # 获取主窗口引用
                    main_window = None
                    try:
                        # 从父页面获取主窗口
                        import sys
                        for obj in sys.modules.values():
                            if hasattr(obj, '__dict__'):
                                for attr_name, attr_value in obj.__dict__.items():
                                    if hasattr(attr_value, 'main_window'):
                                        main_window = attr_value.main_window
                                        break
                                if main_window:
                                    break
                    except:
                        pass

                    api_client = get_forum_api_client(main_window=main_window)

                    success, response = api_client.like_post(self.username, self.post_id)

                    if success:
                        # 从响应中获取点赞状态
                        action = response.get('data', {}).get('action', '')
                        is_liked = action == 'liked'
                        self.success.emit(response, is_liked)
                    else:
                        error_msg = response.get('message', '点赞失败')
                        self.error.emit(error_msg)

                except Exception as e:
                    self.error.emit(f"网络错误: {str(e)}")

        # 创建并启动线程
        self.like_toggler = LikeToggler(post_id, username)
        self.like_toggler.success.connect(lambda response, is_liked: self._on_like_success(post_id, post, post_card, response, is_liked))
        self.like_toggler.error.connect(lambda error: self._on_like_error(post_id, post, post_card, error))
        self.like_toggler.start()

    def _on_like_success(self, post_id, post, post_card, response, is_liked):
        """点赞成功回调"""
        try:
            # 更新本地状态
            if is_liked:
                self.user_likes.add(post_id)
            else:
                self.user_likes.discard(post_id)

            # 更新帖子数据
            likes_count = response.get('data', {}).get('likes_count', post.get('likes_count', 0))
            post['likes_count'] = likes_count

            # 更新UI
            if post_card and hasattr(post_card, 'update_like_status'):
                post_card.update_like_status(likes_count, is_liked)

            action_text = "点赞" if is_liked else "取消点赞"
            if self.logger:
                self.logger.info(f"{action_text}成功: {post.get('title', 'Unknown')}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"处理点赞成功回调失败: {e}")

    def _on_like_error(self, post_id, post, post_card, error_msg):
        """点赞失败回调"""
        if self.logger:
            self.logger.error(f"点赞失败: {error_msg}")

        # 使用本地逻辑作为备用
        self._toggle_like_local(post_id, post, post_card)

    def _toggle_like_local(self, post_id, post, post_card):
        """本地点赞逻辑（备用方案）"""
        try:
            # 切换本地点赞状态
            if post_id in self.user_likes:
                self.user_likes.discard(post_id)
                likes_count = max(0, post.get('likes_count', 0) - 1)
                is_liked = False
                action_text = "取消点赞"
            else:
                self.user_likes.add(post_id)
                likes_count = post.get('likes_count', 0) + 1
                is_liked = True
                action_text = "点赞"

            # 更新帖子数据
            post['likes_count'] = likes_count

            # 更新UI
            if post_card and hasattr(post_card, 'update_like_status'):
                post_card.update_like_status(likes_count, is_liked)

            if self.logger:
                self.logger.info(f"本地{action_text}: {post.get('title', 'Unknown')}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"本地点赞操作失败: {e}")

    def _get_current_username(self):
        """获取当前用户名"""
        try:
            # 从主窗口获取用户信息
            if self.main_window and hasattr(self.main_window, 'security_manager'):
                security_manager = self.main_window.security_manager
                if security_manager and hasattr(security_manager, 'logged_in_user'):
                    user_info = security_manager.logged_in_user
                    if user_info and user_info.get('username'):
                        return user_info.get('username')

            # 备用方案
            try:
                from utils.config_manager import ConfigManager
            except ImportError:
                from src.utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            return config_manager.get('current_user', {}).get('username', 'guest')

        except Exception as e:
            if self.logger:
                self.logger.warning(f"获取用户名失败: {e}")
            return 'guest'

    def hideEvent(self, event):
        """页面隐藏事件 - 停止后台线程"""
        super().hideEvent(event)
        try:
            self._stop_loading_thread()
            if self.logger:
                self.logger.info("论坛页面隐藏，已停止后台线程")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"论坛页面隐藏时停止线程失败: {e}")

    def closeEvent(self, event):
        """页面关闭事件 - 清理资源"""
        try:
            self._stop_loading_thread()
            if self.logger:
                self.logger.info("论坛页面关闭，已清理所有资源")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"论坛页面关闭时清理资源失败: {e}")
        finally:
            super().closeEvent(event)

    def showEvent(self, event):
        """页面显示事件 - 恢复数据加载"""
        super().showEvent(event)
        try:
            # 如果没有数据，重新加载
            if not self.posts:
                self._load_forum_data()
                if self.logger:
                    self.logger.info("论坛页面显示，重新加载数据")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"论坛页面显示时加载数据失败: {e}")

    def _load_user_likes(self):
        """加载用户点赞状态"""
        try:
            username = self._get_current_username()
            if username == 'guest':
                return

            # 异步加载用户点赞状态
            from PyQt5.QtCore import QThread, pyqtSignal

            class UserLikesLoader(QThread):
                likes_loaded = pyqtSignal(set)

                def __init__(self, username):
                    super().__init__()
                    self.username = username

                def run(self):
                    try:
                        try:
                            from utils.forum_api_client import get_forum_api_client
                        except ImportError:
                            from src.utils.forum_api_client import get_forum_api_client
                        # 获取主窗口引用
                        main_window = None
                        if hasattr(self.parent(), 'main_window'):
                            main_window = self.parent().main_window
                        api_client = get_forum_api_client(main_window=main_window)

                        success, response = api_client.get_user_likes(self.username)

                        if success:
                            likes_data = response.get('data', {}).get('likes', [])
                            liked_post_ids = set()
                            for like in likes_data:
                                if like.get('target_type') == 'post':
                                    liked_post_ids.add(like.get('target_id'))
                            self.likes_loaded.emit(liked_post_ids)
                        else:
                            self.likes_loaded.emit(set())

                    except Exception:
                        self.likes_loaded.emit(set())

            # 启动加载线程
            self.likes_loader = UserLikesLoader(username)
            self.likes_loader.likes_loaded.connect(self._on_user_likes_loaded)
            self.likes_loader.start()

        except Exception as e:
            if self.logger:
                self.logger.warning(f"加载用户点赞状态失败: {e}")

    def _on_user_likes_loaded(self, liked_post_ids):
        """用户点赞状态加载完成"""
        try:
            self.user_likes = liked_post_ids

            # 更新所有帖子卡片的点赞状态
            for i in range(self.posts_layout.count()):
                item = self.posts_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    if hasattr(widget, 'post_data') and hasattr(widget, 'update_like_status'):
                        post_id = widget.post_data.get('id')
                        if post_id:
                            is_liked = post_id in self.user_likes
                            likes_count = widget.post_data.get('likes_count', 0)
                            widget.update_like_status(likes_count, is_liked)

            if self.logger:
                self.logger.info(f"用户点赞状态加载完成，共 {len(liked_post_ids)} 个点赞")

        except Exception as e:
            if self.logger:
                self.logger.error(f"处理用户点赞状态失败: {e}")
