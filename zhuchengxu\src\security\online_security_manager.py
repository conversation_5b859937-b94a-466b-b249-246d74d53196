#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线安全管理器
集成在线认证和安全防护功能
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
from typing import Dict, Any, Optional, Callable
import threading
import time

try:
    from .online_auth import OnlineAuthClient
    from utils.logger import get_logger
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from security.online_auth import OnlineAuthClient
    from utils.logger import get_logger


class OnlineSecurityManager:
    """在线安全管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化在线安全管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger()
        
        # 初始化认证客户端
        self.auth_client = OnlineAuthClient(config)
        
        # 设置回调函数
        self.auth_client.on_kicked_out = self._handle_kicked_out
        self.auth_client.on_auth_expired = self._handle_auth_expired
        
        # 状态
        self.is_authorized = False
        self.login_dialog = None
        
        # 回调函数
        self.on_login_success: Optional[Callable] = None
        self.on_login_failed: Optional[Callable] = None
        self.on_logout: Optional[Callable] = None
    
    def initialize(self) -> bool:
        """初始化安全系统"""
        try:
            self.logger.info("🔐 初始化在线认证系统...")
            
            # 尝试自动登录
            if self.auth_client.auto_login():
                self.is_authorized = True
                user_info = self.auth_client.get_user_info()
                self.logger.info(f"✅ 自动登录成功，欢迎 {user_info.get('username', 'Unknown')}")
                
                if self.on_login_success:
                    self.on_login_success(user_info)
                
                return True
            else:
                self.logger.info("🔑 需要手动登录")
                return self._show_login_dialog()
                
        except Exception as e:
            self.logger.error(f"❌ 在线认证系统初始化失败: {e}")
            return False
    
    def _show_login_dialog(self) -> bool:
        """显示登录对话框"""
        try:
            # 创建根窗口（如果不存在）
            root = tk.Tk()
            root.withdraw()  # 隐藏根窗口

            # 创建登录窗口
            self.login_dialog = tk.Toplevel(root)
            self.login_dialog.title("用户登录 - Augment Tool")
            self.login_dialog.geometry("400x300")
            self.login_dialog.resizable(False, False)

            # 居中显示
            self.login_dialog.transient(root)
            self.login_dialog.grab_set()

            # 窗口居中
            self.login_dialog.update_idletasks()
            x = (self.login_dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (self.login_dialog.winfo_screenheight() // 2) - (300 // 2)
            self.login_dialog.geometry(f"400x300+{x}+{y}")

            # 创建登录界面
            self._create_login_ui()

            # 等待登录结果
            self.login_dialog.wait_window()

            # 清理根窗口
            try:
                root.destroy()
            except:
                pass

            return self.is_authorized

        except Exception as e:
            self.logger.error(f"显示登录对话框失败: {e}")
            return False
    
    def _create_login_ui(self):
        """创建登录界面"""
        # 主框架
        main_frame = tk.Frame(self.login_dialog, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔐 用户登录", 
                              font=("Microsoft YaHei", 16, "bold"),
                              bg='#f0f0f0', fg='#333333')
        title_label.pack(pady=(0, 20))
        
        # 用户名
        username_frame = tk.Frame(main_frame, bg='#f0f0f0')
        username_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(username_frame, text="用户名:", 
                font=("Microsoft YaHei", 10),
                bg='#f0f0f0', fg='#333333').pack(anchor='w')
        
        self.username_entry = tk.Entry(username_frame, 
                                      font=("Microsoft YaHei", 10),
                                      width=30)
        self.username_entry.pack(fill='x', pady=(5, 0))
        self.username_entry.focus()
        
        # 密码
        password_frame = tk.Frame(main_frame, bg='#f0f0f0')
        password_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(password_frame, text="密码:", 
                font=("Microsoft YaHei", 10),
                bg='#f0f0f0', fg='#333333').pack(anchor='w')
        
        self.password_entry = tk.Entry(password_frame, 
                                      font=("Microsoft YaHei", 10),
                                      width=30, show='*')
        self.password_entry.pack(fill='x', pady=(5, 0))
        
        # 状态标签
        self.status_label = tk.Label(main_frame, text="", 
                                    font=("Microsoft YaHei", 9),
                                    bg='#f0f0f0', fg='#ff0000')
        self.status_label.pack(pady=(0, 10))
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill='x')
        
        # 登录按钮
        login_btn = tk.Button(button_frame, text="登录",
                             font=("Microsoft YaHei", 10, "bold"),
                             bg='#007acc', fg='white',
                             width=10, height=2,
                             command=self._do_login)
        login_btn.pack(side='left', padx=(0, 10))
        
        # 取消按钮
        cancel_btn = tk.Button(button_frame, text="取消",
                              font=("Microsoft YaHei", 10),
                              bg='#cccccc', fg='#333333',
                              width=10, height=2,
                              command=self._cancel_login)
        cancel_btn.pack(side='left')
        
        # 帮助信息
        help_frame = tk.Frame(main_frame, bg='#f0f0f0')
        help_frame.pack(fill='x', pady=(20, 0))
        
        help_text = """💡 测试账号:
用户名: admin, 密码: admin123
用户名: test, 密码: test123

📧 如需正式账号请联系:
QQ群: 735821698"""
        
        help_label = tk.Label(help_frame, text=help_text,
                             font=("Microsoft YaHei", 8),
                             bg='#f0f0f0', fg='#666666',
                             justify='left')
        help_label.pack(anchor='w')
        
        # 绑定回车键
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda e: self._do_login())
    
    def _do_login(self):
        """执行登录"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            self.status_label.config(text="请输入用户名和密码", fg='#ff0000')
            return
        
        # 显示登录中状态
        self.status_label.config(text="正在登录...", fg='#0066cc')
        self.login_dialog.update()
        
        # 在后台线程中执行登录
        def login_thread():
            try:
                result = self.auth_client.login(username, password)
                
                # 在主线程中更新UI
                self.login_dialog.after(0, self._handle_login_result, result)
                
            except Exception as e:
                error_result = {
                    'success': False,
                    'message': f'登录异常: {e}'
                }
                self.login_dialog.after(0, self._handle_login_result, error_result)
        
        threading.Thread(target=login_thread, daemon=True).start()
    
    def _handle_login_result(self, result: Dict[str, Any]):
        """处理登录结果"""
        if result['success']:
            self.is_authorized = True
            user_info = result.get('user_info', {})
            
            self.logger.info(f"✅ 登录成功，欢迎 {user_info.get('username', 'Unknown')}")
            
            if self.on_login_success:
                self.on_login_success(user_info)
            
            # 关闭登录对话框
            self.login_dialog.destroy()
        else:
            # 显示错误信息
            error_msg = result.get('message', '登录失败')
            self.status_label.config(text=error_msg, fg='#ff0000')
            
            if self.on_login_failed:
                self.on_login_failed(error_msg)
    
    def _cancel_login(self):
        """取消登录"""
        self.is_authorized = False
        self.login_dialog.destroy()
    
    def _handle_kicked_out(self, message: str):
        """处理被踢出事件"""
        self.logger.warning(f"⚠️ 被踢出: {message}")
        self.is_authorized = False

        # 显示提示对话框
        def show_dialog():
            # 创建临时根窗口
            temp_root = tk.Tk()
            temp_root.withdraw()

            messagebox.showwarning(
                "会话已失效",
                f"您的账号在其他地方登录，当前会话已失效。\n\n"
                f"详情: {message}\n\n"
                f"如果不是您本人操作，请立即修改密码。"
            )

            temp_root.destroy()

            # 重新显示登录对话框
            self._show_login_dialog()

        # 在主线程中显示对话框
        if hasattr(self, 'login_dialog') and self.login_dialog:
            self.login_dialog.after(0, show_dialog)
        else:
            show_dialog()
    
    def _handle_auth_expired(self, message: str):
        """处理认证过期事件"""
        self.logger.warning(f"⚠️ 认证过期: {message}")
        self.is_authorized = False

        # 显示提示对话框
        def show_dialog():
            # 创建临时根窗口
            temp_root = tk.Tk()
            temp_root.withdraw()

            messagebox.showinfo(
                "会话已过期",
                f"您的登录会话已过期，请重新登录。\n\n"
                f"详情: {message}"
            )

            temp_root.destroy()

            # 重新显示登录对话框
            self._show_login_dialog()

        # 在主线程中显示对话框
        if hasattr(self, 'login_dialog') and self.login_dialog:
            self.login_dialog.after(0, show_dialog)
        else:
            show_dialog()
    
    def logout(self) -> bool:
        """用户登出"""
        try:
            if self.auth_client.logout():
                self.is_authorized = False
                self.logger.info("✅ 用户已登出")
                
                if self.on_logout:
                    self.on_logout()
                
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"登出失败: {e}")
            return False
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self.is_authorized and self.auth_client.is_logged_in()
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if self.is_authorized:
            return self.auth_client.get_user_info()
        return None

    def get_username(self) -> Optional[str]:
        """获取当前用户名"""
        user_info = self.get_user_info()
        if user_info:
            return user_info.get('username')
        return None
    
    def get_auth_status(self) -> Dict[str, Any]:
        """获取认证状态"""
        status = self.auth_client.get_auth_status()
        status['authorized'] = self.is_authorized
        return status
