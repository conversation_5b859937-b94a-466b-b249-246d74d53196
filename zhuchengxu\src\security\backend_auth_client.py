#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后台认证客户端
对接后端网站的认证系统
"""

import json
import time
import hashlib
import requests
import threading
import os
import uuid
import platform
from typing import Dict, Any, Optional, Callable

try:
    from src.utils.logger import get_logger
    from src.utils.path_utils import get_path
except ImportError:
    import logging
    def get_logger():
        return logging.getLogger(__name__)
    def get_path(*args):
        return os.path.join(*args)


class BackendAuthClient:
    """后台认证客户端"""
    
    def __init__(self, api_url: str = "http://127.0.0.1:777/api.php"):
        """
        初始化认证客户端
        
        Args:
            api_url: 后台API地址
        """
        self.api_url = api_url
        self.logger = get_logger()
        
        # 认证状态
        self.is_authenticated = False
        self.current_user = None
        self.access_token = None
        self.device_id = self._generate_device_id()
        
        # 心跳检测
        self.heartbeat_thread = None
        self.heartbeat_running = False
        self.heartbeat_interval = 60  # 60秒

        # 网络异常处理
        self.network_error_count = 0
        self.max_network_errors = 3  # 最大连续网络错误次数
        self.network_retry_delay = 30  # 网络错误后重试延迟（秒）
        self.last_network_error_time = 0
        self.network_recovery_threshold = 300  # 5分钟后重置错误计数
        
        # 🔧 本地存储 - 使用与main.py一致的路径检测逻辑
        import sys
        import os
        # 直接使用main.py中设置的环境变量
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
        if config_dir:
            self.auth_cache_file = os.path.join(config_dir, "auth_cache.json")
        else:
            # 备选方案：使用sys.argv[0]
            if sys.argv and len(sys.argv) > 0:
                argv_path = os.path.abspath(sys.argv[0])
                if argv_path.endswith('.exe'):
                    self.auth_cache_file = os.path.join(os.path.dirname(argv_path), "auth_cache.json")
                else:
                    self.auth_cache_file = os.path.join(os.path.dirname(argv_path), "auth_cache.json")
            else:
                self.auth_cache_file = os.path.join(os.getcwd(), "auth_cache.json")
        
        # HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AugmentTool/1.0.0',
            'Content-Type': 'application/json'
        })
        
        # 回调函数
        self.on_auth_success: Optional[Callable] = None
        self.on_auth_failed: Optional[Callable] = None
        self.on_auth_expired: Optional[Callable] = None
        
        self.logger.info(f"🔐 后台认证客户端初始化完成，设备ID: {self.device_id[:8]}...")
    
    def _generate_device_id(self) -> str:
        """生成设备唯一ID"""
        try:
            # 使用多个硬件特征生成设备ID
            machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
            device_id = hashlib.sha256(machine_info.encode()).hexdigest()
            return device_id
        except Exception as e:
            self.logger.warning(f"生成设备ID失败，使用随机ID: {e}")
            return str(uuid.uuid4()).replace('-', '')
    
    def _make_request(self, action: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送API请求"""
        try:
            # 将action包含在JSON数据中，而不是URL参数中
            request_data = data.copy() if data else {}
            request_data['action'] = action

            # 调试日志
            self.logger.info(f"📡 API请求: {action} -> {self.api_url}")
            if request_data:
                debug_data = request_data.copy()
                if 'password' in debug_data:
                    debug_data['password'] = '*' * len(debug_data['password']) if debug_data['password'] else ''
                self.logger.info(f"📤 请求数据: {debug_data}")

            # 添加认证头
            headers = {}
            if self.access_token:
                headers['Authorization'] = f'Bearer {self.access_token}'

            # 尝试使用异步网络请求
            try:
                # 优先尝试简化版异步网络
                from src.utils.simple_async_network import get_simple_network_manager
                network_manager = get_simple_network_manager()

                # 使用简化异步请求
                if hasattr(network_manager, 'async_request'):
                    import threading
                    result_container = {'response': None, 'error': None, 'done': False}

                    def async_callback(response_data):
                        result_container['response'] = response_data
                        result_container['done'] = True

                    # 发送异步POST请求
                    network_manager.async_request('POST', self.api_url, data=request_data,
                                                 headers=headers, callback=async_callback, timeout=10)

                    # 等待响应（最多等待10秒）
                    wait_time = 0
                    while not result_container['done'] and wait_time < 10:
                        time.sleep(0.05)  # 50ms检查间隔
                        wait_time += 0.05

                    if result_container['done'] and result_container['response']:
                        async_response = result_container['response']
                        if async_response['success']:
                            # 模拟requests响应对象
                            class MockResponse:
                                def __init__(self, data):
                                    self.status_code = data['status_code']
                                    self._json_data = data['data']
                                    self.headers = data.get('headers', {})
                                    self.text = json.dumps(data['data']) if isinstance(data['data'], dict) else str(data['data'])

                                def json(self):
                                    return self._json_data

                            response = MockResponse(async_response)
                            self.logger.info(f"📡 使用异步网络请求: {action}")
                        else:
                            raise Exception(f"异步请求失败: {async_response.get('error', 'Unknown error')}")
                    else:
                        raise Exception("异步请求超时")
                else:
                    raise Exception("异步网络不可用")

            except Exception as async_error:
                # 降级到同步请求
                self.logger.info(f"📡 降级到同步网络请求: {async_error}")
                response = self.session.post(
                    self.api_url,
                    json=request_data,
                    headers=headers,
                    timeout=10
                )

            # 调试日志：HTTP响应
            self.logger.info(f"📥 HTTP状态码: {response.status_code}")
            self.logger.info(f"📥 响应内容: {response.text[:500]}...")  # 只显示前500字符
            
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    # 确保返回的是字典
                    if isinstance(json_response, dict):
                        return json_response
                    else:
                        self.logger.error(f"API响应不是字典格式: {type(json_response)}")
                        return {
                            'success': False,
                            'code': 'INVALID_RESPONSE_FORMAT',
                            'message': f'API响应格式错误: {type(json_response)}'
                        }
                except ValueError as e:
                    self.logger.error(f"JSON解析失败: {e}")
                    return {
                        'success': False,
                        'code': 'JSON_PARSE_ERROR',
                        'message': f'JSON解析失败: {e}'
                    }
            else:
                self.logger.error(f"API请求失败: {response.status_code}")
                return {
                    'success': False,
                    'code': 'HTTP_ERROR',
                    'message': f'HTTP错误: {response.status_code}'
                }
                
        except requests.exceptions.Timeout:
            self.logger.warning("API请求超时 - 可能是网络延迟或服务器繁忙")
            return {
                'success': False,
                'code': 'TIMEOUT',
                'message': '网络超时，可能是网络延迟或VPN连接问题'
            }
        except requests.exceptions.ConnectionError as e:
            # 更详细的连接错误分析
            error_str = str(e).lower()
            if 'name or service not known' in error_str or 'nodename nor servname provided' in error_str:
                error_msg = '无法解析服务器地址，请检查域名或网络设置'
            elif 'connection refused' in error_str:
                error_msg = '服务器拒绝连接，请检查服务器状态'
            elif 'timeout' in error_str:
                error_msg = '连接超时，可能是网络不稳定或VPN问题'
            else:
                error_msg = '网络连接失败，请检查网络连接或代理设置'

            self.logger.warning(f"网络连接错误: {error_msg}")
            return {
                'success': False,
                'code': 'CONNECTION_ERROR',
                'message': error_msg
            }
        except Exception as e:
            self.logger.error(f"API请求异常: {e}")
            return {
                'success': False,
                'code': 'UNKNOWN_ERROR',
                'message': f'请求异常: {e}'
            }
    
    def login(self, username: str, password: str, qq_number: str = '') -> Dict[str, Any]:
        """
        用户登录

        Args:
            username: 用户名
            password: 密码
            qq_number: QQ号（可选）

        Returns:
            登录结果
        """
        try:
            self.logger.info(f"🔐 尝试登录用户: {username}")

            # 准备登录数据
            login_data = {
                'username': username,
                'password': password,
                'device_id': self.device_id,
                'qq_number': qq_number
            }

            # 调试日志：显示发送的数据
            debug_data = login_data.copy()
            debug_data['password'] = '*' * len(password) if password else ''
            self.logger.info(f"📤 发送登录数据: {debug_data}")
            
            # 发送登录请求
            result = self._make_request('login', login_data)

            # 检查result是否为None
            if result is None:
                error_msg = "API请求返回空响应"
                self.logger.error(error_msg)
                if self.on_auth_failed:
                    self.on_auth_failed(error_msg)
                return {
                    'success': False,
                    'message': error_msg
                }

            # 检查result是否为字典
            if not isinstance(result, dict):
                error_msg = f"API响应格式错误: {type(result)}"
                self.logger.error(error_msg)
                if self.on_auth_failed:
                    self.on_auth_failed(error_msg)
                return {
                    'success': False,
                    'message': error_msg
                }

            if result.get('success'):
                # 登录成功
                data = result.get('data', {})
                if isinstance(data, dict):
                    self.access_token = data.get('token')
                    self.current_user = data.get('user')
                else:
                    # 兼容旧格式
                    self.access_token = result.get('token')
                    self.current_user = result.get('user')

                self.is_authenticated = True

                # 保存新的认证缓存（令牌轮换后的新令牌）
                self._save_auth_cache()
                
                # 保存认证信息
                self._save_auth_cache()
                
                # 启动心跳检测
                self._start_heartbeat()
                
                self.logger.info(f"✅ 登录成功，欢迎 {username}")
                
                # 触发成功回调
                if self.on_auth_success:
                    self.on_auth_success(self.current_user)
                
                return {
                    'success': True,
                    'message': '登录成功',
                    'user': self.current_user
                }
            else:
                # 登录失败
                error_msg = result.get('message', '登录失败')
                self.logger.warning(f"❌ 登录失败: {error_msg}")
                
                # 触发失败回调
                if self.on_auth_failed:
                    self.on_auth_failed(error_msg)
                
                return {
                    'success': False,
                    'message': error_msg,
                    'code': result.get('code', 'LOGIN_FAILED')
                }
                
        except Exception as e:
            error_msg = f"登录异常: {e}"
            self.logger.error(error_msg)
            
            if self.on_auth_failed:
                self.on_auth_failed(error_msg)
            
            return {
                'success': False,
                'message': error_msg
            }
    
    def verify_token(self) -> bool:
        """验证当前令牌是否有效"""
        try:
            if not self.access_token:
                return False

            result = self._make_request('verify')

            # 检查result是否为None或非字典
            if result is None or not isinstance(result, dict):
                self.logger.warning(f"令牌验证响应格式错误: {type(result)}")
                self._clear_auth_data()
                return False

            if result.get('success'):
                # 更新用户信息
                self.current_user = result.get('user', self.current_user)
                return True
            else:
                # 令牌无效
                self.logger.warning(f"令牌验证失败: {result.get('message')}")
                self._clear_auth_data()
                return False
                
        except Exception as e:
            self.logger.error(f"令牌验证异常: {e}")
            return False
    
    def logout(self) -> bool:
        """用户登出"""
        try:
            self.logger.info("🚪 用户登出")

            logout_success = False

            if self.access_token and self.device_id:
                # 发送登出请求到服务器
                logout_data = {'device_id': self.device_id}
                result = self._make_request('logout', logout_data)

                if result and result.get('success'):
                    self.logger.info("✅ 服务器端登出成功，设备已解绑")
                    logout_success = True
                else:
                    error_msg = result.get('message', '未知错误') if result else '网络请求失败'
                    self.logger.warning(f"⚠️ 服务器端登出失败: {error_msg}")
                    # 即使服务器端失败，也继续清理本地数据
            else:
                self.logger.info("ℹ️ 无有效令牌或设备ID，仅清理本地数据")

            # 清理本地状态（无论服务器端是否成功）
            self._clear_auth_data()

            if logout_success:
                self.logger.info("✅ 完整登出成功（服务器端 + 本地）")
            else:
                self.logger.info("✅ 本地登出成功（服务器端可能失败）")

            return logout_success

        except Exception as e:
            self.logger.error(f"登出异常: {e}")
            # 即使出错也清理本地状态
            self._clear_auth_data()
            self.logger.info("✅ 异常情况下的本地数据清理完成")
            return False
    
    def auto_login(self) -> bool:
        """尝试自动登录（使用缓存的认证信息）"""
        try:
            self.logger.info("🔄 尝试自动登录...")

            # 加载缓存的认证信息
            if not self._load_auth_cache():
                self.logger.info("❌ 没有可用的认证缓存")
                return False

            # 验证令牌是否仍然有效
            if self.verify_token():
                # 启动心跳检测
                self._start_heartbeat()
                self.logger.info("✅ 自动登录成功")

                if self.on_auth_success:
                    self.on_auth_success(self.current_user)

                return True
            else:
                self.logger.info("❌ 自动登录失败，令牌已失效（可能被新登录替换）")
                # 清理失效的缓存
                self._clear_auth_data()
                return False

        except Exception as e:
            self.logger.error(f"自动登录异常: {e}")
            return False
    
    def _save_auth_cache(self):
        """保存认证缓存"""
        try:
            cache_data = {
                'access_token': self.access_token,
                'current_user': self.current_user,
                'device_id': self.device_id,
                'save_time': time.time()
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.auth_cache_file), exist_ok=True)
            
            with open(self.auth_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug("认证缓存已保存")
            
        except Exception as e:
            self.logger.error(f"保存认证缓存失败: {e}")
    
    def _load_auth_cache(self) -> bool:
        """加载认证缓存"""
        try:
            if not os.path.exists(self.auth_cache_file):
                return False
            
            with open(self.auth_cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查设备ID是否匹配
            if cache_data.get('device_id') != self.device_id:
                self.logger.warning("设备ID不匹配，清理认证缓存")
                self._clear_auth_data()
                return False
            
            # 检查缓存是否过期（24小时）
            save_time = cache_data.get('save_time', 0)
            if time.time() - save_time > 24 * 3600:
                self.logger.info("认证缓存已过期")
                self._clear_auth_data()
                return False
            
            # 恢复认证状态
            self.access_token = cache_data.get('access_token')
            self.current_user = cache_data.get('current_user')
            self.is_authenticated = True
            
            self.logger.info("✅ 认证缓存加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"加载认证缓存失败: {e}")
            self._clear_auth_data()
            return False
    
    def _clear_auth_data(self):
        """清理认证数据"""
        # 停止心跳
        self._stop_heartbeat()
        
        # 清理状态
        self.is_authenticated = False
        self.current_user = None
        self.access_token = None
        
        # 删除缓存文件
        try:
            if os.path.exists(self.auth_cache_file):
                os.remove(self.auth_cache_file)
        except Exception as e:
            self.logger.error(f"删除认证缓存失败: {e}")
    
    def _start_heartbeat(self):
        """启动心跳检测"""
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            return
        
        self.heartbeat_running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        self.logger.info("💓 心跳检测已启动")
    
    def _stop_heartbeat(self):
        """停止心跳检测"""
        self.heartbeat_running = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
        self.logger.info("💓 心跳检测已停止")
    
    def _heartbeat_loop(self):
        """心跳检测循环 - 智能网络异常处理"""
        while self.heartbeat_running and self.is_authenticated:
            try:
                time.sleep(self.heartbeat_interval)

                if not self.heartbeat_running:
                    break

                # 发送心跳
                heartbeat_data = {'device_id': self.device_id}
                result = self._make_request('heartbeat', heartbeat_data)

                # 检查result是否为None或非字典
                if result is None or not isinstance(result, dict):
                    self.logger.warning(f"心跳响应格式错误: {type(result)}")
                    self._handle_network_error("响应格式错误")
                    continue

                if result.get('success'):
                    self.logger.debug("💓 心跳正常")
                    # 心跳成功，重置网络错误计数
                    self._reset_network_error_count()
                else:
                    error_code = result.get('code', '')
                    error_message = result.get('message', '未知错误')

                    # 区分网络错误和认证错误
                    if error_code in ['TIMEOUT', 'CONNECTION_ERROR']:
                        # 网络相关错误，进行智能处理
                        self._handle_network_error(error_message)
                    elif error_code in ['INVALID_TOKEN', 'USER_EXPIRED', 'USER_NOT_FOUND']:
                        # 认证相关错误，立即处理
                        self.logger.warning(f"💔 认证失效: {error_message}")
                        self.is_authenticated = False
                        if self.on_auth_expired:
                            self.on_auth_expired(error_message)
                        break
                    else:
                        # 其他错误，记录但不立即断开
                        self.logger.warning(f"💔 心跳失败: {error_message}")
                        self._handle_network_error(error_message)

            except Exception as e:
                self.logger.error(f"心跳检测异常: {e}")
                self._handle_network_error(f"异常: {e}")

    def _handle_network_error(self, error_message: str):
        """处理网络错误"""
        current_time = time.time()

        # 检查是否需要重置错误计数（超过恢复阈值时间）
        if current_time - self.last_network_error_time > self.network_recovery_threshold:
            self.network_error_count = 0
            self.logger.info("🔄 网络错误计数已重置")

        self.network_error_count += 1
        self.last_network_error_time = current_time

        if self.network_error_count <= self.max_network_errors:
            # 还在容忍范围内，记录警告但继续尝试
            self.logger.warning(f"🌐 网络异常 ({self.network_error_count}/{self.max_network_errors}): {error_message}")
            self.logger.info(f"💡 可能原因：网络断开、VPN/代理连接、服务器临时不可达")

            # 使用递增的重试延迟
            retry_delay = min(self.network_retry_delay * self.network_error_count, 120)  # 最大2分钟
            self.logger.info(f"⏰ {retry_delay}秒后重试心跳检测...")
            time.sleep(retry_delay)
        else:
            # 超过最大错误次数，认为是持续的网络问题
            self.logger.error(f"🚫 连续网络错误超过限制 ({self.network_error_count}次)")
            self.logger.error("💔 可能原因：")
            self.logger.error("   1. 网络连接中断")
            self.logger.error("   2. VPN/代理服务器问题")
            self.logger.error("   3. 防火墙阻止连接")
            self.logger.error("   4. 认证服务器维护")

            # 触发认证过期回调，但使用特殊的网络错误消息
            self.is_authenticated = False
            if self.on_auth_expired:
                network_error_msg = (
                    f"网络连接异常，无法维持认证状态\n\n"
                    f"可能原因：\n"
                    f"• 网络连接中断\n"
                    f"• VPN/代理服务器问题\n"
                    f"• 防火墙阻止连接\n\n"
                    f"请检查网络连接后重新登录"
                )
                self.on_auth_expired(network_error_msg)

    def _reset_network_error_count(self):
        """重置网络错误计数"""
        if self.network_error_count > 0:
            self.logger.info(f"✅ 网络连接恢复，重置错误计数 (之前: {self.network_error_count}次)")
            self.network_error_count = 0
            self.last_network_error_time = 0
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        return self.current_user
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self.is_authenticated and self.access_token is not None
