<?php
/**
 * Augment认证系统 - API接口
 * 为前端客户端提供认证服务
 */

declare(strict_types=1);

// 清理输出缓冲，确保干净的JSON输出
while (ob_get_level()) {
    ob_end_clean();
}

// 启动会话
session_start();

// 错误报告 - 开发环境显示错误，生产环境记录到日志
error_reporting(E_ALL);
ini_set('display_errors', '0');
ini_set('log_errors', '1');

// 安全头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *'); // 生产环境应限制为特定域名
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入核心类
require_once __DIR__ . '/includes/Database.php';
require_once __DIR__ . '/includes/AuthManager.php';
require_once __DIR__ . '/includes/ApiResponse.php';
require_once __DIR__ . '/includes/Logger.php';

try {
    // 初始化组件
    $database = new Database();
    $authManager = new AuthManager($database);
    $logger = new Logger();
    
    // 获取请求参数
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true) ?: [];
    $data = array_merge($_POST, $input);

    // 获取action，优先从URL参数，然后从POST数据，最后从JSON数据
    $action = $_GET['action'] ?? $_POST['action'] ?? $input['action'] ?? '';

    // 记录API请求
    $logger->apiCall($action, $_SERVER['REQUEST_METHOD'], [
        'data' => $data,
        'raw_input' => $rawInput,
        'content_length' => strlen($rawInput),
        'request_time' => date('Y-m-d H:i:s')
    ]);
    
    // 检查维护模式（除了ping和status接口）
    if (!in_array($action, ['ping', 'status']) && isMaintenanceMode()) {
        $logger->warning("维护模式阻止API访问", ['action' => $action, 'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
        ApiResponse::error('MAINTENANCE_MODE', '系统正在维护中，请稍后再试', [
            'maintenance_mode' => true,
            'retry_after' => 3600 // 建议1小时后重试
        ]);
        exit;
    }

    // 路由处理
    switch ($action) {
        case 'login':
            handleLogin($authManager, $data, $logger, $rawInput);
            break;

        case 'auth':
            handleAuth($authManager, $data, $logger);
            break;

        case 'verify':
            handleVerify($authManager, $logger);
            break;

        case 'heartbeat':
            handleHeartbeat($authManager, $data, $logger);
            break;

        case 'logout':
            handleLogout($authManager, $data, $logger);
            break;

        case 'license_info':
            handleLicenseInfo($authManager, $logger);
            break;

        case 'device_bind':
            handleDeviceBind($authManager, $data, $logger);
            break;

        case 'device_unbind':
            handleDeviceUnbind($authManager, $data, $logger);
            break;

        case 'ping':
            handlePing();
            break;

        case 'handshake':
            handleHandshake($data, $logger);
            break;

        case 'verify_client':
            handleVerifyClient($data, $logger);
            break;

        case 'status':
            handleStatus();
            break;

        case 'get_qq_config':
            handleGetQQConfig($database, $logger);
            break;

        case 'get_announcements':
            handleGetAnnouncements($database, $logger);
            break;

        case 'mark_announcement_read':
            handleMarkAnnouncementRead($database, $data, $logger);
            break;

        default:
            ApiResponse::error('INVALID_ACTION', '无效的操作');
    }
    
} catch (Exception $e) {
    $logger->error("API异常", ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    ApiResponse::error('INTERNAL_ERROR', '服务器内部错误');
}

/**
 * 处理用户名密码登录
 */
function handleLogin(AuthManager $authManager, array $data, Logger $logger, string $rawInput = ''): void
{
    $username = $data['username'] ?? '';
    $password = $data['password'] ?? '';
    $deviceId = $data['device_id'] ?? '';
    $qqNumber = $data['qq_number'] ?? '';

    if (empty($username) || empty($password) || empty($deviceId)) {
        $logger->warning("登录参数缺失", [
            'username' => $username ? 'provided' : 'missing',
            'password' => $password ? 'provided' : 'missing',
            'device_id' => $deviceId ? 'provided' : 'missing',
            'all_data' => $data,
            'raw_input' => $rawInput
        ]);

        $missingParams = [];
        if (empty($username)) $missingParams[] = 'username';
        if (empty($password)) $missingParams[] = 'password';
        if (empty($deviceId)) $missingParams[] = 'device_id';

        ApiResponse::error('MISSING_PARAMS', '缺少必需参数: ' . implode(', ', $missingParams), [
            'missing_params' => $missingParams,
            'received_data' => array_keys($data)
        ]);
        return;
    }



    // 使用用户名作为授权码进行验证
    $result = $authManager->authenticateWithPassword($username, $password, $deviceId, $qqNumber);

    if ($result['success']) {
        $logger->info("用户登录成功", ['username' => substr($username, 0, 8) . '...', 'device_id' => $deviceId, 'qq_number' => $qqNumber ? substr($qqNumber, 0, 3) . '***' : '未填写']);
        ApiResponse::success('登录成功', [
            'token' => $result['token'],
            'user' => $result['user'],
            'expires_at' => $result['expires_at']
        ]);
    } else {
        $logger->warning("用户登录失败", ['username' => substr($username, 0, 8) . '...', 'reason' => $result['message']]);
        ApiResponse::error($result['code'] ?? 'LOGIN_FAILED', $result['message']);
    }
}

/**
 * 处理软件授权验证
 */
function handleAuth(AuthManager $authManager, array $data, Logger $logger): void
{
    $licenseKey = $data['license_key'] ?? '';
    $deviceId = $data['device_id'] ?? '';

    if (empty($licenseKey) || empty($deviceId)) {
        ApiResponse::error('MISSING_PARAMS', '授权码和设备ID不能为空');
        return;
    }

    $result = $authManager->authenticate($licenseKey, $deviceId);

    if ($result['success']) {
        $logger->info("软件授权成功", ['license_key' => substr($licenseKey, 0, 8) . '...', 'device_id' => $deviceId]);
        ApiResponse::success('授权验证成功', [
            'token' => $result['token'],
            'license' => $result['license'],
            'expires_at' => $result['expires_at']
        ]);
    } else {
        $logger->warning("软件授权失败", ['license_key' => substr($licenseKey, 0, 8) . '...', 'reason' => $result['message']]);
        ApiResponse::error($result['code'] ?? 'AUTH_FAILED', $result['message']);
    }
}

/**
 * 处理令牌验证
 */
function handleVerify(AuthManager $authManager, Logger $logger): void
{
    $token = getBearerToken();
    
    if (empty($token)) {
        ApiResponse::error('MISSING_TOKEN', '缺少访问令牌');
        return;
    }
    
    $result = $authManager->verifyToken($token);
    
    if ($result['success']) {
        ApiResponse::success('令牌有效', ['user' => $result['user']]);
    } else {
        $logger->warning("令牌验证失败", ['token' => substr($token, 0, 10) . '...', 'reason' => $result['message']]);
        ApiResponse::error($result['code'] ?? 'INVALID_TOKEN', $result['message']);
    }
}

/**
 * 处理心跳检测
 */
function handleHeartbeat(AuthManager $authManager, array $data, Logger $logger): void
{
    $token = getBearerToken();
    $deviceId = $data['device_id'] ?? '';
    
    if (empty($token)) {
        ApiResponse::error('MISSING_TOKEN', '缺少访问令牌');
        return;
    }
    
    $result = $authManager->heartbeat($token, $deviceId);
    
    if ($result['success']) {
        ApiResponse::success('心跳正常', ['timestamp' => time()]);
    } else {
        $logger->warning("心跳检测失败", ['device_id' => $deviceId, 'reason' => $result['message']]);
        ApiResponse::error($result['code'] ?? 'HEARTBEAT_FAILED', $result['message']);
    }
}

/**
 * 处理用户登出
 */
function handleLogout(AuthManager $authManager, array $data, Logger $logger): void
{
    $token = getBearerToken();
    $deviceId = $data['device_id'] ?? '';
    
    if (empty($token)) {
        ApiResponse::error('MISSING_TOKEN', '缺少访问令牌');
        return;
    }
    
    $result = $authManager->logout($token, $deviceId);
    
    if ($result['success']) {
        $logger->info("用户登出成功", ['device_id' => $deviceId]);
        ApiResponse::success('登出成功');
    } else {
        ApiResponse::error($result['code'] ?? 'LOGOUT_FAILED', $result['message']);
    }
}

/**
 * 处理授权信息查询
 */
function handleLicenseInfo(AuthManager $authManager, Logger $logger): void
{
    $token = getBearerToken();

    if (empty($token)) {
        ApiResponse::error('MISSING_TOKEN', '缺少访问令牌');
        return;
    }

    $result = $authManager->getLicenseInfo($token);

    if ($result['success']) {
        ApiResponse::success('获取成功', ['license' => $result['license']]);
    } else {
        ApiResponse::error($result['code'] ?? 'GET_LICENSE_FAILED', $result['message']);
    }
}

/**
 * 处理设备绑定
 */
function handleDeviceBind(AuthManager $authManager, array $data, Logger $logger): void
{
    $token = getBearerToken();
    $deviceId = $data['device_id'] ?? '';
    $deviceName = $data['device_name'] ?? '';

    if (empty($token) || empty($deviceId)) {
        ApiResponse::error('MISSING_PARAMS', '缺少必要参数');
        return;
    }

    $result = $authManager->bindDevice($token, $deviceId, $deviceName);

    if ($result['success']) {
        $logger->info("设备绑定成功", ['device_id' => $deviceId]);
        ApiResponse::success('设备绑定成功');
    } else {
        $logger->warning("设备绑定失败", ['device_id' => $deviceId, 'reason' => $result['message']]);
        ApiResponse::error($result['code'] ?? 'BIND_FAILED', $result['message']);
    }
}

/**
 * 处理设备解绑
 */
function handleDeviceUnbind(AuthManager $authManager, array $data, Logger $logger): void
{
    $token = getBearerToken();
    $deviceId = $data['device_id'] ?? '';

    if (empty($token) || empty($deviceId)) {
        ApiResponse::error('MISSING_PARAMS', '缺少必要参数');
        return;
    }

    $result = $authManager->unbindDevice($token, $deviceId);

    if ($result['success']) {
        $logger->info("设备解绑成功", ['device_id' => $deviceId]);
        ApiResponse::success('设备解绑成功');
    } else {
        $logger->warning("设备解绑失败", ['device_id' => $deviceId, 'reason' => $result['message']]);
        ApiResponse::error($result['code'] ?? 'UNBIND_FAILED', $result['message']);
    }
}

/**
 * 处理Ping请求
 */
function handlePing(): void
{
    ApiResponse::success('Pong', [
        'server_time' => date('Y-m-d H:i:s'),
        'version' => '1.0.0',
        'status' => 'running'
    ]);
}

/**
 * 处理握手请求
 */
function handleHandshake(array $data, Logger $logger): void
{
    // 记录接收到的数据用于调试
    $logger->info("握手请求数据", ['data' => $data]);

    $clientNonce = $data['client_nonce'] ?? '';
    $protocolVersion = $data['protocol_version'] ?? '';

    if (empty($clientNonce)) {
        $logger->warning("握手失败：客户端随机数为空", ['data' => $data]);
        ApiResponse::error('INVALID_NONCE', '客户端随机数无效');
        return;
    }

    // 放宽协议版本检查，支持更多版本
    if (!empty($protocolVersion) && !in_array($protocolVersion, ['1.0', '1.0.0', '2.0', '2.0.0'])) {
        $logger->warning("握手失败：协议版本不支持", ['version' => $protocolVersion, 'data' => $data]);
        ApiResponse::error('UNSUPPORTED_PROTOCOL', "协议版本不支持: {$protocolVersion}");
        return;
    }

    // 生成服务器随机数和挑战
    $serverNonce = bin2hex(random_bytes(16));
    $challenge = bin2hex(random_bytes(16));
    $sessionId = bin2hex(random_bytes(16));

    // 存储握手信息到会话（简化实现，实际应该存储到数据库或缓存）
    $_SESSION['handshake'] = [
        'client_nonce' => $clientNonce,
        'server_nonce' => $serverNonce,
        'challenge' => $challenge,
        'session_id' => $sessionId,
        'created_at' => time()
    ];

    $logger->info("握手请求", ['client_nonce' => substr($clientNonce, 0, 8) . '...', 'session_id' => $sessionId]);

    ApiResponse::success('握手成功', [
        'server_nonce' => $serverNonce,
        'challenge' => $challenge,
        'session_id' => $sessionId,
        'expires_in' => 300,
        'server_time' => date('Y-m-d H:i:s')
    ]);
}

/**
 * 处理客户端验证请求
 */
function handleVerifyClient(array $data, Logger $logger): void
{
    $clientNonce = $data['client_nonce'] ?? '';
    $challengeResponse = $data['challenge_response'] ?? '';
    $clientVersion = $data['client_version'] ?? '';
    $sessionId = $data['session_id'] ?? '';

    // 检查握手信息
    if (!isset($_SESSION['handshake'])) {
        ApiResponse::error('NO_HANDSHAKE', '握手会话丢失，请重试');
        return;
    }

    $handshake = $_SESSION['handshake'];

    // 检查会话是否过期
    if (time() - $handshake['created_at'] > 300) {
        ApiResponse::error('HANDSHAKE_EXPIRED', '握手会话过期');
        return;
    }

    // 验证客户端随机数
    if ($handshake['client_nonce'] !== $clientNonce) {
        ApiResponse::error('NONCE_MISMATCH', '随机数不匹配');
        return;
    }

    // 验证挑战响应
    $challengeString = $clientNonce . $handshake['server_nonce'] . $handshake['challenge'];
    $expectedResponse = hash('sha256', $challengeString);

    // 添加详细的调试日志
    $logger->info("挑战验证详情", [
        'client_nonce' => $clientNonce,
        'server_nonce' => $handshake['server_nonce'],
        'challenge' => $handshake['challenge'],
        'challenge_string' => $challengeString,
        'expected_response' => $expectedResponse,
        'received_response' => $challengeResponse,
        'match' => ($challengeResponse === $expectedResponse)
    ]);

    if ($challengeResponse !== $expectedResponse) {
        $logger->warning("挑战验证失败", [
            'expected' => substr($expectedResponse, 0, 16) . '...',
            'received' => substr($challengeResponse, 0, 16) . '...',
            'challenge_string' => $challengeString
        ]);
        ApiResponse::error('CHALLENGE_FAILED', '挑战验证失败', [
            'debug_info' => [
                'expected_prefix' => substr($expectedResponse, 0, 16),
                'received_prefix' => substr($challengeResponse, 0, 16)
            ]
        ]);
        return;
    }

    // 客户端版本检查已移除，允许所有版本

    $logger->info("客户端验证成功", ['session_id' => $sessionId, 'client_version' => $clientVersion]);

    ApiResponse::success('客户端验证成功', [
        'authenticated' => true,
        'server_info' => [
            'name' => 'Augment授权服务器',
            'version' => '1.0.0',
            'session_source' => 'new'
        ],
        'verification_details' => [
            'session_age' => time() - $handshake['created_at']
        ]
    ]);
}

/**
 * 处理系统状态查询
 */
function handleStatus(): void
{
    ApiResponse::success('系统运行正常', [
        'version' => '2.0.0',
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s'),
        'status' => 'online'
    ]);
}

/**
 * 处理QQ群配置获取
 */
function handleGetQQConfig(Database $database, Logger $logger): void
{
    try {
        // 获取所有QQ配置
        $configs = $database->fetchAll("SELECT config_key, config_value FROM qq_config WHERE 1=1");

        if (!$configs) {
            $logger->warning("QQ配置表为空或不存在");
            ApiResponse::error('NO_CONFIG', 'QQ配置不存在，请先初始化配置');
            return;
        }

        // 转换为键值对格式
        $configData = [];
        foreach ($configs as $config) {
            $configData[$config['config_key']] = $config['config_value'];
        }

        $logger->info("QQ配置获取成功", ['config_count' => count($configData)]);

        ApiResponse::success('QQ配置获取成功', $configData);

    } catch (Exception $e) {
        $logger->error("获取QQ配置失败", ['error' => $e->getMessage()]);
        ApiResponse::error('CONFIG_ERROR', '获取QQ配置失败: ' . $e->getMessage());
    }
}

/**
 * 处理公告获取
 */
function handleGetAnnouncements(Database $database, Logger $logger): void
{
    try {
        // 获取当前有效的公告（包含图片数据）
        $sql = "SELECT id, title, content, type, priority, start_time, end_time, target_version, images, click_count, created_at, updated_at
                FROM announcements
                WHERE status = 1
                AND (start_time IS NULL OR start_time <= NOW())
                AND (end_time IS NULL OR end_time >= NOW())
                ORDER BY priority DESC, created_at DESC";

        $announcements = $database->fetchAll($sql);

        if (!$announcements) {
            $logger->info("当前没有有效的公告");
            ApiResponse::success('获取公告成功', []);
            return;
        }

        // 处理公告数据
        $processedAnnouncements = [];
        foreach ($announcements as $announcement) {
            // 解析图片数据
            $images = [];
            if (!empty($announcement['images'])) {
                $imageData = json_decode($announcement['images'], true);
                if (is_array($imageData)) {
                    $images = $imageData;
                }
            }

            $processedAnnouncements[] = [
                'id' => (int)$announcement['id'],
                'title' => $announcement['title'],
                'content' => $announcement['content'],
                'type' => $announcement['type'],
                'priority' => (int)$announcement['priority'],
                'start_time' => $announcement['start_time'],
                'end_time' => $announcement['end_time'],
                'target_version' => $announcement['target_version'],
                'images' => $images,
                'click_count' => (int)$announcement['click_count'],
                'created_at' => $announcement['created_at'],
                'updated_at' => $announcement['updated_at']
            ];
        }

        $logger->info("公告获取成功", ['count' => count($processedAnnouncements)]);

        ApiResponse::success('公告获取成功', $processedAnnouncements);

    } catch (Exception $e) {
        $logger->error("获取公告失败", ['error' => $e->getMessage()]);
        ApiResponse::error('ANNOUNCEMENT_ERROR', '获取公告失败: ' . $e->getMessage());
    }
}

/**
 * 处理公告已读标记
 */
function handleMarkAnnouncementRead(Database $database, array $data, Logger $logger): void
{
    try {
        $announcementId = $data['announcement_id'] ?? '';

        if (empty($announcementId) || !is_numeric($announcementId)) {
            ApiResponse::error('INVALID_PARAMS', '公告ID无效');
            return;
        }

        // 增加点击次数
        $sql = "UPDATE announcements SET click_count = click_count + 1 WHERE id = ? AND status = 1";
        $stmt = $database->query($sql, [(int)$announcementId]);
        $result = $stmt->rowCount() > 0;

        if ($result) {
            $logger->info("公告已读标记成功", ['announcement_id' => $announcementId]);
            ApiResponse::success('标记成功');
        } else {
            $logger->warning("公告已读标记失败", ['announcement_id' => $announcementId]);
            ApiResponse::error('MARK_FAILED', '标记失败，公告不存在或已禁用');
        }

    } catch (Exception $e) {
        $logger->error("标记公告已读失败", ['error' => $e->getMessage()]);
        ApiResponse::error('MARK_ERROR', '标记失败: ' . $e->getMessage());
    }
}

/**
 * 获取Bearer Token
 */
function getBearerToken(): string
{
    $headers = getAllHttpHeaders();
    
    if (isset($headers['Authorization'])) {
        $auth = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth, $matches)) {
            return $matches[1];
        }
    }
    
    return '';
}

/**
 * 获取所有HTTP头
 */
function getAllHttpHeaders(): array
{
    if (function_exists('getallheaders')) {
        return getallheaders();
    }

    $headers = [];
    foreach ($_SERVER as $name => $value) {
        if (substr($name, 0, 5) === 'HTTP_') {
            $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
        }
    }

    return $headers;
}

/**
 * 检查是否处于维护模式
 */
function isMaintenanceMode(): bool
{
    $maintenanceFile = __DIR__ . '/maintenance.lock';
    return file_exists($maintenanceFile);
}
?>
