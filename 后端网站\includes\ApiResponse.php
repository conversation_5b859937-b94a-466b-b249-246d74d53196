<?php
/**
 * API响应处理类
 * 统一处理API响应格式
 */

declare(strict_types=1);

class ApiResponse
{
    /**
     * 发送成功响应
     */
    public static function success(string $message = '操作成功', array $data = [], int $code = 200): void
    {
        self::sendResponse([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'server_time' => date('Y-m-d H:i:s')
        ], $code);
    }
    
    /**
     * 发送错误响应
     */
    public static function error(string $errorCode, string $message = '操作失败', array $data = [], int $httpCode = 400): void
    {
        self::sendResponse([
            'success' => false,
            'code' => $errorCode,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'server_time' => date('Y-m-d H:i:s')
        ], $httpCode);
    }
    
    /**
     * 发送响应
     */
    private static function sendResponse(array $response, int $httpCode): void
    {
        http_response_code($httpCode);
        
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        // 输出JSON响应
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * 验证必需参数
     */
    public static function validateRequired(array $data, array $required): bool
    {
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                self::error('MISSING_PARAMS', "缺少必需参数: {$field}");
                return false;
            }
        }
        return true;
    }
    
    /**
     * 验证参数格式
     */
    public static function validateFormat(array $data, array $rules): bool
    {
        foreach ($rules as $field => $rule) {
            if (!isset($data[$field])) {
                continue;
            }
            
            $value = $data[$field];
            
            switch ($rule['type']) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        self::error('INVALID_FORMAT', "邮箱格式不正确: {$field}");
                        return false;
                    }
                    break;
                    
                case 'length':
                    $len = strlen($value);
                    if (isset($rule['min']) && $len < $rule['min']) {
                        self::error('INVALID_FORMAT', "{$field} 长度不能少于 {$rule['min']} 个字符");
                        return false;
                    }
                    if (isset($rule['max']) && $len > $rule['max']) {
                        self::error('INVALID_FORMAT', "{$field} 长度不能超过 {$rule['max']} 个字符");
                        return false;
                    }
                    break;
                    
                case 'regex':
                    if (!preg_match($rule['pattern'], $value)) {
                        self::error('INVALID_FORMAT', "{$field} 格式不正确");
                        return false;
                    }
                    break;
            }
        }
        return true;
    }
}
