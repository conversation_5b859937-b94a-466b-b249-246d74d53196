<?php
/**
 * 论坛附件上传API
 * 处理图片、音频等文件的上传和管理
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once 'includes/Database.php';
require_once 'includes/Logger.php';

// 响应函数
function sendResponse($success, $data = null, $message = '', $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'message' => $message,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 文件上传配置
$uploadConfig = [
    'max_file_size' => 50 * 1024 * 1024, // 50MB
    'allowed_types' => [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'audio' => ['mp3', 'wav', 'ogg', 'm4a', 'aac'],
        'document' => ['pdf', 'doc', 'docx', 'txt', 'rtf']
    ],
    'upload_path' => 'uploads/forum/',
    'temp_path' => 'uploads/forum/temp/'
];

// MIME类型映射
$mimeTypes = [
    'jpg' => 'image/jpeg', 'jpeg' => 'image/jpeg', 'png' => 'image/png',
    'gif' => 'image/gif', 'webp' => 'image/webp',
    'mp3' => 'audio/mpeg', 'wav' => 'audio/wav', 'ogg' => 'audio/ogg',
    'm4a' => 'audio/mp4', 'aac' => 'audio/aac',
    'pdf' => 'application/pdf', 'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'txt' => 'text/plain', 'rtf' => 'application/rtf'
];

try {
    $database = new Database();
    $logger = new Logger();
    
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'upload':
            // 文件上传
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                sendResponse(false, null, '请使用POST方法上传文件', 405);
            }
            
            // 验证用户身份
            $username = $_POST['username'] ?? '';
            $token = $_POST['token'] ?? '';
            
            if (empty($username)) {
                sendResponse(false, null, '用户名不能为空', 400);
            }
            
            // 验证用户是否存在
            $user = $database->fetchOne("SELECT * FROM users WHERE username = ? AND status = 1", [$username]);
            if (!$user) {
                sendResponse(false, null, '用户不存在或已被禁用', 403);
            }
            
            // 检查是否有文件上传
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                sendResponse(false, null, '文件上传失败', 400);
            }
            
            $file = $_FILES['file'];
            $originalName = $file['name'];
            $fileSize = $file['size'];
            $tempPath = $file['tmp_name'];
            
            // 检查文件大小
            if ($fileSize > $uploadConfig['max_file_size']) {
                sendResponse(false, null, '文件大小超过限制（最大50MB）', 400);
            }
            
            // 获取文件扩展名
            $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
            
            // 检查文件类型
            $fileType = '';
            $isAllowed = false;
            foreach ($uploadConfig['allowed_types'] as $type => $extensions) {
                if (in_array($extension, $extensions)) {
                    $fileType = $type;
                    $isAllowed = true;
                    break;
                }
            }
            
            if (!$isAllowed) {
                sendResponse(false, null, '不支持的文件类型', 400);
            }
            
            // 验证MIME类型
            $detectedMime = mime_content_type($tempPath);
            $expectedMime = $mimeTypes[$extension] ?? '';

            if ($expectedMime && strpos($detectedMime, explode('/', $expectedMime)[0]) !== 0) {
                sendResponse(false, null, '文件类型验证失败', 400);
            }
            
            // 生成唯一文件名
            $storedName = date('Y/m/d/') . uniqid() . '_' . time() . '.' . $extension;
            $uploadDir = $uploadConfig['upload_path'] . $fileType . '/' . dirname($storedName);
            $fullPath = $uploadConfig['upload_path'] . $fileType . '/' . $storedName;
            
            // 创建目录
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // 移动文件
            if (!move_uploaded_file($tempPath, $fullPath)) {
                sendResponse(false, null, '文件保存失败', 500);
            }
            
            // 保存到数据库
            $attachmentId = $database->insert('forum_attachments', [
                'filename' => $originalName,
                'stored_filename' => $storedName,
                'file_path' => $fullPath,
                'file_type' => $fileType,
                'file_size' => $fileSize,
                'mime_type' => $detectedMime,
                'upload_user' => $username,
                'status' => 1
            ]);
            
            $logger->info("文件上传成功", [
                'attachment_id' => $attachmentId,
                'filename' => $originalName,
                'file_type' => $fileType,
                'file_size' => $fileSize,
                'user' => $username
            ]);
            
            sendResponse(true, [
                'attachment_id' => $attachmentId,
                'filename' => $originalName,
                'file_type' => $fileType,
                'file_size' => $fileSize,
                'upload_time' => date('Y-m-d H:i:s')
            ], '文件上传成功');
            break;
            
        case 'attach_to_post':
            // 将附件关联到帖子
            $attachmentId = $_POST['attachment_id'] ?? 0;
            $postId = $_POST['post_id'] ?? 0;
            $username = $_POST['username'] ?? '';
            
            if (!$attachmentId || !$postId || !$username) {
                sendResponse(false, null, '参数不完整', 400);
            }
            
            // 验证附件是否存在且属于该用户
            $attachment = $database->fetchOne(
                "SELECT * FROM forum_attachments WHERE id = ? AND upload_user = ? AND post_id IS NULL AND reply_id IS NULL",
                [$attachmentId, $username]
            );
            
            if (!$attachment) {
                sendResponse(false, null, '附件不存在或无权限', 404);
            }
            
            // 验证帖子是否存在且属于该用户
            $post = $database->fetchOne("SELECT * FROM forum_posts WHERE id = ? AND author = ?", [$postId, $username]);
            if (!$post) {
                sendResponse(false, null, '帖子不存在或无权限', 404);
            }
            
            // 关联附件到帖子
            $database->query("UPDATE forum_attachments SET post_id = ? WHERE id = ?", [$postId, $attachmentId]);
            
            // 更新帖子附件数量
            $database->query("UPDATE forum_posts SET attachments_count = attachments_count + 1 WHERE id = ?", [$postId]);
            
            $logger->info("附件关联到帖子", ['attachment_id' => $attachmentId, 'post_id' => $postId, 'user' => $username]);
            
            sendResponse(true, null, '附件关联成功');
            break;
            
        case 'attach_to_reply':
            // 将附件关联到回复
            $attachmentId = $_POST['attachment_id'] ?? 0;
            $replyId = $_POST['reply_id'] ?? 0;
            $username = $_POST['username'] ?? '';
            
            if (!$attachmentId || !$replyId || !$username) {
                sendResponse(false, null, '参数不完整', 400);
            }
            
            // 验证附件是否存在且属于该用户
            $attachment = $database->fetchOne(
                "SELECT * FROM forum_attachments WHERE id = ? AND upload_user = ? AND post_id IS NULL AND reply_id IS NULL",
                [$attachmentId, $username]
            );
            
            if (!$attachment) {
                sendResponse(false, null, '附件不存在或无权限', 404);
            }
            
            // 验证回复是否存在且属于该用户
            $reply = $database->fetchOne("SELECT * FROM forum_replies WHERE id = ? AND author = ?", [$replyId, $username]);
            if (!$reply) {
                sendResponse(false, null, '回复不存在或无权限', 404);
            }
            
            // 关联附件到回复
            $database->query("UPDATE forum_attachments SET reply_id = ? WHERE id = ?", [$replyId, $attachmentId]);
            
            // 更新回复附件数量
            $database->query("UPDATE forum_replies SET attachments_count = attachments_count + 1 WHERE id = ?", [$replyId]);
            
            $logger->info("附件关联到回复", ['attachment_id' => $attachmentId, 'reply_id' => $replyId, 'user' => $username]);
            
            sendResponse(true, null, '附件关联成功');
            break;
            
        case 'get_attachments':
            // 获取附件列表
            $postId = $_GET['post_id'] ?? 0;
            $replyId = $_GET['reply_id'] ?? 0;
            
            if (!$postId && !$replyId) {
                sendResponse(false, null, '请指定帖子ID或回复ID', 400);
            }
            
            $whereClause = $postId ? "post_id = ?" : "reply_id = ?";
            $param = $postId ?: $replyId;
            
            $attachments = $database->fetchAll(
                "SELECT id, filename, file_type, file_size, upload_time, download_count 
                 FROM forum_attachments 
                 WHERE $whereClause AND status = 1 
                 ORDER BY upload_time ASC",
                [$param]
            );
            
            sendResponse(true, $attachments, '获取附件列表成功');
            break;
            
        case 'download':
            // 下载附件
            $attachmentId = $_GET['id'] ?? 0;
            
            if (!$attachmentId) {
                sendResponse(false, null, '附件ID不能为空', 400);
            }
            
            $attachment = $database->fetchOne(
                "SELECT * FROM forum_attachments WHERE id = ? AND status = 1",
                [$attachmentId]
            );
            
            if (!$attachment) {
                sendResponse(false, null, '附件不存在', 404);
            }
            
            $filePath = $attachment['file_path'];
            if (!file_exists($filePath)) {
                sendResponse(false, null, '文件不存在', 404);
            }
            
            // 更新下载次数
            $database->query("UPDATE forum_attachments SET download_count = download_count + 1 WHERE id = ?", [$attachmentId]);
            
            // 设置下载头
            header('Content-Type: ' . $attachment['mime_type']);
            header('Content-Disposition: attachment; filename="' . $attachment['filename'] . '"');
            header('Content-Length: ' . $attachment['file_size']);
            header('Cache-Control: no-cache, must-revalidate');
            
            // 输出文件
            readfile($filePath);
            exit;
            break;
            
        case 'delete':
            // 删除附件
            $attachmentId = $_POST['attachment_id'] ?? 0;
            $username = $_POST['username'] ?? '';
            
            if (!$attachmentId || !$username) {
                sendResponse(false, null, '参数不完整', 400);
            }
            
            // 验证附件是否存在且属于该用户
            $attachment = $database->fetchOne(
                "SELECT * FROM forum_attachments WHERE id = ? AND upload_user = ?",
                [$attachmentId, $username]
            );
            
            if (!$attachment) {
                sendResponse(false, null, '附件不存在或无权限', 404);
            }
            
            // 删除物理文件
            if (file_exists($attachment['file_path'])) {
                unlink($attachment['file_path']);
            }
            
            // 标记为删除
            $database->query("UPDATE forum_attachments SET status = 0 WHERE id = ?", [$attachmentId]);
            
            // 更新关联的帖子或回复的附件数量
            if ($attachment['post_id']) {
                $database->query("UPDATE forum_posts SET attachments_count = attachments_count - 1 WHERE id = ?", [$attachment['post_id']]);
            }
            if ($attachment['reply_id']) {
                $database->query("UPDATE forum_replies SET attachments_count = attachments_count - 1 WHERE id = ?", [$attachment['reply_id']]);
            }
            
            $logger->info("附件删除", ['attachment_id' => $attachmentId, 'user' => $username]);
            
            sendResponse(true, null, '附件删除成功');
            break;
            
        default:
            sendResponse(false, null, '无效的操作', 400);
    }
    
} catch (Exception $e) {
    error_log("论坛上传API错误: " . $e->getMessage());
    sendResponse(false, null, '服务器内部错误', 500);
}
?>
