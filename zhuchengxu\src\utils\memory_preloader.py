"""
内存预加载器 - 将所有模块和资源预加载到内存中
"""

import sys
import os
import json
import time
import importlib
from typing import Dict, Any


class MemoryPreloader:
    """内存预加载器"""
    
    def __init__(self):
        self.preloaded_modules = {}
        self.preloaded_configs = {}
        self.preloaded_data = {}
        self.start_time = time.time()
        
    def preload_all_modules(self):
        """预加载所有模块到内存"""
        print("🚀 开始预加载所有模块到内存...")
        
        # 核心模块列表（只包含存在的模块）
        core_modules = [
            # GUI模块
            'src.gui.pyqt_ui.pyqt_pages.pyqt_mailbox_page',
            'src.gui.pyqt_ui.pyqt_pages.pyqt_vscode_page',
            'src.gui.pyqt_ui.pyqt_pages.pyqt_config_page',

            # 工具模块
            'src.utils.config_manager',
            'src.utils.logger',
            'src.utils.performance_optimizer',
            'src.utils.simple_async_network',  # 跳过有问题的async_network
            'src.utils.threaded_io',
            'src.utils.high_performance_utils',

            # 安全模块
            'src.security.backend_auth_client',
            'src.security.backend_security_manager',
        ]
        
        loaded_count = 0
        for module_name in core_modules:
            try:
                # 预加载模块
                module = importlib.import_module(module_name)
                self.preloaded_modules[module_name] = module
                loaded_count += 1
                print(f"✅ 已预加载: {module_name}")
            except ImportError as e:
                print(f"⚠️ 跳过模块 {module_name}: {e}")
            except Exception as e:
                print(f"❌ 预加载失败 {module_name}: {e}")
                
        print(f"📦 预加载完成: {loaded_count}/{len(core_modules)} 个模块")
        
    def preload_all_configs(self):
        """预加载所有配置文件到内存"""
        print("📁 开始预加载配置文件到内存...")
        
        config_files = [
            'config.json',
            'auth_cache.json',
            'user_preferences.json',
            'window_state.json'
        ]

        loaded_count = 0
        for config_file in config_files:
            try:
                config_path = os.path.join(os.getcwd(), config_file)
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    self.preloaded_configs[config_file] = config_data
                    loaded_count += 1
                    print(f"✅ 已预加载配置: {config_file}")
                else:
                    # 创建默认配置
                    default_config = self._get_default_config(config_file)
                    self.preloaded_configs[config_file] = default_config
                    loaded_count += 1
                    print(f"📝 创建默认配置: {config_file}")
            except Exception as e:
                print(f"❌ 配置加载失败 {config_file}: {e}")
                self.preloaded_configs[config_file] = {}
                
        print(f"📋 配置预加载完成: {loaded_count} 个文件")

    def _get_default_config(self, config_file: str) -> dict:
        """获取默认配置"""
        if config_file == 'config.json':
            return {
                "email_domain": "hwsyyds.xyz",
                "temp_mail_address": "<EMAIL>",
                "temp_mail_pin": "123456",
                "browser_type": "默认浏览器",
                "verbose_logging": True,
                "save_window_position": True,
                "auth_server_url": "http://*************:777/api.php",
                "ui_config": {
                    "use_light_version": False,
                    "disable_animations": True,
                    "disable_timers": True,
                    "simple_styles": True
                }
            }
        elif config_file == 'auth_cache.json':
            return {
                "auth_info": None,
                "cached_at": 0
            }
        elif config_file == 'user_preferences.json':
            return {
                "theme": "dark",
                "language": "zh_CN",
                "auto_refresh": True,
                "refresh_interval": 2000
            }
        elif config_file == 'window_state.json':
            return {
                "geometry": {
                    "x": 300,
                    "y": 200,
                    "width": 1400,
                    "height": 900
                },
                "maximized": False
            }
        else:
            return {}

    def preload_static_data(self):
        """预加载静态数据到内存"""
        print("💾 开始预加载静态数据到内存...")
        
        # 预加载VSCode清理规则（内置数据）
        self.preloaded_data['vscode_cleanup_rules'] = [
            {
                'name': 'VSCode缓存文件',
                'path': '%APPDATA%\\Code\\User\\workspaceStorage',
                'description': '工作区缓存文件'
            },
            {
                'name': 'VSCode日志文件',
                'path': '%APPDATA%\\Code\\logs',
                'description': '应用程序日志'
            },
            {
                'name': 'VSCode扩展缓存',
                'path': '%USERPROFILE%\\.vscode\\extensions',
                'description': '扩展程序缓存'
            }
        ]
        print("✅ 已预加载VSCode清理规则")
            
        # 预加载邮箱API配置
        self.preloaded_data['mail_api_config'] = {
            'base_url': 'https://tempmail.plus/api',
            'timeout': 10,
            'retry_count': 3,
            'headers': {
                'User-Agent': 'AugmentTool/2.0',
                'Accept': 'application/json'
            }
        }
        print("✅ 已预加载邮箱API配置")
        
        # 预加载UI主题数据
        self.preloaded_data['ui_theme'] = {
            'colors': {
                'primary': '#00d4ff',
                'secondary': '#ff6b35',
                'success': '#00ff88',
                'warning': '#ffaa00',
                'error': '#ff4757'
            },
            'fonts': {
                'primary': 'Microsoft YaHei',
                'monospace': 'Consolas'
            }
        }
        print("✅ 已预加载UI主题数据")
        
        print("💾 静态数据预加载完成")
        
    def get_preloaded_module(self, module_name: str):
        """获取预加载的模块"""
        return self.preloaded_modules.get(module_name)
        
    def get_preloaded_config(self, config_name: str) -> Dict[str, Any]:
        """获取预加载的配置"""
        return self.preloaded_configs.get(config_name, {})
        
    def update_config_memory(self, config_name: str, config_data: Dict[str, Any]):
        """更新内存中的配置"""
        self.preloaded_configs[config_name] = config_data
        
    def get_preloaded_data(self, data_key: str):
        """获取预加载的数据"""
        return self.preloaded_data.get(data_key)
        
    def save_configs_to_disk(self):
        """将内存中的配置保存到磁盘（批量操作）"""
        print("💾 批量保存配置到磁盘...")
        saved_count = 0
        
        for config_file, config_data in self.preloaded_configs.items():
            try:
                config_path = os.path.join(os.getcwd(), config_file)
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                saved_count += 1
            except Exception as e:
                print(f"❌ 保存配置失败 {config_file}: {e}")
                
        print(f"✅ 批量保存完成: {saved_count} 个配置文件")
        
    def get_memory_usage_stats(self) -> Dict[str, Any]:
        """获取内存使用统计"""
        return {
            'preloaded_modules': len(self.preloaded_modules),
            'preloaded_configs': len(self.preloaded_configs),
            'preloaded_data': len(self.preloaded_data),
            'load_time': time.time() - self.start_time,
            'total_items': len(self.preloaded_modules) + len(self.preloaded_configs) + len(self.preloaded_data)
        }
        
    def preload_everything(self):
        """预加载所有内容"""
        print("🚀 开始全面内存预加载...")
        start_time = time.time()
        
        # 预加载模块
        self.preload_all_modules()
        
        # 预加载配置
        self.preload_all_configs()
        
        # 预加载静态数据
        self.preload_static_data()
        
        # 统计信息
        stats = self.get_memory_usage_stats()
        elapsed = time.time() - start_time
        
        print(f"""
🎉 内存预加载完成！
📊 统计信息:
   - 预加载模块: {stats['preloaded_modules']} 个
   - 预加载配置: {stats['preloaded_configs']} 个  
   - 预加载数据: {stats['preloaded_data']} 个
   - 总计项目: {stats['total_items']} 个
   - 预加载耗时: {elapsed:.2f} 秒
   
🚀 现在程序将以纯内存模式运行，性能大幅提升！
        """)

    def dispose_resources(self):
        """VSCode风格的资源释放和清理"""
        self.logger.info("🧹 开始释放内存预加载资源...")
        
        # 1. 释放预加载的模块引用
        module_count = len(self.preloaded_modules)
        self.preloaded_modules.clear()
        self.logger.info(f"📦 已释放 {module_count} 个预加载模块引用")
        
        # 2. 保存并释放配置数据
        if self.preloaded_configs:
            # 保存关键配置到磁盘
            try:
                critical_configs = ['config.json', 'auth_cache.json']
                for config_name in critical_configs:
                    if config_name in self.preloaded_configs:
                        config_path = os.path.join(os.getcwd(), config_name)
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(self.preloaded_configs[config_name], f, 
                                     ensure_ascii=False, indent=2)
                        self.logger.info(f"💾 已保存关键配置: {config_name}")
            except Exception as e:
                self.logger.warning(f"⚠️ 保存配置失败: {e}")
        
        # 释放配置内存
        config_count = len(self.preloaded_configs)
        self.preloaded_configs.clear()
        self.logger.info(f"⚙️ 已释放 {config_count} 个预加载配置")
        
        # 3. 释放静态数据
        data_count = len(self.preloaded_data)
        self.preloaded_data.clear()
        self.logger.info(f"📊 已释放 {data_count} 个预加载数据项")
        
        # 4. 强制垃圾回收
        try:
            import gc
            collected = gc.collect(2)  # 完整收集
            self.logger.info(f"♻️ 垃圾回收: 已释放 {collected} 个对象")
        except Exception as e:
            self.logger.warning(f"⚠️ 垃圾回收失败: {e}")
        
        # 5. 尝试减少内存占用 - VSCode的技术
        try:
            # 清理sys.modules中不需要的模块
            optional_modules = ["matplotlib", "pandas", "numpy", "cv2", "PIL"]
            removed = []
            for module_name in optional_modules:
                if module_name in sys.modules:
                    del sys.modules[module_name]
                    removed.append(module_name)
            
            if removed:
                self.logger.info(f"🧹 已从sys.modules移除未使用的模块: {', '.join(removed)}")
                
            # 清理文件系统缓存
            if hasattr(os, "fspath"):  # Python 3.6+
                os.fspath.__cached__ = {}
        except Exception as e:
            self.logger.warning(f"⚠️ 高级内存优化失败: {e}")
        
        self.logger.info("✅ 内存预加载资源释放完成")
        return True


# 全局预加载器实例
_memory_preloader = None

def get_memory_preloader() -> MemoryPreloader:
    """获取全局内存预加载器"""
    global _memory_preloader
    if _memory_preloader is None:
        _memory_preloader = MemoryPreloader()
    return _memory_preloader

def preload_to_memory():
    """执行全面内存预加载"""
    preloader = get_memory_preloader()
    preloader.preload_everything()
    return preloader

def get_preloaded_module(module_name: str):
    """快速获取预加载的模块"""
    preloader = get_memory_preloader()
    return preloader.get_preloaded_module(module_name)

def get_preloaded_config(config_name: str) -> Dict[str, Any]:
    """快速获取预加载的配置"""
    preloader = get_memory_preloader()
    return preloader.get_preloaded_config(config_name)

def update_memory_config(config_name: str, config_data: Dict[str, Any]):
    """快速更新内存配置"""
    preloader = get_memory_preloader()
    preloader.update_config_memory(config_name, config_data)

def save_memory_configs():
    """批量保存内存配置到磁盘"""
    preloader = get_memory_preloader()
    preloader.save_configs_to_disk()

def dispose_preloader_resources():
    """清理和释放内存预加载器资源"""
    try:
        preloader = get_memory_preloader()
        if preloader:
            preloader.dispose_resources()
            return True
    except Exception as e:
        print(f"❌ 内存预加载器资源释放失败: {e}")
        return False
    return False
