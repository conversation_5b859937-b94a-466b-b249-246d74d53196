#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径工具模块
用于统一管理项目中的文件和目录路径，确保所有文件都创建在主程序目录下
"""

import os
import sys
from pathlib import Path
from typing import Optional, Union


class PathManager:
    """路径管理器，确保所有文件操作都在主程序目录下进行"""

    def __init__(self):
        """初始化路径管理器"""
        # 🔧 使用与main.py一致的路径检测逻辑
        # 直接使用main.py中设置的环境变量
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
        if config_dir:
            self._main_dir = Path(config_dir)
            print(f"✅ PathManager使用main.py设置的目录: {self._main_dir}")
        else:
            # 备选方案：使用sys.argv[0]
            if sys.argv and len(sys.argv) > 0:
                argv_path = os.path.abspath(sys.argv[0])
                if argv_path.endswith('.exe'):
                    self._main_dir = Path(argv_path).parent
                    print(f"✅ PathManager使用sys.argv[0]的exe目录: {self._main_dir}")
                else:
                    # 开发环境
                    self._main_dir = Path(argv_path).parent
                    print(f"✅ PathManager使用开发环境目录: {self._main_dir}")
            else:
                # 最后备选：当前工作目录
                self._main_dir = Path(os.getcwd())
                print(f"✅ PathManager使用当前工作目录: {self._main_dir}")

        self._main_dir = self._main_dir.resolve()

        # 启动时清理根目录下的错误文件
        self._cleanup_root_files()

    def _cleanup_root_files(self):
        """清理根目录下的错误文件，移动到主程序目录"""
        try:
            import shutil

            # 获取根目录（主程序目录的上级目录）
            root_dir = self._main_dir.parent

            # 需要移动的文件列表
            files_to_move = [
                'augment_cleaner.log',
                'auth_system.db',
                'config.json'
            ]

            moved_files = []
            for filename in files_to_move:
                root_file = root_dir / filename
                main_file = self._main_dir / filename

                if root_file.exists() and not main_file.exists():
                    try:
                        shutil.move(str(root_file), str(main_file))
                        moved_files.append(filename)
                    except Exception:
                        # 静默处理移动失败，避免影响程序启动
                        pass

            # 如果移动了文件，可以记录日志（但此时日志系统可能还未初始化）
            if moved_files:
                print(f"🔧 已移动文件到主程序目录: {', '.join(moved_files)}")

        except Exception:
            # 静默处理异常，避免影响程序启动
            pass

    @property
    def main_dir(self) -> Path:
        """获取主程序目录"""
        return self._main_dir
    
    def get_path(self, *path_parts: str) -> Path:
        """
        获取相对于主程序目录的路径
        
        Args:
            *path_parts: 路径组件
            
        Returns:
            完整的路径对象
        """
        return self._main_dir.joinpath(*path_parts)
    
    def get_str_path(self, *path_parts: str) -> str:
        """
        获取相对于主程序目录的路径字符串
        
        Args:
            *path_parts: 路径组件
            
        Returns:
            完整的路径字符串
        """
        return str(self.get_path(*path_parts))
    
    def ensure_dir(self, *path_parts: str) -> Path:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            *path_parts: 路径组件
            
        Returns:
            目录路径对象
        """
        dir_path = self.get_path(*path_parts)
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path
    
    def ensure_dir_str(self, *path_parts: str) -> str:
        """
        确保目录存在，如果不存在则创建，返回字符串路径
        
        Args:
            *path_parts: 路径组件
            
        Returns:
            目录路径字符串
        """
        return str(self.ensure_dir(*path_parts))
    
    def get_logs_dir(self) -> Path:
        """获取日志目录"""
        return self.ensure_dir("logs")
    
    def get_logs_file(self, filename: str = "augment_tool.log") -> str:
        """获取日志文件路径"""
        return str(self.get_logs_dir() / filename)
    
    def get_downloads_dir(self) -> Path:
        """获取下载目录"""
        return self.ensure_dir("downloaded_files")
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        return self.ensure_dir("temp")
    
    def get_config_dir(self) -> Path:
        """获取配置目录"""
        return self.ensure_dir("config")
    
    def get_screenshots_dir(self) -> Path:
        """获取截图目录"""
        return self.ensure_dir("screenshots")
    
    def get_accounts_dir(self) -> Path:
        """获取账号记录目录"""
        return self.ensure_dir("registered_accounts")
    
    def get_chrome_profile_dir(self) -> Path:
        """获取Chrome配置文件目录"""
        return self.ensure_dir("temp_chrome_profile")
    
    def is_relative_to_main(self, path: Union[str, Path]) -> bool:
        """
        检查路径是否相对于主程序目录
        
        Args:
            path: 要检查的路径
            
        Returns:
            是否相对于主程序目录
        """
        try:
            Path(path).resolve().relative_to(self._main_dir)
            return True
        except ValueError:
            return False
    
    def make_relative_to_main(self, path: Union[str, Path]) -> Path:
        """
        将路径转换为相对于主程序目录的路径
        
        Args:
            path: 原始路径
            
        Returns:
            相对于主程序目录的路径
        """
        path_obj = Path(path)
        if path_obj.is_absolute():
            try:
                # 如果已经在主程序目录下，返回相对路径
                return path_obj.relative_to(self._main_dir)
            except ValueError:
                # 如果不在主程序目录下，将其放到主程序目录下
                return self._main_dir / path_obj.name
        else:
            # 相对路径，直接基于主程序目录
            return self._main_dir / path_obj


# 全局路径管理器实例
_path_manager: Optional[PathManager] = None


def get_path_manager() -> PathManager:
    """获取全局路径管理器实例"""
    global _path_manager
    if _path_manager is None:
        _path_manager = PathManager()
    return _path_manager


# 便捷函数
def get_main_dir() -> Path:
    """获取主程序目录"""
    return get_path_manager().main_dir


def get_path(*path_parts: str) -> Path:
    """获取相对于主程序目录的路径"""
    return get_path_manager().get_path(*path_parts)


def get_str_path(*path_parts: str) -> str:
    """获取相对于主程序目录的路径字符串"""
    return get_path_manager().get_str_path(*path_parts)


def ensure_dir(*path_parts: str) -> Path:
    """确保目录存在"""
    return get_path_manager().ensure_dir(*path_parts)


def ensure_dir_str(*path_parts: str) -> str:
    """确保目录存在，返回字符串路径"""
    return get_path_manager().ensure_dir_str(*path_parts)


def get_logs_file(filename: str = "augment_tool.log") -> str:
    """获取日志文件路径"""
    return get_path_manager().get_logs_file(filename)


def get_downloads_dir() -> str:
    """获取下载目录路径字符串"""
    return str(get_path_manager().get_downloads_dir())


def get_temp_dir() -> str:
    """获取临时目录路径字符串"""
    return str(get_path_manager().get_temp_dir())


def get_screenshots_dir() -> str:
    """获取截图目录路径字符串"""
    return str(get_path_manager().get_screenshots_dir())


def get_accounts_dir() -> str:
    """获取账号记录目录路径字符串"""
    return str(get_path_manager().get_accounts_dir())


def get_chrome_profile_dir() -> str:
    """获取Chrome配置文件目录路径字符串"""
    return str(get_path_manager().get_chrome_profile_dir())


if __name__ == "__main__":
    # 测试代码
    pm = PathManager()
    print(f"主程序目录: {pm.main_dir}")
    print(f"日志文件: {pm.get_logs_file()}")
    print(f"下载目录: {pm.get_downloads_dir()}")
    print(f"临时目录: {pm.get_temp_dir()}")
