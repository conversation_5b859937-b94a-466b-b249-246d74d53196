#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI性能优化器 - 专门针对PyQt界面性能优化
"""

import gc
import time
import threading
from typing import Dict, Any, Optional
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
from PyQt5.QtWidgets import QWidget


class UIPerformanceOptimizer(QObject):
    """UI性能优化器"""
    
    # 性能警告信号
    performance_warning = pyqtSignal(str, dict)
    
    def __init__(self):
        super().__init__()
        self.running = True
        self.widget_cache = {}
        self.update_counters = {}
        self.last_cleanup_time = time.time()
        
        # 性能阈值
        self.PERFORMANCE_THRESHOLDS = {
            'max_update_frequency': 10,  # 每秒最大更新次数
            'memory_warning_mb': 100,    # 内存警告阈值（MB）
            'cleanup_interval': 60,      # 清理间隔（秒）
        }
        
        # 启动优化定时器（降低频率）
        self._setup_optimization_timer()
        print("✅ UI性能优化器已启动（轻量模式）")
    
    def _setup_optimization_timer(self):
        """设置优化定时器"""
        self.optimization_timer = QTimer()
        self.optimization_timer.timeout.connect(self._perform_optimization)
        self.optimization_timer.start(120000)  # 2分钟执行一次优化
    
    def _perform_optimization(self):
        """执行性能优化"""
        try:
            current_time = time.time()
            
            # 1. 内存清理
            if current_time - self.last_cleanup_time > self.PERFORMANCE_THRESHOLDS['cleanup_interval']:
                self._cleanup_memory()
                self.last_cleanup_time = current_time
            
            # 2. 清理更新计数器
            self._cleanup_update_counters()
            
            # 3. 检查性能指标
            self._check_performance_metrics()
            
        except Exception as e:
            print(f"❌ UI性能优化失败: {e}")
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            # 清理组件缓存
            cache_size_before = len(self.widget_cache)
            self.widget_cache.clear()
            
            # 强制垃圾回收
            collected = gc.collect()
            
            print(f"🧹 UI内存清理完成: 清理缓存{cache_size_before}项, 回收对象{collected}个")
            
        except Exception as e:
            print(f"❌ UI内存清理失败: {e}")
    
    def _cleanup_update_counters(self):
        """清理更新计数器"""
        current_time = time.time()
        expired_keys = []
        
        for widget_id, counter_data in self.update_counters.items():
            if current_time - counter_data.get('last_update', 0) > 60:  # 1分钟未更新
                expired_keys.append(widget_id)
        
        for key in expired_keys:
            del self.update_counters[key]
        
        if expired_keys:
            print(f"🧹 清理了 {len(expired_keys)} 个过期的更新计数器")
    
    def _check_performance_metrics(self):
        """检查性能指标"""
        try:
            # 检查更新频率
            high_frequency_widgets = []
            for widget_id, counter_data in self.update_counters.items():
                frequency = counter_data.get('frequency', 0)
                if frequency > self.PERFORMANCE_THRESHOLDS['max_update_frequency']:
                    high_frequency_widgets.append((widget_id, frequency))
            
            if high_frequency_widgets:
                warning_msg = f"检测到高频更新组件: {len(high_frequency_widgets)}个"
                self.performance_warning.emit(warning_msg, {
                    'type': 'high_frequency_update',
                    'widgets': high_frequency_widgets
                })
                print(f"⚠️ {warning_msg}")
            
        except Exception as e:
            print(f"❌ 性能指标检查失败: {e}")
    
    def register_widget_update(self, widget_id: str):
        """注册组件更新"""
        current_time = time.time()
        
        if widget_id not in self.update_counters:
            self.update_counters[widget_id] = {
                'count': 0,
                'last_update': current_time,
                'frequency': 0
            }
        
        counter_data = self.update_counters[widget_id]
        counter_data['count'] += 1
        
        # 计算更新频率（每秒更新次数）
        time_diff = current_time - counter_data['last_update']
        if time_diff > 0:
            counter_data['frequency'] = 1.0 / time_diff
        
        counter_data['last_update'] = current_time
    
    def optimize_widget_rendering(self, widget: QWidget) -> bool:
        """优化组件渲染"""
        try:
            if not widget or not widget.isVisible():
                return False
            
            # 暂时禁用更新
            widget.setUpdatesEnabled(False)
            
            # 缓存组件信息
            widget_id = id(widget)
            self.widget_cache[widget_id] = {
                'widget': widget,
                'cached_time': time.time()
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 组件渲染优化失败: {e}")
            return False
    
    def restore_widget_rendering(self, widget: QWidget):
        """恢复组件渲染"""
        try:
            if widget:
                widget.setUpdatesEnabled(True)
                widget.update()
                
        except Exception as e:
            print(f"❌ 恢复组件渲染失败: {e}")
    
    def batch_update_widgets(self, widgets: list, update_func):
        """批量更新组件"""
        try:
            # 暂停所有组件的更新
            for widget in widgets:
                if widget and widget.isVisible():
                    widget.setUpdatesEnabled(False)
            
            # 执行更新函数
            update_func()
            
            # 恢复所有组件的更新
            for widget in widgets:
                if widget and widget.isVisible():
                    widget.setUpdatesEnabled(True)
                    widget.update()
            
            print(f"✅ 批量更新了 {len(widgets)} 个组件")
            
        except Exception as e:
            print(f"❌ 批量更新组件失败: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            'cached_widgets': len(self.widget_cache),
            'tracked_updates': len(self.update_counters),
            'total_updates': sum(data.get('count', 0) for data in self.update_counters.values()),
            'high_frequency_widgets': len([
                w for w in self.update_counters.values() 
                if w.get('frequency', 0) > self.PERFORMANCE_THRESHOLDS['max_update_frequency']
            ])
        }
    
    def shutdown(self):
        """关闭优化器"""
        self.running = False
        if hasattr(self, 'optimization_timer'):
            self.optimization_timer.stop()
        self.widget_cache.clear()
        self.update_counters.clear()
        print("✅ UI性能优化器已关闭")


# 全局UI性能优化器实例
_ui_optimizer = None

def get_ui_optimizer() -> UIPerformanceOptimizer:
    """获取全局UI性能优化器"""
    global _ui_optimizer
    if _ui_optimizer is None:
        _ui_optimizer = UIPerformanceOptimizer()
    return _ui_optimizer

def optimize_widget_update(widget: QWidget, widget_id: str = None):
    """优化组件更新"""
    optimizer = get_ui_optimizer()
    if widget_id:
        optimizer.register_widget_update(widget_id)
    return optimizer.optimize_widget_rendering(widget)

def restore_widget_update(widget: QWidget):
    """恢复组件更新"""
    optimizer = get_ui_optimizer()
    optimizer.restore_widget_rendering(widget)

def batch_widget_update(widgets: list, update_func):
    """批量组件更新"""
    optimizer = get_ui_optimizer()
    optimizer.batch_update_widgets(widgets, update_func)

def get_ui_performance_stats() -> Dict[str, Any]:
    """获取UI性能统计"""
    optimizer = get_ui_optimizer()
    return optimizer.get_performance_stats()
