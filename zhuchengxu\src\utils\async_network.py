"""
异步网络请求模块 - 高性能网络I/O
"""

import asyncio
import aiohttp
import threading
import queue
import time
import json
from typing import Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor
import sys
import os

# 添加lib目录到路径
lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

try:
    import aiohttp
    import asyncio
    # 检查Python版本兼容性
    import sys
    if sys.version_info >= (3, 8):
        AIOHTTP_AVAILABLE = True
        print("✅ aiohttp兼容当前Python版本")
    else:
        AIOHTTP_AVAILABLE = False
        print("⚠️ Python版本过低，禁用aiohttp")
except ImportError as e:
    AIOHTTP_AVAILABLE = False
    print(f"⚠️ aiohttp导入失败: {e}")
except Exception as e:
    AIOHTTP_AVAILABLE = False
    print(f"⚠️ aiohttp初始化失败: {e}")


class AsyncNetworkManager:
    """异步网络管理器"""
    
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.session = None
        self.loop = None
        self.thread = None
        self.running = False
        
        # 请求队列
        self.request_queue = queue.Queue()
        self.response_callbacks = {}
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 连接池配置
        self.connector_config = {
            'limit': 100,  # 总连接池大小
            'limit_per_host': 30,  # 每个主机的连接数
            'ttl_dns_cache': 300,  # DNS缓存5分钟
            'use_dns_cache': True,
        }
        
        self._start_async_loop()
        
    def _start_async_loop(self):
        """启动异步事件循环"""
        if not AIOHTTP_AVAILABLE:
            print("⚠️ aiohttp不可用，跳过异步网络初始化")
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run_async_loop, daemon=True)
        self.thread.start()
        print("✅ 异步网络管理器已启动")
        
    def _run_async_loop(self):
        """运行异步事件循环"""
        try:
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

            # 在事件循环中运行异步任务
            self.loop.run_until_complete(self._setup_and_run())

        except Exception as e:
            print(f"❌ 异步事件循环异常: {e}")
        finally:
            try:
                if self.session and not self.session.closed:
                    self.loop.run_until_complete(self.session.close())
            except:
                pass
            try:
                self.loop.close()
            except:
                pass

    async def _setup_and_run(self):
        """设置并运行异步任务"""
        try:
            # 创建aiohttp会话
            connector = aiohttp.TCPConnector(**self.connector_config)
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': 'AugmentTool/2.0'}
            )

            print("✅ aiohttp会话已创建")

            # 运行工作协程
            await self._async_worker()

        except Exception as e:
            print(f"❌ 异步设置失败: {e}")
            raise
                
    async def _async_worker(self):
        """异步工作协程"""
        while self.running:
            try:
                # 检查请求队列
                if not self.request_queue.empty():
                    request_data = self.request_queue.get_nowait()
                    asyncio.create_task(self._process_request(request_data))
                
                await asyncio.sleep(0.01)  # 避免CPU占用过高
                
            except Exception as e:
                print(f"⚠️ 异步工作协程异常: {e}")
                await asyncio.sleep(1)
                
    async def _process_request(self, request_data: Dict[str, Any]):
        """处理异步请求"""
        try:
            request_id = request_data['id']
            method = request_data['method']
            url = request_data['url']
            data = request_data.get('data')
            headers = request_data.get('headers', {})
            timeout = request_data.get('timeout', 10)
            callback = request_data.get('callback')
            
            # 创建超时配置
            request_timeout = aiohttp.ClientTimeout(total=timeout)
            
            # 发送请求
            async with self.session.request(
                method, url, 
                json=data if method.upper() in ['POST', 'PUT'] else None,
                params=data if method.upper() == 'GET' else None,
                headers=headers,
                timeout=request_timeout
            ) as response:
                
                # 读取响应
                if response.content_type == 'application/json':
                    result = await response.json()
                else:
                    result = await response.text()
                
                # 构建响应数据
                response_data = {
                    'status_code': response.status,
                    'data': result,
                    'headers': dict(response.headers),
                    'success': 200 <= response.status < 300
                }
                
                # 调用回调函数
                if callback:
                    try:
                        callback(response_data)
                    except Exception as e:
                        print(f"⚠️ 回调函数执行异常: {e}")
                        
        except asyncio.TimeoutError:
            if callback:
                callback({'success': False, 'error': 'timeout', 'status_code': 0})
        except Exception as e:
            if callback:
                callback({'success': False, 'error': str(e), 'status_code': 0})
                
    def async_request(self, method: str, url: str, data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None, timeout: int = 10,
                     callback: Optional[Callable] = None) -> str:
        """发送异步请求"""
        if not AIOHTTP_AVAILABLE or not self.running:
            # 降级到同步请求
            return self._sync_request(method, url, data, headers, timeout, callback)
            
        request_id = f"req_{int(time.time() * 1000)}_{id(callback)}"
        
        request_data = {
            'id': request_id,
            'method': method,
            'url': url,
            'data': data,
            'headers': headers or {},
            'timeout': timeout,
            'callback': callback
        }
        
        self.request_queue.put(request_data)
        return request_id
        
    def _sync_request(self, method: str, url: str, data: Optional[Dict] = None,
                     headers: Optional[Dict] = None, timeout: int = 10,
                     callback: Optional[Callable] = None) -> str:
        """同步请求（降级方案）"""
        def _do_request():
            try:
                import requests
                
                response = requests.request(
                    method, url,
                    json=data if method.upper() in ['POST', 'PUT'] else None,
                    params=data if method.upper() == 'GET' else None,
                    headers=headers or {},
                    timeout=timeout
                )
                
                try:
                    result_data = response.json()
                except:
                    result_data = response.text
                
                response_data = {
                    'status_code': response.status_code,
                    'data': result_data,
                    'headers': dict(response.headers),
                    'success': 200 <= response.status_code < 300
                }
                
                if callback:
                    callback(response_data)
                    
            except Exception as e:
                if callback:
                    callback({'success': False, 'error': str(e), 'status_code': 0})
        
        # 在线程池中执行
        self.executor.submit(_do_request)
        return f"sync_req_{int(time.time() * 1000)}"
        
    def get(self, url: str, params: Optional[Dict] = None, **kwargs) -> str:
        """GET请求"""
        return self.async_request('GET', url, data=params, **kwargs)
        
    def post(self, url: str, data: Optional[Dict] = None, **kwargs) -> str:
        """POST请求"""
        return self.async_request('POST', url, data=data, **kwargs)
        
    def put(self, url: str, data: Optional[Dict] = None, **kwargs) -> str:
        """PUT请求"""
        return self.async_request('PUT', url, data=data, **kwargs)
        
    def delete(self, url: str, **kwargs) -> str:
        """DELETE请求"""
        return self.async_request('DELETE', url, **kwargs)
        
    def stop(self):
        """停止异步网络管理器"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        self.executor.shutdown(wait=False)
        print("🛑 异步网络管理器已停止")


# 全局网络管理器
_network_manager = None

def get_network_manager() -> AsyncNetworkManager:
    """获取全局网络管理器"""
    global _network_manager
    if _network_manager is None:
        _network_manager = AsyncNetworkManager()
    return _network_manager

def async_get(url: str, params: Optional[Dict] = None, 
              callback: Optional[Callable] = None, **kwargs) -> str:
    """异步GET请求"""
    return get_network_manager().get(url, params, callback=callback, **kwargs)

def async_post(url: str, data: Optional[Dict] = None,
               callback: Optional[Callable] = None, **kwargs) -> str:
    """异步POST请求"""
    return get_network_manager().post(url, data, callback=callback, **kwargs)
