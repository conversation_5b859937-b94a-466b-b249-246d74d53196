#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文输入法修复工具
解决tkinter中文输入法兼容性问题
"""

import tkinter as tk
import sys
import platform


class ChineseInputFixer:
    """中文输入法修复器"""
    
    @staticmethod
    def fix_entry(entry: tk.Entry):
        """
        修复Entry组件的中文输入法问题
        
        Args:
            entry: 要修复的Entry组件
        """
        try:
            # 设置编码
            ChineseInputFixer._set_encoding(entry)
            
            # 绑定事件处理器
            ChineseInputFixer._bind_events(entry)
            
            # 设置输入法属性
            ChineseInputFixer._set_ime_attributes(entry)
            
        except Exception as e:
            print(f"修复Entry中文输入法失败: {e}")
    
    @staticmethod
    def fix_text(text: tk.Text):
        """
        修复Text组件的中文输入法问题
        
        Args:
            text: 要修复的Text组件
        """
        try:
            # 设置编码
            ChineseInputFixer._set_encoding(text)
            
            # 绑定事件处理器
            ChineseInputFixer._bind_text_events(text)
            
            # 设置输入法属性
            ChineseInputFixer._set_ime_attributes(text)
            
        except Exception as e:
            print(f"修复Text中文输入法失败: {e}")
    
    @staticmethod
    def _set_encoding(widget):
        """设置组件编码"""
        try:
            # 设置系统编码为UTF-8
            widget.tk.call('encoding', 'system', 'utf-8')
            
            # 在Windows上设置特殊编码处理
            if platform.system() == 'Windows':
                widget.tk.call('encoding', 'convertfrom', 'utf-8')
                
        except Exception as e:
            print(f"设置编码失败: {e}")
    
    @staticmethod
    def _bind_events(entry: tk.Entry):
        """绑定Entry事件处理器"""
        def on_key_press(event):
            """按键事件处理"""
            try:
                # 处理特殊按键
                if event.keysym in ['Return', 'Tab', 'Escape']:
                    # 确保输入法候选框关闭
                    entry.tk.call('tk::TextSetCursor', entry, 'insert')
                    
                # 处理中文输入法状态
                if hasattr(event, 'char') and event.char:
                    # 确保字符正确显示
                    entry.update_idletasks()
                    
            except Exception as e:
                print(f"按键事件处理失败: {e}")
            
            return None
        
        def on_focus_in(event):
            """获得焦点事件处理"""
            try:
                # 设置输入法状态
                entry.tk.call('tk::TextSetCursor', entry, 'insert')
                
                # 刷新显示
                entry.update_idletasks()
                
            except Exception as e:
                print(f"焦点事件处理失败: {e}")
            
            return None
        
        def on_focus_out(event):
            """失去焦点事件处理"""
            try:
                # 确保输入法候选框关闭
                entry.tk.call('event', 'generate', entry, '<<Clear>>')
                
                # 强制更新显示
                entry.update()
                
            except Exception as e:
                print(f"失焦事件处理失败: {e}")
            
            return None
        
        def on_configure(event):
            """配置变更事件处理"""
            try:
                # 确保显示正确
                entry.update_idletasks()
            except:
                pass
            return None
        
        # 绑定事件
        entry.bind('<KeyPress>', on_key_press)
        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)
        entry.bind('<Configure>', on_configure)
        
        # 绑定输入法相关事件
        try:
            entry.bind('<<Modified>>', lambda e: entry.update_idletasks())
        except:
            pass
    
    @staticmethod
    def _bind_text_events(text: tk.Text):
        """绑定Text事件处理器"""
        def on_key_press(event):
            """按键事件处理"""
            try:
                # 处理特殊按键
                if event.keysym in ['Return', 'Tab']:
                    text.update_idletasks()
                    
            except Exception as e:
                print(f"Text按键事件处理失败: {e}")
            
            return None
        
        def on_focus_in(event):
            """获得焦点事件处理"""
            try:
                text.update_idletasks()
            except:
                pass
            return None
        
        # 绑定事件
        text.bind('<KeyPress>', on_key_press)
        text.bind('<FocusIn>', on_focus_in)
    
    @staticmethod
    def _set_ime_attributes(widget):
        """设置输入法属性"""
        try:
            # 设置DPI感知
            widget.tk.call('tk', 'scaling', 1.0)
            
            # 在Windows上设置特殊属性
            if platform.system() == 'Windows':
                try:
                    # 设置输入法窗口属性
                    widget.tk.call('wm', 'attributes', widget.winfo_toplevel(), '-alpha', 1.0)
                except:
                    pass
            
            # 设置字体渲染
            try:
                widget.tk.call('font', 'configure', 'TkDefaultFont', '-family', 'Microsoft YaHei')
            except:
                pass
                
        except Exception as e:
            print(f"设置输入法属性失败: {e}")
    
    @staticmethod
    def fix_window(window):
        """
        修复整个窗口的中文输入法问题
        
        Args:
            window: 要修复的窗口
        """
        try:
            # 设置窗口编码
            window.tk.call('encoding', 'system', 'utf-8')
            
            # 设置窗口属性
            if platform.system() == 'Windows':
                try:
                    window.tk.call('wm', 'attributes', window, '-alpha', 1.0)
                except:
                    pass
            
            # 递归修复所有子组件
            ChineseInputFixer._fix_children(window)
            
        except Exception as e:
            print(f"修复窗口失败: {e}")
    
    @staticmethod
    def _fix_children(parent):
        """递归修复子组件"""
        try:
            for child in parent.winfo_children():
                if isinstance(child, tk.Entry):
                    ChineseInputFixer.fix_entry(child)
                elif isinstance(child, tk.Text):
                    ChineseInputFixer.fix_text(child)
                
                # 递归处理子组件
                ChineseInputFixer._fix_children(child)
                
        except Exception as e:
            print(f"修复子组件失败: {e}")


def fix_chinese_input_globally():
    """全局修复中文输入法问题"""
    try:
        # 设置Python编码
        if sys.version_info >= (3, 7):
            import locale
            locale.setlocale(locale.LC_ALL, '')
        
        # 设置tkinter默认编码
        import tkinter as tk
        root = tk._default_root
        if root:
            ChineseInputFixer.fix_window(root)
            
    except Exception as e:
        print(f"全局修复失败: {e}")


# 自动应用修复
if __name__ != "__main__":
    try:
        fix_chinese_input_globally()
    except:
        pass
