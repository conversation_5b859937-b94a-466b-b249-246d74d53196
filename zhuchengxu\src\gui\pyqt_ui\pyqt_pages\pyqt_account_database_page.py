#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt版本的账号库页面
管理自动注册的账号信息和14天倒计时
"""

import sys
import os
import json
from datetime import datetime, timedelta

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QTableWidget, QTableWidgetItem,
                             QHeaderView, QFrame, QGridLayout, QMessageBox,
                             QFileDialog, QProgressDialog)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 导入设计系统
try:
    from ..pyqt_design_system import DS
except ImportError:
    try:
        from src.gui.pyqt_ui.pyqt_design_system import DS
    except ImportError:
        # 如果导入失败，创建一个简单的设计系统
        from PyQt5.QtGui import QColor
        class SimpleDS:
            COLORS = {
                'text_primary': QColor('#ffffff'),
                'text_secondary': QColor('#b8c5d6'),
                'bg_secondary': QColor('#1a1a2e'),
                'bg_primary': QColor('#0a0a0f'),
                'border': QColor('#374151'),
                'neon_blue': QColor('#3b82f6'),
                'neon_green': QColor('#10b981'),
                'neon_orange': QColor('#f59e0b'),
                'neon_red': QColor('#ff4757'),
                'neon_cyan': QColor('#00d4ff'),
            }
        DS = SimpleDS()

try:
    from utils.logger import get_logger
except ImportError:
    from src.utils.logger import get_logger

class PyQtAccountDatabasePage(QWidget):
    """PyQt版本的账号库页面"""
    
    def __init__(self, config_manager=None, parent=None):
        """初始化账号库页面
        
        Args:
            config_manager: 配置管理器
            parent: 父组件，应该是主窗口
        """
        # 简单日志初始化，用于早期记录
        class SimpleLogger:
            def info(self, msg): print(f"INFO: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
            def debug(self, msg): print(f"DEBUG: {msg}")
        
        self._temp_logger = SimpleLogger()
        self._temp_logger.info("账号库页面初始化开始")
        
        # 显式传入parent参数，确保正确设置父组件
        super().__init__(parent)
        
        self.config_manager = config_manager
        
        # 确保当前对象没有被错误地设置为顶层窗口
        self.setWindowFlags(Qt.Widget)
        
        # 记录父组件信息
        parent_info = f"{parent.__class__.__name__}" if parent else "None"
        self._temp_logger.info(f"账号库页面父组件: {parent_info}")
        
        # 初始化真正的日志系统
        try:
        self.logger = get_logger(__name__)
        except Exception as e:
            self._temp_logger.error(f"日志初始化失败: {e}")
            self.logger = self._temp_logger
        
        # 🔧 账号库文件路径
        import sys
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
        if config_dir:
            self.accounts_file = os.path.join(config_dir, 'accounts.json')
        else:
            if sys.argv and len(sys.argv) > 0:
                argv_path = os.path.abspath(sys.argv[0])
                if argv_path.endswith('.exe'):
                    self.accounts_file = os.path.join(os.path.dirname(argv_path), 'accounts.json')
                else:
                    self.accounts_file = os.path.join(os.path.dirname(argv_path), 'accounts.json')
            else:
                self.accounts_file = os.path.join(os.getcwd(), 'accounts.json')
        
        # 创建UI
        self._create_ui()
        
        # 设置定时器刷新
        try:
            self.refresh_timer = QTimer(self)  # 显式指定父对象，确保在同一线程
        self.refresh_timer.timeout.connect(self.refresh_accounts)
        self.refresh_timer.start(60000)  # 每分钟刷新一次
            self.logger.info("✅ 账号库刷新定时器已启动")
        except Exception as e:
            self.logger.error(f"❌ 创建刷新定时器失败: {e}")
        
        # 初始加载
        self.refresh_accounts()
        
    def hideEvent(self, event):
        """页面隐藏时停止定时器"""
        if hasattr(self, 'refresh_timer') and self.refresh_timer.isActive():
            self.refresh_timer.stop()
            self.logger.info("⏸️ 账号库页面刷新定时器已停止")
        super().hideEvent(event)
        
    def showEvent(self, event):
        """页面显示时恢复定时器"""
        if hasattr(self, 'refresh_timer') and not self.refresh_timer.isActive():
            self.refresh_timer.start(60000)
            self.logger.info("▶️ 账号库页面刷新定时器已恢复")
        super().showEvent(event)
    
    def _create_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 页面标题
        self._create_title(layout)

        # 账号列表（删除统计卡片）
        self._create_account_table(layout)
        
        # 操作按钮
        self._create_action_buttons(layout)
    
    def _create_title(self, parent_layout):
        """创建页面标题"""
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(5)
        
        # 主标题
        title_label = QLabel("📊 账号库")
        title_label.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        title_layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("管理自动注册的账号信息和使用期限")
        subtitle_label.setFont(QFont("Microsoft YaHei", 12))
        subtitle_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        title_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(title_frame)
    

    
    def _create_account_table(self, parent_layout):
        """创建账号表格"""
        # 表格标题
        table_title = QLabel("📋 账号列表")
        table_title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        table_title.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()};")
        parent_layout.addWidget(table_title)
        
        # 创建表格
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(6)
        self.account_table.setHorizontalHeaderLabels(['邮箱地址', '创建时间', '剩余天数', '状态', '使用次数', '操作'])
        
        # 设置表格样式 - 修复选中行文字颜色
        self.account_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['border'].name()};
                border-radius: 8px;
                gridline-color: {DS.COLORS['border'].name()};
                color: {DS.COLORS['text_primary'].name()};
                font-size: 14px;
                selection-background-color: {DS.COLORS['neon_blue'].name()};
                selection-color: #ffffff;
            }}
            QTableWidget::item {{
                padding: 15px 10px;
                border-bottom: 1px solid {DS.COLORS['border'].name()};
                min-height: 40px;
                color: {DS.COLORS['text_primary'].name()};
            }}
            QTableWidget::item:selected {{
                background-color: {DS.COLORS['neon_blue'].name()};
                color: #ffffff;
            }}
            QTableWidget::item:hover {{
                background-color: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QHeaderView::section {{
                background-color: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
                padding: 15px 12px;
                border: none;
                border-bottom: 2px solid {DS.COLORS['neon_cyan'].name()};
                font-weight: bold;
                font-size: 14px;
            }}
        """)

        # 设置表格行高
        self.account_table.verticalHeader().setDefaultSectionSize(50)
        
        # 设置表格属性 - 响应式设计
        self.account_table.setAlternatingRowColors(True)
        self.account_table.setSelectionBehavior(QTableWidget.SelectRows)

        # 设置列宽自适应 - 调整操作列宽度
        header = self.account_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 邮箱地址列自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 创建时间列适应内容
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 剩余天数列适应内容
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 状态列适应内容
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 使用次数列适应内容
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 操作列固定宽度
        self.account_table.setColumnWidth(5, 70)  # 操作列宽度70px，适合按钮大小
        
        parent_layout.addWidget(self.account_table)
    
    def _create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(15)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新账号库")
        refresh_btn.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {DS.COLORS['neon_blue'].name()};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DS.COLORS['neon_cyan'].name()};
            }}
            QPushButton:pressed {{
                background-color: {DS.COLORS['neon_blue'].darker().name()};
            }}
        """)
        refresh_btn.clicked.connect(self.refresh_accounts)
        
        # 清理过期账号按钮
        cleanup_btn = QPushButton("🗑️ 清理过期账号")
        cleanup_btn.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        cleanup_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {DS.COLORS['neon_red'].name()};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DS.COLORS['neon_orange'].name()};
            }}
            QPushButton:pressed {{
                background-color: {DS.COLORS['neon_red'].darker().name()};
            }}
        """)
        cleanup_btn.clicked.connect(self.cleanup_expired_accounts)
        
        # 导出账号按钮
        export_btn = QPushButton("📤 导出账号")
        export_btn.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        export_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {DS.COLORS['neon_green'].name()};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DS.COLORS['neon_green'].lighter().name()};
            }}
            QPushButton:pressed {{
                background-color: {DS.COLORS['neon_green'].darker().name()};
            }}
        """)
        export_btn.clicked.connect(self.export_accounts)

        # 导入账号按钮
        import_btn = QPushButton("📥 导入账号")
        import_btn.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        import_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {DS.COLORS['neon_cyan'].name()};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DS.COLORS['neon_cyan'].lighter().name()};
            }}
            QPushButton:pressed {{
                background-color: {DS.COLORS['neon_cyan'].darker().name()};
            }}
        """)
        import_btn.clicked.connect(self.import_accounts)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(cleanup_btn)
        button_layout.addWidget(export_btn)
        button_layout.addWidget(import_btn)
        button_layout.addStretch()
        
        parent_layout.addWidget(button_frame)
    
    def refresh_accounts(self):
        """刷新账号列表"""
        try:
            self.logger.info("🔄 刷新账号库...")
            
            # 读取账号数据
            accounts = self._load_accounts()
            
            # 统计信息已删除，直接更新表格
            
            # 更新账号列表
            self._update_account_table(accounts)
            
            self.logger.info("✅ 账号库刷新完成")
            
        except Exception as e:
            self.logger.error(f"❌ 刷新账号库失败: {e}")
    
    def _load_accounts(self):
        """加载账号数据"""
        try:
            if os.path.exists(self.accounts_file):
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    accounts = json.load(f)
                    
                # 更新剩余天数
                for account in accounts:
                    self._update_account_status(account)
                
                return accounts
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"加载账号数据失败: {e}")
            return []
    
    def _update_account_status(self, account):
        """更新账号状态和剩余天数"""
        try:
            expire_time = datetime.strptime(account['expire_time'], '%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            
            if now > expire_time:
                account['status'] = 'expired'
                account['days_remaining'] = 0
            else:
                remaining = expire_time - now
                account['days_remaining'] = remaining.days
                
                if remaining.days <= 3:
                    account['status'] = 'expiring'
                else:
                    account['status'] = 'active'
                    
        except Exception as e:
            self.logger.error(f"更新账号状态失败: {e}")
            account['status'] = 'unknown'
            account['days_remaining'] = 0
    

    
    def _update_account_table(self, accounts):
        """更新账号表格"""
        try:
            # 设置行数
            self.account_table.setRowCount(len(accounts))
            
            # 添加账号数据
            for row, account in enumerate(accounts):
                # 邮箱地址
                email_item = QTableWidgetItem(account['email'])
                self.account_table.setItem(row, 0, email_item)
                
                # 创建时间
                created_item = QTableWidgetItem(account['created_time'])
                self.account_table.setItem(row, 1, created_item)
                
                # 剩余天数
                days_text = f"{account['days_remaining']} 天" if account['days_remaining'] > 0 else "已过期"
                days_item = QTableWidgetItem(days_text)
                self.account_table.setItem(row, 2, days_item)
                
                # 状态
                status_text = {
                    'active': '✅ 活跃',
                    'expiring': '⚠️ 即将过期',
                    'expired': '❌ 已过期',
                    'unknown': '❓ 未知'
                }.get(account.get('status', 'unknown'), '❓ 未知')
                status_item = QTableWidgetItem(status_text)
                self.account_table.setItem(row, 3, status_item)
                
                # 使用次数
                usage_item = QTableWidgetItem(str(account.get('usage_count', 0)))
                self.account_table.setItem(row, 4, usage_item)

                # 直接创建登录按钮 - 简化设计
                login_btn = QPushButton("登录")
                login_btn.setFixedSize(60, 28)  # 固定尺寸
                login_btn.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {DS.COLORS['neon_blue'].name()},
                            stop:1 {DS.COLORS['neon_blue'].darker(120).name()});
                        color: white;
                        border: 1px solid {DS.COLORS['neon_blue'].darker(150).name()};
                        border-radius: 4px;
                        font-size: 11px;
                        font-weight: bold;
                        padding: 0px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {DS.COLORS['neon_cyan'].name()},
                            stop:1 {DS.COLORS['neon_cyan'].darker(120).name()});
                        border: 1px solid {DS.COLORS['neon_cyan'].darker(150).name()};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {DS.COLORS['neon_blue'].darker(130).name()},
                            stop:1 {DS.COLORS['neon_blue'].darker(160).name()});
                        border: 1px solid {DS.COLORS['neon_blue'].darker(180).name()};
                    }}
                """)

                # 绑定登录功能
                login_btn.clicked.connect(lambda checked, acc=account: self.login_account(acc))

                # 直接设置到表格单元格
                self.account_table.setCellWidget(row, 5, login_btn)
                
        except Exception as e:
            self.logger.error(f"更新账号表格失败: {e}")
    
    def cleanup_expired_accounts(self):
        """清理过期账号"""
        try:
            self.logger.info("🗑️ 开始清理过期账号...")
            
            accounts = self._load_accounts()
            active_accounts = [a for a in accounts if a.get('status') != 'expired']
            
            expired_count = len(accounts) - len(active_accounts)
            
            if expired_count > 0:
                # 确认对话框
                reply = QMessageBox.question(self, '确认清理', 
                                           f'确定要清理 {expired_count} 个过期账号吗？',
                                           QMessageBox.Yes | QMessageBox.No,
                                           QMessageBox.No)
                
                if reply == QMessageBox.Yes:
                    # 保存清理后的账号
                    with open(self.accounts_file, 'w', encoding='utf-8') as f:
                        json.dump(active_accounts, f, ensure_ascii=False, indent=2)
                    
                    self.logger.info(f"✅ 已清理 {expired_count} 个过期账号")
                    
                    # 刷新显示
                    self.refresh_accounts()
                    
                    self._show_styled_message('清理完成', f'已成功清理 {expired_count} 个过期账号', 'success')
            else:
                self.logger.info("💡 没有过期账号需要清理")
                self._show_styled_message('清理结果', '没有过期账号需要清理', 'info')

        except Exception as e:
            self.logger.error(f"❌ 清理过期账号失败: {e}")
            self._show_styled_message('清理失败', f'清理过期账号失败：{str(e)}', 'error')
    
    def export_accounts(self):
        """导出账号数据 - 使用文件对话框选择路径"""
        try:
            self.logger.info("📤 导出账号数据...")

            accounts = self._load_accounts()

            if not accounts:
                self._show_styled_message('导出结果', '没有账号数据可导出', 'info')
                return

            # 弹出文件保存对话框
            default_filename = f"accounts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                '导出账号数据',
                default_filename,
                'JSON文件 (*.json);;所有文件 (*.*)'
            )

            if not file_path:
                self.logger.info("用户取消了导出操作")
                return

            # 导出数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ 账号数据已导出到: {file_path}")
            self._show_styled_message('导出成功', f'账号数据已导出到：\n{file_path}', 'success')

        except Exception as e:
            self.logger.error(f"❌ 导出账号数据失败: {e}")
            self._show_styled_message('导出失败', f'导出账号数据失败：{str(e)}', 'error')
    
    def import_accounts(self):
        """导入账号数据 - 使用文件对话框选择文件"""
        try:
            self.logger.info("📥 导入账号数据...")

            # 弹出文件选择对话框
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                '导入账号数据',
                '',
                'JSON文件 (*.json);;所有文件 (*.*)'
            )

            if not file_path:
                self.logger.info("用户取消了导入操作")
                return

            # 读取导入文件
            with open(file_path, 'r', encoding='utf-8') as f:
                import_accounts = json.load(f)

            if not isinstance(import_accounts, list):
                self._show_styled_message('导入失败', '文件格式错误：数据应该是账号列表', 'error')
                return

            # 验证数据格式
            required_fields = ['email', 'created_time', 'expire_time']
            invalid_accounts = []

            for i, account in enumerate(import_accounts):
                if not isinstance(account, dict):
                    invalid_accounts.append(f"第{i+1}个账号不是有效的对象")
                    continue

                missing_fields = [field for field in required_fields if field not in account]
                if missing_fields:
                    invalid_accounts.append(f"第{i+1}个账号缺少字段: {missing_fields}")

            if invalid_accounts:
                error_msg = "导入文件中存在无效账号：\n" + "\n".join(invalid_accounts[:5])
                if len(invalid_accounts) > 5:
                    error_msg += f"\n... 还有{len(invalid_accounts)-5}个错误"
                self._show_styled_message('导入失败', error_msg, 'error')
                return

            # 确认导入
            reply = QMessageBox.question(
                self, '确认导入',
                f'确定要导入 {len(import_accounts)} 个账号吗？\n这将与现有账号合并。',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 加载现有账号
            existing_accounts = self._load_accounts()

            # 合并账号（避免重复）
            existing_emails = {acc['email'] for acc in existing_accounts}
            new_accounts = []
            duplicate_count = 0

            for account in import_accounts:
                if account['email'] not in existing_emails:
                    # 分配新ID
                    account['id'] = len(existing_accounts) + len(new_accounts) + 1
                    # 更新账号状态
                    self._update_account_status(account)
                    new_accounts.append(account)
                    existing_emails.add(account['email'])
                else:
                    duplicate_count += 1

            # 保存合并后的账号
            all_accounts = existing_accounts + new_accounts

            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(all_accounts, f, ensure_ascii=False, indent=2)

            # 刷新显示
            self.refresh_accounts()

            # 显示结果
            result_msg = f'成功导入 {len(new_accounts)} 个账号'
            if duplicate_count > 0:
                result_msg += f'\n跳过 {duplicate_count} 个重复账号'

            self.logger.info(f"✅ {result_msg}")
            self._show_styled_message('导入成功', result_msg, 'success')

        except json.JSONDecodeError:
            self._show_styled_message('导入失败', '文件格式错误：不是有效的JSON文件', 'error')
        except Exception as e:
            self.logger.error(f"❌ 导入账号数据失败: {e}")
            self._show_styled_message('导入失败', f'导入账号数据失败：{str(e)}', 'error')

    def login_account(self, account):
        """注册账号 - 和主页完全相同的自动注册流程"""
        try:
            self.logger.info(f"🔑 开始注册账号: {account['email']}")

            # 检查账号状态
            if account.get('status') == 'expired':
                self._show_styled_message('登录失败', '该账号已过期，无法登录', 'warning')
                return

            # 选择注册模式 - 自定义对话框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle('选择注册模式')
            msg_box.setText(f'请选择账号 {account["email"]} 的注册模式：')
            msg_box.setInformativeText(
                '🤖 自动注册：和主页完全相同的自动注册流程（推荐）\n'
                '   • 识别已打开augmentcode.com的浏览器\n'
                '   • 执行Tab 6次 + Enter自动操作\n'
                '   • 自动输入预留邮箱并处理验证码\n'
                '📋 手动注册：打开浏览器，复制邮箱，手动完成\n'
                '❌ 取消：不执行任何操作'
            )

            # 添加自定义按钮
            auto_btn = msg_box.addButton('🤖 自动注册', QMessageBox.AcceptRole)
            manual_btn = msg_box.addButton('📋 手动注册', QMessageBox.ActionRole)
            cancel_btn = msg_box.addButton('❌ 取消', QMessageBox.RejectRole)
            msg_box.setDefaultButton(auto_btn)

            # 设置对话框样式确保文字可见 - 强制白色文字
            msg_box.setStyleSheet(f"""
                QMessageBox {{
                    background-color: {DS.COLORS['bg_primary'].name()};
                    color: #ffffff;
                }}
                QMessageBox QLabel {{
                    color: #ffffff;
                    font-size: 14px;
                    padding: 10px;
                    background-color: transparent;
                }}
                QMessageBox QPushButton {{
                    background-color: {DS.COLORS['neon_blue'].name()};
                    color: #ffffff;
                    border: none;
                    padding: 8px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                }}
                QMessageBox QPushButton:hover {{
                    background-color: {DS.COLORS['neon_cyan'].name()};
                    color: #ffffff;
                }}
            """)

            msg_box.exec_()
            clicked_button = msg_box.clickedButton()

            # 根据点击的按钮决定执行模式
            if clicked_button == cancel_btn:
                return
            elif clicked_button == auto_btn:
                # 自动模式 - 完整自动注册流程
                progress_title = "自动注册"
                progress_text = "正在执行自动注册流程..."
                use_auto_mode = True
            elif clicked_button == manual_btn:
                # 手动模式 - 简化流程
                progress_title = "手动注册"
                progress_text = "正在准备手动注册..."
                use_auto_mode = False
            else:
                return

            # 创建进度对话框
            progress = QProgressDialog(progress_text, "取消", 0, 100, self)
            progress.setWindowTitle(progress_title)
            progress.setModal(True)
            progress.show()

            # 根据模式启动相应的流程
            if use_auto_mode:
                success = self._execute_browser_login(account, progress)
            else:
                success = self._simple_browser_login(account, progress)

            progress.close()

            if success:
                # 更新使用次数
                account['usage_count'] = account.get('usage_count', 0) + 1
                account['last_used'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 保存更新
                self._save_account_update(account)

                # 刷新显示
                self.refresh_accounts()

                self.logger.info(f"✅ 账号注册成功: {account['email']}")
                self._show_styled_message('注册成功', f'账号 {account["email"]} 注册成功！', 'success')
            else:
                self.logger.error(f"❌ 账号注册失败: {account['email']}")
                self._show_styled_message('注册失败', '账号注册失败，请检查浏览器状态', 'error')

        except Exception as e:
            self.logger.error(f"❌ 注册账号失败: {e}")
            self._show_styled_message('注册失败', f'注册账号失败：{str(e)}', 'error')

    def _execute_browser_login(self, account, progress):
        """执行浏览器自动登录流程"""
        self.logger.info(f"🔍 开始自动登录流程: {account['email']}")
        
        # 初始化进度
            progress.setValue(10)
        progress.setLabelText("正在检测浏览器...")

        # 检测运行中的浏览器
        browser_proc = self._find_browser_process()
        if not browser_proc:
            progress.setValue(100)
            self._show_styled_message("登录失败", "未找到运行中的Chrome浏览器，请先打开浏览器并访问登录页面", "warning")
            return False

            progress.setValue(20)
        progress.setLabelText("正在准备输入序列...")

        try:
            # 导入操作库
            import pyautogui
            import time
            import pyperclip
            from ctypes import windll
            
            # 防止鼠标移动导致操作失败
            pyautogui.FAILSAFE = False
            
            # 预置邮箱到剪贴板
            email = account['email']
            pyperclip.copy(email)
            
            # 输入Tab键六次，到达邮箱输入框
            progress.setValue(30)
            progress.setLabelText("正在定位表单域...")
            time.sleep(1)  # 等待浏览器响应
            
            # Tab 5次到密码框
            for i in range(5):
                pyautogui.press('tab')
                time.sleep(0.3)

            progress.setValue(40)
            progress.setLabelText("正在粘贴邮箱...")

            # 粘贴邮箱
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(0.5)
            
            # 重要：等待4秒，确保验证码等操作完成
            progress.setValue(50)
            progress.setLabelText("等待4秒钟，确保系统响应...")
            self.logger.info("⏳ 等待4秒，确保系统响应...")
            time.sleep(4)

            # 继续Tab到"马上开始"按钮
            progress.setValue(60)
            progress.setLabelText("定位提交按钮...")
            pyautogui.press('tab')
            time.sleep(0.3)
            
            # 按回车键提交
            progress.setValue(70)
            progress.setLabelText("提交表单...")
            pyautogui.press('enter')

            # 等待登录完成
            progress.setValue(80)
            progress.setLabelText("等待登录完成...")
            time.sleep(2)
            
            # 更新账号使用记录
            try:
                # 复制当前账号信息
                updated_account = account.copy()
                # 更新最后使用时间
                updated_account['last_used'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # 增加使用次数
                if 'usage_count' not in updated_account:
                    updated_account['usage_count'] = 0
                updated_account['usage_count'] += 1
                
                # 保存更新
                self._save_account_update(updated_account)

            progress.setValue(90)
                progress.setLabelText("更新账号使用记录...")

            except Exception as e:
                self.logger.error(f"❌ 更新账号记录失败: {e}")
                
            # 操作完成
            progress.setValue(100)
            self._show_styled_message("登录成功", f"已成功使用账号 {email} 登录", "success")
            self.logger.info(f"✅ 成功登录账号: {email}")

            # 刷新账号列表
            self.refresh_accounts()
            return True

        except Exception as e:
            self.logger.error(f"❌ 自动登录失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            progress.setValue(100)
            self._show_styled_message("登录失败", f"自动登录过程出现错误: {str(e)}", "error")
            return False

    def _simple_browser_login(self, account, progress):
        """简化版浏览器登录"""
        try:
            import time
            import subprocess

            progress.setValue(30)
            progress.setLabelText("正在查找浏览器进程...")

            # 查找浏览器进程
            browser_found = self._find_browser_process()
            if not browser_found:
                return False

            progress.setValue(60)
            progress.setLabelText("正在模拟登录操作...")

            # 模拟登录操作（简化版）
            time.sleep(2)  # 模拟操作时间

            progress.setValue(100)

            return True

        except Exception as e:
            self.logger.error(f"简化版浏览器登录失败: {e}")
            return False

    def _find_browser_process(self):
        """查找浏览器进程"""
        try:
            import psutil

            browser_names = ['chrome.exe', 'firefox.exe', 'msedge.exe', 'opera.exe']

            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() in [name.lower() for name in browser_names]:
                    self.logger.info(f"找到浏览器进程: {proc.info['name']}")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"查找浏览器进程失败: {e}")
            return False

    def _save_account_update(self, updated_account):
        """保存账号更新"""
        try:
            accounts = self._load_accounts()

            # 找到并更新账号
            for i, account in enumerate(accounts):
                if account['email'] == updated_account['email']:
                    accounts[i] = updated_account
                    break

            # 保存更新
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存账号更新失败: {e}")

    def _show_styled_message(self, title, message, msg_type='info'):
        """显示自定义样式的消息框"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)

            # 根据消息类型设置图标和颜色
            if msg_type == 'success':
                msg_box.setIcon(QMessageBox.Information)
                color = DS.COLORS['neon_green'].name()
            elif msg_type == 'error':
                msg_box.setIcon(QMessageBox.Critical)
                color = DS.COLORS['neon_red'].name()
            elif msg_type == 'warning':
                msg_box.setIcon(QMessageBox.Warning)
                color = DS.COLORS['neon_orange'].name()
            else:
                msg_box.setIcon(QMessageBox.Information)
                color = DS.COLORS['neon_blue'].name()

            # 设置样式确保文字可见 - 强制白色文字
            msg_box.setStyleSheet(f"""
                QMessageBox {{
                    background-color: {DS.COLORS['bg_primary'].name()};
                    color: #ffffff;
                    border: 2px solid {color};
                    border-radius: 8px;
                }}
                QMessageBox QLabel {{
                    color: #ffffff;
                    font-size: 14px;
                    padding: 15px;
                    background-color: transparent;
                }}
                QMessageBox QPushButton {{
                    background-color: {color};
                    color: #ffffff;
                    border: none;
                    padding: 10px 25px;
                    border-radius: 5px;
                    font-weight: bold;
                    min-width: 80px;
                    font-size: 12px;
                }}
                QMessageBox QPushButton:hover {{
                    background-color: {DS.COLORS['neon_cyan'].name()};
                    color: #ffffff;
                }}
                QMessageBox QPushButton:pressed {{
                    background-color: {DS.COLORS['bg_secondary'].name()};
                    color: #ffffff;
                }}
            """)

            msg_box.exec_()

        except Exception as e:
            self.logger.error(f"显示消息框失败: {e}")
            # 备用方案
            print(f"{title}: {message}")

    def get_statistics(self):
        """获取统计数据"""
        try:
            accounts = self._load_accounts()
            return {
                'total': len(accounts),
                'active': len([a for a in accounts if a.get('status') == 'active']),
                'expiring': len([a for a in accounts if a.get('status') == 'expiring']),
                'expired': len([a for a in accounts if a.get('status') == 'expired'])
            }
        except Exception:
            return {'total': 0, 'active': 0, 'expiring': 0, 'expired': 0}
