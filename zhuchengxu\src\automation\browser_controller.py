#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器控制器
接管当前浏览器窗口执行登录操作
"""

import time
import ctypes
import subprocess
from ctypes import wintypes

# 兼容性导入
try:
    from src.utils.logger import get_logger
except ImportError:
    try:
        from utils.logger import get_logger
    except ImportError:
        # 如果都失败，使用标准logging
        import logging
        def get_logger(name):
            return logging.getLogger(name)

class BrowserController:
    """浏览器控制器 - 接管当前浏览器窗口"""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.browser_hwnd = None
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32

    def login_with_takeover_mode(self, email):
        """接管模式登录 - 和自动注册相同原理，使用固定邮箱，不启动浏览器"""
        try:
            self.logger.info(f"🔑 开始接管模式登录，使用邮箱: {email}")

            # 1. 寻找可用的浏览器窗口（和自动注册相同逻辑）
            if not self._find_any_browser_window():
                self.logger.error("❌ 未找到任何可用的浏览器窗口")
                return False

            # 2. 激活浏览器窗口
            self._activate_browser_window()
            time.sleep(1)

            # 3. 使用和自动注册完全相同的6次Tab键策略
            success = self._execute_registration_strategy(email)

            if success:
                self.logger.info(f"✅ 接管模式登录成功: {email}")
            else:
                self.logger.error(f"❌ 接管模式登录失败: {email}")

            return success

        except Exception as e:
            self.logger.error(f"❌ 接管模式登录异常: {e}")
            return False

    def _find_any_browser_window(self):
        """寻找任何可用的浏览器窗口 - 和自动注册相同逻辑"""
        try:
            self.logger.info("🔍 寻找可用的浏览器窗口...")

            windows = []

            # 枚举所有窗口，寻找浏览器
            def enum_windows_callback(hwnd, lparam):
                if self.user32.IsWindowVisible(hwnd):
                    length = self.user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                        title = buffer.value.lower()

                        # 检查是否是浏览器窗口
                        browser_keywords = ['chrome', 'firefox', 'edge', 'safari', 'opera', 'browser']
                        if any(keyword in title for keyword in browser_keywords):
                            windows.append((hwnd, buffer.value))
                return True

            enum_windows_proc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
            self.user32.EnumWindows(enum_windows_proc(enum_windows_callback), 0)

            if windows:
                # 选择第一个找到的浏览器窗口
                self.browser_hwnd = windows[0][0]
                self.logger.info(f"✅ 找到浏览器窗口: {windows[0][1]}")
                return True
            else:
                self.logger.error("❌ 未找到任何浏览器窗口")
                return False

        except Exception as e:
            self.logger.error(f"❌ 寻找浏览器窗口失败: {e}")
            return False

    def _activate_browser_window(self):
        """激活浏览器窗口"""
        try:
            # 将窗口置于前台
            self.user32.SetForegroundWindow(self.browser_hwnd)
            time.sleep(0.5)

            # 点击窗口标题栏确保激活
            self._click_window_titlebar()
            time.sleep(0.5)

            self.logger.info("✅ 浏览器窗口已激活")

        except Exception as e:
            self.logger.error(f"❌ 激活浏览器窗口失败: {e}")

    def _execute_registration_strategy(self, email):
        """执行和自动注册完全相同的策略"""
        try:
            self.logger.info(f"📧 执行注册策略输入邮箱: {email}")

            # 和自动注册完全相同：按6次Tab键定位到邮箱输入框
            self.logger.info("🎯 执行6次Tab键定位邮箱输入框")
            for i in range(6):
                self.user32.keybd_event(0x09, 0, 0, 0)  # Tab键按下
                self.user32.keybd_event(0x09, 0, 0x0002, 0)  # Tab键释放
                time.sleep(0.1)

            # 输入邮箱地址（和自动注册相同的逐字符输入）
            if not self._type_email_like_registration(email):
                return False

            # 提交（和自动注册相同的Enter键）
            return self._submit_like_registration()

        except Exception as e:
            self.logger.error(f"❌ 执行注册策略失败: {e}")
            return False

    def _type_email_like_registration(self, email):
        """和自动注册相同的方式输入邮箱"""
        try:
            self.logger.info(f"⌨️ 输入邮箱地址: {email}")

            # 清空当前输入框（和自动注册相同）
            self.user32.keybd_event(0x11, 0, 0, 0)  # Ctrl按下
            self.user32.keybd_event(0x41, 0, 0, 0)  # A按下
            self.user32.keybd_event(0x41, 0, 0x0002, 0)  # A释放
            self.user32.keybd_event(0x11, 0, 0x0002, 0)  # Ctrl释放
            time.sleep(0.1)

            # 逐字符输入邮箱（和自动注册完全相同）
            for char in email:
                if char == '@':
                    # 输入@符号
                    self.user32.keybd_event(0x10, 0, 0, 0)  # Shift按下
                    self.user32.keybd_event(0x32, 0, 0, 0)  # 2按下
                    self.user32.keybd_event(0x32, 0, 0x0002, 0)  # 2释放
                    self.user32.keybd_event(0x10, 0, 0x0002, 0)  # Shift释放
                elif char == '.':
                    # 输入.符号
                    self.user32.keybd_event(0xBE, 0, 0, 0)  # .按下
                    self.user32.keybd_event(0xBE, 0, 0x0002, 0)  # .释放
                else:
                    # 输入普通字符
                    vk_code = ord(char.upper())
                    self.user32.keybd_event(vk_code, 0, 0, 0)
                    self.user32.keybd_event(vk_code, 0, 0x0002, 0)

                time.sleep(0.05)  # 字符间延迟

            self.logger.info(f"✅ 邮箱输入完成: {email}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 输入邮箱地址失败: {e}")
            return False

    def _submit_like_registration(self):
        """和自动注册相同的方式提交"""
        try:
            self.logger.info("🚀 提交登录信息")

            # 等待输入完成
            time.sleep(0.5)

            # 按Enter键提交（和自动注册完全相同）
            self.user32.keybd_event(0x0D, 0, 0, 0)  # Enter按下
            self.user32.keybd_event(0x0D, 0, 0x0002, 0)  # Enter释放

            self.logger.info("✅ 登录信息已提交")
            return True

        except Exception as e:
            self.logger.error(f"❌ 提交登录失败: {e}")
            return False
    
    def find_browser_window(self):
        """查找正在访问augmentcode.com的浏览器窗口"""
        try:
            self.logger.info("🔍 查找正在访问augmentcode.com的浏览器窗口...")

            # 目标网站关键词
            target_keywords = ['augmentcode', 'Augment Code', 'augment']

            # 浏览器窗口类名
            browser_classes = ['Chrome_WidgetWin_1', 'MozillaWindowClass', 'OperaWindowClass']

            found_windows = []

            def enum_windows_callback(hwnd, lParam):
                if self.user32.IsWindowVisible(hwnd):
                    # 获取窗口标题
                    title_length = self.user32.GetWindowTextLengthW(hwnd)
                    if title_length > 0:
                        title_buffer = ctypes.create_unicode_buffer(title_length + 1)
                        self.user32.GetWindowTextW(hwnd, title_buffer, title_length + 1)
                        title = title_buffer.value

                        # 获取窗口类名
                        class_buffer = ctypes.create_unicode_buffer(256)
                        self.user32.GetClassNameW(hwnd, class_buffer, 256)
                        class_name = class_buffer.value

                        # 检查是否是浏览器窗口且包含目标网站
                        if any(browser_class in class_name for browser_class in browser_classes):
                            if any(keyword.lower() in title.lower() for keyword in target_keywords):
                                found_windows.append({
                                    'hwnd': hwnd,
                                    'title': title,
                                    'class_name': class_name
                                })
                                self.logger.info(f"✅ 找到目标浏览器窗口: {title}")
                return True

            # 枚举所有窗口
            EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            self.user32.EnumWindows(EnumWindowsProc(enum_windows_callback), 0)

            if found_windows:
                # 选择第一个找到的窗口
                self.browser_hwnd = found_windows[0]['hwnd']
                self.logger.info(f"🎯 接管浏览器窗口: {found_windows[0]['title']}")

                # 激活浏览器窗口
                self._activate_window()
                return True
            else:
                self.logger.warning("❌ 未找到正在访问augmentcode.com的浏览器窗口")
                return False

        except Exception as e:
            self.logger.error(f"❌ 查找浏览器窗口失败: {e}")
            return False
    
    def _activate_window(self):
        """激活浏览器窗口"""
        try:
            # 将窗口置于前台
            self.user32.SetForegroundWindow(self.browser_hwnd)
            self.user32.ShowWindow(self.browser_hwnd, 9)  # SW_RESTORE
            time.sleep(0.5)
            self.logger.info("✅ 浏览器窗口已激活")
            
        except Exception as e:
            self.logger.error(f"激活浏览器窗口失败: {e}")
    
    def check_current_page(self):
        """检查当前页面是否为augmentcode.com"""
        try:
            self.logger.info("🔍 检查当前页面...")

            # 获取当前窗口标题
            title_length = self.user32.GetWindowTextLengthW(self.browser_hwnd)
            if title_length > 0:
                title_buffer = ctypes.create_unicode_buffer(title_length + 1)
                self.user32.GetWindowTextW(self.browser_hwnd, title_buffer, title_length + 1)
                title = title_buffer.value

                # 检查是否包含augmentcode关键词
                if any(keyword.lower() in title.lower() for keyword in ['augmentcode', 'augment']):
                    self.logger.info(f"✅ 当前页面正确: {title}")
                    return True
                else:
                    self.logger.warning(f"⚠️ 当前页面不是augmentcode.com: {title}")
                    return False

            return False

        except Exception as e:
            self.logger.error(f"❌ 检查当前页面失败: {e}")
            return False
    
    def input_account_info(self, email):
        """在当前页面输入账号信息"""
        try:
            self.logger.info(f"⌨️ 在当前页面输入账号信息: {email}")

            # 等待页面稳定
            time.sleep(1)

            # 方法1: 点击窗口标题栏激活，然后Tab查找输入框
            self._click_window_titlebar()
            time.sleep(0.5)

            # 尝试多种方式定位邮箱输入框
            success = False

            # 方法2: 使用已知的成功模式（6次Tab + Enter）
            self.logger.info("🎯 使用已知成功模式...")
            for _ in range(6):
                self._send_key('tab')
                time.sleep(0.1)

            # 输入邮箱
            self._type_text(email)
            time.sleep(0.5)

            # 提交（Tab + Enter）
            self._send_key('tab')
            time.sleep(0.2)
            self._send_key('enter')

            self.logger.info("✅ 使用已知模式输入完成")
            success = True

            if not success:
                # 方法3: 尝试快捷键定位（如果页面支持）
                self.logger.info("🔄 尝试其他定位方法...")
                self._send_key_combination(['ctrl', 'f'])  # 打开查找
                time.sleep(0.5)
                self._type_text('email')  # 查找email关键词
                time.sleep(0.5)
                self._send_key('escape')  # 关闭查找
                time.sleep(0.5)

                # 再次尝试输入
                self._type_text(email)
                success = True

            return success

        except Exception as e:
            self.logger.error(f"❌ 输入账号信息失败: {e}")
            return False

    def _click_window_titlebar(self):
        """点击窗口标题栏激活浏览器"""
        try:
            # 获取窗口矩形
            rect = wintypes.RECT()
            self.user32.GetWindowRect(self.browser_hwnd, ctypes.byref(rect))

            # 计算点击位置：窗口标题栏中央
            click_x = (rect.left + rect.right) // 2
            click_y = rect.top + 15  # 标题栏位置，通常在顶部15px左右

            self.logger.info(f"点击窗口标题栏激活浏览器: ({click_x}, {click_y})")

            # 设置鼠标位置并点击
            self.user32.SetCursorPos(click_x, click_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # 左键按下
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # 左键释放

            # 等待窗口激活
            time.sleep(0.2)

        except Exception as e:
            self.logger.error(f"点击窗口标题栏失败: {e}")
    
    def submit_login(self):
        """提交登录（已在input_account_info中完成）"""
        try:
            self.logger.info("🔑 确认登录提交...")

            # 等待页面响应
            time.sleep(2)

            # 检查是否需要额外操作（如人机验证）
            self.logger.info("⏳ 等待页面响应和可能的验证步骤...")
            time.sleep(3)

            self.logger.info("✅ 登录流程完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 登录确认失败: {e}")
            return False
    
    def _send_key(self, key):
        """发送单个按键"""
        try:
            key_codes = {
                'enter': 0x0D,
                'tab': 0x09,
                'space': 0x20,
                'delete': 0x2E,
                'backspace': 0x08,
                'escape': 0x1B,
                'ctrl': 0x11,
                'alt': 0x12,
                'shift': 0x10,
                'l': 0x4C,
                'a': 0x41,
            }
            
            vk_code = key_codes.get(key.lower())
            if vk_code:
                self.user32.keybd_event(vk_code, 0, 0, 0)  # 按下
                time.sleep(0.05)
                self.user32.keybd_event(vk_code, 0, 2, 0)  # 释放
                time.sleep(0.05)
            
        except Exception as e:
            self.logger.error(f"发送按键失败: {e}")
    
    def _send_key_combination(self, keys):
        """发送组合键"""
        try:
            # 按下所有键
            for key in keys:
                self._press_key(key)
                time.sleep(0.05)
            
            # 释放所有键（逆序）
            for key in reversed(keys):
                self._release_key(key)
                time.sleep(0.05)
            
        except Exception as e:
            self.logger.error(f"发送组合键失败: {e}")
    
    def _press_key(self, key):
        """按下按键"""
        key_codes = {
            'ctrl': 0x11,
            'alt': 0x12,
            'shift': 0x10,
            'l': 0x4C,
            'a': 0x41,
        }
        
        vk_code = key_codes.get(key.lower())
        if vk_code:
            self.user32.keybd_event(vk_code, 0, 0, 0)
    
    def _release_key(self, key):
        """释放按键"""
        key_codes = {
            'ctrl': 0x11,
            'alt': 0x12,
            'shift': 0x10,
            'l': 0x4C,
            'a': 0x41,
        }
        
        vk_code = key_codes.get(key.lower())
        if vk_code:
            self.user32.keybd_event(vk_code, 0, 2, 0)
    
    def _type_text(self, text):
        """输入文本"""
        try:
            for char in text:
                if char == '@':
                    # @符号需要特殊处理
                    self.user32.keybd_event(0x10, 0, 0, 0)  # Shift按下
                    self.user32.keybd_event(0x32, 0, 0, 0)  # 2按下
                    self.user32.keybd_event(0x32, 0, 2, 0)  # 2释放
                    self.user32.keybd_event(0x10, 0, 2, 0)  # Shift释放
                elif char == '.':
                    # .符号
                    self.user32.keybd_event(0xBE, 0, 0, 0)  # .按下
                    self.user32.keybd_event(0xBE, 0, 2, 0)  # .释放
                elif char.isalpha():
                    # 字母
                    vk_code = ord(char.upper()) - ord('A') + 0x41
                    self.user32.keybd_event(vk_code, 0, 0, 0)
                    self.user32.keybd_event(vk_code, 0, 2, 0)
                elif char.isdigit():
                    # 数字
                    vk_code = ord(char) - ord('0') + 0x30
                    self.user32.keybd_event(vk_code, 0, 0, 0)
                    self.user32.keybd_event(vk_code, 0, 2, 0)
                elif char == ':':
                    # :符号
                    self.user32.keybd_event(0x10, 0, 0, 0)  # Shift按下
                    self.user32.keybd_event(0xBA, 0, 0, 0)  # ;按下
                    self.user32.keybd_event(0xBA, 0, 2, 0)  # ;释放
                    self.user32.keybd_event(0x10, 0, 2, 0)  # Shift释放
                elif char == '/':
                    # /符号
                    self.user32.keybd_event(0xBF, 0, 0, 0)  # /按下
                    self.user32.keybd_event(0xBF, 0, 2, 0)  # /释放
                
                time.sleep(0.05)  # 每个字符间隔
            
        except Exception as e:
            self.logger.error(f"输入文本失败: {e}")
    
    def get_current_url(self):
        """获取当前页面URL（简化版）"""
        try:
            # 按Ctrl+L选中地址栏
            self._send_key_combination(['ctrl', 'l'])
            time.sleep(0.5)
            
            # 按Ctrl+C复制URL
            self._send_key_combination(['ctrl', 'c'])
            time.sleep(0.5)
            
            # 从剪贴板获取URL
            import win32clipboard
            win32clipboard.OpenClipboard()
            url = win32clipboard.GetClipboardData()
            win32clipboard.CloseClipboard()
            
            # 按Escape取消选择
            self._send_key('escape')
            
            return url
            
        except Exception as e:
            self.logger.error(f"获取当前URL失败: {e}")
            return None
    
    def wait_for_page_load(self, timeout=10):
        """等待页面加载完成"""
        try:
            self.logger.info(f"⏳ 等待页面加载完成，超时时间: {timeout}秒")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                # 简单的等待策略
                time.sleep(1)
                
                # 可以在这里添加更复杂的页面加载检测逻辑
                # 比如检测页面标题变化、特定元素出现等
                
            self.logger.info("✅ 页面加载等待完成")
            return True
            
        except Exception as e:
            self.logger.error(f"等待页面加载失败: {e}")
            return False
