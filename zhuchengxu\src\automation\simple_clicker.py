"""
简单的自动点击器
使用Windows API查找和点击网页元素
"""

import time
import threading
import webbrowser
import ctypes
import ctypes.wintypes
from ctypes import wintypes

from src.utils.logger import get_logger


class SimpleAutoClicker:
    """简单的自动点击器"""
    
    def __init__(self):
        self.logger = get_logger()
        self.is_running = False
        self.monitor_thread = None
        
    def start_auto_register(self):
        """启动自动注册流程"""
        try:
            self.logger.info("🚀 启动简单自动注册流程...")
            
            # 打开浏览器
            target_url = "https://augmentcode.com"
            self.logger.info(f"🌐 正在打开浏览器访问: {target_url}")
            webbrowser.open(target_url)
            
            # 启动监测线程
            self.is_running = True
            self.monitor_thread = threading.Thread(target=self._monitor_and_click)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            
            self.logger.info("✅ 浏览器已打开，开始监测登录按钮...")
            self.logger.info("💡 将在5秒后尝试自动点击登录按钮")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 启动失败: {e}")
            return False
    
    def _monitor_and_click(self):
        """监测并点击"""
        try:
            # 等待页面加载
            self.logger.info("⏳ 等待页面加载...")
            time.sleep(5)
            
            if not self.is_running:
                return
            
            # 尝试点击
            self.logger.info("🎯 开始查找登录按钮...")
            success = self._try_click_login()
            
            if success:
                self.logger.info("✅ 成功点击登录按钮！")
            else:
                self.logger.warning("⚠️ 未能自动点击，请手动点击登录按钮")
            
            # 结束任务
            self._end_process()
            
        except Exception as e:
            self.logger.error(f"❌ 监测过程出错: {e}")
            self._end_process()
    
    def _try_click_login(self):
        """尝试点击登录按钮"""
        try:
            # 获取当前活动窗口
            hwnd = ctypes.windll.user32.GetForegroundWindow()
            
            if hwnd == 0:
                self.logger.warning("未找到活动窗口")
                return False
            
            # 获取窗口标题
            title_length = ctypes.windll.user32.GetWindowTextLengthW(hwnd)
            title_buffer = ctypes.create_unicode_buffer(title_length + 1)
            ctypes.windll.user32.GetWindowTextW(hwnd, title_buffer, title_length + 1)
            window_title = title_buffer.value
            
            self.logger.info(f"当前窗口: {window_title}")
            
            # 检查是否是浏览器窗口
            if any(browser in window_title.lower() for browser in ['chrome', 'firefox', 'edge', 'safari', 'browser']):
                self.logger.info("✅ 检测到浏览器窗口")
                
                # 尝试使用键盘导航点击
                return self._keyboard_navigation_click()
            else:
                self.logger.warning("当前窗口不是浏览器")
                return False
                
        except Exception as e:
            self.logger.error(f"点击尝试失败: {e}")
            return False
    
    def _keyboard_navigation_click(self):
        """使用键盘导航点击"""
        try:
            # 模拟按键
            user32 = ctypes.windll.user32
            
            # 按Tab键导航到登录按钮
            self.logger.info("🔍 使用Tab键查找登录按钮...")
            
            for i in range(10):  # 最多按10次Tab
                if not self.is_running:
                    break
                    
                # 按Tab键
                user32.keybd_event(0x09, 0, 0, 0)  # Tab键按下
                user32.keybd_event(0x09, 0, 2, 0)  # Tab键释放
                time.sleep(0.2)
                
                # 按Enter尝试点击
                user32.keybd_event(0x0D, 0, 0, 0)  # Enter键按下
                user32.keybd_event(0x0D, 0, 2, 0)  # Enter键释放
                time.sleep(0.3)
                
                self.logger.info(f"已尝试第{i+1}次Tab+Enter")
            
            return True
            
        except Exception as e:
            self.logger.error(f"键盘导航失败: {e}")
            return False
    
    def _end_process(self):
        """结束进程"""
        try:
            self.logger.info("🛑 结束后台监测进程")
            self.is_running = False
            self.logger.info("✅ 任务完成，浏览器继续运行")
        except Exception as e:
            self.logger.error(f"结束进程时出错: {e}")
    
    def stop(self):
        """停止监测"""
        self.is_running = False


# 全局实例
simple_clicker = SimpleAutoClicker()
