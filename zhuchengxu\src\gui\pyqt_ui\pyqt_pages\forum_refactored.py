"""
重构的论坛页面 - 基于PyQt5最佳实践
采用模块化设计、响应式布局和现代化UI
"""

import sys
from typing import Dict, List, Optional
from datetime import datetime

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.gui.pyqt_ui.pyqt_design_system import DS
from src.utils.logger import get_logger


class ForumPostCard(QFrame):
    """论坛帖子卡片 - 重构版本"""
    
    # 信号定义
    clicked = pyqtSignal(dict)
    like_clicked = pyqtSignal(int)
    reply_clicked = pyqtSignal(int)
    
    def __init__(self, post_data: Dict, parent=None):
        super().__init__(parent)
        self.post_data = post_data
        self.logger = get_logger()
        self._setup_ui()
        self._setup_interactions()
    
    def _setup_ui(self):
        """设置UI - 响应式卡片设计"""
        # 响应式尺寸设置
        self.setMinimumHeight(120)
        self.setMaximumHeight(180)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.setCursor(Qt.PointingHandCursor)
        
        # 样式设置
        self._apply_card_style()
        
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(15, 12, 15, 12)
        main_layout.setSpacing(12)
        
        # 左侧：类型图标
        icon_widget = self._create_type_icon()
        main_layout.addWidget(icon_widget)
        
        # 中间：内容区域
        content_widget = self._create_content_area()
        main_layout.addWidget(content_widget, 1)  # 占据剩余空间
        
        # 右侧：操作按钮
        actions_widget = self._create_actions_area()
        main_layout.addWidget(actions_widget)
    
    def _apply_card_style(self):
        """应用卡片样式"""
        post_type = self.post_data.get('post_type', 'discussion')
        is_pinned = self.post_data.get('status') == 2
        
        # 类型颜色映射
        type_colors = {
            'discussion': DS.COLORS['neon_cyan'],
            'question': DS.COLORS['neon_orange'], 
            'announcement': DS.COLORS['neon_red'],
            'feedback': DS.COLORS['neon_green']
        }
        
        accent_color = type_colors.get(post_type, DS.COLORS['neon_cyan'])
        bg_color = DS.COLORS['bg_tertiary']
        
        if is_pinned:
            bg_color = DS.COLORS['bg_tertiary'].lighter(110)
        
        self.setStyleSheet(f"""
            ForumPostCard {{
                background: {bg_color.name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-left: 4px solid {accent_color.name()};
                border-radius: 8px;
                margin: 2px 0;
            }}
            ForumPostCard:hover {{
                background: {bg_color.lighter(110).name()};
                border-color: {accent_color.name()};
            }}
        """)
    
    def _create_type_icon(self) -> QWidget:
        """创建类型图标"""
        icon_widget = QWidget()
        icon_widget.setFixedSize(50, 50)
        
        layout = QVBoxLayout(icon_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setAlignment(Qt.AlignCenter)
        
        # 图标映射
        type_icons = {
            'discussion': '💬',
            'question': '❓',
            'announcement': '📢',
            'feedback': '💡'
        }
        
        post_type = self.post_data.get('post_type', 'discussion')
        icon_text = type_icons.get(post_type, '💬')
        
        icon_label = QLabel(icon_text)
        icon_label.setFont(QFont("Segoe UI Emoji", 20))
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # 置顶标识
        if self.post_data.get('status') == 2:
            pin_label = QLabel("📌")
            pin_label.setFont(QFont("Segoe UI Emoji", 12))
            pin_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(pin_label)
        
        return icon_widget
    
    def _create_content_area(self) -> QWidget:
        """创建内容区域"""
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(6)
        
        # 标题
        title = self.post_data.get('title', '无标题')
        title_label = QLabel(title)
        title_label.setFont(DS.get_font('heading_sm'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(50)  # 限制标题高度
        layout.addWidget(title_label)
        
        # 内容预览
        content = self.post_data.get('content', '')
        preview = content[:100] + '...' if len(content) > 100 else content
        content_label = QLabel(preview)
        content_label.setFont(DS.get_font('body_sm'))
        content_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        content_label.setWordWrap(True)
        content_label.setMaximumHeight(40)
        layout.addWidget(content_label)
        
        # 元信息
        meta_widget = self._create_meta_info()
        layout.addWidget(meta_widget)
        
        layout.addStretch()  # 弹性空间
        return content_widget
    
    def _create_meta_info(self) -> QWidget:
        """创建元信息"""
        meta_widget = QWidget()
        layout = QHBoxLayout(meta_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # 作者
        author = self.post_data.get('author', '匿名')
        author_label = QLabel(f"👤 {author}")
        author_label.setFont(DS.get_font('caption'))
        author_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        layout.addWidget(author_label)
        
        # 时间
        created_at = self.post_data.get('created_at', '')
        if created_at:
            time_label = QLabel(f"🕒 {created_at}")
            time_label.setFont(DS.get_font('caption'))
            time_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
            layout.addWidget(time_label)
        
        layout.addStretch()
        return meta_widget
    
    def _create_actions_area(self) -> QWidget:
        """创建操作区域"""
        actions_widget = QWidget()
        actions_widget.setFixedWidth(80)
        
        layout = QVBoxLayout(actions_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        layout.setAlignment(Qt.AlignCenter)
        
        # 点赞按钮 - 安全转换数据类型
        likes_count = self._safe_int(self.post_data.get('likes_count', 0))
        like_btn = QPushButton(f"👍 {likes_count}")
        like_btn.setFont(DS.get_font('caption'))
        like_btn.setStyleSheet(self._get_action_button_style())
        like_btn.clicked.connect(lambda: self.like_clicked.emit(self._safe_int(self.post_data.get('id', 0))))
        layout.addWidget(like_btn)

        # 回复按钮 - 安全转换数据类型
        replies_count = self._safe_int(self.post_data.get('replies_count', 0))
        reply_btn = QPushButton(f"💬 {replies_count}")
        reply_btn.setFont(DS.get_font('caption'))
        reply_btn.setStyleSheet(self._get_action_button_style())
        reply_btn.clicked.connect(lambda: self.reply_clicked.emit(self._safe_int(self.post_data.get('id', 0))))
        layout.addWidget(reply_btn)

        # 浏览数 - 安全转换数据类型
        views_count = self._safe_int(self.post_data.get('views_count', 0))
        views_label = QLabel(f"👁️ {views_count}")
        views_label.setFont(DS.get_font('caption'))
        views_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()}; text-align: center;")
        views_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(views_label)
        
        layout.addStretch()
        return actions_widget
    
    def _get_action_button_style(self) -> str:
        """获取操作按钮样式"""
        return f"""
            QPushButton {{
                background: transparent;
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 4px;
                padding: 4px 8px;
                color: {DS.COLORS['text_secondary'].name()};
                min-width: 50px;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].darker(200).name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['neon_cyan'].name()};
            }}
        """
    
    def _safe_int(self, value, default=0):
        """安全地转换值为整数"""
        try:
            if isinstance(value, str):
                return int(value) if value.isdigit() else default
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default

    def _setup_interactions(self):
        """设置交互"""
        # 阻止操作按钮触发卡片点击
        pass
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        try:
            if event.button() == Qt.LeftButton:
                # 检查是否点击在操作按钮区域
                pos = event.pos()
                if pos.x() < self.width() - 100:  # 不在操作区域
                    self.clicked.emit(self.post_data)
            super().mousePressEvent(event)
        except RuntimeError as e:
            # 对象已被删除，忽略事件
            if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                return
            else:
                raise e

    def __del__(self):
        """析构函数 - 确保安全删除"""
        try:
            # 断开所有信号连接
            if hasattr(self, 'clicked'):
                self.clicked.disconnect()
            if hasattr(self, 'like_clicked'):
                self.like_clicked.disconnect()
            if hasattr(self, 'reply_clicked'):
                self.reply_clicked.disconnect()
        except:
            pass


class ForumFilterBar(QWidget):
    """论坛筛选栏"""
    
    filter_changed = pyqtSignal(str)  # 筛选条件改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # 筛选标签
        filter_label = QLabel("分类:")
        filter_label.setFont(DS.get_font('body_md'))
        filter_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; font-weight: bold;")
        layout.addWidget(filter_label)
        
        # 筛选按钮组
        self.button_group = QButtonGroup(self)
        self.buttons = {}
        
        categories = [
            ('', '全部', '📋'),
            ('discussion', '讨论', '💬'),
            ('question', '问答', '❓'),
            ('announcement', '公告', '📢'),
            ('feedback', '反馈', '💡')
        ]
        
        for cat_type, cat_name, cat_icon in categories:
            btn = QPushButton(f"{cat_icon} {cat_name}")
            btn.setCheckable(True)
            btn.setFont(DS.get_font('body_sm'))
            btn.setStyleSheet(self._get_filter_button_style())
            
            # 默认选中"全部"
            if cat_type == '':
                btn.setChecked(True)
            
            btn.clicked.connect(lambda checked, t=cat_type: self.filter_changed.emit(t))
            
            self.button_group.addButton(btn)
            self.buttons[cat_type] = btn
            layout.addWidget(btn)
        
        layout.addStretch()
    
    def _get_filter_button_style(self) -> str:
        """获取筛选按钮样式"""
        return f"""
            QPushButton {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 8px 16px;
                color: {DS.COLORS['text_secondary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QPushButton:checked {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['neon_cyan'].name()};
            }}
        """


class ForumPagination(QWidget):
    """论坛分页组件"""

    page_changed = pyqtSignal(int)  # 页码改变信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_page = 1
        self.total_pages = 1
        self._setup_ui()

    def _setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        layout.setAlignment(Qt.AlignCenter)

        # 上一页按钮
        self.prev_btn = QPushButton("◀ 上一页")
        self.prev_btn.setFont(DS.get_font('body_sm'))
        self.prev_btn.setStyleSheet(self._get_nav_button_style())
        self.prev_btn.clicked.connect(self._prev_page)
        layout.addWidget(self.prev_btn)

        # 页码显示
        self.page_label = QLabel("1 / 1")
        self.page_label.setFont(DS.get_font('body_md'))
        self.page_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold; margin: 0 15px;")
        layout.addWidget(self.page_label)

        # 下一页按钮
        self.next_btn = QPushButton("下一页 ▶")
        self.next_btn.setFont(DS.get_font('body_sm'))
        self.next_btn.setStyleSheet(self._get_nav_button_style())
        self.next_btn.clicked.connect(self._next_page)
        layout.addWidget(self.next_btn)

    def _get_nav_button_style(self) -> str:
        """获取导航按钮样式"""
        return f"""
            QPushButton {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 8px 16px;
                color: {DS.COLORS['text_secondary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover:enabled {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['neon_cyan'].name()};
            }}
            QPushButton:disabled {{
                background: {DS.COLORS['bg_tertiary'].darker(120).name()};
                border-color: {DS.COLORS['glass_border'].darker(150).name()};
                color: {DS.COLORS['text_tertiary'].name()};
            }}
        """

    def update_pagination(self, current_page: int, total_pages: int):
        """更新分页信息"""
        self.current_page = current_page
        self.total_pages = total_pages

        # 更新显示
        self.page_label.setText(f"{current_page} / {total_pages}")

        # 更新按钮状态
        self.prev_btn.setEnabled(current_page > 1)
        self.next_btn.setEnabled(current_page < total_pages)

    def _prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.page_changed.emit(self.current_page - 1)

    def _next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.page_changed.emit(self.current_page + 1)


class ForumToolbar(QWidget):
    """论坛工具栏"""

    new_post_clicked = pyqtSignal()
    refresh_clicked = pyqtSignal()
    search_requested = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()

    def _setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 左侧：标题和统计
        left_layout = QHBoxLayout()

        # 论坛标题
        title_label = QLabel("💬 论坛讨论")
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        left_layout.addWidget(title_label)

        # 统计信息
        self.stats_label = QLabel("加载中...")
        self.stats_label.setFont(DS.get_font('caption'))
        self.stats_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()}; margin-left: 15px;")
        left_layout.addWidget(self.stats_label)

        left_layout.addStretch()
        layout.addLayout(left_layout)

        # 中间：搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜索帖子...")
        self.search_input.setFont(DS.get_font('body_md'))
        self.search_input.setStyleSheet(self._get_search_style())
        self.search_input.setMaximumWidth(300)
        self.search_input.returnPressed.connect(self._on_search)
        layout.addWidget(self.search_input)

        # 右侧：操作按钮
        right_layout = QHBoxLayout()
        right_layout.setSpacing(10)

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setFont(DS.get_font('body_sm'))
        refresh_btn.setStyleSheet(self._get_action_button_style())
        refresh_btn.clicked.connect(self.refresh_clicked.emit)
        right_layout.addWidget(refresh_btn)

        # 发帖按钮
        new_post_btn = QPushButton("✏️ 发帖")
        new_post_btn.setFont(DS.get_font('body_sm'))
        new_post_btn.setStyleSheet(self._get_primary_button_style())
        new_post_btn.clicked.connect(self.new_post_clicked.emit)
        right_layout.addWidget(new_post_btn)

        layout.addLayout(right_layout)

    def _get_search_style(self) -> str:
        """获取搜索框样式"""
        return f"""
            QLineEdit {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                padding: 10px 15px;
                color: {DS.COLORS['text_primary'].name()};
            }}
            QLineEdit:focus {{
                border-color: {DS.COLORS['neon_cyan'].name()};
                background: {DS.COLORS['bg_tertiary'].name()};
            }}
        """

    def _get_action_button_style(self) -> str:
        """获取操作按钮样式"""
        return f"""
            QPushButton {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 10px 20px;
                color: {DS.COLORS['text_secondary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
        """

    def _get_primary_button_style(self) -> str:
        """获取主要按钮样式"""
        return f"""
            QPushButton {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border: 1px solid {DS.COLORS['neon_cyan'].name()};
                border-radius: 6px;
                padding: 10px 20px;
                color: {DS.COLORS['neon_cyan'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].darker(120).name()};
                color: white;
            }}
        """

    def update_stats(self, total_posts: int, total_replies: int):
        """更新统计信息"""
        self.stats_label.setText(f"📊 {total_posts} 个帖子 · {total_replies} 条回复")

    def _on_search(self):
        """搜索事件"""
        search_text = self.search_input.text().strip()
        if search_text:
            self.search_requested.emit(search_text)


class ForumDataLoader(QThread):
    """论坛数据加载线程"""

    data_loaded = pyqtSignal(dict)  # 数据加载完成信号
    error_occurred = pyqtSignal(str)  # 错误信号

    def __init__(self, api_client, page=1, limit=20, post_type=''):
        super().__init__()
        self.api_client = api_client
        self.page = page
        self.limit = limit
        self.post_type = post_type
        self.logger = get_logger()

    def run(self):
        """运行数据加载"""
        try:
            self.logger.info(f"开始加载论坛数据: page={self.page}, limit={self.limit}, type={self.post_type}")

            # 调用API获取数据
            success, response = self.api_client.get_posts(
                page=self.page,
                limit=self.limit,
                post_type=self.post_type if self.post_type else None
            )

            if success:
                self.logger.info(f"论坛数据加载成功: {len(response.get('data', {}).get('posts', []))} 个帖子")
                self.data_loaded.emit(response)
            else:
                error_msg = response.get('message', '未知错误')
                self.logger.error(f"论坛数据加载失败: {error_msg}")
                self.error_occurred.emit(error_msg)

        except Exception as e:
            error_msg = f"数据加载异常: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)


class RefactoredForumPage(QWidget):
    """重构的论坛页面 - 主类"""

    def __init__(self, main_window=None, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.logger = get_logger()

        # 数据状态
        self.posts = []
        self.current_page = 1
        self.total_pages = 1
        self.current_filter = ''
        self.search_query = ''

        # API客户端
        self.api_client = None
        self._init_api_client()

        # 数据加载线程
        self.data_loader = None

        # 设置UI
        self._setup_ui()
        self._connect_signals()

        # 初始加载数据
        self._load_data()

    def _init_api_client(self):
        """初始化API客户端"""
        try:
            try:
                from utils.forum_api_client import get_forum_api_client
            except ImportError:
                from src.utils.forum_api_client import get_forum_api_client
            self.api_client = get_forum_api_client()
            self.logger.info("论坛API客户端初始化成功")
        except Exception as e:
            self.logger.error(f"论坛API客户端初始化失败: {e}")

    def _setup_ui(self):
        """设置UI - 响应式布局"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 设置最小尺寸
        self.setMinimumSize(800, 600)

        # 工具栏
        self.toolbar = ForumToolbar()
        main_layout.addWidget(self.toolbar)

        # 筛选栏
        self.filter_bar = ForumFilterBar()
        main_layout.addWidget(self.filter_bar)

        # 内容区域
        content_layout = self._create_content_area()
        main_layout.addLayout(content_layout, 1)  # 占据剩余空间

        # 分页组件
        self.pagination = ForumPagination()
        main_layout.addWidget(self.pagination)

        # 状态栏
        self.status_bar = self._create_status_bar()
        main_layout.addWidget(self.status_bar)

    def _create_content_area(self) -> QVBoxLayout:
        """创建内容区域"""
        content_layout = QVBoxLayout()
        content_layout.setSpacing(0)

        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background: {DS.COLORS['bg_primary'].name()};
                border: none;
                border-radius: 8px;
            }}
            QScrollBar:vertical {{
                background: {DS.COLORS['bg_secondary'].name()};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {DS.COLORS['neon_cyan'].name()};
            }}
        """)

        # 滚动内容
        self.scroll_content = QWidget()
        self.posts_layout = QVBoxLayout(self.scroll_content)
        self.posts_layout.setContentsMargins(0, 0, 0, 0)
        self.posts_layout.setSpacing(8)
        self.posts_layout.setAlignment(Qt.AlignTop)

        self.scroll_area.setWidget(self.scroll_content)
        content_layout.addWidget(self.scroll_area)

        return content_layout

    def _create_status_bar(self) -> QWidget:
        """创建状态栏"""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)

        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setFont(DS.get_font('caption'))
        self.status_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        # 加载指示器
        self.loading_label = QLabel()
        self.loading_label.setFont(DS.get_font('caption'))
        self.loading_label.setStyleSheet(f"color: {DS.COLORS['neon_cyan'].name()};")
        self.loading_label.hide()
        status_layout.addWidget(self.loading_label)

        return status_widget

    def _safe_int(self, value, default=0):
        """安全地转换值为整数"""
        try:
            if isinstance(value, str):
                return int(value) if value.isdigit() else default
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default

    def _connect_signals(self):
        """连接信号槽"""
        # 工具栏信号
        self.toolbar.new_post_clicked.connect(self._show_new_post_dialog)
        self.toolbar.refresh_clicked.connect(self._refresh_data)
        self.toolbar.search_requested.connect(self._search_posts)

        # 筛选栏信号
        self.filter_bar.filter_changed.connect(self._filter_posts)

        # 分页信号
        self.pagination.page_changed.connect(self._change_page)

    def _load_data(self, page=1, post_type='', search_query=''):
        """加载数据"""
        if not self.api_client:
            self._show_error("API客户端未初始化")
            return

        # 停止之前的加载线程
        if self.data_loader and self.data_loader.isRunning():
            self.data_loader.terminate()
            self.data_loader.wait()

        # 显示加载状态
        self._show_loading(True)

        # 创建新的加载线程
        self.data_loader = ForumDataLoader(
            self.api_client,
            page=page,
            limit=20,
            post_type=post_type
        )

        # 连接信号
        self.data_loader.data_loaded.connect(self._on_data_loaded)
        self.data_loader.error_occurred.connect(self._on_data_error)

        # 启动线程
        self.data_loader.start()

    def _on_data_loaded(self, response):
        """数据加载完成"""
        try:
            data = response.get('data', {})
            posts = data.get('posts', [])
            pagination = data.get('pagination', {})

            # 更新数据
            self.posts = posts
            self.current_page = pagination.get('page', 1)
            self.total_pages = pagination.get('pages', 1)

            # 更新UI
            self._update_posts_display()
            self._update_pagination()
            self._update_stats()

            # 隐藏加载状态
            self._show_loading(False)

            self.logger.info(f"论坛数据更新完成: {len(posts)} 个帖子")

        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            self._show_error(f"数据处理失败: {e}")

    def _on_data_error(self, error_msg):
        """数据加载错误"""
        self._show_loading(False)
        self._show_error(error_msg)

    def _update_posts_display(self):
        """更新帖子显示"""
        # 清空现有帖子
        self._clear_posts()

        if not self.posts:
            # 显示空状态
            self._show_empty_state()
            return

        # 添加帖子卡片
        for post in self.posts:
            card = ForumPostCard(post)
            card.clicked.connect(self._on_post_clicked)
            card.like_clicked.connect(self._on_like_clicked)
            card.reply_clicked.connect(self._on_reply_clicked)
            self.posts_layout.addWidget(card)

        # 添加弹性空间
        self.posts_layout.addStretch()

    def _clear_posts(self):
        """清空帖子显示"""
        # 先断开所有信号连接，防止删除过程中触发事件
        widgets_to_delete = []

        while self.posts_layout.count():
            child = self.posts_layout.takeAt(0)
            if child.widget():
                widget = child.widget()

                # 如果是ForumPostCard，断开信号连接
                if hasattr(widget, 'clicked'):
                    try:
                        widget.clicked.disconnect()
                    except:
                        pass

                if hasattr(widget, 'like_clicked'):
                    try:
                        widget.like_clicked.disconnect()
                    except:
                        pass

                if hasattr(widget, 'reply_clicked'):
                    try:
                        widget.reply_clicked.disconnect()
                    except:
                        pass

                # 禁用鼠标事件
                widget.setEnabled(False)

                # 添加到删除列表
                widgets_to_delete.append(widget)

        # 延迟删除所有widget
        for widget in widgets_to_delete:
            widget.deleteLater()

    def _show_empty_state(self):
        """显示空状态"""
        empty_widget = QWidget()
        empty_layout = QVBoxLayout(empty_widget)
        empty_layout.setAlignment(Qt.AlignCenter)

        # 空状态图标
        icon_label = QLabel("📭")
        icon_label.setFont(QFont("Segoe UI Emoji", 48))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        empty_layout.addWidget(icon_label)

        # 空状态文本
        text_label = QLabel("暂无帖子")
        text_label.setFont(DS.get_font('heading_md'))
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; margin-top: 20px;")
        empty_layout.addWidget(text_label)

        # 提示文本
        hint_label = QLabel("点击\"发帖\"按钮创建第一个帖子吧！")
        hint_label.setFont(DS.get_font('body_md'))
        hint_label.setAlignment(Qt.AlignCenter)
        hint_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()}; margin-top: 10px;")
        empty_layout.addWidget(hint_label)

        self.posts_layout.addWidget(empty_widget)

    def _update_pagination(self):
        """更新分页"""
        self.pagination.update_pagination(self.current_page, self.total_pages)

    def _update_stats(self):
        """更新统计信息"""
        total_posts = len(self.posts)
        # 安全地转换replies_count为整数
        total_replies = 0
        for post in self.posts:
            replies_count = post.get('replies_count', 0)
            try:
                # 尝试转换为整数
                if isinstance(replies_count, str):
                    total_replies += int(replies_count) if replies_count.isdigit() else 0
                else:
                    total_replies += int(replies_count) if replies_count else 0
            except (ValueError, TypeError):
                # 如果转换失败，跳过这个值
                continue

        self.toolbar.update_stats(total_posts, total_replies)

    def _show_loading(self, show: bool):
        """显示/隐藏加载状态"""
        if show:
            self.loading_label.setText("🔄 加载中...")
            self.loading_label.show()
            self.status_label.setText("正在加载论坛数据...")
        else:
            self.loading_label.hide()
            self.status_label.setText("准备就绪")

    def _show_error(self, error_msg: str):
        """显示错误信息"""
        self.status_label.setText(f"❌ {error_msg}")
        self.logger.error(f"论坛页面错误: {error_msg}")

        # 创建自定义错误对话框，修复颜色问题
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("论坛错误")
        msg_box.setText(error_msg)
        msg_box.setIcon(QMessageBox.Warning)

        # 设置样式，确保文字可见
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 2px solid {DS.COLORS['neon_red'].name()};
                border-radius: 8px;
            }}
            QMessageBox QLabel {{
                color: {DS.COLORS['text_primary'].name()};
                font-size: 14px;
                padding: 10px;
            }}
            QMessageBox QPushButton {{
                background-color: {DS.COLORS['neon_red'].darker(150).name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 1px solid {DS.COLORS['neon_red'].name()};
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {DS.COLORS['neon_red'].name()};
                color: white;
            }}
        """)

        msg_box.exec_()

    # 事件处理方法
    def _show_new_post_dialog(self):
        """显示发帖对话框"""
        try:
            from src.gui.pyqt_ui.pyqt_pages.forum_dialogs import NewPostDialog
            dialog = NewPostDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # 发帖成功，刷新数据
                self._refresh_data()
        except Exception as e:
            self.logger.error(f"显示发帖对话框失败: {e}")
            self._show_error("无法打开发帖对话框")

    def _refresh_data(self):
        """刷新数据"""
        self._load_data(
            page=self.current_page,
            post_type=self.current_filter,
            search_query=self.search_query
        )

    def _search_posts(self, search_query: str):
        """搜索帖子"""
        self.search_query = search_query
        self.current_page = 1  # 重置到第一页
        self._load_data(
            page=1,
            post_type=self.current_filter,
            search_query=search_query
        )

    def _filter_posts(self, post_type: str):
        """筛选帖子"""
        self.current_filter = post_type
        self.current_page = 1  # 重置到第一页
        self._load_data(
            page=1,
            post_type=post_type,
            search_query=self.search_query
        )

    def _change_page(self, page: int):
        """切换页面"""
        self.current_page = page
        self._load_data(
            page=page,
            post_type=self.current_filter,
            search_query=self.search_query
        )

    def _on_post_clicked(self, post_data: Dict):
        """帖子点击事件"""
        try:
            # 先增加观看量
            self._increment_post_view(post_data.get('id', 0))

            # 优先使用重构的详情对话框
            try:
                from src.gui.pyqt_ui.pyqt_pages.forum_detail_refactored import PostDetailDialog
                dialog = PostDetailDialog(post_data, self)
                dialog.post_updated.connect(self._refresh_data)  # 连接更新信号
                dialog.exec_()
            except ImportError:
                # 降级到原版对话框
                from src.gui.pyqt_ui.pyqt_pages.forum_dialogs import PostDetailDialog
                dialog = PostDetailDialog(post_data, self)
                dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示帖子详情失败: {e}")
            self._show_error("无法打开帖子详情")

    def _increment_post_view(self, post_id: int):
        """增加帖子观看量"""
        if not self.api_client:
            return

        try:
            post_id = self._safe_int(post_id)
            if post_id <= 0:
                return

            self.logger.info(f"增加帖子 {post_id} 的观看量")

            # 异步调用API增加观看量
            success, response = self.api_client.increment_view(post_id)

            if success:
                new_views = response.get('data', {}).get('views_count', 0)
                self.logger.info(f"帖子 {post_id} 观看量增加成功，新观看量: {new_views}")

                # 更新本地数据中的观看量
                for post in self.posts:
                    if self._safe_int(post.get('id', 0)) == post_id:
                        post['views_count'] = new_views
                        break

            else:
                error_msg = response.get('message', '增加观看量失败')
                self.logger.warning(f"增加观看量失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"增加观看量异常: {e}")
            # 观看量失败不影响主要功能，只记录日志

    def _on_like_clicked(self, post_id: int):
        """点赞事件"""
        try:
            if not self.api_client:
                self._show_error("API客户端未初始化")
                return

            # 获取当前用户名
            username = self._get_current_username()
            if not username:
                self._show_error("请先登录")
                return

            # 调用点赞API
            success, response = self.api_client.toggle_like(username, 'post', post_id)

            if success:
                # 刷新当前页面数据
                self._refresh_data()
            else:
                error_msg = response.get('message', '点赞失败')
                self._show_error(error_msg)

        except Exception as e:
            self.logger.error(f"点赞操作失败: {e}")
            self._show_error("点赞操作失败")

    def _on_reply_clicked(self, post_id: int):
        """回复事件"""
        try:
            # 找到对应的帖子数据
            post_data = None
            for post in self.posts:
                if self._safe_int(post.get('id', 0)) == post_id:
                    post_data = post
                    break

            if not post_data:
                self._show_error("找不到帖子信息")
                return

            # 优先使用重构的详情对话框
            try:
                from src.gui.pyqt_ui.pyqt_pages.forum_detail_refactored import PostDetailDialog
                dialog = PostDetailDialog(post_data, self)
                dialog.post_updated.connect(self._refresh_data)
                dialog.exec_()
            except ImportError:
                # 降级到原版回复对话框
                from src.gui.pyqt_ui.pyqt_pages.forum_dialogs import ReplyDialog
                dialog = ReplyDialog(post_data, self)
                if dialog.exec_() == QDialog.Accepted:
                    # 回复成功，刷新数据
                    self._refresh_data()

        except Exception as e:
            self.logger.error(f"显示回复对话框失败: {e}")
            self._show_error("无法打开回复对话框")

    def _get_current_username(self) -> Optional[str]:
        """获取当前用户名"""
        try:
            if (self.main_window and
                hasattr(self.main_window, 'security_manager') and
                self.main_window.security_manager):
                return self.main_window.security_manager.get_username()
            return None
        except Exception:
            return None

    def showEvent(self, event):
        """页面显示事件"""
        super().showEvent(event)
        # 页面显示时刷新数据
        if hasattr(self, 'api_client') and self.api_client:
            self._refresh_data()

    def closeEvent(self, event):
        """页面关闭事件"""
        # 停止数据加载线程
        if self.data_loader and self.data_loader.isRunning():
            self.data_loader.terminate()
            self.data_loader.wait()
        super().closeEvent(event)
