#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt邮箱查看页面 - 基于PyQt5最佳实践重构
Modern Mailbox Page - 响应式设计与标准布局管理
"""

import os
import json
import threading
import time
from typing import Optional, List, Dict, Any

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QSplitter,
    QLabel, QTreeWidget, QTreeWidgetItem, QTextEdit, QPushButton,
    QFrame, QSizePolicy, QHeaderView, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPalette, QColor

from src.utils.logger import get_logger


class EmailFetchWorker(QThread):
    """邮件获取工作线程 - 避免阻塞UI"""

    # 信号定义
    emails_fetched = pyqtSignal(list)  # 邮件获取完成
    error_occurred = pyqtSignal(str)   # 发生错误
    status_updated = pyqtSignal(str)   # 状态更新

    def __init__(self, config):
        super().__init__()
        self.config = config
        self.is_running = True

    def run(self):
        """在后台线程中获取邮件"""
        try:
            if not self.is_running:
                return

            self.status_updated.emit("🔄 正在获取邮件...")

            # 导入EmailAPI（在工作线程中导入，避免阻塞主线程）
            from src.email_service.email_api import EmailAPI

            if not self.is_running:
                return

            # 创建API实例并获取邮件
            api = EmailAPI(self.config)
            emails = api.get_emails()

            if not self.is_running:
                return

            # 发送结果信号
            self.emails_fetched.emit(emails)

            # 更新状态
            if emails:
                self.status_updated.emit(f"✅ 获取到 {len(emails)} 封邮件")
            else:
                self.status_updated.emit("📭 暂无新邮件")

        except Exception as e:
            if self.is_running:
                self.error_occurred.emit(str(e))

    def stop(self):
        """停止工作线程"""
        self.is_running = False
        self.quit()
        self.wait()


# 尝试导入邮件相关模块
try:
    from src.email_service.email_api import EmailAPI
    from src.utils.email_utils import format_email_display
    EMAIL_MODULES_AVAILABLE = True
except ImportError:
    EMAIL_MODULES_AVAILABLE = False

# 简化的设计系统 - 基于搜索到的最佳实践
class MailboxDesignSystem:
    """邮箱页面设计系统"""

    # 现代化配色方案
    COLORS = {
        'bg_primary': '#0a0a0f',      # 深空黑
        'bg_secondary': '#1a1a2e',    # 深紫蓝
        'bg_tertiary': '#16213e',     # 深蓝灰
        'glass_bg': '#1d1d31',        # 玻璃效果背景
        'glass_border': '#2a2a3e',    # 玻璃效果边框

        # 霓虹色彩
        'neon_cyan': '#00f5ff',       # 霓虹青
        'neon_green': '#00ff88',      # 霓虹绿
        'neon_red': '#ff0080',        # 霓虹红

        # 文字颜色
        'text_primary': '#ffffff',    # 主要文字
        'text_secondary': '#b8bcc8',  # 次要文字
        'text_muted': '#6c7293',      # 弱化文字
    }

    # 字体系统
    FONTS = {
        'primary': 'Microsoft YaHei UI',
        'secondary': 'Consolas',
    }

    # 尺寸系统
    SIZES = {
        'title': 18,
        'heading': 16,
        'body': 14,
        'small': 12,
        'tiny': 10,
    }

    # 间距系统
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 16,
        'lg': 24,
        'xl': 32,
    }


class PyQtMailboxPage(QWidget):
    """
    PyQt邮箱查看页面 - 基于PyQt5最佳实践重构

    设计原理：
    1. 标准布局管理 - 使用QVBoxLayout、QHBoxLayout、QSplitter
    2. 响应式设计 - 自适应窗口大小变化
    3. 现代化样式 - CSS样式表美化界面
    4. 高效交互 - 信号槽机制处理用户操作
    """

    # 定义信号
    email_selected = pyqtSignal(dict)  # 邮件选择信号
    refresh_requested = pyqtSignal()   # 刷新请求信号

    def __init__(self, parent=None, config_manager=None):
        """
        初始化邮箱页面

        Args:
            parent: 父窗口
            config_manager: 配置管理器
        """
        super().__init__(parent)

        # 核心组件初始化
        self.logger = get_logger()
        self.config_manager = config_manager
        self.ds = MailboxDesignSystem()  # 设计系统实例

        # 状态管理
        self.auto_refresh_enabled = False
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self._auto_refresh_emails)
        self.mail_data = []

        # 邮件获取工作线程
        self.email_worker = None

        # 刷新间隔：3秒（快速响应，但使用异步避免阻塞）
        self.REFRESH_INTERVAL = 3000

        # UI组件引用
        self.email_label = None
        self.mail_tree = None
        self.detail_text = None
        self.status_label = None
        self.count_label = None

        # 初始化多进程管理
        self._init_process_manager()

        # 初始化邮箱监控
        self._init_mailbox_monitor()

        # 初始化界面
        self._init_ui()
        self._apply_styles()
        self._update_email_display()
    
    # ==================== 多进程管理方法 ====================

    def _init_process_manager(self):
        """初始化线程池管理"""
        try:
            from src.utils.thread_pool_manager import get_thread_pool_manager
            self.thread_pool_manager = get_thread_pool_manager()

            # 注册邮箱页面的回调
            self.thread_pool_manager.register_callback(
                'mailbox',
                self._handle_task_success,
                self._handle_task_error
            )

            print("✅ 邮箱页面线程池管理初始化完成")

        except Exception as e:
            print(f"⚠️ 线程池管理初始化失败: {e}")
            self.thread_pool_manager = None

    def _handle_task_success(self, result):
        """处理任务成功回调"""
        try:
            action = result.get('action')

            if action == 'emails_result':
                emails = result.get('data', [])
                count = result.get('count', 0)

                # 更新UI（在主线程中）
                self._update_mail_list(emails)
                self._update_status(f"✅ 已获取 {count} 封邮件")
                self.logger.info(f"✅ 获取到 {count} 封邮件")

            elif action == 'test_result':
                count = result.get('count', 0)
                self._update_status(f"✅ 连接成功！当前有 {count} 封邮件")
                self.logger.info(f"✅ 连接测试成功，{count} 封邮件")

        except Exception as e:
            print(f"❌ 处理任务成功回调失败: {e}")

    def _handle_task_error(self, error):
        """处理任务错误回调"""
        try:
            self._update_status("❌ 任务执行失败")
            self.logger.error(f"任务执行失败: {error}")
        except Exception as e:
            print(f"❌ 处理任务错误回调失败: {e}")

    def _init_mailbox_monitor(self):
        """初始化邮箱后台监控"""
        try:
            from src.utils.mailbox_monitor import get_mailbox_monitor
            self.mailbox_monitor = get_mailbox_monitor()

            # 连接监控信号
            self.mailbox_monitor.new_emails_detected.connect(self._on_new_emails_detected)
            self.mailbox_monitor.email_count_changed.connect(self._on_email_count_changed)
            self.mailbox_monitor.monitor_status_changed.connect(self._on_monitor_status_changed)

            print("✅ 邮箱后台监控初始化完成")

        except Exception as e:
            print(f"⚠️ 邮箱监控初始化失败: {e}")
            self.mailbox_monitor = None

    def _on_new_emails_detected(self, emails, new_count):
        """处理新邮件检测信号"""
        try:
            # 更新邮件列表
            self._update_mail_list(emails)

            # 更新状态
            total_count = len(emails)
            if new_count > 0:
                self._update_status(f"🆕 发现 {new_count} 封新邮件，共 {total_count} 封")
                self.logger.info(f"🆕 发现 {new_count} 封新邮件，共 {total_count} 封")
            else:
                self._update_status(f"✅ 已获取 {total_count} 封邮件")
                self.logger.info(f"✅ 获取到 {total_count} 封邮件")

        except Exception as e:
            print(f"❌ 处理新邮件检测失败: {e}")

    def _on_email_count_changed(self, count):
        """处理邮件数量变化信号"""
        try:
            # 更新计数标签
            if hasattr(self, 'count_label') and self.count_label:
                self.count_label.setText(f"📧 {count} 封邮件")

        except Exception as e:
            print(f"❌ 处理邮件数量变化失败: {e}")

    def _on_monitor_status_changed(self, status):
        """处理监控状态变化信号"""
        try:
            # 可以在这里更新UI显示监控状态
            print(f"📊 监控状态: {status}")

        except Exception as e:
            print(f"❌ 处理监控状态变化失败: {e}")

    def _start_background_monitoring(self, email_address, pin_code):
        """启动后台监控"""
        try:
            if not self.mailbox_monitor:
                print("⚠️ 邮箱监控器未初始化")
                return

            # 构建邮箱配置
            self.email_config = {
                'temp_mail_address': email_address,
                'temp_mail_pin': pin_code,
                'auto_refresh_enabled': True,
                'refresh_interval': 10
            }

            # 启动监控（30秒检查间隔，降低频率）
            success = self.mailbox_monitor.start_monitoring(self.email_config, interval=30)

            if success:
                print(f"✅ 邮箱后台监控已启动: {email_address}")
                self._update_status("🔄 后台监控已启动")
            else:
                print("❌ 启动邮箱后台监控失败")
                self._update_status("❌ 后台监控启动失败")

        except Exception as e:
            print(f"❌ 启动后台监控失败: {e}")
            self._update_status("❌ 后台监控启动失败")

    def _stop_background_monitoring(self):
        """停止后台监控"""
        try:
            if self.mailbox_monitor:
                self.mailbox_monitor.stop_monitoring()
                print("✅ 邮箱后台监控已停止")

        except Exception as e:
            print(f"❌ 停止后台监控失败: {e}")

    def showEvent(self, event):
        """页面显示事件 - 恢复后台监控"""
        super().showEvent(event)
        try:
            # 如果有邮箱配置且监控器存在但未运行，重新启动监控
            if (self.mailbox_monitor and
                not self.mailbox_monitor.is_monitoring and
                hasattr(self, 'email_config') and
                self.email_config):

                print("📧 页面显示，恢复邮箱后台监控")
                self.mailbox_monitor.start_monitoring(self.email_config, interval=30)

        except Exception as e:
            print(f"❌ 恢复后台监控失败: {e}")

    def hideEvent(self, event):
        """页面隐藏事件 - 暂停后台监控"""
        super().hideEvent(event)
        try:
            # 页面隐藏时暂停后台监控以节省资源
            if self.mailbox_monitor and self.mailbox_monitor.is_monitoring:
                print("📧 页面隐藏，暂停邮箱后台监控")
                self._stop_background_monitoring()

        except Exception as e:
            print(f"❌ 暂停后台监控失败: {e}")

    def closeEvent(self, event):
        """页面关闭事件 - 停止后台监控"""
        try:
            # 页面关闭时完全停止后台监控
            if self.mailbox_monitor:
                print("📧 页面关闭，停止邮箱后台监控")
                self._stop_background_monitoring()

        except Exception as e:
            print(f"❌ 关闭时停止后台监控失败: {e}")
        finally:
            super().closeEvent(event)

    # ==================== UI初始化方法 ====================

    def _init_ui(self):
        """
        初始化用户界面 - 基于PyQt5最佳实践
        使用标准布局管理器实现响应式设计
        """
        # 设置页面策略：完全填充父容器
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(self.ds.SPACING['sm'], self.ds.SPACING['sm'],
                                     self.ds.SPACING['sm'], self.ds.SPACING['sm'])
        main_layout.setSpacing(self.ds.SPACING['sm'])

        # 构建界面组件
        self._create_page_header(main_layout)
        self._create_main_content(main_layout)
        self._create_status_bar(main_layout)

    def _create_page_header(self, parent_layout):
        """
        创建页面标题区域 - 使用标准QFrame
        """
        # 标题容器
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setFixedHeight(80)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(self.ds.SPACING['lg'], self.ds.SPACING['md'],
                                       self.ds.SPACING['lg'], self.ds.SPACING['md'])
        header_layout.setSpacing(self.ds.SPACING['xs'])

        # 顶部工具栏（标题和按钮）
        top_toolbar = QHBoxLayout()

        # 主标题
        title_label = QLabel("📧 邮箱查看")
        title_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['title'], QFont.Bold))
        title_label.setObjectName("titleLabel")
        top_toolbar.addWidget(title_label)

        top_toolbar.addStretch()  # 添加弹性空间

        # 测试连接按钮（参考油猴脚本的实现）
        self.test_btn = QPushButton("🔗 测试连接")
        self.test_btn.setObjectName("testButton")
        self.test_btn.clicked.connect(self._test_connection)
        self.test_btn.setToolTip("测试邮箱API连接状态")
        top_toolbar.addWidget(self.test_btn)

        # 当前邮箱显示
        self.email_label = QLabel("未配置")
        self.email_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        self.email_label.setObjectName("emailLabel")

        header_layout.addLayout(top_toolbar)
        header_layout.addWidget(self.email_label)

        parent_layout.addWidget(header_frame)

    def _create_main_content(self, parent_layout):
        """
        创建主要内容区域 - 使用QSplitter实现左右分栏
        """
        # 使用QSplitter实现可调整大小的左右分栏
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠

        # 创建左右面板
        left_panel = self._create_mail_list_panel()
        right_panel = self._create_mail_detail_panel()

        # 添加到分割器
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)

        # 设置初始比例 (50% : 50%) - 分割线居中
        splitter.setSizes([500, 500])

        parent_layout.addWidget(splitter)

    def _create_mail_list_panel(self):
        """
        创建邮件列表面板 - 使用标准QFrame
        """
        # 左侧面板容器
        left_frame = QFrame()
        left_frame.setFrameStyle(QFrame.StyledPanel)
        left_layout = QVBoxLayout(left_frame)
        left_layout.setContentsMargins(self.ds.SPACING['md'], self.ds.SPACING['md'],
                                     self.ds.SPACING['md'], self.ds.SPACING['md'])
        left_layout.setSpacing(self.ds.SPACING['sm'])

        # 面板标题
        panel_title = QLabel("📬 邮件列表")
        panel_title.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['heading'], QFont.Bold))
        panel_title.setObjectName("panelTitle")
        left_layout.addWidget(panel_title)

        # 邮件列表表格
        self.mail_tree = QTreeWidget()
        self.mail_tree.setHeaderLabels(['发件人', '主题', '时间'])
        self.mail_tree.setObjectName("mailTree")

        # 设置表格属性
        self.mail_tree.setAlternatingRowColors(True)
        self.mail_tree.setRootIsDecorated(False)  # 不显示根节点装饰
        self.mail_tree.setSelectionMode(QTreeWidget.SingleSelection)

        # 设置列宽
        header = self.mail_tree.header()
        header.setStretchLastSection(True)
        header.resizeSection(0, 150)  # 发件人列宽
        header.resizeSection(1, 200)  # 主题列宽

        # 绑定选择事件
        self.mail_tree.itemSelectionChanged.connect(self._on_mail_select)

        left_layout.addWidget(self.mail_tree)

        return left_frame

    def _create_mail_detail_panel(self):
        """
        创建邮件详情面板 - 使用标准QFrame
        """
        # 右侧面板容器
        right_frame = QFrame()
        right_frame.setFrameStyle(QFrame.StyledPanel)
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(self.ds.SPACING['md'], self.ds.SPACING['md'],
                                      self.ds.SPACING['md'], self.ds.SPACING['md'])
        right_layout.setSpacing(self.ds.SPACING['sm'])

        # 面板标题
        panel_title = QLabel("📄 邮件详情")
        panel_title.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['heading'], QFont.Bold))
        panel_title.setObjectName("panelTitle")
        right_layout.addWidget(panel_title)

        # 邮件详情文本区域
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setObjectName("detailText")

        # 设置字体
        detail_font = QFont(self.ds.FONTS['secondary'], self.ds.SIZES['body'])
        self.detail_text.setFont(detail_font)

        # 设置默认内容
        self._show_default_content()

        right_layout.addWidget(self.detail_text)

        return right_frame

    def _create_status_bar(self, parent_layout):
        """
        创建状态栏 - 使用标准QFrame
        """
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setFixedHeight(50)

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(self.ds.SPACING['lg'], self.ds.SPACING['sm'],
                                       self.ds.SPACING['lg'], self.ds.SPACING['sm'])

        # 状态标签
        self.status_label = QLabel("📊 请先配置邮箱地址")
        self.status_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['body']))
        self.status_label.setObjectName("statusLabel")

        # 邮件数量显示
        self.count_label = QLabel("邮件数量: 0")
        self.count_label.setFont(QFont(self.ds.FONTS['primary'], self.ds.SIZES['small']))
        self.count_label.setObjectName("countLabel")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.count_label)

        parent_layout.addWidget(status_frame)

    def _apply_styles(self):
        """
        应用样式表 - 基于PyQt5最佳实践（性能优化版本）
        """
        # 使用缓存避免重复计算样式表
        if hasattr(self, '_cached_styles'):
            self.setStyleSheet(self._cached_styles)
            return

        # 确保颜色值是字符串格式
        colors = self.ds.COLORS
        fonts = self.ds.FONTS

        style = f"""
        /* 主窗口样式 */
        QWidget {{
            background-color: {colors['bg_primary']};
            color: {colors['text_primary']};
            font-family: '{fonts['primary']}';
        }}

        /* 框架样式 */
        QFrame {{
            background-color: {colors['bg_secondary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 8px;
        }}

        /* 标题样式 */
        QLabel#titleLabel {{
            color: {colors['neon_green']};
            font-weight: bold;
            background: transparent;
            border: none;
        }}

        QLabel#emailLabel {{
            color: {colors['text_secondary']};
            background: transparent;
            border: none;
        }}

        QLabel#panelTitle {{
            color: {colors['text_primary']};
            font-weight: bold;
            background: transparent;
            border: none;
        }}

        QLabel#statusLabel {{
            color: {colors['text_secondary']};
            background: transparent;
            border: none;
        }}

        QLabel#countLabel {{
            color: {colors['neon_cyan']};
            background: transparent;
            border: none;
        }}

        /* 邮件列表样式 */
        QTreeWidget#mailTree {{
            background-color: {colors['bg_tertiary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            selection-background-color: {colors['neon_green']}33;
            alternate-background-color: {colors['bg_primary']};
        }}

        QTreeWidget#mailTree::item {{
            padding: 8px;
            border-bottom: 1px solid {colors['glass_border']};
        }}

        QTreeWidget#mailTree::item:selected {{
            background-color: {colors['neon_green']}33;
            color: {colors['neon_green']};
        }}

        QTreeWidget#mailTree::item:hover {{
            background-color: {colors['glass_bg']};
        }}

        QHeaderView::section {{
            background-color: {colors['bg_secondary']};
            color: {colors['text_primary']};
            padding: 8px;
            border: none;
            border-bottom: 2px solid {colors['neon_cyan']};
            font-weight: bold;
        }}

        /* 详情文本样式 */
        QTextEdit#detailText {{
            background-color: {colors['bg_tertiary']};
            border: 1px solid {colors['glass_border']};
            border-radius: 6px;
            padding: 12px;
        }}

        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {colors['bg_secondary']};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background-color: {colors['neon_cyan']};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {colors['neon_green']};
        }}

        /* 分割器样式 */
        QSplitter::handle {{
            background-color: {colors['glass_border']};
        }}

        QSplitter::handle:horizontal {{
            width: 3px;
        }}

        /* 测试连接按钮样式 */
        #testButton {{
            background-color: {colors['neon_cyan']};
            color: {colors['bg_primary']};
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            font-size: 13px;
            min-height: 32px;
        }}

        #testButton:hover {{
            background-color: {colors['neon_cyan']};
            opacity: 0.8;
        }}

        #testButton:pressed {{
            background-color: {colors['neon_cyan']};
            opacity: 0.6;
        }}

        #testButton:disabled {{
            background-color: {colors['bg_tertiary']};
            color: {colors['text_secondary']};
            opacity: 0.5;
        }}
        """

        # 缓存样式表以提高性能
        self._cached_styles = style
        self.setStyleSheet(style)

    # ==================== 内容显示方法 ====================

    def _show_default_content(self):
        """显示默认内容 - 学习现代2025的提示信息设计"""
        default_content = """📧 邮箱查看功能

此功能用于查看临时邮箱中的邮件和验证码。

🔧 使用前需要先配置：
• TempMail.Plus邮箱地址
• 对应的PIN码

📝 功能说明：
• 邮件列表：显示收到的邮件
• 邮件详情：点击邮件查看完整内容
• 验证码提取：自动识别邮件中的验证码

⚙️ 如需配置邮箱，请前往系统配置页面。"""

        self.detail_text.setPlainText(default_content)

    # ==================== 数据更新方法 ====================

    def _update_email_display(self):
        """更新邮箱显示 - 直接从主程序根目录config.json读取"""
        try:
            # 🔧 使用与main.py一致的路径检测逻辑
            import sys
            # 直接使用main.py中设置的环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                config_file = os.path.join(config_dir, "config.json")
            else:
                # 备选方案：使用sys.argv[0]
                if sys.argv and len(sys.argv) > 0:
                    argv_path = os.path.abspath(sys.argv[0])
                    if argv_path.endswith('.exe'):
                        config_file = os.path.join(os.path.dirname(argv_path), "config.json")
                    else:
                        config_file = os.path.join(os.path.dirname(argv_path), "config.json")
                else:
                    config_file = os.path.join(os.getcwd(), "config.json")
            self.logger.info(f"📁 读取配置文件: {config_file}")

            email_address = ""
            pin_code = ""

            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    email_address = config.get('temp_mail_address', '')
                    pin_code = config.get('temp_mail_pin', '')  # 正确的字段名是temp_mail_pin
                    self.logger.info(f"📧 从配置文件读取到邮箱: {email_address}")
                    self.logger.info(f"🔑 从配置文件读取到PIN: {pin_code}")
                except Exception as e:
                    self.logger.error(f"❌ 读取配置文件失败: {e}")
            else:
                self.logger.warning(f"⚠️ 配置文件不存在: {config_file}")

            if email_address:
                self.email_label.setText(f"当前邮箱: {email_address}")
                self.email_label.setStyleSheet(f"color: {self.ds.COLORS['neon_green']};")
                self._update_status("✅ 邮箱已配置，启动后台监控...")

                # 启动后台监控（替代自动刷新）
                self._start_background_monitoring(email_address, pin_code)

                # 获取缓存的邮件（如果有的话）
                if self.mailbox_monitor:
                    cached_emails = self.mailbox_monitor.get_cached_emails()
                    if cached_emails:
                        self._update_mail_list(cached_emails)
                        self._update_status(f"✅ 已加载 {len(cached_emails)} 封缓存邮件")
                        self.logger.info(f"✅ 加载了 {len(cached_emails)} 封缓存邮件")
                    else:
                        self._update_status("🔄 后台监控中，等待邮件检测...")
                        self.logger.info("🔄 后台监控已启动，等待邮件检测...")
            else:
                self.email_label.setText("未配置邮箱地址")
                self.email_label.setStyleSheet(f"color: {self.ds.COLORS['text_secondary']};")
                self._update_status("📊 请先配置邮箱地址")
                self._show_default_content()

        except Exception as e:
            self.logger.error(f"更新邮箱显示失败: {e}")
            self.email_label.setText("邮箱状态获取失败")
            self.email_label.setStyleSheet(f"color: {self.ds.COLORS['neon_red']};")

    def _update_mail_list(self, emails):
        """更新邮件列表 - 基于标准PyQt5实现（性能优化版本）"""
        try:
            # 性能优化：只有数据真正变化时才更新UI
            if hasattr(self, '_last_email_data') and self._last_email_data == emails:
                return

            # 暂停重绘以提高性能
            self.mail_tree.setUpdatesEnabled(False)

            # 清空现有列表
            self.mail_tree.clear()
            self.mail_data = emails
            self._last_email_data = emails.copy() if emails else []

            # 添加邮件项
            for email in emails:
                if EMAIL_MODULES_AVAILABLE:
                    formatted = format_email_display(email)
                    sender = formatted['sender']
                    subject = formatted['subject']
                    time_str = formatted['time']
                else:
                    # 简化显示
                    sender = email.get('from', '未知发件人')
                    subject = email.get('subject', '无主题')
                    time_str = email.get('date', '未知时间')

                item = QTreeWidgetItem([sender, subject, time_str])
                self.mail_tree.addTopLevelItem(item)

            # 更新邮件数量
            self.count_label.setText(f"邮件数量: {len(emails)}")

            # 恢复重绘
            self.mail_tree.setUpdatesEnabled(True)

        except Exception as e:
            # 确保在异常情况下也恢复重绘
            self.mail_tree.setUpdatesEnabled(True)
            self.logger.error(f"更新邮件列表失败: {e}")
            self._update_status(f"❌ 更新列表失败: {e}")

    # ==================== 事件处理方法 ====================

    def _on_mail_select(self):
        """邮件选择事件 - 标准信号槽处理"""
        try:
            current_item = self.mail_tree.currentItem()
            if current_item:
                current_index = self.mail_tree.indexOfTopLevelItem(current_item)
                if 0 <= current_index < len(self.mail_data):
                    email = self.mail_data[current_index]
                    self._show_mail_detail(email)
                    # 发射信号
                    self.email_selected.emit(email)
        except Exception as e:
            self.logger.error(f"选择邮件失败: {e}")

    def _show_mail_detail(self, email):
        """显示邮件详情 - 标准文本显示"""
        try:
            if EMAIL_MODULES_AVAILABLE:
                formatted = format_email_display(email)

                content = f"""📧 邮件详情

发件人: {formatted['sender']}
主题: {formatted['subject']}
时间: {formatted['time']}

内容:
{formatted['content']}

验证码: {formatted['verification_code'] if formatted['verification_code'] else '未找到'}
"""
            else:
                # 简化显示
                content = f"""📧 邮件详情

发件人: {email.get('from', '未知发件人')}
主题: {email.get('subject', '无主题')}
时间: {email.get('date', '未知时间')}

内容:
{email.get('body', '邮件内容获取失败')}

注意: 邮件模块未完全加载，显示可能不完整
"""

            self.detail_text.setPlainText(content)

        except Exception as e:
            self.logger.error(f"显示邮件详情失败: {e}")
            self.detail_text.setPlainText(f"❌ 显示邮件详情失败")

    # ==================== 工具方法 ====================

    def _update_status(self, message):
        """
        更新状态信息

        Args:
            message: 状态消息
        """
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)
        self.logger.info(message)

    # ==================== 公共接口方法 ====================

    def refresh_emails(self):
        """刷新邮件列表"""
        self._update_email_display()
        self.refresh_requested.emit()

    def get_selected_email(self):
        """获取当前选中的邮件"""
        current_item = self.mail_tree.currentItem()
        if current_item:
            current_index = self.mail_tree.indexOfTopLevelItem(current_item)
            if 0 <= current_index < len(self.mail_data):
                return self.mail_data[current_index]
        return None

    def update_mail_data(self, emails):
        """更新邮件数据 - 公共接口"""
        self._update_mail_list(emails)

    def _test_connection(self):
        """测试邮箱API连接 - 参考油猴脚本的实现"""
        try:
            self.test_btn.setEnabled(False)
            self.test_btn.setText("🔄 测试中...")
            self._update_status("🔍 正在测试邮箱API连接...")

            # 从配置文件读取邮箱配置
            import sys
            if hasattr(sys, '_MEIPASS'):
                config_file = os.path.join(os.path.dirname(sys.executable), "config.json")
            else:
                main_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
                config_file = os.path.join(main_dir, "config.json")

            if not os.path.exists(config_file):
                self._update_status("❌ 配置文件不存在")
                self.test_btn.setText("🔗 测试连接")
                self.test_btn.setEnabled(True)
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            email_address = config.get('temp_mail_address', '')
            pin_code = config.get('temp_mail_pin', '')

            if not email_address:
                self._update_status("❌ 请先配置邮箱地址")
                self.test_btn.setText("🔗 测试连接")
                self.test_btn.setEnabled(True)
                return

            self.logger.info(f"🧪 测试连接 - 邮箱: {email_address}")
            self.logger.info(f"🧪 测试连接 - PIN: {pin_code}")

            # 如果后台监控正在运行，触发强制检查
            if self.mailbox_monitor and self.mailbox_monitor.is_monitoring:
                self._update_status("🔄 触发后台监控强制检查...")
                self.mailbox_monitor.force_check()

                # 重置按钮状态
                self.test_btn.setText("🔗 测试连接")
                self.test_btn.setEnabled(True)

                self.logger.info("✅ 已触发后台监控强制检查")
            else:
                # 后台监控未运行，执行传统测试
                def test_thread():
                    try:
                        api = EmailAPI(config)
                        emails = api.get_emails()

                        # 在主线程中更新UI
                        self.test_btn.setText("🔗 测试连接")
                        self.test_btn.setEnabled(True)

                        if emails is not None:
                            self._update_status(f"✅ 连接成功！当前有 {len(emails)} 封邮件")
                            self.logger.info(f"✅ 连接测试成功，{len(emails)} 封邮件")
                        else:
                            self._update_status("✅ 连接成功！暂无邮件")
                            self.logger.info("✅ 连接测试成功，暂无邮件")

                    except Exception as e:
                        self.test_btn.setText("🔗 测试连接")
                        self.test_btn.setEnabled(True)
                        self._update_status(f"❌ 连接失败: {str(e)}")
                        self.logger.error(f"❌ 邮箱API连接测试失败: {e}")

                import threading
                threading.Thread(target=test_thread, daemon=True).start()

        except Exception as e:
            self.test_btn.setText("🔗 测试连接")
            self.test_btn.setEnabled(True)
            self._update_status(f"❌ 测试失败: {str(e)}")
            self.logger.error(f"❌ 测试连接时出错: {e}")

    def start_auto_refresh(self):
        """启动自动刷新 - 已被后台监控替代"""
        # 注意：现在使用后台监控替代了自动刷新
        print("ℹ️ 自动刷新已被后台监控替代，无需手动启动")

    def stop_auto_refresh(self):
        """停止自动刷新 - 停止后台监控"""
        self._stop_background_monitoring()
        self._update_status("📊 状态: 手动模式")
        self.logger.info("⏸️ 邮箱自动刷新已停止")

    def pause_auto_refresh(self):
        """暂停自动刷新（可恢复）"""
        if self.auto_refresh_timer.isActive():
            self.auto_refresh_timer.stop()
            self.logger.info("⏸️ 邮箱自动刷新已暂停")

    def resume_auto_refresh(self):
        """恢复自动刷新"""
        if self.auto_refresh_enabled and not self.auto_refresh_timer.isActive():
            self.auto_refresh_timer.start(self.REFRESH_INTERVAL)
            self.logger.info("▶️ 邮箱自动刷新已恢复")

    def _auto_refresh_emails(self):
        """自动刷新邮件 - 直接从config.json读取配置"""
        if self.auto_refresh_enabled:
            try:
                # 从main.py所在目录读取config.json
                import sys
                if hasattr(sys, '_MEIPASS'):
                    # 打包后的程序
                    config_file = os.path.join(os.path.dirname(sys.executable), "config.json")
                else:
                    # 开发环境，main.py在主程序目录
                    main_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
                    config_file = os.path.join(main_dir, "config.json")

                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)

                        if config.get('temp_mail_address'):
                            self.logger.info(f"🔄 自动刷新：使用邮箱 {config.get('temp_mail_address')}")

                            # 使用工作线程获取邮件，避免阻塞UI
                            self._fetch_emails_async(config)
                        else:
                            self._update_status("📊 请先配置邮箱地址")
                            self.logger.warning("⚠️ 配置文件中没有邮箱地址")
                    except Exception as e:
                        self.logger.error(f"❌ 读取配置失败: {e}")
                        self._update_status("📊 配置读取失败")
                else:
                    self._update_status("📊 配置文件不存在")
                    self.logger.warning(f"⚠️ 配置文件不存在: {config_file}")

            except Exception as e:
                self.logger.error(f"❌ 自动刷新邮件失败: {e}")
                # 出错时不停止自动刷新，继续尝试

    def _fetch_emails_async(self, config):
        """异步获取邮件 - 使用QThread避免阻塞UI"""
        try:
            # 如果已有工作线程在运行，先停止
            if self.email_worker and self.email_worker.isRunning():
                self.email_worker.stop()

            # 创建新的工作线程
            self.email_worker = EmailFetchWorker(config)

            # 连接信号
            self.email_worker.emails_fetched.connect(self._on_emails_fetched)
            self.email_worker.error_occurred.connect(self._on_fetch_error)
            self.email_worker.status_updated.connect(self._update_status)

            # 启动工作线程
            self.email_worker.start()

        except Exception as e:
            self.logger.error(f"❌ 启动邮件获取线程失败: {e}")
            self._update_status("❌ 邮件获取失败")

    def _on_emails_fetched(self, emails):
        """邮件获取完成的回调 - 在主线程中更新UI"""
        try:
            # 更新邮件列表
            self._update_mail_list(emails)

            # 更新状态显示
            if emails:
                self._update_status(f"📊 自动刷新中 ({len(emails)}封邮件)")
                self.logger.info(f"🔄 刷新完成，{len(emails)} 封邮件")
            else:
                self._update_status("📊 自动刷新中 (暂无邮件)")
                self.logger.info("🔄 刷新完成，暂无邮件")

        except Exception as e:
            self.logger.error(f"❌ 更新邮件列表失败: {e}")

    def _on_fetch_error(self, error_msg):
        """邮件获取错误的回调"""
        self.logger.error(f"❌ 邮件获取失败: {error_msg}")
        self._update_status(f"❌ 获取邮件失败: {error_msg}")

    def _fetch_emails(self, email_address, pin_code):
        """获取邮件列表 - 学习modern_mailbox_page.py的正确实现"""
        try:
            if self.thread_pool_manager:
                # 使用线程池获取邮件
                config = {
                    'temp_mail_address': email_address,
                    'temp_mail_pin': pin_code,
                    'auto_refresh_enabled': True,
                    'refresh_interval': 10
                }

                # 提交任务到线程池
                from src.utils.thread_pool_manager import mailbox_get_emails_task
                future = self.thread_pool_manager.submit_task('mailbox', mailbox_get_emails_task, config)

                if future:
                    self._update_status("🔄 正在获取邮件...")
                else:
                    self._update_status("❌ 线程池不可用")

            else:
                # 降级到单线程模式
                self._fetch_emails_sync(email_address, pin_code)

        except Exception as e:
            self.logger.error(f"获取邮件失败: {e}")
            self._update_status(f"❌ 获取邮件失败")

    def _fetch_emails_sync(self, email_address, pin_code):
        """同步获取邮件（降级方案）"""
        try:
            if EMAIL_MODULES_AVAILABLE:
                config = {
                    'temp_mail_address': email_address,
                    'temp_mail_pin': pin_code,
                    'auto_refresh_enabled': True,
                    'refresh_interval': 10
                }

                api = EmailAPI(config)
                emails = api.get_emails()
                self._update_mail_list(emails)

                if emails:
                    self._update_status(f"✅ 已获取 {len(emails)} 封邮件")
                    self.logger.info(f"✅ 获取到 {len(emails)} 封邮件")
                else:
                    self._update_status("📭 暂无邮件")
                    self.logger.info("📭 暂无邮件")
            else:
                self.logger.warning("邮件模块不可用，请检查依赖")
                self._update_status("❌ 邮件模块不可用，请检查依赖")
                self._update_mail_list([])

        except Exception as e:
            self.logger.error(f"获取邮件失败: {e}")
            self._update_status(f"❌ 获取邮件失败")
            self._update_mail_list([])

    def _show_default_content(self):
        """显示默认内容"""
        # 清空邮件列表
        self.mail_tree.clear()

        # 显示默认信息
        default_text = """📧 邮箱查看功能

此功能用于查看临时邮箱中的邮件和验证码。

🔧 使用前需要先配置：
• TempMail.Plus邮箱地址
• 对应的PIN码

💡 配置完成后，程序会自动刷新邮件列表，
   并自动提取验证码供您使用。

📋 配置步骤：
1. 点击左侧"配置"菜单
2. 填写邮箱地址和Pin码
3. 点击保存配置
4. 返回邮箱页面查看邮件

🔄 自动刷新：每1.5秒自动获取新邮件"""

        self.detail_text.setPlainText(default_text)

    def resizeEvent(self, event):
        """窗口大小改变事件 - 响应式布局"""
        super().resizeEvent(event)

        # 根据窗口大小调整列宽
        if hasattr(self, 'mail_tree') and self.mail_tree:
            width = self.width()
            if width > 1200:
                self.mail_tree.header().resizeSection(0, 180)  # 发件人
                self.mail_tree.header().resizeSection(1, 250)  # 主题
            elif width > 800:
                self.mail_tree.header().resizeSection(0, 150)
                self.mail_tree.header().resizeSection(1, 200)
            else:
                self.mail_tree.header().resizeSection(0, 120)
                self.mail_tree.header().resizeSection(1, 150)

    def closeEvent(self, event):
        """页面关闭时清理资源 - 避免内存泄漏"""
        try:
            # 停止自动刷新定时器
            if hasattr(self, 'auto_refresh_timer'):
                self.auto_refresh_timer.stop()

            # 停止邮件获取工作线程
            if self.email_worker and self.email_worker.isRunning():
                self.email_worker.stop()

            # 停止邮箱监控
            if hasattr(self, 'mailbox_monitor') and self.mailbox_monitor:
                self.mailbox_monitor.stop_monitoring()

            print("✅ 邮箱页面资源清理完成")

        except Exception as e:
            print(f"⚠️ 邮箱页面资源清理失败: {e}")

        super().closeEvent(event)
