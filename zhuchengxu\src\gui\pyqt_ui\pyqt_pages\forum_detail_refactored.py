"""
重构的论坛详情对话框
现代化设计，完整的回复功能对接
"""

import json
from datetime import datetime
from typing import Dict, List, Optional

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.gui.pyqt_ui.pyqt_design_system import DS
from src.utils.logger import get_logger


class ReplyCard(QFrame):
    """回复卡片组件"""
    
    # 信号定义
    like_clicked = pyqtSignal(int)  # 回复ID
    reply_clicked = pyqtSignal(int)  # 回复ID
    
    def __init__(self, reply_data: Dict, parent=None):
        super().__init__(parent)
        self.reply_data = reply_data
        self.logger = get_logger()
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 设置卡片样式
        self.setStyleSheet(f"""
            ReplyCard {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                margin: 5px 0;
                padding: 0;
            }}
            ReplyCard:hover {{
                border-color: {DS.COLORS['neon_cyan'].darker(150).name()};
                background: {DS.COLORS['bg_tertiary'].lighter(105).name()};
            }}
        """)
        
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(15, 12, 15, 12)
        main_layout.setSpacing(12)
        
        # 左侧：头像区域
        avatar_widget = self._create_avatar_area()
        main_layout.addWidget(avatar_widget)
        
        # 中间：内容区域
        content_widget = self._create_content_area()
        main_layout.addWidget(content_widget, 1)
        
        # 右侧：操作区域
        actions_widget = self._create_actions_area()
        main_layout.addWidget(actions_widget)
    
    def _create_avatar_area(self) -> QWidget:
        """创建头像区域"""
        avatar_widget = QWidget()
        avatar_widget.setFixedSize(50, 50)
        
        layout = QVBoxLayout(avatar_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setAlignment(Qt.AlignCenter)
        
        # 头像（使用emoji代替）
        avatar_label = QLabel("👤")
        avatar_label.setFont(QFont("Segoe UI Emoji", 24))
        avatar_label.setAlignment(Qt.AlignCenter)
        avatar_label.setStyleSheet(f"""
            QLabel {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border-radius: 25px;
                color: {DS.COLORS['neon_cyan'].name()};
            }}
        """)
        layout.addWidget(avatar_label)
        
        return avatar_widget
    
    def _create_content_area(self) -> QWidget:
        """创建内容区域"""
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 作者信息 - 时间移到底部操作区域
        header_layout = QHBoxLayout()

        # 作者名
        author = self.reply_data.get('author', '匿名')
        author_label = QLabel(f"👤 {author}")
        author_label.setFont(DS.get_font('body_md'))
        author_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        header_layout.addWidget(author_label)

        # 楼层信息（如果有ID的话）
        reply_id = self.reply_data.get('id', 0)
        if reply_id:
            floor_label = QLabel(f"#{reply_id}楼")
            floor_label.setFont(DS.get_font('caption'))
            floor_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()}; padding: 2px 6px; background: {DS.COLORS['bg_secondary'].name()}; border-radius: 3px;")
            header_layout.addWidget(floor_label)

        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # 回复内容
        content = self.reply_data.get('content', '')
        content_label = QLabel(content)
        content_label.setFont(DS.get_font('body_md'))
        content_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; line-height: 1.5;")
        content_label.setWordWrap(True)
        content_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        layout.addWidget(content_label)
        
        # 如果是嵌套回复，显示父回复信息
        parent_id = self.reply_data.get('parent_id')
        if parent_id:
            parent_info = QLabel("↳ 回复某条评论")
            parent_info.setFont(DS.get_font('caption'))
            parent_info.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()}; font-style: italic;")
            layout.addWidget(parent_info)
        
        return content_widget
    
    def _create_actions_area(self) -> QWidget:
        """创建操作区域 - 修复响应式布局"""
        actions_widget = QWidget()
        # 删除固定宽度限制，允许自适应
        # actions_widget.setFixedWidth(120)  # 这行导致按钮显示不全

        layout = QHBoxLayout(actions_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)

        # 只保留回复按钮，删除点赞按钮
        reply_btn = QPushButton("💬 回复")
        reply_btn.setFont(DS.get_font('body'))  # 使用更清晰的字体
        reply_btn.setStyleSheet(self._get_reply_button_style())
        reply_btn.clicked.connect(lambda: self.reply_clicked.emit(self._safe_int(self.reply_data.get('id', 0))))
        layout.addWidget(reply_btn, 0, Qt.AlignLeft)

        # 添加弹性空间
        layout.addStretch()

        # 时间信息移到右侧
        time_str = self.reply_data.get('time', '未知时间')
        time_label = QLabel(f"🕒 {time_str}")
        time_label.setFont(DS.get_font('caption'))
        time_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        layout.addWidget(time_label, 0, Qt.AlignRight)

        return actions_widget
    
    def _get_action_button_style(self) -> str:
        """获取操作按钮样式"""
        return f"""
            QPushButton {{
                background: transparent;
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 4px;
                padding: 4px 8px;
                color: {DS.COLORS['text_secondary'].name()};
                min-width: 45px;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].darker(200).name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['neon_cyan'].name()};
            }}
        """

    def _get_reply_button_style(self) -> str:
        """获取回复按钮样式 - 优化后的样式"""
        return f"""
            QPushButton {{
                background: {DS.COLORS['neon_cyan'].name()};
                border: 1px solid {DS.COLORS['neon_cyan'].name()};
                border-radius: 6px;
                padding: 8px 16px;
                color: white;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_green'].name()};
                border-color: {DS.COLORS['neon_green'].name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['neon_green'].darker(120).name()};
            }}
        """

    def _safe_int(self, value, default=0):
        """安全地转换值为整数"""
        try:
            if isinstance(value, str):
                return int(value) if value.isdigit() else default
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default


class ReplyComposer(QWidget):
    """回复编写组件"""
    
    # 信号定义
    reply_submitted = pyqtSignal(str, int)  # 内容, 父回复ID
    
    def __init__(self, post_id: int, parent_reply_id: Optional[int] = None, parent=None):
        super().__init__(parent)
        self.post_id = post_id
        self.parent_reply_id = parent_reply_id
        self.logger = get_logger()
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # 如果是回复某条评论，显示提示
        if self.parent_reply_id:
            reply_hint = QLabel(f"💬 回复评论 #{self.parent_reply_id}")
            reply_hint.setFont(DS.get_font('caption'))
            reply_hint.setStyleSheet(f"color: {DS.COLORS['neon_cyan'].name()}; font-weight: bold;")
            layout.addWidget(reply_hint)
        
        # 输入区域
        input_layout = QHBoxLayout()
        
        # 文本输入框
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("写下你的回复...")
        self.text_input.setFont(DS.get_font('body_md'))
        self.text_input.setMaximumHeight(100)
        self.text_input.setStyleSheet(f"""
            QTextEdit {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                padding: 10px;
                color: {DS.COLORS['text_primary'].name()};
            }}
            QTextEdit:focus {{
                border-color: {DS.COLORS['neon_cyan'].name()};
            }}
        """)
        input_layout.addWidget(self.text_input, 1)
        
        # 发送按钮
        self.send_btn = QPushButton("发送")
        self.send_btn.setFont(DS.get_font('body_sm'))
        self.send_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border: 1px solid {DS.COLORS['neon_cyan'].name()};
                border-radius: 6px;
                padding: 10px 20px;
                color: {DS.COLORS['neon_cyan'].name()};
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].darker(120).name()};
                color: white;
            }}
            QPushButton:disabled {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border-color: {DS.COLORS['glass_border'].name()};
                color: {DS.COLORS['text_tertiary'].name()};
            }}
        """)
        self.send_btn.clicked.connect(self._submit_reply)
        input_layout.addWidget(self.send_btn)
        
        layout.addLayout(input_layout)
        
        # 连接文本变化事件
        self.text_input.textChanged.connect(self._on_text_changed)
        self._on_text_changed()  # 初始状态
    
    def _on_text_changed(self):
        """文本变化事件"""
        text = self.text_input.toPlainText().strip()
        self.send_btn.setEnabled(len(text) > 0)
    
    def _submit_reply(self):
        """提交回复"""
        content = self.text_input.toPlainText().strip()
        if content:
            self.reply_submitted.emit(content, self.parent_reply_id or 0)
            self.text_input.clear()
    
    def set_placeholder(self, text: str):
        """设置占位符文本"""
        self.text_input.setPlaceholderText(text)


class PostDetailDialog(QDialog):
    """重构的帖子详情对话框"""

    # 信号定义
    post_updated = pyqtSignal()  # 帖子更新信号

    def __init__(self, post_data: Dict, parent=None):
        super().__init__(parent)
        self.post_data = post_data
        self.parent_widget = parent
        self.logger = get_logger()

        # 数据状态
        self.replies = []
        self.api_client = None

        # 初始化API客户端
        self._init_api_client()

        # 设置UI
        self._setup_ui()
        self._setup_connections()

        # 加载回复数据
        self._load_replies()

        # 增加观看量
        self._increment_view()

    def _init_api_client(self):
        """初始化API客户端"""
        try:
            try:
                from utils.forum_api_client import get_forum_api_client
            except ImportError:
                from src.utils.forum_api_client import get_forum_api_client
            self.api_client = get_forum_api_client()
            self.logger.info("帖子详情API客户端初始化成功")
        except Exception as e:
            self.logger.error(f"API客户端初始化失败: {e}")

    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("帖子详情")
        self.setModal(True)

        # 响应式窗口大小 - 增加宽度10%
        screen = QApplication.desktop().screenGeometry()
        # 原来是0.85，增加10%后变为0.935 (0.85 * 1.1)
        window_width = min(1320, int(screen.width() * 0.935))  # 最大宽度也增加10% (1200 * 1.1)
        window_height = min(900, int(screen.height() * 0.85))
        self.resize(window_width, window_height)

        # 居中显示
        self.move(
            (screen.width() - window_width) // 2,
            (screen.height() - window_height) // 2
        )

        # 设置样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {DS.COLORS['bg_primary'].name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 2px solid {DS.COLORS['glass_border'].name()};
                border-radius: 12px;
            }}
        """)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 标题栏
        title_bar = self._create_title_bar()
        main_layout.addWidget(title_bar)

        # 内容区域
        content_area = self._create_content_area()
        main_layout.addWidget(content_area, 1)

    def _create_title_bar(self) -> QWidget:
        """创建标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(60)
        title_bar.setStyleSheet(f"""
            QWidget {{
                background: {DS.COLORS['bg_secondary'].name()};
                border-bottom: 1px solid {DS.COLORS['glass_border'].name()};
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }}
        """)

        layout = QHBoxLayout(title_bar)
        layout.setContentsMargins(20, 0, 20, 0)

        # 标题
        title_label = QLabel("📋 帖子详情")
        title_label.setFont(DS.get_font('heading_md'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        layout.addWidget(title_label)

        layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(40, 40)
        close_btn.setFont(DS.get_font('heading_sm'))
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                border-radius: 20px;
                color: {DS.COLORS['text_secondary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_red'].darker(150).name()};
                color: {DS.COLORS['neon_red'].name()};
            }}
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

        return title_bar

    def _create_content_area(self) -> QWidget:
        """创建内容区域"""
        content_widget = QWidget()
        layout = QHBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 左侧：帖子内容
        post_content = self._create_post_content()
        layout.addWidget(post_content, 2)

        # 右侧：回复区域
        replies_area = self._create_replies_area()
        layout.addWidget(replies_area, 3)

        return content_widget

    def _create_post_content(self) -> QWidget:
        """创建帖子内容区域"""
        post_widget = QWidget()
        layout = QVBoxLayout(post_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 帖子头部信息
        header_widget = self._create_post_header()
        layout.addWidget(header_widget)

        # 帖子内容
        content_widget = self._create_post_body()
        layout.addWidget(content_widget, 1)

        # 帖子操作栏
        actions_widget = self._create_post_actions()
        layout.addWidget(actions_widget)

        return post_widget

    def _create_post_header(self) -> QWidget:
        """创建帖子头部"""
        header_widget = QFrame()
        header_widget.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                padding: 15px;
            }}
        """)

        layout = QVBoxLayout(header_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 帖子类型和状态
        type_layout = QHBoxLayout()

        # 类型标签
        post_type = self.post_data.get('post_type', 'discussion')
        type_icons = {
            'discussion': '💬 讨论',
            'question': '❓ 问答',
            'announcement': '📢 公告',
            'feedback': '💡 反馈'
        }

        type_label = QLabel(type_icons.get(post_type, '💬 讨论'))
        type_label.setFont(DS.get_font('caption'))
        type_label.setStyleSheet(f"""
            QLabel {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                color: {DS.COLORS['neon_cyan'].name()};
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
            }}
        """)
        type_layout.addWidget(type_label)

        # 置顶标识
        if self.post_data.get('status') == 2:
            pinned_label = QLabel("📌 置顶")
            pinned_label.setFont(DS.get_font('caption'))
            pinned_label.setStyleSheet(f"""
                QLabel {{
                    background: {DS.COLORS['neon_red'].darker(150).name()};
                    color: {DS.COLORS['neon_red'].name()};
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                }}
            """)
            type_layout.addWidget(pinned_label)

        type_layout.addStretch()
        layout.addLayout(type_layout)

        # 帖子标题
        title = self.post_data.get('title', '无标题')
        title_label = QLabel(title)
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold; line-height: 1.3;")
        title_label.setWordWrap(True)
        layout.addWidget(title_label)

        # 作者和时间信息
        meta_layout = QHBoxLayout()

        # 作者
        author = self.post_data.get('author', '匿名')
        author_label = QLabel(f"👤 {author}")
        author_label.setFont(DS.get_font('body_sm'))
        author_label.setStyleSheet(f"color: {DS.COLORS['neon_cyan'].name()}; font-weight: bold;")
        meta_layout.addWidget(author_label)

        # 发布时间
        created_at = self.post_data.get('created_at', '')
        if created_at:
            time_label = QLabel(f"🕒 {created_at}")
            time_label.setFont(DS.get_font('body_sm'))
            time_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
            meta_layout.addWidget(time_label)

        meta_layout.addStretch()
        layout.addLayout(meta_layout)

        return header_widget

    def _create_post_body(self) -> QWidget:
        """创建帖子正文"""
        body_widget = QFrame()
        body_widget.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
            }}
        """)

        layout = QVBoxLayout(body_widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # 帖子内容
        content = self.post_data.get('content', '')
        content_label = QLabel(content)
        content_label.setFont(DS.get_font('body_md'))
        content_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_primary'].name()};
                line-height: 1.6;
                padding: 10px 0;
            }}
        """)
        content_label.setWordWrap(True)
        content_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        content_label.setAlignment(Qt.AlignTop)
        layout.addWidget(content_label, 1)

        return body_widget

    def _create_post_actions(self) -> QWidget:
        """创建帖子操作栏"""
        actions_widget = QFrame()
        actions_widget.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
            }}
        """)

        layout = QHBoxLayout(actions_widget)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)

        # 统计信息
        stats_layout = QHBoxLayout()

        # 点赞数
        likes_count = self._safe_int(self.post_data.get('likes_count', 0))
        likes_label = QLabel(f"👍 {likes_count}")
        likes_label.setFont(DS.get_font('body_sm'))
        likes_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        stats_layout.addWidget(likes_label)

        # 回复数
        replies_count = self._safe_int(self.post_data.get('replies_count', 0))
        replies_label = QLabel(f"💬 {replies_count}")
        replies_label.setFont(DS.get_font('body_sm'))
        replies_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        stats_layout.addWidget(replies_label)

        # 浏览数
        views_count = self._safe_int(self.post_data.get('views_count', 0))
        views_label = QLabel(f"👁️ {views_count}")
        views_label.setFont(DS.get_font('body_sm'))
        views_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")
        stats_layout.addWidget(views_label)

        layout.addLayout(stats_layout)
        layout.addStretch()

        # 操作按钮
        # 点赞按钮
        likes_count = self._safe_int(self.post_data.get('likes_count', 0))
        self.like_btn = QPushButton(f"👍 点赞 ({likes_count})")
        self.like_btn.setFont(DS.get_font('body_sm'))
        self.like_btn.setStyleSheet(self._get_action_button_style())
        self.like_btn.clicked.connect(self._toggle_like)
        layout.addWidget(self.like_btn)

        # 检查用户点赞状态
        self._check_user_like_status()

        # 分享按钮
        share_btn = QPushButton("🔗 分享")
        share_btn.setFont(DS.get_font('body_sm'))
        share_btn.setStyleSheet(self._get_action_button_style())
        share_btn.clicked.connect(self._share_post)
        layout.addWidget(share_btn)

        return actions_widget

    def _create_replies_area(self) -> QWidget:
        """创建回复区域"""
        replies_widget = QWidget()
        layout = QVBoxLayout(replies_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 回复标题
        replies_header = QFrame()
        replies_header.setStyleSheet(f"""
            QFrame {{
                background: {DS.COLORS['bg_secondary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
            }}
        """)

        header_layout = QHBoxLayout(replies_header)
        header_layout.setContentsMargins(15, 10, 15, 10)

        replies_title = QLabel("💬 回复讨论")
        replies_title.setFont(DS.get_font('heading_md'))
        replies_title.setStyleSheet(f"color: {DS.COLORS['text_primary'].name()}; font-weight: bold;")
        header_layout.addWidget(replies_title)

        # 回复数量
        self.replies_count_label = QLabel(f"({len(self.replies)} 条回复)")
        self.replies_count_label.setFont(DS.get_font('body_sm'))
        self.replies_count_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        header_layout.addWidget(self.replies_count_label)

        header_layout.addStretch()

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setFont(DS.get_font('caption'))
        refresh_btn.setStyleSheet(self._get_small_button_style())
        refresh_btn.clicked.connect(self._load_replies)
        header_layout.addWidget(refresh_btn)

        layout.addWidget(replies_header)

        # 回复编写器
        post_id = self._safe_int(self.post_data.get('id', 0))
        self.reply_composer = ReplyComposer(post_id)
        self.reply_composer.reply_submitted.connect(self._submit_reply)
        layout.addWidget(self.reply_composer)

        # 回复列表
        self.replies_scroll = QScrollArea()
        self.replies_scroll.setWidgetResizable(True)
        self.replies_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.replies_scroll.setStyleSheet(f"""
            QScrollArea {{
                background: transparent;
                border: none;
            }}
            QScrollBar:vertical {{
                background: {DS.COLORS['bg_secondary'].name()};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {DS.COLORS['neon_cyan'].name()};
            }}
        """)

        # 回复内容容器
        self.replies_content = QWidget()
        self.replies_layout = QVBoxLayout(self.replies_content)
        self.replies_layout.setContentsMargins(0, 0, 0, 0)
        self.replies_layout.setSpacing(8)
        self.replies_layout.setAlignment(Qt.AlignTop)

        self.replies_scroll.setWidget(self.replies_content)
        layout.addWidget(self.replies_scroll, 1)

        return replies_widget

    def _get_action_button_style(self) -> str:
        """获取操作按钮样式"""
        return f"""
            QPushButton {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 6px;
                padding: 8px 16px;
                color: {DS.COLORS['text_secondary'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].darker(150).name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['neon_cyan'].name()};
            }}
        """

    def _get_small_button_style(self) -> str:
        """获取小按钮样式"""
        return f"""
            QPushButton {{
                background: transparent;
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 4px;
                padding: 4px 8px;
                color: {DS.COLORS['text_secondary'].name()};
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_cyan'].darker(200).name()};
                border-color: {DS.COLORS['neon_cyan'].name()};
                color: {DS.COLORS['neon_cyan'].name()};
            }}
        """

    def _safe_int(self, value, default=0):
        """安全地转换值为整数"""
        try:
            if isinstance(value, str):
                return int(value) if value.isdigit() else default
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default

    def _check_user_like_status(self):
        """检查用户点赞状态"""
        if not self.api_client:
            return

        try:
            username = self._get_current_username()
            if not username:
                return

            post_id = self._safe_int(self.post_data.get('id', 0))
            if post_id <= 0:
                return

            # 检查点赞状态
            success, response = self.api_client.check_user_like_status(username, 'post', post_id)

            if success:
                is_liked = response.get('data', {}).get('is_liked', False)
                likes_count = self._safe_int(self.post_data.get('likes_count', 0))

                if is_liked:
                    self.like_btn.setText(f"❤️ 已赞 ({likes_count})")
                    self.like_btn.setStyleSheet(self._get_liked_button_style())
                else:
                    self.like_btn.setText(f"👍 点赞 ({likes_count})")
                    self.like_btn.setStyleSheet(self._get_action_button_style())

        except Exception as e:
            self.logger.error(f"检查点赞状态失败: {e}")

    def _get_liked_button_style(self) -> str:
        """获取已点赞按钮样式"""
        return f"""
            QPushButton {{
                background: {DS.COLORS['neon_red'].darker(150).name()};
                border: 1px solid {DS.COLORS['neon_red'].name()};
                border-radius: 6px;
                padding: 8px 16px;
                color: {DS.COLORS['neon_red'].name()};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['neon_red'].darker(120).name()};
                border-color: {DS.COLORS['neon_red'].lighter(120).name()};
                color: white;
            }}
        """

    def _setup_connections(self):
        """设置信号连接"""
        # 这里可以添加其他信号连接
        pass

    def _load_replies(self):
        """加载回复数据"""
        if not self.api_client:
            self._show_error("API客户端未初始化")
            return

        try:
            post_id = self._safe_int(self.post_data.get('id', 0))
            if post_id <= 0:
                self._show_error("无效的帖子ID")
                return

            self.logger.info(f"开始加载帖子 {post_id} 的回复")

            # 调用API获取回复
            success, response = self.api_client.get_replies(post_id)

            if success:
                self.replies = response.get('data', [])
                self._update_replies_display()
                self.logger.info(f"回复加载成功: {len(self.replies)} 条回复")
            else:
                error_msg = response.get('message', '加载回复失败')
                self._show_error(error_msg)

        except Exception as e:
            error_msg = f"加载回复异常: {str(e)}"
            self.logger.error(error_msg)
            self._show_error(error_msg)

    def _update_replies_display(self):
        """更新回复显示"""
        # 清空现有回复
        self._clear_replies()

        # 更新回复数量
        self.replies_count_label.setText(f"({len(self.replies)} 条回复)")

        if not self.replies:
            # 显示空状态
            self._show_empty_replies()
            return

        # 添加回复卡片
        for reply in self.replies:
            reply_card = ReplyCard(reply)
            reply_card.like_clicked.connect(self._on_reply_like)
            reply_card.reply_clicked.connect(self._on_reply_to_reply)
            self.replies_layout.addWidget(reply_card)

        # 添加弹性空间
        self.replies_layout.addStretch()

    def _clear_replies(self):
        """清空回复显示"""
        while self.replies_layout.count():
            child = self.replies_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def _show_empty_replies(self):
        """显示空回复状态"""
        empty_widget = QWidget()
        empty_layout = QVBoxLayout(empty_widget)
        empty_layout.setAlignment(Qt.AlignCenter)
        empty_layout.setContentsMargins(20, 40, 20, 40)

        # 空状态图标
        icon_label = QLabel("💭")
        icon_label.setFont(QFont("Segoe UI Emoji", 48))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()};")
        empty_layout.addWidget(icon_label)

        # 空状态文本
        text_label = QLabel("暂无回复")
        text_label.setFont(DS.get_font('heading_md'))
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()}; margin-top: 15px;")
        empty_layout.addWidget(text_label)

        # 提示文本
        hint_label = QLabel("成为第一个回复的人吧！")
        hint_label.setFont(DS.get_font('body_md'))
        hint_label.setAlignment(Qt.AlignCenter)
        hint_label.setStyleSheet(f"color: {DS.COLORS['text_tertiary'].name()}; margin-top: 8px;")
        empty_layout.addWidget(hint_label)

        self.replies_layout.addWidget(empty_widget)

    def _submit_reply(self, content: str, parent_reply_id: int):
        """提交回复"""
        if not self.api_client:
            self._show_error("API客户端未初始化")
            return

        try:
            # 获取当前用户名
            username = self._get_current_username()
            if not username:
                self._show_error("请先登录")
                return

            post_id = self._safe_int(self.post_data.get('id', 0))

            self.logger.info(f"提交回复: post_id={post_id}, parent_id={parent_reply_id}")

            # 调用API提交回复
            success, response = self.api_client.create_reply(
                username=username,
                post_id=post_id,
                content=content,
                parent_id=parent_reply_id if parent_reply_id > 0 else None
            )

            if success:
                self.logger.info("回复提交成功")
                # 重新加载回复列表
                self._load_replies()
                # 发送帖子更新信号
                self.post_updated.emit()
            else:
                error_msg = response.get('message', '回复提交失败')
                self._show_error(error_msg)

        except Exception as e:
            error_msg = f"回复提交异常: {str(e)}"
            self.logger.error(error_msg)
            self._show_error(error_msg)

    def _toggle_like(self):
        """切换点赞状态"""
        if not self.api_client:
            self._show_error("API客户端未初始化")
            return

        try:
            username = self._get_current_username()
            if not username:
                self._show_error("请先登录")
                return

            post_id = self._safe_int(self.post_data.get('id', 0))

            # 调用点赞API
            success, response = self.api_client.toggle_like(username, 'post', post_id)

            if success:
                action = response.get('data', {}).get('action', 'liked')
                likes_count = response.get('data', {}).get('likes_count', 0)

                # 更新帖子数据
                self.post_data['likes_count'] = likes_count

                # 更新按钮文本和样式
                if action == 'liked':
                    self.like_btn.setText(f"❤️ 已赞 ({likes_count})")
                    self.like_btn.setStyleSheet(self._get_liked_button_style())
                else:
                    self.like_btn.setText(f"👍 点赞 ({likes_count})")
                    self.like_btn.setStyleSheet(self._get_action_button_style())

                # 发送更新信号
                self.post_updated.emit()

                self.logger.info(f"点赞操作成功: {action}, 新点赞数: {likes_count}")

            else:
                error_msg = response.get('message', '点赞操作失败')
                self._show_error(error_msg)

        except Exception as e:
            error_msg = f"点赞操作异常: {str(e)}"
            self.logger.error(error_msg)
            self._show_error(error_msg)

    def _share_post(self):
        """分享帖子"""
        try:
            # 创建分享链接（这里可以根据实际需求修改）
            post_id = self.post_data.get('id', 0)
            share_text = f"分享帖子: {self.post_data.get('title', '无标题')} (ID: {post_id})"

            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(share_text)

            # 显示成功提示
            self._show_success("分享链接已复制到剪贴板")

        except Exception as e:
            self.logger.error(f"分享失败: {e}")
            self._show_error("分享失败")

    def _on_reply_like(self, reply_id: int):
        """回复点赞事件"""
        if not self.api_client:
            self._show_error("API客户端未初始化")
            return

        try:
            username = self._get_current_username()
            if not username:
                self._show_error("请先登录")
                return

            # 调用回复点赞API
            success, response = self.api_client.toggle_like(username, 'reply', reply_id)

            if success:
                # 重新加载回复列表以更新点赞数
                self._load_replies()
            else:
                error_msg = response.get('message', '点赞失败')
                self._show_error(error_msg)

        except Exception as e:
            error_msg = f"回复点赞异常: {str(e)}"
            self.logger.error(error_msg)
            self._show_error(error_msg)

    def _on_reply_to_reply(self, reply_id: int):
        """回复某条回复"""
        try:
            # 创建嵌套回复编写器
            post_id = self._safe_int(self.post_data.get('id', 0))
            nested_composer = ReplyComposer(post_id, reply_id)
            nested_composer.set_placeholder(f"回复 #{reply_id}...")
            nested_composer.reply_submitted.connect(self._submit_reply)

            # 插入到回复列表中
            self.replies_layout.addWidget(nested_composer)

        except Exception as e:
            self.logger.error(f"创建嵌套回复失败: {e}")
            self._show_error("创建回复失败")

    def _get_current_username(self) -> Optional[str]:
        """获取当前用户名"""
        try:
            # 方法1：从parent_widget获取
            if (self.parent_widget and
                hasattr(self.parent_widget, 'main_window') and
                self.parent_widget.main_window and
                hasattr(self.parent_widget.main_window, 'security_manager') and
                self.parent_widget.main_window.security_manager):
                username = self.parent_widget.main_window.security_manager.get_username()
                if username:
                    self.logger.info(f"从parent_widget获取用户名: {username}")
                    return username

            # 方法2：直接从parent_widget的security_manager获取
            if (self.parent_widget and
                hasattr(self.parent_widget, 'security_manager') and
                self.parent_widget.security_manager):
                username = self.parent_widget.security_manager.get_username()
                if username:
                    self.logger.info(f"从parent_widget.security_manager获取用户名: {username}")
                    return username

            # 方法3：从配置文件获取当前登录用户
            try:
                try:
                    from utils.config_manager import ConfigManager
                except ImportError:
                    from src.utils.config_manager import ConfigManager
                config_manager = ConfigManager()
                username = config_manager.get('current_user')
                if username:
                    self.logger.info(f"从配置文件获取用户名: {username}")
                    return username
            except Exception:
                pass

            # 方法4：尝试从全局查找主窗口
            try:
                import sys
                for obj in sys.modules.values():
                    if hasattr(obj, '__dict__'):
                        for attr_name, attr_value in obj.__dict__.items():
                            if (hasattr(attr_value, 'security_manager') and
                                hasattr(attr_value.security_manager, 'get_username')):
                                username = attr_value.security_manager.get_username()
                                if username:
                                    self.logger.info(f"从全局查找获取用户名: {username}")
                                    return username
            except Exception:
                pass

            self.logger.warning("无法获取当前用户名")
            return None

        except Exception as e:
            self.logger.error(f"获取用户名异常: {e}")
            return None

    def _show_error(self, error_msg: str):
        """显示错误信息"""
        self.logger.error(f"帖子详情错误: {error_msg}")

        # 创建自定义错误对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("错误")
        msg_box.setText(error_msg)
        msg_box.setIcon(QMessageBox.Warning)

        # 设置样式
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 2px solid {DS.COLORS['neon_red'].name()};
                border-radius: 8px;
            }}
            QMessageBox QLabel {{
                color: {DS.COLORS['text_primary'].name()};
                font-size: 14px;
                padding: 10px;
            }}
            QMessageBox QPushButton {{
                background-color: {DS.COLORS['neon_red'].darker(150).name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 1px solid {DS.COLORS['neon_red'].name()};
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {DS.COLORS['neon_red'].name()};
                color: white;
            }}
        """)

        msg_box.exec_()

    def _show_success(self, message: str):
        """显示成功信息"""
        # 创建自定义成功对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("成功")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)

        # 设置样式
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {DS.COLORS['bg_secondary'].name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 2px solid {DS.COLORS['neon_green'].name()};
                border-radius: 8px;
            }}
            QMessageBox QLabel {{
                color: {DS.COLORS['text_primary'].name()};
                font-size: 14px;
                padding: 10px;
            }}
            QMessageBox QPushButton {{
                background-color: {DS.COLORS['neon_green'].darker(150).name()};
                color: {DS.COLORS['text_primary'].name()};
                border: 1px solid {DS.COLORS['neon_green'].name()};
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {DS.COLORS['neon_green'].name()};
                color: white;
            }}
        """)

        msg_box.exec_()

    def _increment_view(self):
        """增加帖子观看量"""
        if not self.api_client:
            return

        try:
            post_id = self._safe_int(self.post_data.get('id', 0))
            if post_id <= 0:
                return

            self.logger.info(f"详情页面增加帖子 {post_id} 的观看量")

            # 调用API增加观看量
            success, response = self.api_client.increment_view(post_id)

            if success:
                new_views = response.get('data', {}).get('views_count', 0)
                self.logger.info(f"详情页面观看量增加成功，新观看量: {new_views}")

                # 更新帖子数据中的观看量
                self.post_data['views_count'] = new_views

                # 发送更新信号，通知主页面刷新
                self.post_updated.emit()

            else:
                error_msg = response.get('message', '增加观看量失败')
                self.logger.warning(f"详情页面增加观看量失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"详情页面增加观看量异常: {e}")
            # 观看量失败不影响主要功能，只记录日志
