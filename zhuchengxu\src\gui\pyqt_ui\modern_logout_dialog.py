#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化退出登录对话框
Modern 2025 Logout Dialog - 学习现代界面设计原理
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from .pyqt_design_system import DS


class ModernLogoutDialog(QDialog):
    """现代化退出登录对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self._setup_ui()
        self._setup_animations()
    
    def _setup_ui(self):
        """设置UI"""
        self.setFixedSize(400, 250)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 主容器
        self.main_container = QFrame()
        self.main_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {DS.COLORS['bg_secondary'].name()},
                    stop:1 {DS.COLORS['bg_primary'].name()});
                border: 2px solid {DS.COLORS['glass_border'].name()};
                border-radius: 16px;
            }}
        """)
        
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(30, 25, 30, 25)
        container_layout.setSpacing(20)
        
        # 标题区域
        title_layout = QHBoxLayout()
        
        # 图标
        icon_label = QLabel("🚪")
        icon_label.setFont(QFont("Segoe UI Emoji", 24))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"color: {DS.COLORS['neon_orange'].name()};")
        title_layout.addWidget(icon_label)
        
        # 标题文本
        title_label = QLabel("退出登录")
        title_label.setFont(DS.get_font('heading_lg'))
        title_label.setStyleSheet(f"""
            color: {DS.COLORS['text_primary'].name()};
            font-weight: bold;
            margin-left: 10px;
        """)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        container_layout.addLayout(title_layout)
        
        # 消息文本
        message_label = QLabel("确定要退出当前登录吗？")
        message_label.setFont(DS.get_font('body_lg'))
        message_label.setStyleSheet(f"""
            color: {DS.COLORS['text_secondary'].name()};
            line-height: 1.5;
            padding: 10px 0;
        """)
        message_label.setAlignment(Qt.AlignCenter)
        container_layout.addWidget(message_label)
        
        # 提示文本
        hint_label = QLabel("退出后需要重新登录才能使用完整功能")
        hint_label.setFont(DS.get_font('caption'))
        hint_label.setStyleSheet(f"""
            color: {DS.COLORS['text_tertiary'].name()};
            font-style: italic;
        """)
        hint_label.setAlignment(Qt.AlignCenter)
        container_layout.addWidget(hint_label)
        
        container_layout.addStretch()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setFont(DS.get_font('body_md'))
        self.cancel_btn.setFixedHeight(45)
        self.cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background: {DS.COLORS['bg_tertiary'].name()};
                border: 1px solid {DS.COLORS['glass_border'].name()};
                border-radius: 8px;
                color: {DS.COLORS['text_secondary'].name()};
                font-weight: bold;
                padding: 0 20px;
            }}
            QPushButton:hover {{
                background: {DS.COLORS['bg_tertiary'].lighter(110).name()};
                border-color: {DS.COLORS['text_secondary'].name()};
                color: {DS.COLORS['text_primary'].name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['bg_tertiary'].darker(110).name()};
            }}
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        # 确认按钮
        self.confirm_btn = QPushButton("确认退出")
        self.confirm_btn.setFont(DS.get_font('body_md'))
        self.confirm_btn.setFixedHeight(45)
        self.confirm_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {DS.COLORS['neon_orange'].name()},
                    stop:1 {DS.COLORS['neon_orange'].darker(120).name()});
                border: 1px solid {DS.COLORS['neon_orange'].name()};
                border-radius: 8px;
                color: white;
                font-weight: bold;
                padding: 0 20px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {DS.COLORS['neon_orange'].lighter(110).name()},
                    stop:1 {DS.COLORS['neon_orange'].name()});
                border-color: {DS.COLORS['neon_orange'].lighter(120).name()};
            }}
            QPushButton:pressed {{
                background: {DS.COLORS['neon_orange'].darker(130).name()};
            }}
        """)
        self.confirm_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.confirm_btn)
        
        container_layout.addLayout(button_layout)
        main_layout.addWidget(self.main_container)
        
        # 居中显示
        if self.parent():
            parent_rect = self.parent().geometry()
            self.move(
                parent_rect.x() + (parent_rect.width() - self.width()) // 2,
                parent_rect.y() + (parent_rect.height() - self.height()) // 2
            )
    
    def _setup_animations(self):
        """设置动画效果"""
        # 淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 缩放动画
        self.scale_animation = QPropertyAnimation(self.main_container, b"geometry")
        self.scale_animation.setDuration(200)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)
    
    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        
        # 启动淡入动画
        self.fade_animation.start()
        
        # 启动缩放动画
        final_rect = self.main_container.geometry()
        start_rect = QRect(
            final_rect.x() + final_rect.width() // 4,
            final_rect.y() + final_rect.height() // 4,
            final_rect.width() // 2,
            final_rect.height() // 2
        )
        
        self.scale_animation.setStartValue(start_rect)
        self.scale_animation.setEndValue(final_rect)
        self.scale_animation.start()
    
    def keyPressEvent(self, event):
        """键盘事件"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.accept()
        else:
            super().keyPressEvent(event)
    
    @staticmethod
    def show_logout_dialog(parent=None):
        """
        显示退出登录对话框
        
        Args:
            parent: 父窗口
            
        Returns:
            bool: True表示确认退出，False表示取消
        """
        dialog = ModernLogoutDialog(parent)
        result = dialog.exec_()
        return result == QDialog.Accepted


# 兼容性别名
class LogoutDialog(ModernLogoutDialog):
    """兼容性别名"""
    pass
