#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt响应式布局工具
Responsive Layout Utilities for PyQt
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, 
                             QFrame, QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, QSize


class ResponsiveContainer(QWidget):
    """响应式容器基类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_responsive_layout()
    
    def setup_responsive_layout(self):
        """设置响应式布局"""
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 滚动内容容器
        self.scroll_content = QWidget()
        self.content_layout = QVBoxLayout(self.scroll_content)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)
        
        # 设置滚动区域
        self.scroll_area.setWidget(self.scroll_content)
        
        # 主容器布局
        container_layout = QVBoxLayout(self)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(self.scroll_area)
    
    def add_widget(self, widget, stretch=0):
        """添加组件到内容布局"""
        self.content_layout.addWidget(widget, stretch)
    
    def add_layout(self, layout, stretch=0):
        """添加布局到内容布局"""
        self.content_layout.addLayout(layout, stretch)
    
    def add_stretch(self, stretch=0):
        """添加弹性空间"""
        self.content_layout.addStretch(stretch)


class ResponsiveButton:
    """响应式按钮工具类"""
    
    @staticmethod
    def setup_button(button, min_height=40, max_height=60):
        """设置按钮为响应式"""
        button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        button.setMinimumHeight(min_height)
        button.setMaximumHeight(max_height)
        return button
    
    @staticmethod
    def create_button_group(buttons_data, layout, button_class):
        """创建响应式按钮组
        
        Args:
            buttons_data: 按钮数据列表 [(text, color, action, stretch), ...]
            layout: 目标布局
            button_class: 按钮类
        """
        for text, color, action, stretch in buttons_data:
            btn = button_class(text, color, 'solid')
            ResponsiveButton.setup_button(btn)
            btn.clicked.connect(action)
            layout.addWidget(btn, stretch)


class ResponsiveFrame:
    """响应式框架工具类"""
    
    @staticmethod
    def setup_frame(frame):
        """设置框架为响应式"""
        frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        return frame
    
    @staticmethod
    def create_section_layout(frame, margins=(20, 15, 20, 15), spacing=15):
        """创建区域布局"""
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(*margins)
        layout.setSpacing(spacing)
        return layout
    
    @staticmethod
    def create_horizontal_layout(frame, margins=(20, 15, 20, 15), spacing=15):
        """创建水平布局"""
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(*margins)
        layout.setSpacing(spacing)
        return layout


class ResponsiveGrid:
    """响应式网格布局工具类"""
    
    @staticmethod
    def setup_grid_layout(layout, rows, cols):
        """设置网格布局拉伸"""
        # 设置列拉伸
        for col in range(cols):
            layout.setColumnStretch(col, 1)
        
        # 设置行拉伸
        for row in range(rows):
            layout.setRowStretch(row, 1)
    
    @staticmethod
    def add_widgets_to_grid(layout, widgets, cols=2):
        """将组件添加到网格布局"""
        for i, widget in enumerate(widgets):
            row = i // cols
            col = i % cols
            layout.addWidget(widget, row, col)


class ResponsiveText:
    """响应式文本工具类"""
    
    @staticmethod
    def setup_label(label, alignment=Qt.AlignCenter):
        """设置标签为响应式"""
        label.setAlignment(alignment)
        label.setWordWrap(True)
        label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        return label
    
    @staticmethod
    def setup_text_edit(text_edit, min_height=100, max_height=200):
        """设置文本编辑器为响应式"""
        text_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        text_edit.setMinimumHeight(min_height)
        text_edit.setMaximumHeight(max_height)
        return text_edit


class BreakpointManager:
    """断点管理器 - 处理不同屏幕尺寸"""
    
    # 断点定义
    BREAKPOINTS = {
        'xs': 480,   # 超小屏幕
        'sm': 768,   # 小屏幕
        'md': 1024,  # 中等屏幕
        'lg': 1200,  # 大屏幕
        'xl': 1400   # 超大屏幕
    }
    
    @classmethod
    def get_breakpoint(cls, width):
        """根据宽度获取断点"""
        if width < cls.BREAKPOINTS['xs']:
            return 'xs'
        elif width < cls.BREAKPOINTS['sm']:
            return 'sm'
        elif width < cls.BREAKPOINTS['md']:
            return 'md'
        elif width < cls.BREAKPOINTS['lg']:
            return 'lg'
        else:
            return 'xl'
    
    @classmethod
    def get_responsive_margins(cls, breakpoint):
        """根据断点获取响应式边距"""
        margins = {
            'xs': (10, 10, 10, 10),
            'sm': (15, 15, 15, 15),
            'md': (20, 20, 20, 20),
            'lg': (25, 25, 25, 25),
            'xl': (30, 30, 30, 30)
        }
        return margins.get(breakpoint, (20, 20, 20, 20))
    
    @classmethod
    def get_responsive_spacing(cls, breakpoint):
        """根据断点获取响应式间距"""
        spacing = {
            'xs': 10,
            'sm': 15,
            'md': 20,
            'lg': 25,
            'xl': 30
        }
        return spacing.get(breakpoint, 20)


class ResponsivePage(ResponsiveContainer):
    """响应式页面基类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_breakpoint = 'md'
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.handle_responsive_resize(event.size())
    
    def handle_responsive_resize(self, size):
        """处理响应式调整"""
        width = size.width()
        new_breakpoint = BreakpointManager.get_breakpoint(width)
        
        if new_breakpoint != self.current_breakpoint:
            self.current_breakpoint = new_breakpoint
            self.update_responsive_layout(new_breakpoint)
    
    def update_responsive_layout(self, breakpoint):
        """更新响应式布局 - 子类重写此方法"""
        # 更新边距和间距
        margins = BreakpointManager.get_responsive_margins(breakpoint)
        spacing = BreakpointManager.get_responsive_spacing(breakpoint)
        
        self.content_layout.setContentsMargins(*margins)
        self.content_layout.setSpacing(spacing)


class ModernPageBuilder:
    """现代化页面构建器"""

    @staticmethod
    def create_page_header(title, subtitle, description, icon_color, parent_layout):
        """创建现代化页面标题"""
        from .pyqt_components import GlassWidget
        from .pyqt_design_system import DS
        from PyQt5.QtWidgets import QVBoxLayout, QLabel, QSizePolicy
        from PyQt5.QtCore import Qt

        header_frame = GlassWidget(glass_opacity=0.08, blur_radius=25)
        header_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(40, 30, 40, 30)
        header_layout.setSpacing(15)

        # 标题
        title_label = QLabel(title)
        title_label.setFont(DS.get_font('display'))
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS[icon_color].name()};
                background: transparent;
                font-weight: bold;
            }}
        """)
        title_label.setAlignment(Qt.AlignCenter)

        # 副标题
        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(DS.get_font('heading_lg'))
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_secondary'].name()};
                background: transparent;
                font-weight: 500;
            }}
        """)
        subtitle_label.setAlignment(Qt.AlignCenter)

        # 描述文本
        desc_label = QLabel(description)
        desc_label.setFont(DS.get_font('body_lg'))
        desc_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_primary'].name()};
                background: transparent;
                line-height: 1.6;
            }}
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addWidget(desc_label)

        parent_layout.addWidget(header_frame)
        return header_frame

    @staticmethod
    def create_info_card(icon, title, content, color='neon_cyan'):
        """创建信息卡片"""
        from .pyqt_components import GlassWidget
        from .pyqt_design_system import DS
        from PyQt5.QtWidgets import QHBoxLayout, QVBoxLayout, QLabel, QWidget, QSizePolicy
        from PyQt5.QtCore import Qt

        card = GlassWidget(glass_opacity=0.04, blur_radius=10)
        card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout = QHBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setSpacing(15)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setFont(DS.get_font('heading_lg'))
        icon_label.setStyleSheet(f"color: {DS.COLORS[color].name()};")
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)

        # 信息区域
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(5)

        # 标题
        title_label = QLabel(title)
        title_label.setFont(DS.get_font('body_md'))
        title_label.setStyleSheet(f"color: {DS.COLORS['text_secondary'].name()};")

        # 内容
        content_label = QLabel(content)
        content_label.setFont(DS.get_font('body_lg'))
        content_label.setStyleSheet(f"""
            QLabel {{
                color: {DS.COLORS['text_primary'].name()};
                background: transparent;
                font-weight: 500;
            }}
        """)

        info_layout.addWidget(title_label)
        info_layout.addWidget(content_label)

        card_layout.addWidget(icon_label)
        card_layout.addWidget(info_widget, 1)

        return card
