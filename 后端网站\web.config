<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <handlers>
            <remove name="WebServiceHandlerFactory-ISAPI-4.0_32bit" />
            <remove name="SimpleHandlerFactory-ISAPI-4.0_32bit" />
            <remove name="PageHandlerFactory-ISAPI-4.0_32bit" />
            <remove name="WebServiceHandlerFactory-Integrated-4.0" />
            <remove name="SimpleHandlerFactory-Integrated-4.0" />
            <remove name="PageHandlerFactory-Integrated-4.0" />
            <remove name="WebServiceHandlerFactory-ISAPI-4.0_64bit" />
            <remove name="SimpleHandlerFactory-ISAPI-4.0_64bit" />
            <remove name="PageHandlerFactory-ISAPI-4.0_64bit" />
            <remove name="WebServiceHandlerFactory-ISAPI-2.0-64" />
            <remove name="SimpleHandlerFactory-ISAPI-2.0-64" />
            <remove name="PageHandlerFactory-ISAPI-2.0-64" />
            <remove name="WebServiceHandlerFactory-ISAPI-2.0" />
            <remove name="SimpleHandlerFactory-ISAPI-2.0" />
            <remove name="PageHandlerFactory-ISAPI-2.0" />
            <remove name="WebServiceHandlerFactory-Integrated" />
            <remove name="SimpleHandlerFactory-Integrated" />
            <remove name="PageHandlerFactory-Integrated" />
            <remove name="ASPClassic" />
            <add name="php_74" path="*.php" verb="*" modules="FastCgiModule" scriptProcessor="D:\BtSoft\php\74\php-cgi.exe" resourceType="File" requireAccess="Script" />
        </handlers>
    </system.webServer>
</configuration>
