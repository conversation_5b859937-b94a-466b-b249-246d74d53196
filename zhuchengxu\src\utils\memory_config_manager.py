"""
内存化配置管理器 - 完全在内存中操作配置，减少磁盘I/O
"""

import json
import os
import time
from typing import Dict, Any, Optional
from src.utils.memory_preloader import get_memory_preloader


class MemoryConfigManager:
    """内存化配置管理器"""
    
    def __init__(self, config_file: str = 'config.json'):
        # 🔧 使用与main.py一致的路径检测逻辑
        import sys
        import os
        # 直接使用main.py中设置的环境变量
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
        if config_dir:
            self.config_file = os.path.join(config_dir, config_file)
        else:
            # 备选方案：使用sys.argv[0]
            if sys.argv and len(sys.argv) > 0:
                argv_path = os.path.abspath(sys.argv[0])
                if argv_path.endswith('.exe'):
                    self.config_file = os.path.join(os.path.dirname(argv_path), config_file)
                else:
                    self.config_file = os.path.join(os.path.dirname(argv_path), config_file)
            else:
                self.config_file = os.path.join(os.getcwd(), config_file)

        self.memory_preloader = get_memory_preloader()
        self.last_save_time = time.time()
        self.save_interval = 30  # 30秒批量保存一次
        self.dirty = False  # 标记是否有未保存的更改

        print(f"✅ 内存化配置管理器已启动: {self.config_file}")
        
    def get(self, key: str, default: Any = None) -> Any:
        """从内存获取配置值"""
        config_data = self.memory_preloader.get_preloaded_config(self.config_file)
        
        # 支持点号分隔的嵌套键
        keys = key.split('.')
        value = config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any) -> bool:
        """在内存中设置配置值"""
        try:
            config_data = self.memory_preloader.get_preloaded_config(self.config_file)
            
            # 支持点号分隔的嵌套键
            keys = key.split('.')
            current = config_data
            
            # 创建嵌套结构
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
                
            # 设置最终值
            current[keys[-1]] = value
            
            # 更新内存中的配置
            self.memory_preloader.update_config_memory(self.config_file, config_data)
            self.dirty = True
            
            # 检查是否需要保存到磁盘
            self._check_auto_save()
            
            return True
        except Exception as e:
            print(f"❌ 设置配置失败 {key}: {e}")
            return False
            
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.memory_preloader.get_preloaded_config(self.config_file)
        
    def update(self, config_dict: Dict[str, Any]) -> bool:
        """批量更新配置"""
        try:
            config_data = self.memory_preloader.get_preloaded_config(self.config_file)
            config_data.update(config_dict)
            self.memory_preloader.update_config_memory(self.config_file, config_data)
            self.dirty = True
            self._check_auto_save()
            return True
        except Exception as e:
            print(f"❌ 批量更新配置失败: {e}")
            return False
            
    def delete(self, key: str) -> bool:
        """删除配置项"""
        try:
            config_data = self.memory_preloader.get_preloaded_config(self.config_file)
            
            keys = key.split('.')
            current = config_data
            
            # 导航到父级
            for k in keys[:-1]:
                current = current[k]
                
            # 删除最终键
            if keys[-1] in current:
                del current[keys[-1]]
                self.memory_preloader.update_config_memory(self.config_file, config_data)
                self.dirty = True
                self._check_auto_save()
                return True
            return False
        except Exception as e:
            print(f"❌ 删除配置失败 {key}: {e}")
            return False
            
    def _check_auto_save(self):
        """检查是否需要自动保存"""
        current_time = time.time()
        if (self.dirty and 
            current_time - self.last_save_time > self.save_interval):
            self.save_to_disk()
            
    def save_to_disk(self) -> bool:
        """手动保存到磁盘"""
        try:
            config_data = self.memory_preloader.get_preloaded_config(self.config_file)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
                
            self.dirty = False
            self.last_save_time = time.time()
            print(f"💾 配置已保存到磁盘: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 保存配置到磁盘失败: {e}")
            return False
            
    def reload_from_disk(self) -> bool:
        """从磁盘重新加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.memory_preloader.update_config_memory(self.config_file, config_data)
                print(f"🔄 配置已从磁盘重新加载: {self.config_file}")
                return True
            return False
        except Exception as e:
            print(f"❌ 从磁盘重新加载配置失败: {e}")
            return False
            
    def has_unsaved_changes(self) -> bool:
        """检查是否有未保存的更改"""
        return self.dirty
        
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        config_data = self.memory_preloader.get_preloaded_config(self.config_file)
        return {
            'config_file': self.config_file,
            'total_keys': len(config_data),
            'dirty': self.dirty,
            'last_save_time': self.last_save_time,
            'memory_size_kb': len(json.dumps(config_data)) / 1024
        }


class MemoryAuthCache:
    """内存化认证缓存"""
    
    def __init__(self):
        # 🔧 使用与main.py一致的路径检测逻辑
        import sys
        import os
        # 直接使用main.py中设置的环境变量
        config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
        if config_dir:
            self.cache_file = os.path.join(config_dir, 'auth_cache.json')
        else:
            # 备选方案：使用sys.argv[0]
            if sys.argv and len(sys.argv) > 0:
                argv_path = os.path.abspath(sys.argv[0])
                if argv_path.endswith('.exe'):
                    self.cache_file = os.path.join(os.path.dirname(argv_path), 'auth_cache.json')
                else:
                    self.cache_file = os.path.join(os.path.dirname(argv_path), 'auth_cache.json')
            else:
                self.cache_file = os.path.join(os.getcwd(), 'auth_cache.json')
        self.memory_preloader = get_memory_preloader()
        
    def get_cached_auth(self) -> Optional[Dict[str, Any]]:
        """获取缓存的认证信息"""
        auth_data = self.memory_preloader.get_preloaded_config(self.cache_file)
        return auth_data.get('auth_info')
        
    def save_auth_cache(self, auth_info: Dict[str, Any]) -> bool:
        """保存认证信息到内存缓存"""
        try:
            auth_data = self.memory_preloader.get_preloaded_config(self.cache_file)
            auth_data['auth_info'] = auth_info
            auth_data['cached_at'] = time.time()
            
            self.memory_preloader.update_config_memory(self.cache_file, auth_data)
            print("✅ 认证信息已缓存到内存")
            return True
        except Exception as e:
            print(f"❌ 缓存认证信息失败: {e}")
            return False
            
    def clear_auth_cache(self) -> bool:
        """清除认证缓存"""
        try:
            self.memory_preloader.update_config_memory(self.cache_file, {})
            print("🗑️ 认证缓存已清除")
            return True
        except Exception as e:
            print(f"❌ 清除认证缓存失败: {e}")
            return False
            
    def is_cache_valid(self, max_age_hours: int = 24) -> bool:
        """检查缓存是否有效"""
        auth_data = self.memory_preloader.get_preloaded_config(self.cache_file)
        cached_at = auth_data.get('cached_at', 0)
        
        if not cached_at:
            return False
            
        age_hours = (time.time() - cached_at) / 3600
        return age_hours < max_age_hours


# 全局实例
_memory_config_manager = None
_memory_auth_cache = None

def get_memory_config_manager(config_file: str = 'config.json') -> MemoryConfigManager:
    """获取内存化配置管理器"""
    global _memory_config_manager
    if _memory_config_manager is None:
        _memory_config_manager = MemoryConfigManager(config_file)
    return _memory_config_manager

def get_memory_auth_cache() -> MemoryAuthCache:
    """获取内存化认证缓存"""
    global _memory_auth_cache
    if _memory_auth_cache is None:
        _memory_auth_cache = MemoryAuthCache()
    return _memory_auth_cache

def save_all_memory_configs():
    """保存所有内存配置到磁盘"""
    try:
        # 保存主配置
        if _memory_config_manager:
            _memory_config_manager.save_to_disk()
            
        # 批量保存所有配置
        from src.utils.memory_preloader import save_memory_configs
        save_memory_configs()
        
        print("💾 所有内存配置已保存到磁盘")
    except Exception as e:
        print(f"❌ 保存内存配置失败: {e}")
