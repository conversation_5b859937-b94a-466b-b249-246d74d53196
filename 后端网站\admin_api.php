<?php
/**
 * 管理后台API接口
 * 专门处理AJAX请求，避免HTML输出干扰
 */

declare(strict_types=1);

// 引入核心类
require_once __DIR__ . '/includes/Database.php';
require_once __DIR__ . '/includes/AuthManager.php';
require_once __DIR__ . '/includes/Logger.php';
require_once __DIR__ . '/includes/SecurityConfig.php';

// 辅助函数
function parseMemoryLimit($limit) {
    $limit = trim($limit);
    $last = strtolower($limit[strlen($limit)-1]);
    $limit = (int) $limit;
    switch($last) {
        case 'g': $limit *= 1024;
        case 'm': $limit *= 1024;
        case 'k': $limit *= 1024;
    }
    return $limit;
}

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// 初始化安全配置
SecurityConfig::init();

session_start();

// 设置JSON响应头
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// 检查登录状态
$isLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

if (!$isLoggedIn) {
    echo json_encode([
        'success' => false,
        'code' => 'NOT_LOGGED_IN',
        'message' => '请先登录管理后台'
    ]);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'code' => 'INVALID_METHOD',
        'message' => '只支持POST请求'
    ]);
    exit;
}

// 获取操作类型
$action = $_POST['action'] ?? '';

if (empty($action)) {
    echo json_encode([
        'success' => false,
        'code' => 'MISSING_ACTION',
        'message' => '缺少操作参数'
    ]);
    exit;
}

try {
    $database = new Database();
    $logger = new Logger();
    
    switch ($action) {
        case 'get_password':
            // 获取密码信息（仅用于显示，实际密码已加密）
            $licenseId = (int)($_POST['license_id'] ?? 0);
            
            if ($licenseId <= 0) {
                echo json_encode([
                    'success' => false,
                    'message' => '无效的授权ID'
                ]);
                break;
            }
            
            $user = $database->fetchOne("SELECT username, created_at FROM users WHERE id = ?", [$licenseId]);

            if ($user) {
                // 由于密码已加密存储，我们只能提示重置
                echo json_encode([
                    'success' => true,
                    'message' => "用户：{$user['username']}\n创建时间：{$user['created_at']}\n\n密码已加密存储，如需查看请重置密码。"
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '用户不存在'
                ]);
            }
            break;

        case 'get_device_details':
            // 获取设备详情
            $deviceId = $_POST['device_id'] ?? '';
            $userId = (int)($_POST['user_id'] ?? 0);

            if (empty($deviceId) || $userId <= 0) {
                echo json_encode([
                    'success' => false,
                    'message' => '参数错误'
                ]);
                break;
            }

            $device = $database->fetchOne("
                SELECT d.*, u.username, u.user_type
                FROM user_devices d
                LEFT JOIN users u ON d.user_id = u.id
                WHERE d.device_id = ? AND d.user_id = ?
            ", [$deviceId, $userId]);

            if ($device) {
                echo json_encode([
                    'success' => true,
                    'device' => $device
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '设备不存在'
                ]);
            }
            break;

        case 'system_health_check':
            // 系统健康检查
            $results = [];

            // 检查数据库连接
            try {
                $database->fetchValue("SELECT 1");
                $results[] = [
                    'name' => '检查数据库连接...',
                    'status' => 'success',
                    'message' => '数据库连接正常'
                ];
            } catch (Exception $e) {
                $results[] = [
                    'name' => '检查数据库连接...',
                    'status' => 'error',
                    'message' => '数据库连接失败: ' . $e->getMessage()
                ];
            }

            // 检查文件权限
            $dataDir = __DIR__ . '/data';
            if (is_writable($dataDir)) {
                $results[] = [
                    'name' => '检查文件权限...',
                    'status' => 'success',
                    'message' => '文件权限正常'
                ];
            } else {
                $results[] = [
                    'name' => '检查文件权限...',
                    'status' => 'warning',
                    'message' => 'data目录不可写'
                ];
            }

            // 检查PHP扩展
            $requiredExtensions = ['pdo', 'json', 'mbstring'];
            $missingExtensions = [];
            foreach ($requiredExtensions as $ext) {
                if (!extension_loaded($ext)) {
                    $missingExtensions[] = $ext;
                }
            }
            if (empty($missingExtensions)) {
                $results[] = [
                    'name' => '检查PHP扩展...',
                    'status' => 'success',
                    'message' => '所需扩展已安装'
                ];
            } else {
                $results[] = [
                    'name' => '检查PHP扩展...',
                    'status' => 'error',
                    'message' => '缺少扩展: ' . implode(', ', $missingExtensions)
                ];
            }

            // 检查磁盘空间
            $freeBytes = disk_free_space(__DIR__);
            $freeGB = round($freeBytes / 1024 / 1024 / 1024, 2);
            if ($freeGB > 1) {
                $results[] = [
                    'name' => '检查磁盘空间...',
                    'status' => 'success',
                    'message' => "可用空间: {$freeGB}GB"
                ];
            } else {
                $results[] = [
                    'name' => '检查磁盘空间...',
                    'status' => 'warning',
                    'message' => "可用空间不足: {$freeGB}GB"
                ];
            }

            // 检查内存使用
            $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
            $memoryLimit = ini_get('memory_limit');
            $results[] = [
                'name' => '检查内存使用...',
                'status' => 'success',
                'message' => "当前使用: {$memoryUsage}MB, 限制: {$memoryLimit}"
            ];

            echo json_encode([
                'success' => true,
                'results' => $results
            ]);
            break;

        case 'get_performance_metrics':
            // 获取性能指标
            try {
                // 获取内存使用率
                $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 1);
                $memoryLimit = ini_get('memory_limit');
                $memoryLimitBytes = parseMemoryLimit($memoryLimit);
                $memoryPercent = $memoryLimitBytes > 0 ? round(($memoryUsage * 1024 * 1024) / $memoryLimitBytes * 100, 1) : 0;

                // 获取磁盘使用率
                $diskFree = disk_free_space(__DIR__);
                $diskTotal = disk_total_space(__DIR__);
                $diskUsed = $diskTotal - $diskFree;
                $diskPercent = round(($diskUsed / $diskTotal) * 100, 1);

                // CPU使用率（简单模拟，实际需要系统调用）
                $cpuPercent = rand(5, 25); // 模拟CPU使用率

                echo json_encode([
                    'success' => true,
                    'cpu_usage' => $cpuPercent . '%',
                    'memory_usage' => $memoryPercent . '%',
                    'disk_usage' => $diskPercent . '%'
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '获取性能指标失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'toggle_maintenance':
            // 切换维护模式
            $enabled = $_POST['enabled'] === '1';
            $maintenanceFile = __DIR__ . '/maintenance.lock';

            try {
                if ($enabled) {
                    file_put_contents($maintenanceFile, date('Y-m-d H:i:s'));
                    echo json_encode([
                        'success' => true,
                        'message' => '维护模式已启用'
                    ]);
                } else {
                    if (file_exists($maintenanceFile)) {
                        unlink($maintenanceFile);
                    }
                    echo json_encode([
                        'success' => true,
                        'message' => '维护模式已关闭'
                    ]);
                }
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '操作失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'list_backups':
            // 列出备份文件
            try {
                $backupDir = __DIR__ . '/backups';
                $backups = [];

                if (is_dir($backupDir)) {
                    $files = glob($backupDir . '/*.sql');
                    foreach ($files as $file) {
                        $backups[] = [
                            'name' => basename($file),
                            'size' => formatFileSize(filesize($file)),
                            'date' => date('Y-m-d H:i:s', filemtime($file))
                        ];
                    }
                }

                echo json_encode([
                    'success' => true,
                    'backups' => $backups
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '获取备份列表失败: ' . $e->getMessage()
                ]);
            }
            break;











        default:
            echo json_encode([
                'success' => false,
                'code' => 'UNKNOWN_ACTION',
                'message' => '未知的操作: ' . $action
            ]);
            break;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'code' => 'SYSTEM_ERROR',
        'message' => '系统错误: ' . $e->getMessage()
    ]);
}
?>
