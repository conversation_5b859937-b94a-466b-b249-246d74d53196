#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责配置文件的读取、保存和管理
"""

import json
import os
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
        """
        # 确定配置文件的绝对路径 - 直接使用main.py中设置的环境变量
        if not os.path.isabs(config_file):
            # 直接使用main.py中设置的配置目录环境变量
            config_dir = os.environ.get('AUGMENT_CONFIG_DIR')
            if config_dir:
                self.config_file = os.path.join(config_dir, config_file)
                print(f"📁 使用main.py设置的配置目录: {self.config_file}")
            else:
                # 如果环境变量未设置，使用当前工作目录作为备选
                cwd = os.getcwd()
                self.config_file = os.path.join(cwd, config_file)
                print(f"⚠️ 环境变量未设置，使用当前工作目录: {self.config_file}")
        else:
            self.config_file = config_file
            print(f"📁 使用绝对路径配置文件: {self.config_file}")

        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        default_config = {
            'email_domain': 'hwsyyds.xyz',
            'temp_mail_address': '',
            'pin_code': '',
            'auto_refresh_enabled': False,
            'auto_refresh_interval': 1000,  # 毫秒
            'browser_headless': False,
            'browser_timeout': 30,
            'max_retry_count': 3,
            'log_level': 'INFO'
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    default_config.update(loaded_config)
                    return default_config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return default_config
        else:
            # 创建默认配置文件
            self.save_config(default_config)
            return default_config
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置，如果为None则保存当前配置
            
        Returns:
            是否保存成功
        """
        try:
            config_to_save = config if config is not None else self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)
            if config is not None:
                self.config = config_to_save
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.config[key] = value
    
    def update(self, updates: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            updates: 要更新的配置字典
        """
        self.config.update(updates)
    
    def get_all(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            配置字典
        """
        return self.config.copy()
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = {
            'email_domain': 'hwsyyds.xyz',
            'temp_mail_address': '',
            'pin_code': '',
            'auto_refresh_enabled': False,
            'auto_refresh_interval': 1000,
            'browser_headless': False,
            'browser_timeout': 30,
            'max_retry_count': 3,
            'log_level': 'INFO'
        }
        self.save_config()
    
    def validate_config(self) -> Dict[str, str]:
        """
        验证配置的有效性
        
        Returns:
            验证错误信息字典，空字典表示验证通过
        """
        errors = {}
        
        # 验证邮箱域名
        email_domain = self.get('email_domain')
        if not email_domain or not isinstance(email_domain, str):
            errors['email_domain'] = '邮箱域名不能为空'
        
        # 验证TempMail配置
        temp_mail_address = self.get('temp_mail_address')
        if temp_mail_address and '@' not in temp_mail_address:
            errors['temp_mail_address'] = 'TempMail邮箱地址格式不正确'
        
        # 验证刷新间隔
        refresh_interval = self.get('auto_refresh_interval')
        if not isinstance(refresh_interval, int) or refresh_interval < 100:
            errors['auto_refresh_interval'] = '自动刷新间隔必须大于100毫秒'
        
        # 验证超时时间
        timeout = self.get('browser_timeout')
        if not isinstance(timeout, int) or timeout < 5:
            errors['browser_timeout'] = '浏览器超时时间必须大于5秒'
        
        return errors
