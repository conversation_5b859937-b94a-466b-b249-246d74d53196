<?php
/**
 * Augment认证系统 - 主页
 * PHP 7.4+ 现代化认证网站
 */

declare(strict_types=1);

// 基本配置
define('SITE_NAME', 'Augment软件授权系统');
define('SITE_VERSION', '2.0.0');
define('SITE_DESCRIPTION', '专业的软件防盗版认证与授权管理平台');

// 安全头
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SITE_NAME ?></title>
    <meta name="description" content="<?= SITE_DESCRIPTION ?>">
    <meta name="keywords" content="软件认证,授权管理,在线验证,用户管理">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="apple-touch-icon" href="favicon.svg">

    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
        }
        
        .hero-section {
            padding: 100px 0;
            color: white;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }
        
        .feature-icon.primary { background: var(--primary-gradient); }
        .feature-icon.secondary { background: var(--secondary-gradient); }
        .feature-icon.success { background: var(--success-gradient); }
        .feature-icon.dark { background: var(--dark-gradient); }
        
        .btn-custom {
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary-custom {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-outline-custom {
            background: transparent;
            border: 2px solid white;
            color: white;
        }
        
        .btn-outline-custom:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
        }
        
        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem 2rem;
            margin: 3rem 0;
        }
        
        .stat-item {
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .footer-section {
            background: rgba(0, 0, 0, 0.2);
            color: white;
            padding: 3rem 0;
            margin-top: 5rem;
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
            margin: 0 10px;
            transition: transform 0.3s ease;
        }
        
        .social-links a:hover {
            transform: scale(1.2);
            color: #f8f9fa;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .feature-card {
                margin-bottom: 1.5rem;
            }
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .fade-in {
            animation: fadeIn 1s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: rgba(0,0,0,0.1); backdrop-filter: blur(10px);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-shield-check me-2"></i><?= SITE_NAME ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特色</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#stats">数据统计</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="api.php">API文档</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin.php">管理后台</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <h1 class="hero-title fade-in"><?= SITE_NAME ?></h1>
                        <p class="hero-subtitle fade-in"><?= SITE_DESCRIPTION ?></p>
                        <div class="fade-in">
                            <a href="admin.php" class="btn btn-primary-custom btn-custom me-3">
                                <i class="bi bi-shield-lock me-2"></i>管理后台
                            </a>
                            <a href="api.php?action=status" class="btn btn-outline-custom btn-custom">
                                <i class="bi bi-gear me-2"></i>API状态
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能特色 -->
        <section id="features" class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="feature-card floating-animation">
                            <div class="feature-icon primary">
                                <i class="bi bi-shield-lock"></i>
                            </div>
                            <h4 class="text-center mb-3">防盗版保护</h4>
                            <p class="text-center text-muted">
                                采用多重加密技术，有效防止软件被破解和盗版传播
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="feature-card floating-animation" style="animation-delay: 0.2s;">
                            <div class="feature-icon secondary">
                                <i class="bi bi-devices"></i>
                            </div>
                            <h4 class="text-center mb-3">授权管理</h4>
                            <p class="text-center text-muted">
                                灵活的授权策略，支持时间限制、设备绑定和远程控制
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="feature-card floating-animation" style="animation-delay: 0.4s;">
                            <div class="feature-icon success">
                                <i class="bi bi-lightning"></i>
                            </div>
                            <h4 class="text-center mb-3">实时验证</h4>
                            <p class="text-center text-muted">
                                毫秒级响应的在线验证，确保只有正版用户才能使用软件
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="feature-card floating-animation" style="animation-delay: 0.6s;">
                            <div class="feature-icon dark">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <h4 class="text-center mb-3">使用监控</h4>
                            <p class="text-center text-muted">
                                详细的使用统计和异常监控，及时发现盗版和滥用行为
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 数据统计 -->
        <section id="stats" class="py-5">
            <div class="container">
                <div class="stats-section">
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-item">
                                <span class="stat-number" data-count="1000">0</span>
                                <span class="stat-label">授权用户</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-item">
                                <span class="stat-number" data-count="5000">0</span>
                                <span class="stat-label">活跃授权</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-item">
                                <span class="stat-number" data-count="99.9">0</span>
                                <span class="stat-label">服务可用性(%)</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-item">
                                <span class="stat-number" data-count="24">0</span>
                                <span class="stat-label">7x24小时服务</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h5><i class="bi bi-shield-check me-2"></i><?= SITE_NAME ?></h5>
                    <p class="mb-3"><?= SITE_DESCRIPTION ?></p>
                    <p class="text-muted">版本 <?= SITE_VERSION ?> | © 2025 All Rights Reserved</p>
                </div>
                <div class="col-lg-3">
                    <h6>快速链接</h6>
                    <ul class="list-unstyled">
                        <li><a href="api.php" class="text-white-50">API文档</a></li>
                        <li><a href="admin.php" class="text-white-50">管理后台</a></li>
                        <li><a href="help.php" class="text-white-50">帮助中心</a></li>
                        <li><a href="contact.php" class="text-white-50">联系我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-3">
                    <h6>技术支持</h6>
                    <p class="text-white-50">
                        <i class="bi bi-envelope me-2"></i><EMAIL><br>
                        <i class="bi bi-chat-dots me-2"></i>QQ群: 735821698
                    </p>
                    <div class="social-links">
                        <a href="#"><i class="bi bi-github"></i></a>
                        <a href="#"><i class="bi bi-twitter"></i></a>
                        <a href="#"><i class="bi bi-telegram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JS -->
    <script>
        // 数字动画
        function animateNumbers() {
            const counters = document.querySelectorAll('.stat-number');
            
            counters.forEach(counter => {
                const target = parseFloat(counter.getAttribute('data-count'));
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    
                    if (target === 99.9) {
                        counter.textContent = current.toFixed(1);
                    } else {
                        counter.textContent = Math.floor(current).toLocaleString();
                    }
                }, 20);
            });
        }
        
        // 滚动动画
        function handleScrollAnimations() {
            const statsSection = document.getElementById('stats');
            const rect = statsSection.getBoundingClientRect();
            
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                animateNumbers();
                window.removeEventListener('scroll', handleScrollAnimations);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加滚动监听
            window.addEventListener('scroll', handleScrollAnimations);
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
