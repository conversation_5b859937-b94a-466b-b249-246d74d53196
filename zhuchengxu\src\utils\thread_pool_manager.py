"""
高性能线程池管理器 - 替代多进程方案
为每个页面创建独立线程池，主窗口负责显示，线程池负责数据处理
"""

import threading
import queue
import time
import concurrent.futures
from typing import Dict, Any, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal


class ThreadPoolManager(QObject):
    """线程池管理器"""
    
    # 定义信号
    task_completed = pyqtSignal(str, dict)  # 任务完成信号
    task_failed = pyqtSignal(str, str)      # 任务失败信号
    
    def __init__(self):
        super().__init__()
        self.thread_pools = {}  # 存储各个页面的线程池
        self.task_queues = {}   # 存储任务队列
        self.callbacks = {}     # 存储回调函数
        self.running = True
        
        print("✅ 线程池管理器初始化完成")
        
    def create_page_thread_pool(self, page_name: str, max_workers: int = 2):
        """为页面创建独立线程池"""
        try:
            # 创建线程池
            thread_pool = concurrent.futures.ThreadPoolExecutor(
                max_workers=max_workers,
                thread_name_prefix=f"{page_name}_worker"
            )
            
            # 创建任务队列
            task_queue = queue.Queue()
            
            # 存储线程池信息
            self.thread_pools[page_name] = {
                'pool': thread_pool,
                'queue': task_queue,
                'max_workers': max_workers,
                'created_at': time.time(),
                'task_count': 0
            }
            
            print(f"✅ 已为 {page_name} 页面创建线程池 (最大工作线程: {max_workers})")
            return True
            
        except Exception as e:
            print(f"❌ 创建 {page_name} 页面线程池失败: {e}")
            return False
            
    def submit_task(self, page_name: str, task_func: Callable, *args, **kwargs) -> Optional[concurrent.futures.Future]:
        """向页面线程池提交任务"""
        try:
            if page_name not in self.thread_pools:
                print(f"⚠️ 页面线程池 {page_name} 不存在")
                return None
                
            pool_info = self.thread_pools[page_name]
            thread_pool = pool_info['pool']
            
            # 提交任务
            future = thread_pool.submit(task_func, *args, **kwargs)
            
            # 增加任务计数
            pool_info['task_count'] += 1
            
            # 添加完成回调
            future.add_done_callback(lambda f: self._task_done_callback(page_name, f))
            
            return future
            
        except Exception as e:
            print(f"❌ 向 {page_name} 提交任务失败: {e}")
            return None
            
    def _task_done_callback(self, page_name: str, future: concurrent.futures.Future):
        """任务完成回调"""
        try:
            if future.cancelled():
                return
                
            if future.exception():
                # 任务失败
                error_msg = str(future.exception())
                self.task_failed.emit(page_name, error_msg)
            else:
                # 任务成功
                result = future.result()
                if isinstance(result, dict):
                    self.task_completed.emit(page_name, result)
                else:
                    self.task_completed.emit(page_name, {'result': result})
                    
        except Exception as e:
            print(f"❌ 任务回调处理失败: {e}")
            
    def register_callback(self, page_name: str, success_callback: Callable, error_callback: Callable = None):
        """注册页面回调函数"""
        self.callbacks[page_name] = {
            'success': success_callback,
            'error': error_callback or (lambda error: print(f"❌ {page_name} 任务失败: {error}"))
        }
        
        # 连接信号
        self.task_completed.connect(lambda name, result: self._handle_success(name, result))
        self.task_failed.connect(lambda name, error: self._handle_error(name, error))
        
    def _handle_success(self, page_name: str, result: dict):
        """处理成功回调"""
        if page_name in self.callbacks:
            try:
                self.callbacks[page_name]['success'](result)
            except Exception as e:
                print(f"❌ 执行 {page_name} 成功回调失败: {e}")
                
    def _handle_error(self, page_name: str, error: str):
        """处理错误回调"""
        if page_name in self.callbacks:
            try:
                self.callbacks[page_name]['error'](error)
            except Exception as e:
                print(f"❌ 执行 {page_name} 错误回调失败: {e}")
                
    def shutdown_page_pool(self, page_name: str, wait: bool = True):
        """关闭页面线程池"""
        try:
            if page_name not in self.thread_pools:
                return
                
            pool_info = self.thread_pools[page_name]
            thread_pool = pool_info['pool']
            
            # 关闭线程池
            thread_pool.shutdown(wait=wait)
            
            # 清理资源
            del self.thread_pools[page_name]
            print(f"✅ 已关闭 {page_name} 页面线程池")
            
        except Exception as e:
            print(f"❌ 关闭 {page_name} 页面线程池失败: {e}")
            
    def shutdown_all_pools(self, wait: bool = True):
        """关闭所有线程池"""
        print("🛑 正在关闭所有线程池...")
        
        for page_name in list(self.thread_pools.keys()):
            self.shutdown_page_pool(page_name, wait=wait)
            
        self.running = False
        print("✅ 所有线程池已关闭")
        
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取线程池统计信息"""
        stats = {
            'total_pools': len(self.thread_pools),
            'pools': {}
        }
        
        for page_name, pool_info in self.thread_pools.items():
            thread_pool = pool_info['pool']
            
            stats['pools'][page_name] = {
                'max_workers': pool_info['max_workers'],
                'task_count': pool_info['task_count'],
                'created_at': pool_info['created_at'],
                'uptime': time.time() - pool_info['created_at'],
                'active': not thread_pool._shutdown
            }
            
        return stats


# 邮箱任务函数
def mailbox_get_emails_task(config: dict) -> dict:
    """邮箱获取邮件任务"""
    try:
        # 延迟导入，避免主线程阻塞
        import sys
        from pathlib import Path
        
        # 确保路径正确
        project_root = Path(__file__).parent.parent.parent
        if str(project_root) not in sys.path:
            sys.path.insert(0, str(project_root))
        
        from src.email_service.email_api import EmailAPI

        # 创建API实例
        api = EmailAPI(config)
        emails = api.get_emails()
        
        return {
            'action': 'emails_result',
            'success': True,
            'data': emails,
            'count': len(emails) if emails else 0
        }
        
    except Exception as e:
        return {
            'action': 'emails_result',
            'success': False,
            'error': str(e)
        }

def mailbox_test_connection_task(config: dict) -> dict:
    """邮箱连接测试任务"""
    try:
        import sys
        from pathlib import Path
        
        project_root = Path(__file__).parent.parent.parent
        if str(project_root) not in sys.path:
            sys.path.insert(0, str(project_root))
        
        from src.email_service.email_api import EmailAPI

        api = EmailAPI(config)
        emails = api.get_emails()
        
        return {
            'action': 'test_result',
            'success': True,
            'count': len(emails) if emails else 0
        }
        
    except Exception as e:
        return {
            'action': 'test_result',
            'success': False,
            'error': str(e)
        }

# VSCode任务函数
def vscode_scan_task() -> dict:
    """VSCode扫描任务"""
    try:
        # 模拟VSCode扫描
        import time
        time.sleep(0.1)  # 模拟扫描时间
        
        return {
            'action': 'scan_result',
            'success': True,
            'data': {
                'cache_size': '125.6 MB',
                'log_files': 45,
                'temp_files': 23
            }
        }
        
    except Exception as e:
        return {
            'action': 'scan_result',
            'success': False,
            'error': str(e)
        }


# 全局线程池管理器实例
_thread_pool_manager = None

def get_thread_pool_manager() -> ThreadPoolManager:
    """获取全局线程池管理器"""
    global _thread_pool_manager
    if _thread_pool_manager is None:
        _thread_pool_manager = ThreadPoolManager()
    return _thread_pool_manager

def init_page_thread_pools():
    """初始化所有页面线程池"""
    manager = get_thread_pool_manager()
    
    # 创建邮箱页面线程池
    manager.create_page_thread_pool('mailbox', max_workers=2)
    
    # 创建VSCode页面线程池
    manager.create_page_thread_pool('vscode', max_workers=1)
    
    print("🚀 所有页面线程池初始化完成")

def cleanup_thread_pools():
    """清理所有线程池 - VSCode内存优化版本"""
    try:
        print("🧹 开始全面线程池清理...")
        manager = get_thread_pool_manager()
        if manager:
            # 获取线程池统计信息
            stats = manager.get_pool_stats()
            total_pools = stats.get('total_pools', 0)
            print(f"📊 线程池清理前状态: {total_pools}个活动池")
            
            # 关闭所有线程池
            manager.shutdown_all_pools(wait=True)
            print(f"✅ 所有线程池已关闭并清理")
            
            # 注销所有回调以避免内存泄漏
            if hasattr(manager, 'callbacks') and manager.callbacks:
                callback_count = len(manager.callbacks)
                manager.callbacks.clear()
                print(f"🔄 已注销 {callback_count} 个回调函数")
                
            # 清理任务队列
            if hasattr(manager, 'task_queues'):
                queue_count = len(manager.task_queues)
                for queue_name, queue in list(manager.task_queues.items()):
                    if hasattr(queue, 'queue') and hasattr(queue.queue, 'clear'):
                        queue.queue.clear()
                print(f"🗑️ 已清空 {queue_count} 个任务队列")
                
        # 额外的垃圾回收和内存优化
        try:
            import gc
            collected = gc.collect(2)  # 完全收集
            print(f"♻️ 垃圾回收: 已释放 {collected} 个对象")
        except Exception as e:
            print(f"⚠️ 垃圾回收异常: {e}")
            
        # 检查引用计数泄漏
        try:
            import sys
            if hasattr(sys, 'gettotalrefcount'):  # 仅在debug版本的Python中可用
                before = sys.gettotalrefcount()
                gc.collect()
                after = sys.gettotalrefcount()
                if after < before:
                    print(f"🔍 检测到引用计数减少: {before - after} (已修复的泄漏)")
        except Exception:
            pass  # 如果不可用则跳过
            
        print("🎉 线程池资源已完全清理")
        return True
        
    except Exception as e:
        print(f"❌ 线程池清理失败: {e}")
        import traceback
        traceback.print_exc()
        return False
